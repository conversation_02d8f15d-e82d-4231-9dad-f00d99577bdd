<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;
use Illuminate\Support\Facades\URL;

class GenerateReferralLinkAction extends Action
{
    /**
     * Generate referral links for an agent, including tier-specific links.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $agent = Agent::find($data['agent_id']);

        if (!$agent) {
            return [
                'success' => false,
                'message' => 'Agent not found'
            ];
        }

        // Ensure agent has a referral code and token
        if (!$agent->referral_code || !$agent->referral_token) {
            $agent->generateReferralCode();
        }

        // Generate the general referral URL using the shorter referral_code
        $referralUrl = URL::to('/agent/register') . '?agent_ref=' . $agent->referral_code;

        // Generate tier-specific referral URLs
        $tierSpecificUrls = $agent->generateTierSpecificReferralUrls();

        return [
            'success' => true,
            'referral_url' => $referralUrl,
            'referral_code' => $agent->referral_code,
            'tier_specific_urls' => $tierSpecificUrls,
            'message' => 'Referral links generated successfully'
        ];
    }
}
