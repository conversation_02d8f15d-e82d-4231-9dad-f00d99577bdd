<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\Business;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class GetDirectReferralsAction extends Action
{
    /**
     * Get direct patient and business referrals for an agent.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $agent = $data['agent'];
        $perPage = $data['per_page'] ?? 10;

        // Get patients directly referred by this agent
        $patients = $agent->referredPatients()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'patient');
            })
            ->with(['subscriptions' => function ($query) {
                $query->latest();
            }])
            ->latest()
            ->paginate($perPage, ['*'], 'patient_page');

        // Get businesses directly referred by this agent
        $businesses = $agent->referredBusinesses()
            ->with(['plans', 'users' => function ($query) {
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'business_admin');
                });
            }])
            ->latest()
            ->paginate($perPage, ['*'], 'business_page');

        // Calculate total counts
        $totalPatients = $agent->referredPatients()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'patient');
            })
            ->count();

        $totalBusinesses = $agent->referredBusinesses()->count();

        return [
            'patients' => $patients,
            'businesses' => $businesses,
            'total_patients' => $totalPatients,
            'total_businesses' => $totalBusinesses,
        ];
    }
}
