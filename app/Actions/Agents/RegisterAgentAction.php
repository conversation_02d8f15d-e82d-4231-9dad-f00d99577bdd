<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RegisterAgentAction extends Action
{
    /**
     * Register a new agent with optional referral.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            // Create or find user
            $user = $this->createOrFindUser($data);

            // Validate tier based on referrer
            $referringAgent = null;
            if (!empty($data['referring_agent_id'])) {
                $referringAgent = Agent::find($data['referring_agent_id']);

                // Validate tier restrictions
                if ($referringAgent && !$this->validateTierRestriction($referringAgent, $data['tier'])) {
                    return [
                        'success' => false,
                        'message' => 'Invalid tier selection for this referral. Please select a lower tier.'
                    ];
                }
            }

            // Set commission rate based on tier
            $commissionRate = Agent::TIERS[$data['tier']] ?? 30.00;

            // Check if agent already exists for this user
            $agent = Agent::where('user_id', $user->id)->first();
            $wasExistingAgent = false;

            if ($agent) {
                // Update existing agent
                $wasExistingAgent = true;
                $agent->status = 'approved';
                $agent->tier = $data['tier'];
                $agent->commission_rate = $commissionRate;
                $agent->company = $data['company'] ?? $agent->company;
                $agent->experience = $data['experience'];
                $agent->npn = $data['npn'] ?? $agent->npn;
                $agent->referring_agent_id = $referringAgent ? $referringAgent->id : $agent->referring_agent_id;
                $agent->save();
            } else {
                // Create new agent profile
                $agent = Agent::create([
                    'user_id' => $user->id,
                    'referring_agent_id' => $referringAgent ? $referringAgent->id : null,
                    'company' => $data['company'] ?? null,
                    'experience' => $data['experience'],
                    'tier' => $data['tier'],
                    'status' => 'approved', // Agents now start as approved
                    'commission_rate' => $commissionRate,
                    'npn' => $data['npn'] ?? null,
                ]);
            }

            // Generate referral code and token
            $agent->generateReferralCode();

            // Assign agent role to user
            $user->assignRole('agent');

            DB::commit();

            // $wasExistingAgent is already set above
            $message = $wasExistingAgent
                ? 'Agent updated successfully. Your account is now active and approved.'
                : 'Agent registered successfully. Your account is now active and approved.';

            return [
                'success' => true,
                'agent' => $agent,
                'user' => $user,
                'message' => $message
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Agent registration failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create a new user or find existing one by email.
     *
     * @param array $data
     * @return User
     */
    private function createOrFindUser(array $data): User
    {
        $user = User::where('email', $data['email'])->first();

        if (!$user) {
            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'fname' => $data['fname'] ?? null,
                'lname' => $data['lname'] ?? null,
                'phone' => $data['phone'] ?? null,
                'address1' => $data['address1'] ?? null,
                'address2' => $data['address2'] ?? null,
                'city' => $data['city'] ?? null,
                'state' => $data['state'] ?? null,
                'zip' => $data['zip'] ?? null,
                'status' => 'active',
            ]);
        } else {
            // Update existing user with new information
            $user->status = 'active';

            // Update address fields if provided
            if (isset($data['address1'])) $user->address1 = $data['address1'];
            if (isset($data['address2'])) $user->address2 = $data['address2'];
            if (isset($data['city'])) $user->city = $data['city'];
            if (isset($data['state'])) $user->state = $data['state'];
            if (isset($data['zip'])) $user->zip = $data['zip'];

            $user->save();
        }

        return $user;
    }

    /**
     * Validate tier restriction based on referrer's tier.
     *
     * @param Agent $referringAgent
     * @param string $selectedTier
     * @return bool
     */
    private function validateTierRestriction(Agent $referringAgent, string $selectedTier): bool
    {
        return $referringAgent->canReferTier($selectedTier);
    }
}
