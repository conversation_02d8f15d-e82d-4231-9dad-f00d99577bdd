<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;
use App\Models\User;
use App\Notifications\TemporaryPasswordNotification;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class UploadAgentsAction extends Action
{
    /**
     * Execute the action.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data)
    {
        return $this->handle($data['csv_file']);
    }

    /**
     * Upload agents from a CSV file
     *
     * @param UploadedFile $csvFile
     * @return array
     */
    public function handle(UploadedFile $csvFile)
    {
        try {
            $csv = array_map('str_getcsv', file($csvFile->getPathname()));
            $headers = array_shift($csv);
            
            // Convert headers to lowercase for case-insensitive matching
            $headers = array_map('strtolower', $headers);
            
            // Create associative array with headers as keys
            $data = [];
            foreach ($csv as $row) {
                $data[] = array_combine($headers, $row);
            }
            
            // Validate required fields
            $requiredFields = ['email', 'name', 'tier'];
            foreach ($data as $row) {
                foreach ($requiredFields as $field) {
                    if (!isset($row[$field]) || empty($row[$field])) {
                        return [
                            'success' => false,
                            'message' => "Missing required field: $field"
                        ];
                    }
                }
                
                // Validate tier
                if (!isset(Agent::TIERS[strtoupper($row['tier'])])) {
                    return [
                        'success' => false,
                        'message' => "Invalid tier: {$row['tier']}. Valid tiers are: " . implode(', ', array_keys(Agent::TIERS))
                    ];
                }
            }
            
            // Process each row
            $createdCount = 0;
            $updatedCount = 0;
            $errorCount = 0;
            
            DB::beginTransaction();
            
            foreach ($data as $row) {
                try {
                    // Check if user exists
                    $user = User::where('email', $row['email'])->first();
                    
                    if (!$user) {
                        // Create new user
                        $tempPassword = Str::random(10);
                        
                        $user = User::create([
                            'name' => $row['name'],
                            'email' => $row['email'],
                            'password' => Hash::make($tempPassword),
                            'status' => 'active',
                            'fname' => $row['fname'] ?? explode(' ', $row['name'])[0] ?? '',
                            'lname' => $row['lname'] ?? (count(explode(' ', $row['name'])) > 1 ? explode(' ', $row['name'])[1] : ''),
                            'phone' => $row['phone'] ?? null,
                        ]);
                        
                        // Send temporary password notification
                        $user->notify(new TemporaryPasswordNotification($tempPassword));
                    }
                    
                    // Check if agent exists
                    $agent = Agent::where('user_id', $user->id)->first();
                    
                    if ($agent) {
                        // Update existing agent
                        $agent->update([
                            'tier' => strtoupper($row['tier']),
                            'commission_rate' => Agent::TIERS[strtoupper($row['tier'])],
                            'company' => $row['company'] ?? $agent->company,
                            'experience' => $row['experience'] ?? $agent->experience,
                            'status' => $row['status'] ?? 'approved',
                        ]);
                        
                        $updatedCount++;
                    } else {
                        // Create new agent
                        $agent = Agent::create([
                            'user_id' => $user->id,
                            'tier' => strtoupper($row['tier']),
                            'commission_rate' => Agent::TIERS[strtoupper($row['tier'])],
                            'company' => $row['company'] ?? null,
                            'experience' => $row['experience'] ?? '1-3',
                            'status' => $row['status'] ?? 'approved',
                        ]);
                        
                        // Generate referral code and token
                        $agent->generateReferralCode();
                        
                        // Assign agent role to user
                        $user->assignRole('agent');
                        
                        $createdCount++;
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing agent row: ' . $e->getMessage(), [
                        'row' => $row,
                        'exception' => $e
                    ]);
                    $errorCount++;
                }
            }
            
            DB::commit();
            
            return [
                'success' => true,
                'created' => $createdCount,
                'updated' => $updatedCount,
                'errors' => $errorCount,
                'message' => "Processed {$createdCount} new agents and updated {$updatedCount} existing agents. {$errorCount} errors occurred."
            ];
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to upload agents: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Failed to upload agents: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate a CSV template for agent uploads
     *
     * @return string
     */
    public function generateCsvTemplate()
    {
        $headers = [
            'email',
            'name',
            'fname',
            'lname',
            'phone',
            'tier',
            'company',
            'experience',
            'status',
        ];

        $sample = [
            '<EMAIL>',
            'John Doe',
            'John',
            'Doe',
            '************',
            'AGENT',
            'ABC Agency',
            '1-3',
            'approved',
        ];

        $output = fopen('php://temp', 'r+');
        fputcsv($output, $headers);
        fputcsv($output, $sample);

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }
}
