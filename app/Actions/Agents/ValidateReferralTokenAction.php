<?php

namespace App\Actions\Agents;

use App\Actions\Action;
use App\Models\Agent;

class ValidateReferralTokenAction extends Action
{
    /**
     * Validate a referral token and return the referring agent.
     * Also handles tier-specific referrals.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $token = $data['token'] ?? null;
        $specifiedTier = $data['tier'] ?? null;

        if (!$token) {
            return [
                'success' => false,
                'message' => 'No referral token provided'
            ];
        }

        // Use referral_code instead of referral_token for shorter URLs
        $referringAgent = Agent::where('referral_code', $token)
            ->where('status', 'approved')
            ->first();

        if (!$referringAgent) {
            return [
                'success' => false,
                'message' => 'Invalid or expired referral token'
            ];
        }

        // Get allowed tiers for this referrer
        $allowedTiers = $referringAgent->getAllowedTiersForReferrals();

        // If a specific tier was requested, validate it
        if ($specifiedTier) {
            // If AGENT tier is specified or no tier is specified, always allow it
            if ($specifiedTier === 'AGENT') {
                $selectedTiers = ['AGENT' => Agent::TIERS['AGENT']];

                return [
                    'success' => true,
                    'referring_agent' => $referringAgent,
                    'allowed_tiers' => $allowedTiers,
                    'selected_tier' => 'AGENT',
                    'selected_tiers' => $selectedTiers,
                    'message' => 'Referral token validated successfully with AGENT tier'
                ];
            }

            // For other tiers, validate against allowed tiers
            if (!isset($allowedTiers[$specifiedTier])) {
                return [
                    'success' => false,
                    'message' => 'The specified tier is not allowed for this referral',
                    'referring_agent' => $referringAgent,
                    'allowed_tiers' => $allowedTiers
                ];
            }

            // Return only the specified tier
            $selectedTiers = [$specifiedTier => $allowedTiers[$specifiedTier]];

            return [
                'success' => true,
                'referring_agent' => $referringAgent,
                'allowed_tiers' => $allowedTiers,
                'selected_tier' => $specifiedTier,
                'selected_tiers' => $selectedTiers,
                'message' => 'Referral token validated successfully with specified tier'
            ];
        }

        // Default to AGENT tier if no specific tier was requested
        $selectedTiers = ['AGENT' => Agent::TIERS['AGENT']];

        return [
            'success' => true,
            'referring_agent' => $referringAgent,
            'allowed_tiers' => $allowedTiers,
            'selected_tier' => 'AGENT',
            'selected_tiers' => $selectedTiers,
            'message' => 'Referral token validated successfully with default AGENT tier'
        ];
    }
}
