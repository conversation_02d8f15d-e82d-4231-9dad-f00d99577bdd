<?php

namespace App\Actions\Auth;

use App\Actions\Action;
use App\Mail\LoginLinkEmail;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendLoginLink extends Action
{
    /**
     * Execute the action.
     *
     * @param array $data
     * @return bool
     */
    public function execute(array $data)
    {
        // Extract the user from the data array
        $user = $data['user'] ?? null;

        // Check if we have a valid User instance
        if (!$user instanceof User) {
            Log::error('SendLoginLink action failed: Invalid or missing user', [
                'provided_data' => $data
            ]);
            return false;
        }

        try {
            Log::info('Starting SendLoginLink action for user', ['user_id' => $user->id, 'email' => $user->email]);

            // Create a token with an expiration date
            $token = $user->createToken('login-link', ['*'], now()->addHours(24))->plainTextToken;

            // Send email
            Mail::to($user)->send(new LoginLinkEmail($user, $token));

            Log::info('Login link email sent successfully');

            return true;
        } catch (\Exception $e) {
            Log::error('Error in SendLoginLink action', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
}
