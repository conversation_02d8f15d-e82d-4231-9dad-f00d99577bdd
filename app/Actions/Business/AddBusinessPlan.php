<?php

namespace App\Actions\Business;

use App\Actions\Action;
use App\Actions\Subscriptions\CreatePendingSubscription;
use App\Models\Business;
use App\Models\BusinessPlan;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class AddBusinessPlan extends Action
{
    protected $createPendingSubscription;

    /**
     * Constructor
     *
     * @param CreatePendingSubscription $createPendingSubscription
     */
    public function __construct(CreatePendingSubscription $createPendingSubscription)
    {
        $this->createPendingSubscription = $createPendingSubscription;
    }
    /**
     * Execute the action to add a new plan to the business
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data)
    {
        // Extract business from data
        $business = $data['business'] ?? null;

        if (!$business instanceof Business) {
            throw new \InvalidArgumentException('Business is required');
        }
        // Validate data
        if (!isset($data['plan_quantity']) || !isset($data['price_per_plan']) || !isset($data['duration_months'])) {
            throw new \InvalidArgumentException('Plan quantity, price per plan, and duration are required');
        }

        // Get plan details
        $planQuantity = $data['plan_quantity'];
        $pricePerPlan = $data['price_per_plan'];
        $durationMonths = $data['duration_months'];
        $active = $data['active'] ?? false;

        // Calculate discount based on duration
        $discountPercent = $this->getDiscountPercent($durationMonths);
        $discountMultiplier = (100 - $discountPercent) / 100;

        // Calculate total price with discount
        $blocks = ceil($planQuantity / 5);
        $monthlyPrice = $blocks * 5 * $pricePerPlan; // Monthly price in cents
        $totalPrice = $monthlyPrice * $durationMonths * $discountMultiplier; // Total price for the entire duration

        try {
            DB::beginTransaction();

            // Calculate start and end dates
            // Start date is the beginning of the current month
            $startDate = Carbon::now()->startOfMonth();

            // End date is the end of the month after adding (duration_months - 1) months
            // We subtract 1 because the current month counts as the first month
            $endDate = $startDate->copy()->addMonths($durationMonths - 1)->endOfMonth();

            // Get ClickID and AFID for affiliate tracking
            $clickId = \Illuminate\Support\Facades\Cookie::get('LTClickID') ??
                      session('LTClickID') ??
                      request()->query('ClickID');

            $afid = \Illuminate\Support\Facades\Cookie::get('AFID') ??
                   session('AFID') ??
                   request()->query('AFID');

            // Create business plan
            $plan = BusinessPlan::create([
                'business_id' => $business->id,
                'plan_quantity' => $planQuantity,
                'price_per_plan' => $pricePerPlan,
                'total_price' => $totalPrice,
                'starts_at' => $startDate,
                'ends_at' => $endDate,
                'active' => $active,
                'duration_months' => $durationMonths,
                'discount_percent' => $discountPercent,
                'click_id' => $clickId, // Store ClickID for affiliate tracking
                'afid' => $afid, // Store AFID for recurring billing
            ]);

            // Get the business admin user
            $adminUser = $business->users()
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'business_admin');
                })
                ->first();

            // If no admin user is found, use the first user associated with the business
            if (!$adminUser) {
                $adminUser = $business->users()->first();
            }

            // Only create subscription if we have a user
            $subscription = null;
            if ($adminUser) {
                // Get a premium business plan (using the first premium plan)
                $premiumPlan = SubscriptionPlan::where('group_id', 3) // Premium Plans group
                    ->where('is_active', true)
                    ->first();

                if ($premiumPlan) {
                    // Create subscription with pending_payment status
                    $subscriptionResult = $this->createPendingSubscription->execute([
                        'user' => $adminUser,
                        'plan' => $premiumPlan,
                        'session_data' => [
                            'source' => 'business_plan_creation'
                        ]
                    ]);

                    if ($subscriptionResult['success']) {
                        $subscription = $subscriptionResult['subscription'];

                        // Update subscription with business-specific data
                        $subscription->update([
                            'ends_at' => $plan->ends_at, // Use the same end date as the business plan
                            'user_type' => 'business_admin',
                            'meta_data' => array_merge($subscription->meta_data ?? [], [
                                'business_id' => $business->id,
                                'business_plan_id' => $plan->id,
                                'enrollment_type' => 'business'
                            ])
                        ]);

                        Log::info('Created subscription for business plan', [
                            'business_id' => $business->id,
                            'business_plan_id' => $plan->id,
                            'subscription_id' => $subscription->id,
                            'user_id' => $adminUser->id
                        ]);
                    } else {
                        Log::warning('Failed to create subscription for business plan', [
                            'business_id' => $business->id,
                            'business_plan_id' => $plan->id,
                            'error' => $subscriptionResult['error'] ?? 'Unknown error'
                        ]);
                    }
                } else {
                    Log::warning('No premium subscription plan found for business subscription', [
                        'business_id' => $business->id,
                        'business_plan_id' => $plan->id
                    ]);
                }
            } else {
                Log::warning('No admin user found for business when creating subscription', [
                    'business_id' => $business->id,
                    'business_plan_id' => $plan->id
                ]);
            }

            DB::commit();

            // Format message with discount information
            $message = 'Plan blocks added successfully!';
            if ($discountPercent > 0) {
                $message .= " You received a {$discountPercent}% discount for selecting a {$durationMonths}-month plan.";
            }

            return [
                'success' => true,
                'plan' => $plan,
                'subscription' => $subscription,
                'message' => $message
            ];
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to add business plan', [
                'error' => $e->getMessage(),
                'business_id' => $business->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to add plan blocks. Please try again.',
                'error' => $e->getMessage(),
                'subscription' => null
            ];
        }
    }

    /**
     * Get the discount percentage based on the plan duration
     *
     * @param int $durationMonths
     * @return int
     */
    private function getDiscountPercent(int $durationMonths): int
    {
        switch ($durationMonths) {
            case 3:
                return 5; // 5% discount for 3 months
            case 6:
                return 10; // 10% discount for 6 months
            case 12:
                return 20; // 20% discount for 12 months
            default:
                return 0; // No discount for 1 month
        }
    }
}
