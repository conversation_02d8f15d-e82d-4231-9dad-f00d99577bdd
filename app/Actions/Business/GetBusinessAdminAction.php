<?php

namespace App\Actions\Business;

use App\Actions\Action;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class GetBusinessAdminAction extends Action
{
    /**
     * Get the business admin user for a given business HR user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        
        try {
            // If user is business_admin, return self
            if ($user->hasRole('business_admin')) {
                return [
                    'success' => true,
                    'business_admin' => $user,
                    'is_self' => true
                ];
            }
            
            // If user is business_hr, find the business_admin for the same business
            if ($user->hasRole('business_hr') && $user->business_id) {
                $businessAdmin = User::role('business_admin')
                    ->where('business_id', $user->business_id)
                    ->first();
                
                if ($businessAdmin) {
                    return [
                        'success' => true,
                        'business_admin' => $businessAdmin,
                        'is_self' => false
                    ];
                } else {
                    Log::warning('Business HR user without a Business Admin', [
                        'user_id' => $user->id,
                        'business_id' => $user->business_id
                    ]);
                    
                    return [
                        'success' => false,
                        'message' => 'No business admin found for this business'
                    ];
                }
            }
            
            return [
                'success' => false,
                'message' => 'User is not a business admin or business HR'
            ];
        } catch (\Exception $e) {
            Log::error('Error in GetBusinessAdminAction', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => 'Error retrieving business admin: ' . $e->getMessage()
            ];
        }
    }
}
