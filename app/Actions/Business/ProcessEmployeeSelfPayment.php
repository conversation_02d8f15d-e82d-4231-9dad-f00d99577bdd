<?php

namespace App\Actions\Business;

use App\Models\BusinessEmployee;
use App\Services\BusinessPlanSelfPaymentService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProcessEmployeeSelfPayment
{
    public function __construct(
        protected BusinessPlanSelfPaymentService $selfPaymentService
    ) {}

    /**
     * Execute the self-payment process for an employee.
     */
    public function execute(BusinessEmployee $employee, array $paymentData): array
    {
        try {
            // Validate payment data
            $validator = Validator::make($paymentData, [
                'card_number' => ['required', 'string', 'regex:/^\d{13,19}$/'],
                'expiration_month' => ['required', 'string', 'regex:/^(0[1-9]|1[0-2])$/'],
                'expiration_year' => ['required', 'string', 'regex:/^\d{4}$/', 'after_or_equal:' . date('Y')],
                'cvv' => ['required', 'string', 'regex:/^\d{3,4}$/'],
                'cardholder_name' => ['required', 'string', 'max:255'],
                'billing_address' => ['sometimes', 'array'],
                'billing_address.address1' => ['sometimes', 'string', 'max:255'],
                'billing_address.city' => ['sometimes', 'string', 'max:100'],
                'billing_address.state' => ['sometimes', 'string', 'max:50'],
                'billing_address.zip' => ['sometimes', 'string', 'max:20'],
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Invalid payment information provided.',
                    'errors' => $validator->errors()->toArray(),
                ];
            }

            // Validate self-payment eligibility
            $validationResult = $this->selfPaymentService->validateSelfPayment($employee);
            if (!$validationResult['success']) {
                return $validationResult;
            }

            $businessPlan = $validationResult['business_plan'];

            // Clean up any stale pending payments before proceeding
            $this->selfPaymentService->cleanupStalePendingPayments($employee, $businessPlan);

            // Process the payment atomically (creates pending record and processes payment in one transaction)
            $paymentResult = $this->selfPaymentService->processAtomicSelfPayment(
                $employee,
                $businessPlan,
                array_merge($paymentData, [
                    'payment_method' => 'credit_card',
                    'cardholder_name' => $paymentData['cardholder_name'],
                    'billing_address' => $paymentData['billing_address'] ?? null,
                ])
            );

            if ($paymentResult['success']) {
                $selfPayment = $paymentResult['self_payment'];

                Log::info('Self-payment processed successfully', [
                    'self_payment_id' => $selfPayment->id,
                    'transaction_id' => $paymentResult['transaction_id'],
                    'employee_id' => $employee->id,
                ]);

                return [
                    'success' => true,
                    'message' => 'Your payment of $20.00 has been processed successfully! Your business plan has been updated.',
                    'data' => [
                        'self_payment_id' => $selfPayment->id,
                        'transaction_id' => $paymentResult['transaction_id'],
                        'amount_paid' => $selfPayment->amount_in_dollars,
                        'business_plan' => [
                            'id' => $businessPlan->id,
                            'remaining_quantity' => $businessPlan->fresh()->remaining_quantity,
                            'total_price_dollars' => $businessPlan->fresh()->total_price_in_dollars,
                        ],
                    ],
                ];
            } else {
                Log::warning('Self-payment failed', [
                    'employee_id' => $employee->id,
                    'error_message' => $paymentResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => $paymentResult['message'],
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during self-payment processing', [
                'employee_id' => $employee->id,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An unexpected error occurred while processing your payment. Please try again or contact support.',
            ];
        }
    }

    /**
     * Get self-payment eligibility information for an employee.
     */
    public function getEligibilityInfo(BusinessEmployee $employee): array
    {
        try {
            $validationResult = $this->selfPaymentService->validateSelfPayment($employee);

            if (!$validationResult['success']) {
                return [
                    'eligible' => false,
                    'message' => $validationResult['message'],
                ];
            }

            $businessPlan = $validationResult['business_plan'];

            return [
                'eligible' => true,
                'message' => 'You are eligible to make a self-payment of $20.00.',
                'data' => [
                    'amount_dollars' => $businessPlan->price_per_plan / 100,
                    'business_plan' => [
                        'id' => $businessPlan->id,
                        'current_quantity' => $businessPlan->plan_quantity,
                        'remaining_quantity' => $businessPlan->remaining_quantity,
                        'total_price_dollars' => $businessPlan->total_price_in_dollars,
                        'starts_at' => $businessPlan->starts_at->format('M j, Y'),
                        'ends_at' => $businessPlan->ends_at?->format('M j, Y'),
                        'duration_months' => $businessPlan->duration_months,
                    ],
                    'business' => [
                        'name' => $employee->business->name,
                        'total_employees' => $employee->business->employees()->where('status', 'active')->count(),
                    ],
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Exception while checking self-payment eligibility', [
                'employee_id' => $employee->id,
                'exception' => $e->getMessage(),
            ]);

            return [
                'eligible' => false,
                'message' => 'Unable to check eligibility at this time. Please try again later.',
            ];
        }
    }

    /**
     * Get self-payment history for an employee.
     */
    public function getPaymentHistory(BusinessEmployee $employee): array
    {
        try {
            return [
                'success' => true,
                'data' => $this->selfPaymentService->getEmployeeSelfPaymentHistory($employee),
            ];
        } catch (\Exception $e) {
            Log::error('Exception while retrieving self-payment history', [
                'employee_id' => $employee->id,
                'exception' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Unable to retrieve payment history at this time.',
                'data' => [],
            ];
        }
    }
}
