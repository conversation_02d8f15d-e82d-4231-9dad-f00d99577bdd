<?php

namespace App\Actions\Business;

use App\Actions\Action;
use App\Actions\Auth\SendLoginLink;
use App\Actions\Subscriptions\CreatePendingSubscription;
use App\Models\Business;
use App\Models\BusinessContact;
use App\Models\BusinessPlan;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class RegisterBusinessAction extends Action
{
    protected $createPendingSubscription;

    /**
     * Constructor
     *
     * @param CreatePendingSubscription $createPendingSubscription
     */
    public function __construct(CreatePendingSubscription $createPendingSubscription)
    {
        $this->createPendingSubscription = $createPendingSubscription;
    }
    /**
     * Execute the business registration action.
     *
     * @param array $data The validated data from the request
     * @return array The result of the registration
     */
    public function execute(array $data): array
    {
        try {
            DB::beginTransaction();

            // Create the business record
            $business = $this->createBusiness($data);

            // Start trial if enabled
            if ($business->trial_enabled) {
                $business->startTrial();
            }

            // Create business contact
            $this->createBusinessContact($business, $data);

            // Create admin user first (needed for subscription)
            $adminUser = $this->createAdminUser($business, $data);

            // Create business plan and subscription
            $planResult = $this->createBusinessPlan($business, $adminUser, $data);
            $businessPlan = $planResult['business_plan'];
            $subscription = $planResult['subscription'];

            // Create HR user if email is provided
            $hrUser = null;
            if (!empty($data['hr_contact_email'])) {
                $hrUser = $this->createHrUser($business, $data);
            }

            // Get tracking IDs for affiliate tracking
            $clickId = Cookie::get('LTClickID') ??
                      session('LTClickID') ??
                      $data['click_id'] ?? null;

            $afid = Cookie::get('AFID') ??
                   session('AFID') ??
                   $data['afid'] ?? null;

            // Get referring agent ID from cookie or session
            $referringAgentId = Cookie::get('referring_agent_id') ??
                               session('referring_agent_id') ??
                               $data['referring_agent_id'] ?? null;

            // Create transaction record
            $transactionId = 'BUSINESS-REG-' . $business->id . '-' . time();
            $transaction = $this->createTransaction($adminUser, $business, $businessPlan, $subscription, $transactionId, $clickId, $afid);

            DB::commit();

            return [
                'success' => true,
                'business' => $business,
                'admin_user' => $adminUser,
                'hr_user' => $hrUser,
                'business_plan' => $businessPlan,
                'subscription' => $subscription,
                'transaction' => $transaction,
                'transaction_id' => $transactionId,
                'message' => 'Business registration completed successfully!'
            ];
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Business registration failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage(),
                'exception' => $e
            ];
        }
    }

    /**
     * Create a new business record.
     *
     * @param array $data
     * @return Business
     */
    private function createBusiness(array $data): Business
    {
        // Get referring agent ID from cookie or session
        $referringAgentId = Cookie::get('referring_agent_id') ??
                           session('referring_agent_id') ??
                           $data['referring_agent_id'] ?? null;

        // Log the referring agent ID sources for debugging
        Log::info('Creating business with referring agent', [
            'cookie_agent_id' => Cookie::get('referring_agent_id'),
            'session_agent_id' => session('referring_agent_id'),
            'data_agent_id' => $data['referring_agent_id'] ?? null,
            'final_agent_id' => $referringAgentId
        ]);

        return Business::create([
            'name' => $data['business_name'],
            'email' => $data['business_email'],
            'phone' => $data['phone'],
            'address1' => $data['address1'],
            'address2' => $data['address2'] ?? null,
            'city' => $data['city'],
            'state' => $data['state'],
            'zip' => $data['zip'],
            'referring_agent_id' => $referringAgentId,
            // 'trial_enabled' => (bool) ($data['trial_enabled'] ?? false),
        ]);
    }

    /**
     * Create a business contact record.
     *
     * @param Business $business
     * @param array $data
     * @return BusinessContact
     */
    private function createBusinessContact(Business $business, array $data): BusinessContact
    {
        return BusinessContact::create([
            'business_id' => $business->id,
            'owner_fname' => $data['owner_fname'],
            'owner_lname' => $data['owner_lname'],
            'billing_contact_email' => $data['billing_contact_email'],
            'billing_contact_phone' => $data['billing_contact_phone'],
            'hr_contact_email' => $data['hr_contact_email'] ?? null,
            'hr_contact_phone' => $data['hr_contact_phone'] ?? null,
        ]);
    }

    /**
     * Create a business plan record and associated subscription.
     *
     * @param Business $business
     * @param User $adminUser
     * @param array $data
     * @return array
     */
    private function createBusinessPlan(Business $business, User $adminUser, array $data): array
    {
        // Calculate total price
        $planQuantity = $data['plan_quantity'];
        $blocks = ceil($planQuantity / 5);
        $pricePerPlan = 2000; // $20.00 per plan stored as 2000 cents
        // Always charge for complete blocks of 5 - if quantity is 6-10, charge for 10 (2 blocks)
        $totalPrice = $blocks * 5 * $pricePerPlan; // Price in cents

        // Create business plan - active only for the current month
        $now = Carbon::now();
        $endOfMonth = Carbon::now()->endOfMonth();

        $businessPlan = BusinessPlan::create([
            'business_id' => $business->id,
            'plan_quantity' => $planQuantity,
            'price_per_plan' => $pricePerPlan,
            'total_price' => $totalPrice,
            'starts_at' => $now,
            'ends_at' => $endOfMonth,
            'active' => false,
        ]);

        // Create a subscription for the business admin
        $subscription = null;

        // Get a premium business plan (using the first premium plan)
        $premiumPlan = SubscriptionPlan::where('group_id', 3) // Premium Plans group
            ->where('is_active', true)
            ->first();

        if ($premiumPlan) {
            // Create subscription with pending_payment status
            $subscriptionResult = $this->createPendingSubscription->execute([
                'user' => $adminUser,
                'plan' => $premiumPlan,
                'session_data' => [
                    'source' => 'business_registration'
                ]
            ]);

            if ($subscriptionResult['success']) {
                $subscription = $subscriptionResult['subscription'];

                // Update subscription with business-specific data
                $subscription->update([
                    'ends_at' => $businessPlan->ends_at, // Use the same end date as the business plan
                    'user_type' => 'business_admin',
                    'meta_data' => array_merge($subscription->meta_data ?? [], [
                        'business_id' => $business->id,
                        'business_plan_id' => $businessPlan->id,
                        'enrollment_type' => 'business'
                    ])
                ]);

                Log::info('Created subscription for business registration', [
                    'business_id' => $business->id,
                    'business_plan_id' => $businessPlan->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $adminUser->id
                ]);
            } else {
                Log::warning('Failed to create subscription for business registration', [
                    'business_id' => $business->id,
                    'business_plan_id' => $businessPlan->id,
                    'error' => $subscriptionResult['error'] ?? 'Unknown error'
                ]);
            }
        } else {
            Log::warning('No premium subscription plan found for business registration', [
                'business_id' => $business->id,
                'business_plan_id' => $businessPlan->id
            ]);
        }

        return [
            'business_plan' => $businessPlan,
            'subscription' => $subscription
        ];
    }

    /**
     * Create an admin user for the business.
     *
     * @param Business $business
     * @param array $data
     * @return User
     */
    private function createAdminUser(Business $business, array $data): User
    {
        $adminUser = User::create([
            'name' => $data['owner_fname'] . ' ' . $data['owner_lname'],
            'fname' => $data['owner_fname'],
            'lname' => $data['owner_lname'],
            'phone' => $data['billing_contact_phone'],
            'email' => $data['billing_contact_email'],
            'password' => bcrypt($data['billing_contact_phone']),
            'business_id' => $business->id,
            'status' => 'active',
            'email_verified_at' => now(),
            'address1' => $data['address1'],
            'address2' => $data['address2'] ?? null,
            'city' => $data['city'],
            'state' => $data['state'],
            'zip' => $data['zip'],
        ]);

        // Assign business_admin role from Spatie permissions
        $businessAdminRole = Role::findByName('business_admin', 'web');
        $adminUser->assignRole($businessAdminRole);

        // Send login link
        $this->sendLoginLink($adminUser);

        return $adminUser;
    }

    /**
     * Create an HR user for the business.
     *
     * @param Business $business
     * @param array $data
     * @return User|null
     */
    private function createHrUser(Business $business, array $data): ?User
    {
        if (empty($data['hr_contact_email'])) {
            return null;
        }

        $hrUser = User::create([
            'business_id' => $business->id,
            'name' => 'HR',
            'email' => $data['hr_contact_email'],
            'phone' => $data['hr_contact_phone'] ?? $data['billing_contact_phone'],
            'password' => bcrypt($data['hr_contact_phone'] ?? $data['billing_contact_phone']),
            'status' => 'active',
        ]);

        $businessHrRole = Role::findByName('business_hr', 'web');
        $hrUser->assignRole($businessHrRole);

        // Send login link
        $this->sendLoginLink($hrUser);

        return $hrUser;
    }

    /**
     * Send a login link to the user.
     *
     * @param User $user
     * @return bool
     */
    private function sendLoginLink(User $user): bool
    {
        try {
            $sendLoginLinkAction = app(SendLoginLink::class);
            $result = $sendLoginLinkAction->execute(['user' => $user]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to send login link to user', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Create a transaction record.
     *
     * @param User $user
     * @param Business $business
     * @param BusinessPlan $businessPlan
     * @param Subscription|null $subscription
     * @param string $transactionId
     * @param string|null $clickId
     * @param string|null $afid
     * @return Transaction
     */
    private function createTransaction(
        User $user,
        Business $business,
        BusinessPlan $businessPlan,
        ?Subscription $subscription,
        string $transactionId,
        ?string $clickId,
        ?string $afid
    ): Transaction {
        // Get referring agent ID
        $referringAgentId = $business->referring_agent_id;

        $metaData = [
            'business_id' => $business->id,
            'business_plan_id' => $businessPlan->id,
            'click_id' => $clickId,
            'afid' => $afid,
            'registration_type' => 'new_business',
            'referring_agent_id' => $referringAgentId,
        ];

        // Add subscription ID if available
        if ($subscription) {
            $metaData['subscription_id'] = $subscription->id;
        }

        return Transaction::create([
            'user_id' => $user->id,
            'subscription_id' => $subscription ? $subscription->id : null,
            'amount' => $businessPlan->total_price,
            'currency' => 'USD',
            'transaction_id' => $transactionId,
            'payment_method' => 'business_registration',
            'status' => 'success',
            'meta_data' => $metaData,
        ]);
    }
}
