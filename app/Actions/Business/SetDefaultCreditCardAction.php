<?php

namespace App\Actions\Business;

use App\Actions\Action;
use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SetDefaultCreditCardAction extends Action
{
    /**
     * Set a credit card as default for a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $creditCard = $data['credit_card'];

        try {
            // Ensure the credit card belongs to the user
            if ($creditCard->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'This credit card does not belong to the user'
                ];
            }

            // Set all cards to non-default
            $user->creditCards()->update(['is_default' => false]);

            // Set the selected card as default
            $creditCard->is_default = true;
            $creditCard->save();

            return [
                'success' => true,
                'message' => 'Default credit card updated successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error setting default credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to update default credit card: ' . $e->getMessage()
            ];
        }
    }
}
