<?php

namespace App\Actions\MedicalRecord;

use App\Actions\Action;
use App\Models\Condition;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CreateMedicalCondition extends Action
{
    /**
     * Create a new medical condition record
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            $user = $data['user'];
            $request = $data['request'];
            
            $conditions = Condition::select('id', 'name')
                ->whereIn('id', $request->condition_ids)
                ->get();

            $conditionsData = $conditions->map(function ($condition) {
                return [
                    'condition_id' => $condition->id,
                    'condition_name' => $condition->name,
                    'is_custom' => false,
                ];
            })->toArray();

            $user->medicalConditions()->createMany($conditionsData);

            return [
                'success' => true,
                'message' => 'Medical conditions saved successfully!'
            ];
        } catch (\Exception $e) {
            Log::error('Error in CreateMedicalCondition action: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to save medical conditions. Please try again.',
                'error' => $e->getMessage()
            ];
        }
    }
}
