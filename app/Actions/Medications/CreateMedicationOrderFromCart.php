<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\Cart;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class CreateMedicationOrderFromCart extends Action
{
    /**
     * Create a medication order from cart items
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $sessionId = $data['session_id'] ?? Session::getId();
        $patientNotes = $data['patient_notes'] ?? null;

        try {
            // Start a database transaction
            DB::beginTransaction();

            // Get all cart items that might be associated with this user
            // This includes items with the current session ID, any items with the user's ID,
            // and we'll also check for items with no user_id that might have been added before login

            // First, get all session IDs from cookies that might contain cart items
            $originalSessionId = $data['original_session_id'] ?? null;
            $allSessionIds = $data['all_session_ids'] ?? [];

            // Log all session IDs for debugging
            Log::info('All session IDs for cart lookup', [
                'user_id' => $user->id,
                'current_session_id' => $sessionId,
                'original_session_id' => $originalSessionId,
                'all_session_ids' => $allSessionIds
            ]);

            // Build a query to find cart items that belong to this user
            $cartQuery = Cart::query();

            // Add conditions to find cart items that belong to this specific user
            $cartQuery->where(function($query) use ($user, $sessionId, $originalSessionId, $allSessionIds) {
                // Find items associated with the user ID
                $query->where('user_id', $user->id);

                // Or find items with the current session ID (but only if no user_id is set)
                $query->orWhere(function($subQuery) use ($sessionId) {
                    $subQuery->where('session_id', $sessionId)
                             ->whereNull('user_id');
                });

                // Or find items with the original session ID (if provided and different from current)
                if ($originalSessionId && $originalSessionId !== $sessionId) {
                    $query->orWhere(function($subQuery) use ($originalSessionId) {
                        $subQuery->where('session_id', $originalSessionId)
                                 ->whereNull('user_id');
                    });
                }

                // Or find items with any of the provided session IDs that belong to this user's session history
                if (!empty($allSessionIds)) {
                    foreach ($allSessionIds as $sid) {
                        if ($sid && $sid !== $sessionId && $sid !== $originalSessionId) {
                            $query->orWhere(function($subQuery) use ($sid) {
                                $subQuery->where('session_id', $sid)
                                         ->whereNull('user_id');
                            });
                        }
                    }
                }
            });

            // Get the cart items with their related medication data
            $cartItems = $cartQuery->with(['medicationVariant.medicationBase'])->get();

            // Log the query details for debugging
            Log::info('Cart query details', [
                'user_id' => $user->id,
                'session_id' => $sessionId,
                'original_session_id' => $originalSessionId,
                'items_found' => $cartItems->count(),
                'cart_items' => $cartItems->map(function($item) {
                    return [
                        'id' => $item->id,
                        'session_id' => $item->session_id,
                        'medication_variant_id' => $item->medication_variant_id
                    ];
                })
            ]);

            // If no cart items, return early
            if ($cartItems->isEmpty()) {
                Log::info('No cart items found for user', [
                    'user_id' => $user->id,
                    'session_id' => $sessionId
                ]);

                return [
                    'success' => false,
                    'message' => 'No items in cart'
                ];
            }

            // Create a new medication order
            $medicationOrder = MedicationOrder::create([
                'patient_id' => $user->id,
                'status' => 'pending',
                'patient_notes' => $patientNotes,
            ]);

            Log::info('Created medication order from cart', [
                'order_id' => $medicationOrder->id,
                'user_id' => $user->id,
                'cart_items_count' => $cartItems->count()
            ]);

            // First, associate any cart items that don't have a user_id with this user
            foreach ($cartItems as $cartItem) {
                if ($cartItem->user_id === null) {
                    $cartItem->user_id = $user->id;
                    $cartItem->save();

                    Log::info('Associated cart item with user', [
                        'cart_item_id' => $cartItem->id,
                        'user_id' => $user->id,
                        'session_id' => $cartItem->session_id
                    ]);
                }
            }

            // Create medication order items from cart items
            foreach ($cartItems as $cartItem) {
                try {
                    $medicationVariant = $cartItem->medicationVariant;

                    if (!$medicationVariant) {
                        Log::warning('Cart item has no medication variant', [
                            'cart_item_id' => $cartItem->id,
                            'medication_variant_id' => $cartItem->medication_variant_id
                        ]);
                        continue;
                    }

                    $medicationBase = $medicationVariant->medicationBase;

                    if (!$medicationBase) {
                        Log::warning('Medication variant has no base', [
                            'cart_item_id' => $cartItem->id,
                            'medication_variant_id' => $cartItem->medication_variant_id
                        ]);
                        continue;
                    }

                    // Find the corresponding Medication record by matching generic name
                    $medication = \App\Models\Medication::where('generic_name', $medicationBase->generic_name)
                        ->orWhere('name', $medicationBase->generic_name)
                        ->first();

                    if ($medication) {
                        // Found a matching medication, use it
                        MedicationOrderItem::create([
                            'medication_order_id' => $medicationOrder->id,
                            'medication_id' => $medication->id,
                            'requested_dosage' => $medicationVariant->strength,
                            'requested_quantity' => $cartItem->quantity,
                            'status' => 'pending',
                        ]);

                        Log::info('Created medication order item with matched medication', [
                            'order_id' => $medicationOrder->id,
                            'medication_id' => $medication->id,
                            'medication_name' => $medication->name,
                            'cart_item_id' => $cartItem->id
                        ]);
                    } else {
                        // No matching medication found, use custom medication
                        MedicationOrderItem::create([
                            'medication_order_id' => $medicationOrder->id,
                            'medication_id' => null,
                            'custom_medication_name' => $medicationBase->generic_name,
                            'custom_medication_details' => "Brand: {$medicationVariant->brand_name}, Strength: {$medicationVariant->strength}, Form: {$medicationVariant->dosage_form}",
                            'requested_dosage' => $medicationVariant->strength,
                            'requested_quantity' => $cartItem->quantity,
                            'status' => 'pending',
                        ]);

                        Log::info('Created medication order item as custom medication', [
                            'order_id' => $medicationOrder->id,
                            'custom_medication_name' => $medicationBase->generic_name,
                            'cart_item_id' => $cartItem->id
                        ]);
                    }

                    // Delete the cart item
                    $cartItem->delete();
                } catch (\Exception $e) {
                    Log::error('Error processing cart item', [
                        'cart_item_id' => $cartItem->id,
                        'error' => $e->getMessage()
                    ]);
                    // Continue processing other cart items
                }
            }

            // Commit the transaction
            DB::commit();

            return [
                'success' => true,
                'medication_order' => $medicationOrder,
                'message' => 'Medication order created successfully'
            ];
        } catch (\Exception $e) {
            // Roll back the transaction if there was an error
            DB::rollBack();

            Log::error('Error creating medication order from cart', [
                'error' => $e->getMessage(),
                'user_id' => $user->id
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
