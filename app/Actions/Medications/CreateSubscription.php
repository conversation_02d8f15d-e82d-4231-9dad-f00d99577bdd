<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class CreateSubscription extends Action
{
    /**
     * Create a subscription for a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $plan = $data['plan'];

        // Check if the user already has an active subscription for this plan
        $existingSubscription = $user->subscriptions()
            ->where('plan_id', $plan->id)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>', now());
            })
            ->first();

        if ($existingSubscription) {
            // Log that we're using an existing subscription
            Log::info('Using existing subscription', [
                'user_id' => $user->id,
                'subscription_id' => $existingSubscription->id,
                'plan_id' => $plan->id
            ]);

            // Update meta_data if provided
            if (isset($data['meta_data'])) {
                // Merge with existing meta_data if any
                $currentMetaData = $existingSubscription->meta_data ?? [];
                $newMetaData = array_merge($currentMetaData, $data['meta_data']);

                $existingSubscription->meta_data = $newMetaData;
                $existingSubscription->save();

                Log::info('Updated meta_data for existing subscription', [
                    'user_id' => $user->id,
                    'subscription_id' => $existingSubscription->id,
                    'meta_data' => $newMetaData
                ]);
            }

            return [
                'success' => true,
                'subscription' => $existingSubscription,
                'is_new' => false
            ];
        }

        // Prepare subscription data
        $subscriptionData = [
            'plan_id' => $plan->id,
            'starts_at' => now(),
            'ends_at' => $plan->duration_months > 0
                ? now()->addMonths($plan->duration_months)
                : now()->addDays(7),
            'status' => 'active',
            'is_discounted' => $data['is_discounted'] ?? false,
            'discounted_price' => $data['discounted_price'] ?? null,
        ];

        // Get tracking parameters directly from request, cookies, or session
        // Get tracking IDs for affiliate tracking
        // First try to get ClickID (primary source)
        $clickId = Cookie::get('ClickID') ??
                  Session::get('ClickID') ??
                  request()->query('ClickID');

        // Get LTClickID for backwards compatibility
        $ltClickId = Cookie::get('LTClickID') ??
                    Session::get('LTClickID') ??
                    request()->query('LTClickID');

        // Get AFID
        $afid = Cookie::get('AFID') ??
                Session::get('AFID') ??
                request()->query('AFID');

        // Handle ClickID first (primary source)
        if (!empty($clickId)) {
            $subscriptionData['click_id'] = $clickId;
            Log::info('Setting ClickID from request/cookie/session', [
                'click_id' => $clickId
            ]);
        }
        // Handle LTClickID only as fallback for backwards compatibility
        else if (!empty($ltClickId)) {
            $subscriptionData['click_id'] = $ltClickId;
            Log::info('Setting ClickID from LTClickID (fallback)', [
                'lt_click_id' => $ltClickId
            ]);
        }
        // Handle ClickID from data array (fallback)
        else if (isset($data['click_id']) && !empty($data['click_id'])) {
            $subscriptionData['click_id'] = $data['click_id'];
            Log::info('Setting ClickID from data array', [
                'click_id' => $data['click_id']
            ]);
        }
        // Handle LTClickID from data array (fallback)
        else if (isset($data['lt_click_id']) && !empty($data['lt_click_id'])) {
            $subscriptionData['click_id'] = $data['lt_click_id'];
            Log::info('Setting ClickID from LTClickID (fallback) from data array', [
                'lt_click_id' => $data['lt_click_id']
            ]);
        }

        // Handle AFID from request/cookie/session
        if (!empty($afid)) {
            $subscriptionData['afid'] = $afid;
            Log::info('Setting AFID from request/cookie/session', [
                'afid' => $afid
            ]);
        }
        // Handle AFID from data array (fallback)
        else if (isset($data['afid']) && !empty($data['afid'])) {
            $subscriptionData['afid'] = $data['afid'];
            Log::info('Setting AFID from data array', [
                'afid' => $data['afid']
            ]);
        }

        // Initialize meta_data if not set
        if (!isset($data['meta_data'])) {
            $data['meta_data'] = [];
        }

        // Store LTClickID in meta_data for reference (since it doesn't have its own field)
        if (!empty($ltClickId)) {
            $data['meta_data']['lt_click_id'] = $ltClickId;
        }
        else if (isset($data['lt_click_id']) && !empty($data['lt_click_id'])) {
            $data['meta_data']['lt_click_id'] = $data['lt_click_id'];
        }

        // Add amount to meta_data if provided
        if (isset($data['amount'])) {
            $data['meta_data']['amount'] = $data['amount'];
        }

        // Add meta_data if provided
        if (isset($data['meta_data'])) {
            $subscriptionData['meta_data'] = $data['meta_data'];

            // Log the meta_data being added
            Log::info('Adding meta_data to subscription', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'meta_data' => $data['meta_data']
            ]);
        }

        // Create a new subscription
        $subscription = $user->subscriptions()->create($subscriptionData);

        Log::info('Created new subscription', [
            'user_id' => $user->id,
            'subscription_id' => $subscription->id,
            'plan_id' => $plan->id
        ]);

        return [
            'success' => true,
            'subscription' => $subscription,
            'is_new' => true
        ];
    }
}
