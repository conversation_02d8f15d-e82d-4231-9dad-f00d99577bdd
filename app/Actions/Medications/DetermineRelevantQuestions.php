<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\Cart;
use App\Models\MedicationBase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class DetermineRelevantQuestions extends Action
{
    /**
     * Determine which questions are relevant based on selected medications
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $sessionId = Session::getId();
        $userId = Auth::id();
        
        // Get medications in cart
        $cartItems = Cart::with('medicationVariant.medicationBase')
            ->where('session_id', $sessionId)
            ->when($userId, function($query) use ($userId) {
                $query->where(function($subQuery) use ($userId) {
                    $subQuery->whereNull('user_id')
                             ->orWhere('user_id', $userId);
                });
            })
            ->get();
        
        $medicationTypes = $cartItems->map(function($item) {
            return $item->medicationVariant->medicationBase->type;
        })->unique()->values()->toArray();
        
        $relevantQuestionSections = $this->getRelevantSections($medicationTypes);
        
        return [
            'success' => true,
            'relevant_sections' => $relevantQuestionSections,
            'medication_types' => $medicationTypes
        ];
    }
    
    /**
     * Get relevant question sections based on medication types
     *
     * @param array $medicationTypes
     * @return array
     */
    private function getRelevantSections(array $medicationTypes): array
    {
        $sectionMap = [
            'pain_relief' => ['pain', 'general'],
            'cardiovascular' => ['cardiovascular', 'general'],
            'neurological' => ['neuro', 'general'],
            'gastrointestinal' => ['gi', 'general'],
            'weight_loss' => ['weight', 'endocrine', 'general'],
            'mental_health' => ['mh', 'general'],
            'respiratory' => ['respiratory', 'general'],
            'skin' => ['skin', 'general'],
            'immune' => ['immune', 'general'],
        ];
        
        $relevantSections = ['general']; // Always include general questions
        
        foreach ($medicationTypes as $type) {
            if (isset($sectionMap[$type])) {
                $relevantSections = array_merge($relevantSections, $sectionMap[$type]);
            }
        }
        
        return array_unique($relevantSections);
    }
}
