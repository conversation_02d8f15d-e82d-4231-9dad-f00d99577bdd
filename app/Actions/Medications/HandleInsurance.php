<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\User;
use App\Services\InsuranceService;

class HandleInsurance extends Action
{
    public function __construct(
        private InsuranceService $insuranceService
    ) {}

    /**
     * Handle insurance data for a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $insuranceData = $data['insurance_data'];
        
        if ($user->insurances()->exists()) {
            return [
                'success' => true,
                'message' => 'User already has insurance information'
            ];
        }
        
        $this->insuranceService->createUserInsurance($user, $insuranceData);
        
        return [
            'success' => true
        ];
    }
}
