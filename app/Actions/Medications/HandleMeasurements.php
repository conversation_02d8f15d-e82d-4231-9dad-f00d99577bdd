<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\User;
use App\Services\MeasurementService;

class HandleMeasurements extends Action
{
    public function __construct(
        private MeasurementService $measurementService
    ) {}

    /**
     * Handle measurements for a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $measurementData = $data['measurement_data'];
        
        if (isset($measurementData['height']) && isset($measurementData['weight'])) {
            $this->measurementService->createMeasurement(
                $user,
                $measurementData['height'],
                $measurementData['weight'],
                $measurementData['height_unit'] ?? 'in',
                $measurementData['weight_unit'] ?? 'lb'
            );
        }
        
        return [
            'success' => true
        ];
    }
}
