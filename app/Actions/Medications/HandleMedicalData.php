<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\Session;

class HandleMedicalData extends Action
{
    public function __construct(
        private UserService $userService
    ) {}

    /**
     * Handle medical data for a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $medicalQuestions = $data['medical_questions'] ?? null;
        $medications = $data['medications'] ?? null;
        $allergies = $data['allergies'] ?? null;
        
        if ($medicalQuestions) {
            $user->medicalQuestionnaires()->create($medicalQuestions);
        }
        
        if ($medications) {
            $this->userService->createUserMedications($user, $medications);
        }
        
        if ($allergies) {
            $this->userService->createUserAllergies($user, $allergies);
        }
        
        return [
            'success' => true
        ];
    }
}
