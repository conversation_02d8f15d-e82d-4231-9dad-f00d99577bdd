<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class HandleUserCreation extends Action
{
    /**
     * Create or retrieve a user
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $userData = $data['user_data'];

        // Check if user already exists
        $user = User::where('email', $userData['email'])->first();

        if (!$user) {
            // Get referring agent ID from cookie or session
            $referringAgentId = request()->cookie('referring_agent_id') ??
                               session('referring_agent_id');

            // Create new user
            $user = User::create([
                'email' => $userData['email'],
                'password' => Hash::make($userData['password'] ?? Str::random(10)),
                'name' => $userData['first_name'] . ' ' . $userData['last_name'],
                'fname' => $userData['first_name'],
                'lname' => $userData['last_name'],
                'phone' => $userData['phone'] ?? null,
                'dob' => $userData['dob'] ?? null,
                'gender' => $userData['gender'] ?? null,
                'address1' => $userData['address1'], // Required field
                'address2' => $userData['address2'] ?? null, // Optional field
                'city' => $userData['city'] ?? null,
                'state' => $userData['state'] ?? null,
                'zip' => $userData['zip'] ?? null,
                'status' => 'active',
                'referring_agent_id' => $referringAgentId,
            ]);

            $user->assignRole('patient');

            // Fire registered event
            event(new Registered($user));

            // Log the referral for tracking if an agent referred this user
            if ($referringAgentId) {
                \Illuminate\Support\Facades\Log::info('User created with agent referral', [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'referring_agent_id' => $referringAgentId
                ]);
            }
        }

        // Log the user in if not already logged in
        if (!Auth::check()) {
            Auth::login($user);
        }

        return [
            'success' => true,
            'user' => $user,
            'is_new' => !$user->exists
        ];
    }
}
