<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Api\AuthorizeNetService;
use App\Services\PaymentMethodService;
use App\Services\PaymentMethodTransitionService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPayment extends Action
{
    public function __construct(
        private AuthorizeNetService $authorizeNetService,
        private PaymentMethodService $paymentMethodService,
        private PaymentMethodTransitionService $paymentMethodTransitionService
    ) {}

    /**
     * Process a payment for a medication order
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $cardData = $data['card_data'];
        $amount = $data['amount'];

        // Start a database transaction
        DB::beginTransaction();

        // Create a lock key for this user and amount
        $lockKey = "payment_lock_{$user->id}_{$amount}";
        $lock = \Illuminate\Support\Facades\Cache::lock($lockKey, 30); // Increase timeout to 30 seconds
        $lockAcquired = false;

        try {
            // Get or create customer profile
            $profileResult = $this->authorizeNetService->getOrCreateCustomerProfile($user);

            if (!$profileResult['success']) {
                throw new \Exception($profileResult['message'] ?? 'Failed to get customer profile');
            }

            $customerProfileId = $profileResult['profile_id'];

            // Handle different formats of expiration date
            if (isset($cardData['expiry_date'])) {
                $expMonth = date('m', strtotime($cardData['expiry_date']));
                $expYear = date('Y', strtotime($cardData['expiry_date']));
            } elseif (isset($cardData['expiration_month']) && isset($cardData['expiration_year'])) {
                $expMonth = $cardData['expiration_month'];
                $expYear = $cardData['expiration_year'];
            } elseif (isset($cardData['exp_month']) && isset($cardData['exp_year'])) {
                $expMonth = $cardData['exp_month'];
                $expYear = $cardData['exp_year'];
            } else {
                throw new \Exception('Missing expiration date information');
            }

            // Add payment method if it doesn't exist or use existing one
            $paymentMethodResult = $this->paymentMethodService->addCreditCardPaymentMethod(
                $user,
                $cardData['card_number'],
                $expMonth,
                $expYear,
                $cardData['cvv']
            );

            if (!$paymentMethodResult['success']) {
                throw new \Exception($paymentMethodResult['message'] ?? 'Failed to add payment method');
            }

            $paymentMethod = $paymentMethodResult['payment_method'];
            $paymentProfileId = $paymentMethod->cc_token;

            // Try to acquire the lock
            // Wait for the lock for up to 5 seconds
            $lockAcquired = $lock->block(5);

            if (!$lockAcquired) {
                Log::error('Could not acquire payment lock', [
                    'user_id' => $user->id,
                    'amount' => $amount
                ]);
                throw new \Exception('Another payment is being processed. Please try again in a moment.');
            }

            Log::info('Payment lock acquired', [
                'user_id' => $user->id,
                'amount' => $amount
            ]);

            // Now that we have the lock, check if a transaction already exists
            $existingTransaction = Transaction::where('user_id', $user->id)
                ->where('amount', $amount)
                ->where('status', 'pending')
                ->where('created_at', '>=', now()->subMinutes(5))
                ->orderBy('created_at', 'desc')
                ->first();

            if ($existingTransaction) {
                // Use the existing transaction
                $transaction = $existingTransaction;
                Log::info('Using existing pending transaction', [
                    'transaction_id' => $transaction->id,
                    'user_id' => $user->id,
                    'amount' => $amount
                ]);
            } else {
                // Create a new transaction record
                $transaction = Transaction::create([
                    'user_id' => $user->id,
                    'amount' => $amount,
                    'payment_method' => $paymentMethod->type,
                    'status' => 'pending',
                ]);
                Log::info('Created new transaction record', [
                    'transaction_id' => $transaction->id,
                    'user_id' => $user->id,
                    'amount' => $amount
                ]);
            }

            // Process the payment
            $transactionResult = $this->authorizeNetService->processTransaction(
                $amount,
                $customerProfileId,
                $paymentProfileId,
                'Medication order payment'
            );

            if ($transactionResult['success']) {
                // Log the successful transaction
                Log::info('Payment processed successfully', [
                    'transaction_id' => $transactionResult['transaction_id'],
                    'user_id' => $user->id,
                    'amount' => $amount
                ]);

                // Make sure we have a transaction ID
                if (empty($transactionResult['transaction_id'])) {
                    Log::error('Missing transaction_id in transactionResult', [
                        'transactionResult' => $transactionResult,
                        'user_id' => $user->id
                    ]);

                    // Generate a fake transaction ID if needed
                    $transactionResult['transaction_id'] = 'manual-' . uniqid();

                    Log::info('Generated manual transaction ID', [
                        'transaction_id' => $transactionResult['transaction_id'],
                        'user_id' => $user->id
                    ]);
                }

                // Check if a transaction with this transaction_id already exists
                $existingTransactionWithId = Transaction::where('transaction_id', $transactionResult['transaction_id'])
                    ->first();

                if ($existingTransactionWithId) {
                    // A transaction with this ID already exists
                    Log::info('Transaction with this ID already exists', [
                        'transaction_id' => $transactionResult['transaction_id'],
                        'existing_transaction_id' => $existingTransactionWithId->id,
                        'current_transaction_id' => $transaction->id,
                        'user_id' => $user->id
                    ]);

                    // If the existing transaction is different from our current one, delete the current one
                    if ($existingTransactionWithId->id !== $transaction->id) {
                        Log::info('Deleting duplicate pending transaction', [
                            'transaction_id' => $transaction->id,
                            'user_id' => $user->id
                        ]);

                        // Delete the current transaction since we'll use the existing one
                        $transaction->delete();
                    }

                    // Use the existing transaction
                    $transaction = $existingTransactionWithId;
                } else {
                    // Update the transaction
                    try {
                        $transaction->status = 'success';
                        $transaction->transaction_id = $transactionResult['transaction_id'];
                        // Only set is_discounted if specified in the data
                        $transaction->is_discounted = $data['is_discounted'] ?? false;
                        $transaction->save();

                        // Double-check that the transaction was saved correctly
                        $savedTransaction = Transaction::find($transaction->id);
                        Log::info('Transaction saved', [
                            'transaction_id' => $savedTransaction->transaction_id,
                            'transaction_db_id' => $savedTransaction->id,
                            'status' => $savedTransaction->status,
                            'user_id' => $user->id
                        ]);
                    } catch (\Exception $e) {
                        // If we get a duplicate entry error, find the existing transaction
                        if (strpos($e->getMessage(), 'Duplicate entry') !== false &&
                            strpos($e->getMessage(), 'transactions_transaction_id_unique') !== false) {

                            Log::warning('Caught duplicate transaction_id error', [
                                'transaction_id' => $transactionResult['transaction_id'],
                                'error' => $e->getMessage(),
                                'user_id' => $user->id
                            ]);

                            // Find the existing transaction
                            $existingTransactionWithId = Transaction::where('transaction_id', $transactionResult['transaction_id'])
                                ->first();

                            if ($existingTransactionWithId) {
                                // If the existing transaction is different from our current one, delete the current one
                                if ($existingTransactionWithId->id !== $transaction->id) {
                                    Log::info('Deleting duplicate pending transaction after error', [
                                        'transaction_id' => $transaction->id,
                                        'user_id' => $user->id
                                    ]);

                                    // Delete the current transaction since we'll use the existing one
                                    $transaction->delete();
                                }

                                // Use the existing transaction
                                $transaction = $existingTransactionWithId;
                                Log::info('Using existing transaction with same transaction_id', [
                                    'transaction_id' => $transactionResult['transaction_id'],
                                    'transaction_db_id' => $transaction->id,
                                    'user_id' => $user->id
                                ]);
                            } else {
                                // This shouldn't happen, but just in case
                                throw $e;
                            }
                        } else {
                            // Re-throw any other exception
                            throw $e;
                        }
                    }
                }

                // Force a database commit to ensure the transaction is saved
                DB::commit();

                return [
                    'success' => true,
                    'transaction_id' => $transactionResult['transaction_id'],
                    'transaction' => $transaction
                ];
            } else {
                $transaction->status = 'failed';
                $transaction->error_message = $transactionResult['message'] ?? 'Payment processing failed';
                $transaction->save();

                // Commit the transaction even though it failed
                DB::commit();

                return [
                    'success' => false,
                    'error' => $transactionResult['message'] ?? 'Payment processing failed'
                ];
            }
        } catch (\Exception $e) {
            Log::error('Payment processing error', ['error' => $e->getMessage()]);

            // Roll back the transaction
            DB::rollBack();

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } finally {
            // Make sure to release the lock if it was acquired
            if ($lockAcquired) {
                $lock->release();
                Log::info('Payment lock released', [
                    'user_id' => $user->id,
                    'amount' => $amount
                ]);
            }
        }
    }


}
