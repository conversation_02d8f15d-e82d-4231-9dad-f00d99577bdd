<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use Illuminate\Support\Facades\Session;

class SaveMedicalQuestions extends Action
{
    /**
     * Save medical questions to session
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $questionData = $data['question_data'];
        $medications = $data['medications'] ?? null;
        $allergies = $data['allergies'] ?? null;
        
        // Save medical questions to session
        Session::put('medical_questions', $questionData);
        Session::put('medical_questions.cart_session_id', Session::id());
        
        // Save medications and allergies if provided
        if ($medications) {
            Session::put('medications', $medications);
        }
        
        if ($allergies) {
            Session::put('allergies', $allergies);
        }
        
        return [
            'success' => true
        ];
    }
}
