<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class SecurelyStoreMedicalData extends Action
{
    /**
     * Securely store medical data
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $medicalData = $data['medical_data'];
        $sessionId = $data['session_id'];
        
        try {
            // Encrypt sensitive medical data
            $encryptedData = $this->encryptSensitiveFields($medicalData);
            
            // Create or update medical questionnaire
            $questionnaire = MedicalQuestionnaire::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'cart_session_id' => $sessionId
                ],
                $encryptedData
            );
            
            // Log access for HIPAA compliance
            $this->logMedicalDataAccess('store', $user->id, $questionnaire->id);
            
            return [
                'success' => true,
                'questionnaire_id' => $questionnaire->id
            ];
        } catch (\Exception $e) {
            Log::error('Failed to store medical data securely', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => 'Failed to securely store medical data'
            ];
        }
    }
    
    /**
     * Encrypt sensitive medical fields
     *
     * @param array $data
     * @return array
     */
    private function encryptSensitiveFields(array $data): array
    {
        $sensitiveFields = [
            'medications',
            'allergies',
            'cardiovascular_diagnosis',
            'neuro_diagnosis',
            'gi_symptoms',
            'endocrine_diagnosis',
            'skin_conditions',
            'immune_conditions',
            'mh_symptoms_severity',
            'pain_location_type',
            'respiratory_symptoms',
            'additional_symptoms',
            'weight_history',
        ];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                if (is_array($data[$field])) {
                    $data[$field] = Crypt::encrypt(json_encode($data[$field]));
                } else {
                    $data[$field] = Crypt::encrypt($data[$field]);
                }
            }
        }
        
        return $data;
    }
    
    /**
     * Log medical data access for HIPAA compliance
     *
     * @param string $action
     * @param int $userId
     * @param int $questionnaireId
     * @return void
     */
    private function logMedicalDataAccess(string $action, int $userId, int $questionnaireId): void
    {
        Log::info('Medical data access', [
            'action' => $action,
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId,
            'timestamp' => now()->toIso8601String(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}
