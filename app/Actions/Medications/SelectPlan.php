<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use Illuminate\Support\Facades\Session;

class SelectPlan extends Action
{
    /**
     * Select a subscription plan and save to session
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $planId = $data['plan_id'];
        $hasInsurance = $data['has_insurance'] ? 'yes' : 'no';
        
        // Save plan selection to session
        Session::put('plan', [
            'id' => $planId, 
            'hasInsurance' => $hasInsurance
        ]);
        
        return [
            'success' => true,
            'plan_id' => $planId,
            'has_insurance' => $hasInsurance
        ];
    }
}
