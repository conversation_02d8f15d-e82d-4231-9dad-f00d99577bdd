<?php

namespace App\Actions\Medications;

use App\Actions\Action;
use Illuminate\Support\Facades\Session;

class TrackOrderProgress extends Action
{
    /**
     * Track user progress through the ordering flow
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $currentStep = $data['current_step'];
        $completedSteps = Session::get('completed_steps', []);
        
        // Mark current step as completed
        if (!in_array($currentStep, $completedSteps)) {
            $completedSteps[] = $currentStep;
            Session::put('completed_steps', $completedSteps);
        }
        
        // Calculate progress percentage
        $totalSteps = 4; // medication_selection, plan_selection, medical_questions, payment
        $progress = min(count($completedSteps) / $totalSteps * 100, 100);
        
        return [
            'success' => true,
            'current_step' => $currentStep,
            'completed_steps' => $completedSteps,
            'progress' => $progress,
            'next_step' => $this->getNextStep($currentStep)
        ];
    }
    
    /**
     * Get the next step in the flow
     *
     * @param string $currentStep
     * @return string
     */
    private function getNextStep(string $currentStep): string
    {
        $steps = [
            'medication_selection' => 'plan_selection',
            'plan_selection' => 'medical_questions',
            'medical_questions' => 'payment',
            'payment' => 'success'
        ];
        
        return $steps[$currentStep] ?? 'medication_selection';
    }
}
