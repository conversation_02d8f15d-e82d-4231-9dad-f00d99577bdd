<?php

namespace App\Actions\PaymentMethods;

use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\Api\AuthorizeNetService;
use Illuminate\Support\Facades\Log;

class AddPaymentMethod
{
    public function __construct(
        private AuthorizeNetService $authorizeNetService
    ) {}

    public function execute(array $data): array
    {
        $user = $data['user'];
        $cardNumber = $data['card_number'];
        $expirationMonth = $data['expiration_month'];
        $expirationYear = $data['expiration_year'];
        $cvv = $data['cvv'];

        try {
            // Check if card already exists in payment_methods table
            $existingPaymentMethod = $user->paymentMethods()
                ->where('type', 'credit_card')
                ->where('cc_last_four', substr($cardNumber, -4))
                ->first();

            if ($existingPaymentMethod) {
                return [
                    'success' => true,
                    'message' => 'This credit card is already on file',
                    'card' => $existingPaymentMethod,
                    'is_new' => false
                ];
            }

            // Also check legacy credit_cards table for backward compatibility
            $existingLegacyCard = $user->creditCards()
                ->where('last_four', substr($cardNumber, -4))
                ->first();

            if ($existingLegacyCard) {
                return [
                    'success' => true,
                    'message' => 'This credit card is already on file',
                    'card' => $existingLegacyCard,
                    'is_new' => false
                ];
            }

            // Add card using AuthorizeNetService
            $cardResult = $this->authorizeNetService->addCreditCard(
                $user,
                $cardNumber,
                $expirationMonth,
                $expirationYear,
                $cvv
            );

            if (!$cardResult['success']) {
                throw new \Exception($cardResult['message'] ?? 'Failed to add credit card');
            }

            // Check if this is the first payment method for the user
            $isFirstPaymentMethod = $user->paymentMethods()->count() === 0 && $user->creditCards()->count() === 0;

            // Create payment method record in database
            $paymentMethod = new PaymentMethod([
                'user_id' => $user->id,
                'type' => 'credit_card',
                'cc_last_four' => substr($cardNumber, -4),
                'cc_brand' => $cardResult['brand'],
                'cc_expiration_month' => $expirationMonth,
                'cc_expiration_year' => $expirationYear,
                'cc_token' => $cardResult['payment_profile_id'],
                'is_default' => $isFirstPaymentMethod
            ]);

            $paymentMethod->save();

            return [
                'success' => true,
                'message' => 'Credit card added successfully',
                'card' => $paymentMethod,
                'is_new' => true
            ];
        } catch (\Exception $e) {
            Log::error('Error adding credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to add credit card: ' . $e->getMessage()
            ];
        }
    }
}
