<?php

namespace App\Actions\PaymentMethods;

use App\Models\CreditCard;
use App\Models\User;
use App\Services\Api\AuthorizeNetService;
use Illuminate\Support\Facades\Log;

class AddPaymentMethod
{
    public function __construct(
        private AuthorizeNetService $authorizeNetService
    ) {}

    public function execute(array $data): array
    {
        $user = $data['user'];
        $cardNumber = $data['card_number'];
        $expirationMonth = $data['expiration_month'];
        $expirationYear = $data['expiration_year'];
        $cvv = $data['cvv'];

        try {
            // Check if card already exists
            $existingCard = $user->creditCards()
                ->where('last_four', substr($cardNumber, -4))
                ->first();

            if ($existingCard) {
                return [
                    'success' => true,
                    'message' => 'This credit card is already on file',
                    'card' => $existingCard,
                    'is_new' => false
                ];
            }

            // Add card using AuthorizeNetService
            $cardResult = $this->authorizeNetService->addCreditCard(
                $user,
                $cardNumber,
                $expirationMonth,
                $expirationYear,
                $cvv
            );

            if (!$cardResult['success']) {
                throw new \Exception($cardResult['message'] ?? 'Failed to add credit card');
            }

            // Create credit card record in database
            $creditCard = new CreditCard([
                'user_id' => $user->id,
                'last_four' => substr($cardNumber, -4),
                'brand' => $cardResult['brand'],
                'expiration_month' => $expirationMonth,
                'expiration_year' => $expirationYear,
                'token' => $cardResult['payment_profile_id'],
                'is_default' => $user->creditCards()->count() === 0 ? true : false
            ]);

            $creditCard->save();

            return [
                'success' => true,
                'message' => 'Credit card added successfully',
                'card' => $creditCard,
                'is_new' => true
            ];
        } catch (\Exception $e) {
            Log::error('Error adding credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to add credit card: ' . $e->getMessage()
            ];
        }
    }
}
