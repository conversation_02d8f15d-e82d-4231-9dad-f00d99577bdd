<?php

namespace App\Actions\PaymentMethods;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class GetPaymentMethods
{
    public function execute(array $data): array
    {
        $user = $data['user'];

        try {
            $creditCards = $user->creditCards()->get()->map(function ($card) {
                return [
                    'id' => $card->id,
                    'brand' => $card->brand,
                    'cc' => substr($card->last_four, -4),
                    'expiration_month' => $card->expiration_month,
                    'expiration_year' => $card->expiration_year,
                    'is_default' => (bool) $card->is_default,
                ];
            });

            return [
                'success' => true,
                'credit_cards' => $creditCards
            ];
        } catch (\Exception $e) {
            Log::error('Error getting credit cards: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to get credit cards: ' . $e->getMessage(),
                'credit_cards' => []
            ];
        }
    }
}
