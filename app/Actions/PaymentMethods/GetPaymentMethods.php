<?php

namespace App\Actions\PaymentMethods;

use App\Models\User;
use App\Models\PaymentMethod;
use Illuminate\Support\Facades\Log;

class GetPaymentMethods
{
    public function execute(array $data): array
    {
        $user = $data['user'];

        try {
            $creditCards = collect();

            // Get credit cards from payment_methods table
            $paymentMethods = $user->paymentMethods()
                ->where('type', 'credit_card')
                ->get()
                ->map(function ($method) {
                    return [
                        'id' => $method->id,
                        'brand' => $method->cc_brand,
                        'cc' => substr($method->cc_last_four, -4),
                        'expiration_month' => $method->cc_expiration_month,
                        'expiration_year' => $method->cc_expiration_year,
                        'is_default' => (bool) $method->is_default,
                        'source' => 'payment_methods'
                    ];
                });

            $creditCards = $creditCards->merge($paymentMethods);

            // For backward compatibility, also get from legacy credit_cards table
            $legacyCreditCards = $user->creditCards()->get()->map(function ($card) {
                return [
                    'id' => 'cc_' . $card->id, // Prefix to distinguish from payment methods
                    'brand' => $card->brand,
                    'cc' => substr($card->last_four, -4),
                    'expiration_month' => $card->expiration_month,
                    'expiration_year' => $card->expiration_year,
                    'is_default' => (bool) $card->is_default,
                    'source' => 'credit_cards'
                ];
            });

            $creditCards = $creditCards->merge($legacyCreditCards);

            return [
                'success' => true,
                'credit_cards' => $creditCards
            ];
        } catch (\Exception $e) {
            Log::error('Error getting credit cards: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to get credit cards: ' . $e->getMessage(),
                'credit_cards' => []
            ];
        }
    }
}
