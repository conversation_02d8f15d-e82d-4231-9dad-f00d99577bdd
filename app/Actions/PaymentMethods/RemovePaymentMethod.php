<?php

namespace App\Actions\PaymentMethods;

use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class RemovePaymentMethod
{
    public function __construct(
        private SetDefaultPaymentMethod $setDefaultPaymentMethod
    ) {}

    public function execute(array $data): array
    {
        $user = $data['user'];
        $paymentMethod = $data['payment_method'] ?? $data['credit_card']; // Support both for backward compatibility

        try {
            // Handle both PaymentMethod and CreditCard models
            if ($paymentMethod instanceof PaymentMethod) {
                // Ensure the payment method belongs to the user
                if ($paymentMethod->user_id !== $user->id) {
                    return [
                        'success' => false,
                        'message' => 'This payment method does not belong to the user'
                    ];
                }

                // Check if this is the default payment method
                if ($paymentMethod->is_default) {
                    // Find another payment method to set as default
                    $anotherPaymentMethod = $user->paymentMethods()
                        ->where('id', '!=', $paymentMethod->id)
                        ->first();

                    if (!$anotherPaymentMethod) {
                        // Check legacy credit cards as fallback
                        $anotherPaymentMethod = $user->creditCards()->first();
                    }

                    if ($anotherPaymentMethod) {
                        $this->setDefaultPaymentMethod->execute([
                            'user' => $user,
                            'payment_method' => $anotherPaymentMethod
                        ]);
                    }
                }

                $paymentMethod->delete(); // This will soft delete the payment method
                $message = 'Payment method removed successfully';

            } elseif ($paymentMethod instanceof CreditCard) {
                // Legacy credit card handling
                if ($paymentMethod->user_id !== $user->id) {
                    return [
                        'success' => false,
                        'message' => 'This credit card does not belong to the user'
                    ];
                }

                // Check if this is the default card
                if ($paymentMethod->is_default) {
                    // Find another card to set as default (check payment methods first, then credit cards)
                    $anotherPaymentMethod = $user->paymentMethods()->first();

                    if (!$anotherPaymentMethod) {
                        $anotherPaymentMethod = $user->creditCards()
                            ->where('id', '!=', $paymentMethod->id)
                            ->first();
                    }

                    if ($anotherPaymentMethod) {
                        $this->setDefaultPaymentMethod->execute([
                            'user' => $user,
                            'payment_method' => $anotherPaymentMethod
                        ]);
                    }
                }

                $paymentMethod->delete(); // This will soft delete the card
                $message = 'Credit card removed successfully';
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid payment method type'
                ];
            }

            return [
                'success' => true,
                'message' => $message
            ];
        } catch (\Exception $e) {
            Log::error('Error removing credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to remove credit card: ' . $e->getMessage()
            ];
        }
    }
}
