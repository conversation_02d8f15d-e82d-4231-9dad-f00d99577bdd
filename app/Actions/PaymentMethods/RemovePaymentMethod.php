<?php

namespace App\Actions\PaymentMethods;

use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class RemovePaymentMethod
{
    public function __construct(
        private SetDefaultPaymentMethod $setDefaultPaymentMethod
    ) {}

    public function execute(array $data): array
    {
        $user = $data['user'];
        $creditCard = $data['credit_card'];

        try {
            // Ensure the credit card belongs to the user
            if ($creditCard->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'This credit card does not belong to the user'
                ];
            }

            // Check if this is the default card
            if ($creditCard->is_default) {
                // Find another card to set as default
                $anotherCard = $user->creditCards()->where('id', '!=', $creditCard->id)->first();
                if ($anotherCard) {
                    $this->setDefaultPaymentMethod->execute([
                        'user' => $user,
                        'credit_card' => $anotherCard
                    ]);
                }
            }

            $creditCard->delete(); // This will soft delete the card

            return [
                'success' => true,
                'message' => 'Credit card removed successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error removing credit card: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to remove credit card: ' . $e->getMessage()
            ];
        }
    }
}
