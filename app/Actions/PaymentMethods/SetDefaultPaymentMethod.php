<?php

namespace App\Actions\PaymentMethods;

use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SetDefaultPaymentMethod
{
    public function execute(array $data): array
    {
        $user = $data['user'];
        $paymentMethod = $data['payment_method'] ?? $data['credit_card']; // Support both for backward compatibility

        try {
            // Handle both PaymentMethod and CreditCard models
            if ($paymentMethod instanceof PaymentMethod) {
                // Ensure the payment method belongs to the user
                if ($paymentMethod->user_id !== $user->id) {
                    return [
                        'success' => false,
                        'message' => 'This payment method does not belong to the user'
                    ];
                }

                // Set all payment methods to non-default
                $user->paymentMethods()->update(['is_default' => false]);
                // Also set legacy credit cards to non-default for consistency
                $user->creditCards()->update(['is_default' => false]);

                // Set the selected payment method as default
                $paymentMethod->is_default = true;
                $paymentMethod->save();

            } elseif ($paymentMethod instanceof CreditCard) {
                // Legacy credit card handling
                if ($paymentMethod->user_id !== $user->id) {
                    return [
                        'success' => false,
                        'message' => 'This credit card does not belong to the user'
                    ];
                }

                // Set all payment methods and credit cards to non-default
                $user->paymentMethods()->update(['is_default' => false]);
                $user->creditCards()->update(['is_default' => false]);

                // Set the selected card as default
                $paymentMethod->is_default = true;
                $paymentMethod->save();
            } else {
                return [
                    'success' => false,
                    'message' => 'Invalid payment method type'
                ];
            }

            return [
                'success' => true,
                'message' => 'Default payment method updated successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error setting default payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to update default payment method: ' . $e->getMessage()
            ];
        }
    }
}
