<?php

namespace App\Actions\Subscriptions;

use App\Actions\Action;
use App\Models\SubscriptionPlan;
use App\Models\User;

class CalculatePlanPrice extends Action
{
    /**
     * Calculate the price for a subscription plan
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $plan = $data['plan'];
        $discountedPrice = $data['discounted_price'] ?? null;
        
        // If there's a discounted price, use it
        if ($discountedPrice !== null) {
            return [
                'original_price' => $plan->price,
                'final_price' => $discountedPrice,
                'is_discounted' => true,
                'discount_amount' => $plan->price - $discountedPrice
            ];
        }
        
        // Calculate prorated price if upgrading
        $amount = $plan->price;
        if ($user->hasActiveSubscription()) {
            $daysLeft = now()->diffInDays($user->subscription_ends_at);
            $amount = $plan->getProratedPrice($daysLeft);
            
            return [
                'original_price' => $plan->price,
                'final_price' => $amount,
                'is_prorated' => true,
                'days_remaining' => $daysLeft
            ];
        }
        
        return [
            'original_price' => $plan->price,
            'final_price' => $amount,
            'is_discounted' => false,
            'is_prorated' => false
        ];
    }
}
