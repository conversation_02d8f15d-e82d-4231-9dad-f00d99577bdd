<?php

namespace App\Actions\Subscriptions;

use App\Actions\Action;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class CreatePendingSubscription extends Action
{
    /**
     * Create a subscription with pending payment status
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        try {
            $user = $data['user'];
            $plan = $data['plan'];
            $sessionData = $data['session_data'] ?? [];
            
            // Get AFID and ClickID from cookie or session for tracking
            $afid = \Illuminate\Support\Facades\Cookie::get('AFID') ??
                   session('AFID') ??
                   request()->query('AFID');

            $clickId = \Illuminate\Support\Facades\Cookie::get('LTClickID') ??
                      session('LTClickID') ??
                      request()->query('ClickID');
            
            // Check for discounted price in the session
            $discountedPrice = $sessionData['discounted_price'] ?? null;
            $isDiscounted = !is_null($discountedPrice);
            
            // Create the subscription with pending_payment status
            $subscription = $user->subscriptions()->create([
                'plan_id' => $plan->id,
                'starts_at' => now(),
                'ends_at' => $plan->duration_months > 0 ? now()->addMonths($plan->duration_months) : now()->addDays(7),
                'status' => Subscription::STATUS_PENDING_PAYMENT,
                'afid' => $afid,
                'click_id' => $clickId,
                'is_discounted' => $isDiscounted,
                'discounted_price' => $isDiscounted ? $discountedPrice : null,
            ]);
            
            // Store additional metadata if provided
            if (isset($sessionData['source'])) {
                $subscription->setFeature('source', $sessionData['source']);
            }
            
            Log::info('Created pending payment subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'plan_id' => $plan->id
            ]);
            
            return [
                'success' => true,
                'subscription' => $subscription,
                'message' => 'Pending payment subscription created successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error creating pending payment subscription: ' . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'Failed to create subscription: ' . $e->getMessage()
            ];
        }
    }
}
