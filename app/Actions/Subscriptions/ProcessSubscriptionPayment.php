<?php

namespace App\Actions\Subscriptions;

use App\Actions\Action;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserService;
use App\Services\Api\AuthorizeNetApi;
use App\Services\AgentReferralService;
use App\Services\Api\CustomerProfileService;
use App\Services\Api\PaymentProfileService;
use App\Services\Api\TransactionService;
use App\Services\PaymentMethodService;
use App\Services\PaymentMethodTransitionService;
use App\Services\TrialPlanService;
use App\Services\InvoicePaymentService;
use Illuminate\Support\Facades\Log;

class ProcessSubscriptionPayment extends Action
{
    public function __construct(
        private AuthorizeNetApi $authorizeNetApi,
        private TrialPlanService $trialPlanService,
        private AgentReferralService $agentReferralService,
        private PaymentMethodService $paymentMethodService,
        private PaymentMethodTransitionService $paymentMethodTransitionService,
        private InvoicePaymentService $invoicePaymentService
    ) {
        $this->customerProfileService = new CustomerProfileService($this->authorizeNetApi);
        $this->paymentProfileService = new PaymentProfileService($this->authorizeNetApi);
        $this->transactionService = new TransactionService($this->authorizeNetApi);
    }

    /**
     * Process a subscription payment
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        Log::info('Payment process started');

        $user = $data['user'];
        $plan = $data['plan'];
        $sessionData = $data['session_data'] ?? [];
        $useExistingCard = $data['use_existing_card'] ?? false;
        $existingSubscription = $data['subscription'] ?? null;

        // Check for discounted price in the session
        $discountedPrice = $sessionData['discounted_price'] ?? null;
        $isDiscounted = !is_null($discountedPrice);

        // Determine the amount to charge
        if ($isDiscounted) {
            $amount = $discountedPrice;
        } else {
            $amount = $plan->price;

            // If user is upgrading/downgrading, calculate prorated price
            if ($user->hasActiveSubscription()) {
                $daysLeft = now()->diffInDays($user->subscription_ends_at);
                $amount = $plan->getProratedPrice($daysLeft);
            }
        }

        Log::info('Amount to charge: ' . $amount);

        try {
            // Get or create customer profile
            $customerProfileId = $user->authorize_net_customer_id;
            if (!$customerProfileId) {
                $customerProfileId = $this->customerProfileService->createCustomerProfile($user);
                $user->authorize_net_customer_id = $customerProfileId;
                $user->save();
            }

            // Use existing payment method or add a new one
            if ($useExistingCard && isset($data['credit_card_id'])) {
                // Check if this is a credit_card ID or a payment_method ID
                if (strpos($data['credit_card_id'], 'cc_') === 0) {
                    // This is a credit_card ID from the old table
                    $creditCardId = substr($data['credit_card_id'], 3); // Remove 'cc_' prefix
                    $creditCard = $user->creditCards()->findOrFail($creditCardId);
                    Log::info('Using existing credit card for user: ' . $user->id);
                    $paymentProfileId = $creditCard->token;
                } else {
                    // This is a payment_method ID from the new table
                    $paymentMethod = $user->paymentMethods()->findOrFail($data['credit_card_id']);
                    Log::info('Using existing payment method for user: ' . $user->id . ', type: ' . $paymentMethod->type);

                    // Get the appropriate token based on payment method type
                    if ($paymentMethod->isCreditCard()) {
                        $paymentProfileId = $paymentMethod->cc_token;
                    } elseif ($paymentMethod->isAch()) {
                        $paymentProfileId = $paymentMethod->ach_token;
                    } elseif ($paymentMethod->isInvoice()) {
                        // For invoice payment methods, we'll handle it differently
                        // Skip the Authorize.Net payment processing and create a subscription with invoice payment
                        Log::info('Processing invoice payment for user: ' . $user->id);

                        // Process the invoice payment
                        $invoiceResult = $this->invoicePaymentService->processInvoicePayment(
                            $user,
                            $paymentMethod,
                            $amount,
                            'Subscription payment for plan: ' . $plan->name
                        );

                        if (!$invoiceResult['success']) {
                            throw new \Exception($invoiceResult['message'] ?? 'Failed to process invoice payment');
                        }

                        // Create or update subscription
                        $subscription = $this->createSubscription($user, $plan, $isDiscounted, $discountedPrice, $sessionData, $existingSubscription);

                        // Update user service status
                        $this->updateUserService($user);

                        // Update transaction record if needed
                        if (isset($invoiceResult['transaction_id'])) {
                            $this->updateTransaction($invoiceResult['transaction_id'], $subscription->id, $isDiscounted);
                        }

                        // Process agent commission if applicable
                        if (isset($invoiceResult['transaction_id'])) {
                            $this->processAgentCommission($subscription, $invoiceResult['transaction_id']);
                        }

                        Log::info('Invoice payment process completed successfully for user: ' . $user->id);

                        return [
                            'success' => true,
                            'subscription' => $subscription,
                            'transaction_id' => $invoiceResult['transaction_id'] ?? null,
                            'amount' => $amount,
                            'payment_method' => 'invoice'
                        ];
                    } else {
                        throw new \Exception('Unsupported payment method type: ' . $paymentMethod->type);
                    }
                }
            } else {
                $cardData = $data['card_data'];

                // Add payment method using PaymentMethodService
                $paymentMethodResult = $this->paymentMethodService->addCreditCardPaymentMethod(
                    $user,
                    $cardData['card_number'],
                    $cardData['expiration_month'],
                    $cardData['expiration_year'],
                    $cardData['cvv']
                );

                if (!$paymentMethodResult['success']) {
                    throw new \Exception($paymentMethodResult['message'] ?? 'Failed to add payment method');
                }

                $paymentMethod = $paymentMethodResult['payment_method'];
                $paymentProfileId = $paymentMethod->cc_token;

                Log::info('Added new payment method for user: ' . $user->id);
            }

            // Proceed with charging the payment method
            Log::info('Attempting to process payment for user: ' . $user->id);

            try {
                // Process payment using TransactionService
                // Let the TransactionService handle creating the transaction record
                $transactionResult = $this->transactionService->processProfileTransaction(
                    $amount,
                    $customerProfileId,
                    $paymentProfileId,
                    TransactionService::TRANSACTION_TYPE_AUTH_CAPTURE,
                    'Subscription payment for plan: ' . $plan->name,
                    $user->id,
                    null, // Don't set subscription_id yet, we'll update it later
                    false,
                    'USD'
                );

                $paymentResult = [
                    'success' => true,
                    'transaction_id' => $transactionResult['transaction_id']
                ];
            } catch (\Exception $e) {
                Log::error('Payment processing failed: ' . $e->getMessage(), [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'amount' => $amount
                ]);

                $paymentResult = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            Log::info('Charge result: ' . json_encode($paymentResult));

            if ($paymentResult['success']) {
                Log::info('Payment successful, creating subscription');

                // Create or update subscription
                $subscription = $this->createSubscription($user, $plan, $isDiscounted, $discountedPrice, $sessionData, $existingSubscription);

                // Update user service status
                $this->updateUserService($user);

                // Update transaction record
                $this->updateTransaction($paymentResult['transaction_id'], $subscription->id, $isDiscounted);

                // Process agent commission if applicable
                $this->processAgentCommission($subscription, $paymentResult['transaction_id']);

                Log::info('Payment process completed successfully for user: ' . $user->id);

                return [
                    'success' => true,
                    'subscription' => $subscription,
                    'transaction_id' => $paymentResult['transaction_id'],
                    'amount' => $amount
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $paymentResult['error']
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error processing payment: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Payment processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create or update a subscription for the user
     *
     * @param User $user The user to create/update subscription for
     * @param SubscriptionPlan $plan The subscription plan
     * @param bool $isDiscounted Whether the subscription is discounted
     * @param float|null $discountedPrice The discounted price if applicable
     * @param array $sessionData Additional session data
     * @param object|null $existingSubscription An existing subscription to update instead of creating new
     * @return object The subscription object
     */
    private function createSubscription(
        User $user,
        SubscriptionPlan $plan,
        bool $isDiscounted,
        ?float $discountedPrice,
        array $sessionData,
        ?object $existingSubscription = null
    ): object
    {
        // For trial plans, use the trial service
        if ($plan->id == 5 && !$existingSubscription) {
            return $this->trialPlanService->startTrial($user);
        }

        // Get AFID and ClickID from cookie or session for recurring billing
        $afid = \Illuminate\Support\Facades\Cookie::get('AFID') ??
               session('AFID') ??
               request()->query('AFID');

        $clickId = \Illuminate\Support\Facades\Cookie::get('LTClickID') ??
                  session('LTClickID') ??
                  request()->query('ClickID');

        // If we have an existing subscription, update it instead of creating a new one
        if ($existingSubscription) {
            Log::info('Updating existing subscription', [
                'subscription_id' => $existingSubscription->id,
                'previous_status' => $existingSubscription->status,
                'new_status' => \App\Models\Subscription::STATUS_ACTIVE
            ]);

            // Update the existing subscription
            $existingSubscription->update([
                'status' => \App\Models\Subscription::STATUS_ACTIVE,
                'afid' => $afid ?: $existingSubscription->afid,
                'click_id' => $clickId ?: $existingSubscription->click_id,
                'is_discounted' => $isDiscounted,
                'discounted_price' => $isDiscounted ? $discountedPrice : null,
            ]);

            // Update metadata if needed
            if (isset($sessionData['source']) && !$existingSubscription->hasFeature('source')) {
                $existingSubscription->setFeature('source', $sessionData['source']);
            }

            // Add agent_id from session data if provided
            if (isset($sessionData['agent_id']) && !$existingSubscription->hasFeature('agent_id')) {
                $existingSubscription->setFeature('agent_id', $sessionData['agent_id']);
            }

            return $existingSubscription;
        } else {
            // Create a new subscription
            $subscription = $user->subscriptions()->create([
                'plan_id' => $plan->id,
                'starts_at' => now(),
                'ends_at' => $plan->duration_months > 0 ? now()->addMonths($plan->duration_months) : now()->addDays(7),
                'status' => \App\Models\Subscription::STATUS_ACTIVE, // Use the constant for consistency
                'afid' => $afid, // Store AFID for recurring billing
                'click_id' => $clickId, // Store ClickID for recurring billing
                'is_discounted' => $isDiscounted,
                'discounted_price' => $isDiscounted ? $discountedPrice : null,
            ]);

            // Add metadata
            if (isset($sessionData['source'])) {
                $subscription->setFeature('source', $sessionData['source']);
            }

            // Add agent_id from session data if provided
            if (isset($sessionData['agent_id'])) {
                $subscription->setFeature('agent_id', $sessionData['agent_id']);
            }

            return $subscription;
        }
    }

    /**
     * Update the user service status
     */
    private function updateUserService(User $user): void
    {
        $userService = UserService::where('user_id', $user->id)->first();
        if ($userService) {
            $userService->status = 'paid';
            $userService->save();
        }
    }

    /**
     * Update the transaction record
     */
    private function updateTransaction(string $transactionId, int $subscriptionId, bool $isDiscounted): void
    {
        try {
            // Find the transaction by transaction_id
            $transaction = Transaction::where('transaction_id', $transactionId)->first();

            if ($transaction) {
                // Use updateOrFail to ensure we don't create a duplicate record
                $transaction->update([
                    'subscription_id' => $subscriptionId,
                    'is_discounted' => $isDiscounted
                ]);
                Log::info("Transaction {$transactionId} updated with subscription ID {$subscriptionId}");
            } else {
                Log::warning("Transaction with transaction_id {$transactionId} not found");
            }
        } catch (\Exception $e) {
            Log::error("Error updating transaction: {$e->getMessage()}", [
                'transaction_id' => $transactionId,
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Process agent commission if applicable
     */
    private function processAgentCommission(object $subscription, string $transactionId): void
    {
        try {
            // Check if subscription has agent referral data
            if ($subscription->hasFeature('agent_id') || $subscription->hasFeature('referral_code')) {
                $transaction = Transaction::where('transaction_id', $transactionId)->first();

                if (!$transaction) {
                    Log::error('Transaction not found for commission processing: ' . $transactionId);
                    return;
                }

                // Get agent ID from subscription metadata
                $agentId = $subscription->getFeature('agent_id');

                if ($agentId) {
                    $agent = \App\Models\Agent::find($agentId);

                    if ($agent && $agent->status === 'approved') {
                        // Process the commission
                        $result = $this->agentReferralService->processTransaction($transaction, $subscription, $agent);

                        if ($result['success']) {
                            Log::info('Agent commission processed successfully', [
                                'agent_id' => $agent->id,
                                'transaction_id' => $transactionId,
                                'subscription_id' => $subscription->id,
                                'commission_id' => $result['commission']->id ?? null
                            ]);
                        } else {
                            Log::error('Agent commission processing failed', [
                                'agent_id' => $agent->id,
                                'transaction_id' => $transactionId,
                                'error' => $result['message']
                            ]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error processing agent commission: ' . $e->getMessage());
        }
    }


}
