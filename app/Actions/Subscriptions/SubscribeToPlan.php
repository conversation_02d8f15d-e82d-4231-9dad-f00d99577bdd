<?php

namespace App\Actions\Subscriptions;

use App\Actions\Action;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SubscribeToPlan extends Action
{
    /**
     * Subscribe a user to a plan
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $plan = $data['plan'];
        $currentPlan = $user->subscriptionPlan;
        
        // Check if downgrading from unlimited plan
        if ($currentPlan && $currentPlan->isUnlimited() && !$plan->isUnlimited()) {
            return [
                'success' => false,
                'message' => 'You cannot downgrade from an unlimited plan.'
            ];
        }
        
        return [
            'success' => true,
            'plan' => $plan,
            'current_plan' => $currentPlan
        ];
    }
}
