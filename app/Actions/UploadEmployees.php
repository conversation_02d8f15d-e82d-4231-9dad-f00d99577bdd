<?php

namespace App\Actions;

use App\Models\Business;
use App\Models\BusinessEmployee;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Notifications\BusinessEmployeeWelcomeNotification;
use App\Notifications\TemporaryPasswordNotification;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class UploadEmployees extends Action
{
    /**
     * Execute the action.
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data)
    {
        return $this->handle(
            $data['business'],
            $data['csv_file']
        );
    }
    /**
     * Upload employees from a CSV file
     *
     * @param Business $business
     * @param UploadedFile $csvFile
     * @return array
     */
    public function handle(Business $business, UploadedFile $csvFile)
    {
        try {
            // Get total plan quantity and active employee count
            $totalPlanQuantity = $business->plans()->where('active', true)->sum('plan_quantity');
            $activeEmployeeCount = $business->employees()->where('status', 'active')->count();

            // Calculate how many more employees can be added based on plan quantity
            $maxAllowedEmployees = $totalPlanQuantity;
            $remainingSlots = $maxAllowedEmployees - $activeEmployeeCount;

            // Also check available seats (which might be different due to the 5x multiplier)
            $availableSeats = $business->available_seats;

            // Parse CSV
            $path = $csvFile->getRealPath();

            // Check if file is empty
            if (filesize($path) === 0) {
                return [
                    'success' => false,
                    'message' => 'The uploaded CSV file is empty. Please check your file and try again.'
                ];
            }

            // Try to parse the CSV file
            try {
                $fileContent = file_get_contents($path);

                // Check for common encoding issues
                if (!mb_check_encoding($fileContent, 'UTF-8')) {
                    // Try to convert to UTF-8
                    $fileContent = mb_convert_encoding($fileContent, 'UTF-8');
                }

                $data = array_map('str_getcsv', explode("\n", $fileContent));

                // Remove empty rows
                $data = array_filter($data, function($row) {
                    return !empty(array_filter($row, function($cell) {
                        return trim($cell) !== '';
                    }));
                });

                if (empty($data)) {
                    return [
                        'success' => false,
                        'message' => 'No valid data found in the CSV file. Please check your file format.'
                    ];
                }

                $headers = array_shift($data);

                // Check if headers exist
                if (empty($headers)) {
                    return [
                        'success' => false,
                        'message' => 'No headers found in the CSV file. The first row should contain column headers.'
                    ];
                }

                // Convert headers to lowercase and trim
                $headers = array_map('strtolower', array_map('trim', $headers));

                // Find required column indexes
                $emailIndex = array_search('email', $headers);
                $firstNameIndex = array_search('first_name', $headers);
                $lastNameIndex = array_search('last_name', $headers);

                // Find additional column indexes
                $phoneIndex = array_search('phone', $headers);
                $mobilePhoneIndex = array_search('mobile_phone', $headers);
                $dobIndex = array_search('dob', $headers);
                $address1Index = array_search('address1', $headers);
                $address2Index = array_search('address2', $headers);
                $cityIndex = array_search('city', $headers);
                $stateIndex = array_search('state', $headers);
                $zipIndex = array_search('zip', $headers);

                if ($emailIndex === false) {
                    return [
                        'success' => false,
                        'message' => 'Email column is required in the CSV file. Please make sure your CSV has a column named "email".'
                    ];
                }

                // Check if there's any data after the headers
                if (empty($data)) {
                    return [
                        'success' => false,
                        'message' => 'No employee data found in the CSV file. Please add employee information after the header row.'
                    ];
                }
            } catch (\Exception $e) {
                return [
                    'success' => false,
                    'message' => 'Failed to parse the CSV file. Please check the file format and try again.',
                    'error' => $e->getMessage()
                ];
            }

            // Prepare data for bulk insert
            $employeesData = [];
            $count = 0;
            $skippedEmails = [];
            $invalidEmails = [];
            $rowNumber = 1; // Start at row 1 (after headers)

            foreach ($data as $row) {
                $rowNumber++;

                // Skip rows that don't have enough columns
                if (!isset($row[$emailIndex])) {
                    continue;
                }

                if (!empty($row[$emailIndex])) {
                    $email = trim($row[$emailIndex]);

                    // Validate email format
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $invalidEmails[] = [
                            'email' => $email,
                            'row' => $rowNumber
                        ];
                        continue;
                    }

                    // Check if employee already exists
                    if ($business->employees()->where('email', $email)->exists()) {
                        // Track skipped emails
                        $skippedEmails[] = $email;
                        continue;
                    }

                    // Check if User already exists with this email
                    if (User::where('email', $email)->exists()) {
                        // Track skipped emails
                        $skippedEmails[] = $email;
                        continue;
                    }

                    $employeesData[] = [
                        'business_id' => $business->id,
                        'email' => $email,
                        'first_name' => $firstNameIndex !== false && isset($row[$firstNameIndex]) ? trim($row[$firstNameIndex]) : null,
                        'last_name' => $lastNameIndex !== false && isset($row[$lastNameIndex]) ? trim($row[$lastNameIndex]) : null,
                        'phone' => $phoneIndex !== false && isset($row[$phoneIndex]) ? trim($row[$phoneIndex]) : null,
                        'mobile_phone' => $mobilePhoneIndex !== false && isset($row[$mobilePhoneIndex]) ? trim($row[$mobilePhoneIndex]) : null,
                        'dob' => $dobIndex !== false && isset($row[$dobIndex]) ? trim($row[$dobIndex]) : null,
                        'address1' => $address1Index !== false && isset($row[$address1Index]) ? trim($row[$address1Index]) : null,
                        'address2' => $address2Index !== false && isset($row[$address2Index]) ? trim($row[$address2Index]) : null,
                        'city' => $cityIndex !== false && isset($row[$cityIndex]) ? trim($row[$cityIndex]) : null,
                        'state' => $stateIndex !== false && isset($row[$stateIndex]) ? trim($row[$stateIndex]) : null,
                        'zip' => $zipIndex !== false && isset($row[$zipIndex]) ? trim($row[$zipIndex]) : null,
                        'status' => 'active',
                    ];

                    $count++;
                }
            }

            // If we have invalid emails, return an error
            if (!empty($invalidEmails)) {
                $invalidCount = count($invalidEmails);
                $message = "Upload failed due to {$invalidCount} invalid " .
                    ($invalidCount === 1 ? "email address" : "email addresses") . " in your CSV file.";

                // List the first 5 invalid emails with their row numbers
                if ($invalidCount <= 5) {
                    $message .= " Please correct the following:";
                    foreach ($invalidEmails as $invalid) {
                        $message .= "\n- Row {$invalid['row']}: \"{$invalid['email']}\"";
                    }
                } else {
                    $message .= " The first 5 invalid emails are:";
                    $count = 0;
                    foreach ($invalidEmails as $invalid) {
                        $message .= "\n- Row {$invalid['row']}: \"{$invalid['email']}\"";
                        $count++;
                        if ($count >= 5) break;
                    }
                    $message .= "\nPlease check your CSV file and correct all invalid email addresses.";
                }

                return [
                    'success' => false,
                    'message' => $message,
                    'invalid_emails' => $invalidEmails
                ];
            }

            // Check if we have enough slots based on plan quantity
            if ($count > $remainingSlots) {
                return [
                    'success' => false,
                    'message' => "Cannot add {$count} employees. Your plan allows a maximum of {$maxAllowedEmployees} employees, and you already have {$activeEmployeeCount} active employees. You can add up to {$remainingSlots} more employees."
                ];
            }

            // Check if we have enough available seats
            if ($count > $availableSeats) {
                return [
                    'success' => false,
                    'message' => "Not enough available seats. You need {$count} seats but only have {$availableSeats} available."
                ];
            }

            // If no employees to add, return early with appropriate message
            if ($count === 0) {
                $message = 'No new employees to add.';

                // If we have skipped emails, include them in the message
                if (!empty($skippedEmails)) {
                    $skippedCount = count($skippedEmails);
                    $message = "No new employees added. {$skippedCount} " .
                        ($skippedCount === 1 ? "email was" : "emails were") .
                        " skipped because " .
                        ($skippedCount === 1 ? "it already exists" : "they already exist") .
                        " in the system.";

                    // If there are not too many skipped emails, list them
                    if ($skippedCount <= 5) {
                        $message .= " Skipped: " . implode(', ', $skippedEmails);
                    }
                }

                return [
                    'success' => true,
                    'message' => $message,
                    'count' => 0,
                    'skipped_emails' => $skippedEmails
                ];
            }

            // Start transaction
            DB::beginTransaction();

            // We can't use insert() because we need the IDs of the created records
            // and we need to create users for each employee
            $createdEmployees = [];
            $createdUsers = [];

            foreach ($employeesData as $employeeData) {
                // Create the employee record
                $employee = BusinessEmployee::create($employeeData);
                $createdEmployees[] = $employee;

                // Generate a random password
                $password = Str::random(10);

                // Create a user account
                $user = User::create([
                    'name' => ($employeeData['first_name'] ?? '') . ' ' . ($employeeData['last_name'] ?? ''),
                    'email' => $employeeData['email'],
                    'password' => Hash::make($password),
                    'business_id' => $business->id,
                    'fname' => $employeeData['first_name'] ?? '',
                    'lname' => $employeeData['last_name'] ?? '',
                    'phone' => $employeeData['phone'] ?? null,
                    'mobile_phone' => $employeeData['mobile_phone'] ?? null,
                    'dob' => $employeeData['dob'] ?? null,
                    'address1' => $employeeData['address1'] ?? null,
                    'address2' => $employeeData['address2'] ?? null,
                    'city' => $employeeData['city'] ?? null,
                    'state' => $employeeData['state'] ?? null,
                    'zip' => $employeeData['zip'] ?? null,
                    'status' => 'active',
                ]);

                // Assign employee role
                $employeeRole = Role::findByName('employee', 'web');
                $user->assignRole($employeeRole);

                // Update the employee record with the user ID
                $employee->update(['user_id' => $user->id]);

                // Find appropriate subscription plan (could be a specific business employee plan)
                $subscriptionPlan = SubscriptionPlan::where('group_id', 4) // Business Plans group
                    ->where('is_active', true)
                    ->first();

                if (!$subscriptionPlan) {
                    // Fallback to premium plans if no specific business plan exists
                    $subscriptionPlan = SubscriptionPlan::premium()
                        ->where('is_active', true)
                        ->first();
                }

                // Get the business plan
                $businessPlan = $business->plans()->where('active', true)->first();

                // Create subscription for the user
                if ($subscriptionPlan && $businessPlan) {
                    $user->subscriptions()->create([
                        'plan_id' => $subscriptionPlan->id,
                        'starts_at' => now(),
                        'ends_at' => $businessPlan->ends_at ?? now()->addYear(),
                        'status' => 'active',
                        'user_type' => 'business_employee',
                        'is_primary_account' => true, // Allow employees to have dependents
                        'family_role' => 'primary',
                        'meta_data' => [
                            'business_plan_id' => $businessPlan->id,
                            'business_id' => $business->id,
                            'employee_id' => $employee->id
                        ]
                    ]);
                }

                // Get plan name if available
                $planName = null;
                if ($subscriptionPlan) {
                    $planName = $subscriptionPlan->name;
                }

                // Send welcome notification with temporary password
                // $user->notify(new BusinessEmployeeWelcomeNotification($password, $business->name, $planName));

                $createdUsers[] = $user;
            }

            DB::commit();

            // Prepare success message
            $message = "{$count} " . ($count === 1 ? "employee" : "employees") . " uploaded successfully and account credentials sent via email.";

            // Add information about skipped emails if any
            if (!empty($skippedEmails)) {
                $skippedCount = count($skippedEmails);
                $message .= " {$skippedCount} " .
                    ($skippedCount === 1 ? "email was" : "emails were") .
                    " skipped because " .
                    ($skippedCount === 1 ? "it already exists" : "they already exist") .
                    " in the system.";

                // If there are not too many skipped emails, list them
                if ($skippedCount <= 5) {
                    $message .= " Skipped: " . implode(', ', $skippedEmails);
                }
            }

            return [
                'success' => true,
                'message' => $message,
                'count' => $count,
                'skipped_count' => count($skippedEmails),
                'skipped_emails' => $skippedEmails,
                'employees' => $createdEmployees,
                'users' => $createdUsers
            ];
        } catch (\Exception $e) {
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }

            // Log the full exception for debugging
            \Illuminate\Support\Facades\Log::error('Employee upload error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);

            // Create a more user-friendly error message
            $errorMessage = 'Failed to upload employees';

            // Check for common error types and provide more specific messages
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $errorMessage = 'One or more employees already exist in the system. Please check your CSV file for duplicate emails.';
            } elseif (strpos($e->getMessage(), 'Column') !== false && strpos($e->getMessage(), 'not found') !== false) {
                $errorMessage = 'CSV format error: One or more required columns are missing or have incorrect names.';
            } elseif (strpos($e->getMessage(), 'syntax error') !== false) {
                $errorMessage = 'There was a database error. Please try again or contact support if the issue persists.';
            } else {
                // Include the actual error message for other cases, but clean it up
                $cleanedMessage = preg_replace('/SQLSTATE\[\w+\]: .*?: /', '', $e->getMessage());
                $errorMessage .= ': ' . $cleanedMessage;
            }

            return [
                'success' => false,
                'message' => $errorMessage,
                'error' => $e->getMessage(),
                'error_type' => get_class($e)
            ];
        }
    }

    /**
     * Generate a CSV template for employee uploads
     *
     * @return string
     */
    public function generateCsvTemplate()
    {
        $headers = [
            'email',
            'first_name',
            'last_name',
            'phone',
            'mobile_phone',
            'dob',
            'address1',
            'address2',
            'city',
            'state',
            'zip',
        ];

        $sample = [
            '<EMAIL>',
            'John',
            'Doe',
            '************',
            '************',
            '1990-12-31',
            '123 Main St',
            'Apt 4B',
            'Anytown',
            'CA',
            '12345',
        ];

        $output = fopen('php://temp', 'r+');
        fputcsv($output, $headers);
        fputcsv($output, $sample);

        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return $csv;
    }
}
