<?php

namespace App\Actions\UrgentCare;

use App\Actions\Action;
use App\Models\User;
use App\Services\MeasurementService;

class HandleUrgentCareMeasurements extends Action
{
    public function __construct(
        private MeasurementService $measurementService
    ) {}

    /**
     * Handle measurements for a user in the urgent care flow
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $measurementData = $data['measurement_data'];
        
        // Update user measurements
        $this->measurementService->updateUserMeasurements(
            $user,
            [
                'height_ft' => $measurementData['height_feet'],
                'height_in' => $measurementData['height_inches'],
                'weight' => $measurementData['weight']
            ]
        );
        
        return [
            'success' => true
        ];
    }
}
