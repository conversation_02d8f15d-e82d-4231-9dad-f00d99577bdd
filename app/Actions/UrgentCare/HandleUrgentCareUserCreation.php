<?php

namespace App\Actions\UrgentCare;

use App\Actions\Action;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class HandleUrgentCareUserCreation extends Action
{
    /**
     * Handle user creation for urgent care
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $paymentData = $data['payment_data'];

        // Check if user is logged in
        if (Auth::check()) {
            return [
                'success' => true,
                'user' => Auth::user(),
                'is_new' => false
            ];
        }

        // Check if user exists with the given email
        $existingUser = User::where('email', $paymentData['email'])->first();

        if ($existingUser) {
            // Log the user in
            Auth::login($existingUser);

            return [
                'success' => true,
                'user' => $existingUser,
                'is_new' => false
            ];
        }

        // Get referring agent ID from cookie or session
        $referringAgentId = request()->cookie('referring_agent_id') ??
                           session('referring_agent_id');

        // Create a new user
        $user = new User([
            'fname' => $paymentData['first_name'],
            'lname' => $paymentData['last_name'],
            'name' => $paymentData['first_name'] . ' ' . $paymentData['last_name'],
            'email' => $paymentData['email'],
            'phone' => $paymentData['phone'],
            'address1' => $paymentData['billing_address'],
            'city' => $paymentData['city'],
            'state' => $paymentData['state'],
            'zip' => $paymentData['zip'],
            'password' => Hash::make(Str::random(16)), // Generate a random password
            'status' => 'active',
            'referring_agent_id' => $referringAgentId,
        ]);
        $user->save();

        // Log the referral for tracking if an agent referred this user
        if ($referringAgentId) {
            \Illuminate\Support\Facades\Log::info('UrgentCare user created with agent referral', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'referring_agent_id' => $referringAgentId
            ]);
        }

        // Assign the patient role
        $user->assignRole('patient');

        // Log the user in
        Auth::login($user);

        return [
            'success' => true,
            'user' => $user,
            'is_new' => true
        ];
    }
}
