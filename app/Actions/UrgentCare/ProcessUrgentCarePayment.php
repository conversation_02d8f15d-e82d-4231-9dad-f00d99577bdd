<?php

namespace App\Actions\UrgentCare;

use App\Actions\Action;
use App\Models\Consultation;
use App\Models\MedicalQuestionnaire;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\Treatment;
use App\Models\User;
use App\Models\UserReportedMedication;
use App\Services\Api\AuthorizeNetService;
use App\Services\PaymentMethodService;
use App\Services\PaymentMethodTransitionService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class ProcessUrgentCarePayment extends Action
{
    public function __construct(
        private AuthorizeNetService $authorizeNetService,
        private PaymentMethodService $paymentMethodService,
        private PaymentMethodTransitionService $paymentMethodTransitionService
    ) {}

    /**
     * Process payment for urgent care
     *
     * @param array $data
     * @return array
     */
    public function execute(array $data): array
    {
        $user = $data['user'];
        $treatment = $data['treatment'];
        $plan = $data['plan'];
        $paymentData = $data['payment_data'];
        $isDiscounted = $data['is_discounted'] ?? false;
        $amount = $data['amount'];

        try {
            // Add the payment method to the user's account
            $paymentMethodResult = $this->paymentMethodService->addCreditCardPaymentMethod(
                $user,
                $paymentData['card_number'],
                $paymentData['expiration_month'],
                $paymentData['expiration_year'],
                $paymentData['cvv']
            );

            if (!$paymentMethodResult['success']) {
                return [
                    'success' => false,
                    'message' => $paymentMethodResult['message'] ?? 'Failed to process payment method'
                ];
            }

            $paymentMethod = $paymentMethodResult['payment_method'];
            Log::info("Payment method saved to database for user: {$user->id}");

            // Process the transaction
            $transactionResult = $this->authorizeNetService->processTransaction(
                $amount,
                $user->authorize_net_customer_id,
                $paymentMethod->cc_token,
                'UrgentCare: ' . $treatment->name . ' - ' . $plan->name,
                $user->id,
                null, // We'll create the subscription after successful payment
                $isDiscounted
            );

            if (!$transactionResult['success']) {
                return [
                    'success' => false,
                    'message' => $transactionResult['message'] ?? 'Failed to process payment'
                ];
            }

            // Get the transaction ID
            $transactionId = $transactionResult['transaction_id'];
            Session::put('transid', $transactionId);

            // Create a subscription for the user
            $subscription = new Subscription([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'starts_at' => now(),
                'ends_at' => now()->addMonths($plan->duration_months ?? 1),
                'status' => 'active',
                'is_discounted' => $isDiscounted,
                'discounted_price' => $isDiscounted ? $amount : null,
            ]);
            $subscription->save();

            // Find existing questionnaire or create a new one
            $questionnaire = MedicalQuestionnaire::where('user_id', $user->id)
                ->where('treatment_id', $treatment->id)
                ->where('subscription_plan_id', $plan->id)
                ->where('status', 'pending')
                ->first();

            if (!$questionnaire) {
                $questionnaire = new MedicalQuestionnaire();
                $questionnaire->user_id = $user->id;
                $questionnaire->treatment_id = $treatment->id;
                $questionnaire->subscription_plan_id = $plan->id;
                $questionnaire->data = json_encode(Session::get('uc_medical_questions'));
                $questionnaire->status = 'pending';
            }

            // Update the questionnaire with transaction details
            $questionnaire->transaction_id = $transactionId;
            $questionnaire->amount = $amount;
            $questionnaire->status = 'paid';

            // Map the universal treatment intake questions to the medical_questionnaires table fields
            $medicalData = Session::get('uc_medical_questions', []);

            if (!empty($medicalData)) {
                $questionnaire->additional_symptoms = $medicalData['additional_symptoms'] ?? null;
                $questionnaire->quality_of_life = $medicalData['quality_of_life'] ?? null;
                $questionnaire->treatment_goals = $medicalData['treatment_goals'] ?? null;
                $questionnaire->concerns = $medicalData['concerns'] ?? null;
                $questionnaire->prevention_medications = $medicalData['prevention_medications'] ?? null;
                $questionnaire->prevention_lifestyle = $medicalData['prevention_lifestyle'] ?? null;
                $questionnaire->mh_symptoms_severity = $medicalData['mh_symptoms_severity'] ?? null;
                $questionnaire->mh_treatment_history = $medicalData['mh_treatment_history'] ?? null;

                // Store medications in user_reported_medications table
                if (isset($medicalData['medications']) && is_array($medicalData['medications'])) {
                    foreach ($medicalData['medications'] as $medication) {
                        if (!empty($medication['medication_name'])) {
                            // Check if this medication already exists for this user to avoid duplicates
                            $exists = UserReportedMedication::where('user_id', $user->id)
                                ->where('medication_name', $medication['medication_name'])
                                ->exists();

                            if (!$exists) {
                                UserReportedMedication::create([
                                    'user_id' => $user->id,
                                    'medication_name' => $medication['medication_name'],
                                    'dosage' => $medication['dosage'] ?? null,
                                    'frequency' => $medication['frequency'] ?? null,
                                ]);
                            }
                        }
                    }
                }
            }

            $questionnaire->save();

            // Create a consultation record and assign it to a doctor
            $consultation = new Consultation([
                'patient_id' => $user->id,
                'status' => 'scheduled',
                'type' => 'urgent-care',
                'patient_complaint' => 'UrgentCare: ' . $treatment->name,
                'scheduled_at' => now()->addHours(24), // Schedule for 24 hours from now
            ]);

            // Find an available doctor
            // $doctor = User::role('doctor')->inRandomOrder()->first();
            // if ($doctor) {
            //     $consultation->doctor_id = $doctor->id;
            // }

            $consultation->save();

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'subscription' => $subscription,
                'consultation' => $consultation,
                'amount' => $amount
            ];

        } catch (\Exception $e) {
            Log::error('Payment processing error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'An error occurred while processing your payment: ' . $e->getMessage()
            ];
        }
    }
}
