<?php

namespace App\Console\Commands\Agent;

use App\Services\GoalsReportsService;
use Illuminate\Console\Command;

class CheckExpiredGoals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:check-expired-goals';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for expired agent goals and update their status';

    /**
     * The goals reports service.
     *
     * @var \App\Services\GoalsReportsService
     */
    protected $goalsReportsService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\GoalsReportsService  $goalsReportsService
     * @return void
     */
    public function __construct(GoalsReportsService $goalsReportsService)
    {
        parent::__construct();
        $this->goalsReportsService = $goalsReportsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for expired goals...');

        $this->goalsReportsService->checkExpiredGoals();

        $this->info('Goals check completed successfully.');

        return Command::SUCCESS;
    }
}
