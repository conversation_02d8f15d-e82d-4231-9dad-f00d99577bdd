<?php

namespace App\Console\Commands\Agent;

use App\Models\Goal;
use App\Services\Agent\NotificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckGoalDeadlines extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'goals:check-deadlines';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for approaching goal deadlines and send reminders';

    /**
     * The notification service instance.
     *
     * @var \App\Services\Agent\NotificationService
     */
    protected $notificationService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\Agent\NotificationService  $notificationService
     * @return void
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking for approaching goal deadlines...');

        $today = Carbon::today();
        $reminderDays = [7, 3, 1]; // Send reminders 7 days, 3 days, and 1 day before deadline

        foreach ($reminderDays as $days) {
            $targetDate = $today->copy()->addDays($days);

            // Find active goals with the target date
            $goals = Goal::where('status', 'active')
                ->whereDate('target_date', $targetDate)
                ->get();

            $this->info("Found {$goals->count()} goals with deadline in {$days} days.");

            foreach ($goals as $goal) {
                $this->info("Sending reminder for goal ID {$goal->id} to user ID {$goal->user_id}");
                $this->notificationService->sendGoalDeadlineReminderNotification($goal, $days);
            }
        }

        $this->info('Goal deadline check completed.');

        return 0;
    }
}
