<?php

namespace App\Console\Commands\Agent;

use App\Models\AgentReportExport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CleanupExpiredExports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:cleanup-expired-exports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired agent report exports';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning up expired report exports...');

        // Get expired exports
        $expiredExports = AgentReportExport::where('expires_at', '<', now())->get();

        $this->info("Found {$expiredExports->count()} expired exports.");

        $deletedCount = 0;

        foreach ($expiredExports as $export) {
            try {
                // Delete the file
                if (Storage::exists($export->file_path)) {
                    Storage::delete($export->file_path);
                }

                // Delete the export record
                $export->delete();

                $deletedCount++;
            } catch (\Exception $e) {
                $this->error("Error deleting export {$export->id}: {$e->getMessage()}");
                Log::error("Error deleting expired export {$export->id}: {$e->getMessage()}", [
                    'export_id' => $export->id,
                    'file_path' => $export->file_path,
                    'exception' => $e
                ]);
            }
        }

        $this->info("Deleted {$deletedCount} expired exports.");

        return Command::SUCCESS;
    }
}
