<?php

namespace App\Console\Commands\Agent;

use App\Models\AgentCustomReport;
use App\Services\GoalsReportsService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateScheduledReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agent:generate-scheduled-reports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate scheduled agent reports';

    /**
     * The goals reports service.
     *
     * @var \App\Services\GoalsReportsService
     */
    protected $goalsReportsService;

    /**
     * Create a new command instance.
     *
     * @param  \App\Services\GoalsReportsService  $goalsReportsService
     * @return void
     */
    public function __construct(GoalsReportsService $goalsReportsService)
    {
        parent::__construct();
        $this->goalsReportsService = $goalsReportsService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating scheduled reports...');

        $now = Carbon::now();
        $currentHour = $now->format('H:00');
        $currentDay = $now->day;
        $currentDayOfWeek = $now->dayOfWeek;
        $currentMonth = $now->month;
        $currentQuarter = ceil($currentMonth / 3);

        // Get scheduled reports for the current hour
        $reports = AgentCustomReport::where('is_scheduled', true)
                                   ->where('schedule_time', $currentHour)
                                   ->get();

        $generatedCount = 0;

        foreach ($reports as $report) {
            // Check if the report should be generated based on frequency
            $shouldGenerate = false;

            switch ($report->schedule_frequency) {
                case 'daily':
                    $shouldGenerate = true;
                    break;

                case 'weekly':
                    // Generate on Mondays (day of week 1)
                    $shouldGenerate = $currentDayOfWeek === 1;
                    break;

                case 'monthly':
                    // Generate on the 1st day of the month
                    $shouldGenerate = $currentDay === 1;
                    break;

                case 'quarterly':
                    // Generate on the 1st day of the quarter
                    $shouldGenerate = $currentDay === 1 && in_array($currentMonth, [1, 4, 7, 10]);
                    break;
            }

            if ($shouldGenerate) {
                try {
                    // Generate the report
                    $result = $this->goalsReportsService->generateReport($report->agent_id, $report);

                    // Export the report to PDF
                    $this->goalsReportsService->exportReport(
                        $report->agent_id,
                        $report,
                        'pdf'
                    );

                    // Update the last generated timestamp
                    $report->last_generated_at = now();
                    $report->save();

                    $generatedCount++;

                    $this->info("Generated report: {$report->name} (ID: {$report->id})");
                } catch (\Exception $e) {
                    $this->error("Error generating report {$report->id}: {$e->getMessage()}");
                    Log::error("Error generating scheduled report {$report->id}: {$e->getMessage()}", [
                        'report_id' => $report->id,
                        'agent_id' => $report->agent_id,
                        'exception' => $e
                    ]);
                }
            }
        }

        $this->info("Generated {$generatedCount} scheduled reports.");

        return Command::SUCCESS;
    }
}
