<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use App\Notifications\SubscriptionAutoRenewedNotification;
use App\Notifications\SubscriptionRenewalFailedNotification;
use App\Services\Api\AuthorizeNetService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AttemptSubscriptionRenewals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:attempt-renewals
                            {--days=1 : Number of days before expiration to attempt renewal}
                            {--dry-run : Run without making actual charges}
                            {--user-id= : Attempt renewal for a specific user ID}
                            {--subscription-id= : Attempt renewal for a specific subscription ID}
                            {--force : Force renewal even if subscription is not expiring soon}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Attempt to auto-renew subscriptions that are about to expire, with options to target specific users or subscriptions';

    /**
     * The Authorize.Net service.
     *
     * @var \App\Services\Api\AuthorizeNetService
     */
    protected $authorizeNetService;

    /**
     * Create a new command instance.
     */
    public function __construct(AuthorizeNetService $authorizeNetService)
    {
        parent::__construct();
        $this->authorizeNetService = $authorizeNetService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $days = (int) $this->option('days');
        $userId = $this->option('user-id');
        $subscriptionId = $this->option('subscription-id');
        $force = $this->option('force');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No charges will be made.');
        }

        // Build the query based on options
        $query = Subscription::where('status', 'active')
            ->with(['user', 'plan']);

        // If specific subscription ID is provided
        if ($subscriptionId) {
            $this->info("Looking for specific subscription ID: {$subscriptionId}");
            $query->where('id', $subscriptionId);
        }
        // If specific user ID is provided
        elseif ($userId) {
            $this->info("Looking for active subscriptions for user ID: {$userId}");
            $query->where('user_id', $userId);

            // If not forcing, still check for expiration date
            if (!$force) {
                // Calculate the date range for subscriptions about to expire
                $startDate = now()->addDays($days)->startOfDay();
                $endDate = now()->addDays($days)->endOfDay();
                $query->whereBetween('ends_at', [$startDate, $endDate]);
                $this->info("Filtering for subscriptions expiring between {$startDate->format('Y-m-d H:i:s')} and {$endDate->format('Y-m-d H:i:s')}");
            }
        }
        // Default behavior - find subscriptions expiring soon
        else {
            // Calculate the date range for subscriptions about to expire
            $startDate = now()->addDays($days)->startOfDay();
            $endDate = now()->addDays($days)->endOfDay();
            $query->whereBetween('ends_at', [$startDate, $endDate]);
            $this->info("Looking for subscriptions expiring between {$startDate->format('Y-m-d H:i:s')} and {$endDate->format('Y-m-d H:i:s')}");
        }

        // Execute the query
        $subscriptions = $query->get();

        $count = $subscriptions->count();

        if ($count === 0) {
            $this->info('No subscriptions found that are about to expire.');
            return 0;
        }

        $this->info("Found {$count} subscriptions that are about to expire.");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $successCount = 0;
        $failureCount = 0;

        foreach ($subscriptions as $subscription) {
            $user = $subscription->user;
            $plan = $subscription->plan;

            if (!$user || !$plan) {
                $this->error("\nSkipping subscription #{$subscription->id}: Missing user or plan data");
                $bar->advance();
                continue;
            }

            $this->line("\nProcessing subscription #{$subscription->id} for user {$user->name}");
            $this->line("  Plan: {$plan->name}, Expires: {$subscription->ends_at->format('Y-m-d')}");

            // Check if user has a default payment method
            $defaultCreditCard = $user->creditCards()->where('is_default', true)->first();

            if (!$defaultCreditCard) {
                $this->line("  No default payment method found. Skipping auto-renewal.");
                $failureCount++;

                if (!$dryRun) {
                    // Notify user that auto-renewal failed due to missing payment method
                    $user->notify(new SubscriptionRenewalFailedNotification($subscription, 'No default payment method found'));
                }

                $bar->advance();
                continue;
            }

            // Calculate the amount to charge
            $amount = $subscription->is_discounted && $subscription->discounted_price
                ? $subscription->discounted_price
                : $plan->price;

            $this->line("  Amount to charge: $" . $amount);

            if ($dryRun) {
                $this->line("  Would attempt to charge credit card ending in {$defaultCreditCard->last_four}");
                $bar->advance();
                continue;
            }

            // Attempt to charge the card
            try {
                // Get customer profile ID
                $customerProfileId = $user->authorize_net_customer_id;
                if (!$customerProfileId) {
                    $this->error("  No Authorize.Net customer profile found for user {$user->id}");
                    $failureCount++;

                    if (!$dryRun) {
                        // Notify user that auto-renewal failed due to missing payment profile
                        $user->notify(new SubscriptionRenewalFailedNotification($subscription, 'No payment profile found'));
                    }

                    $bar->advance();
                    continue;
                }

                // Process payment using the token/payment profile ID
                $paymentResult = $this->authorizeNetService->processTransaction(
                    $amount,
                    $customerProfileId,
                    $defaultCreditCard->token,
                    "Subscription Renewal: {$plan->name}",
                    $subscription->user_id, // Pass the patient's user ID, not the admin's
                    $subscription->id,
                    $subscription->is_discounted
                );

                if ($paymentResult['success']) {
                    // Payment successful, extend the subscription
                    $newEndDate = $subscription->ends_at->copy()->addMonths($plan->duration_months);

                    $subscription->ends_at = $newEndDate;
                    $subscription->save();

                    // Check if a transaction with this ID already exists
                    $transactionId = isset($paymentResult['transaction_id']) ? $paymentResult['transaction_id'] : null;
                    $existingTransaction = null;

                    if ($transactionId) {
                        $existingTransaction = Transaction::where('transaction_id', $transactionId)->first();
                    }

                    if ($existingTransaction) {
                        // If a transaction with this ID already exists, update it if needed
                        $this->line("  Transaction ID {$transactionId} already exists in the database. Using existing transaction record.");

                        // Make sure the existing transaction is marked as successful and linked to this subscription
                        if ($existingTransaction->status !== 'success' || $existingTransaction->subscription_id !== $subscription->id) {
                            $existingTransaction->status = 'success';
                            $existingTransaction->subscription_id = $subscription->id;
                            $existingTransaction->save();
                        }
                    } else {
                        // Create a new transaction record
                        // Make sure to use the subscription owner's user_id
                        Transaction::create([
                            'user_id' => $subscription->user_id, // Use subscription's user_id to ensure it's the patient
                            'subscription_id' => $subscription->id,
                            'transaction_id' => $transactionId,
                            'amount' => $amount,
                            'status' => 'success',
                            'is_discounted' => $subscription->is_discounted,
                        ]);
                    }

                    $this->line("  Payment successful! Subscription extended to {$newEndDate->format('Y-m-d')}");
                    $successCount++;

                    // Notify user of successful renewal
                    $user->notify(new SubscriptionAutoRenewedNotification($subscription, $newEndDate));

                    Log::info("Auto-renewed subscription #{$subscription->id} for user {$user->name}");
                } else {
                    // Payment failed
                    $this->error("  Payment failed: {$paymentResult['message']}");
                    $failureCount++;

                    // Create a failed transaction record
                    // Make sure to use the subscription owner's user_id
                    Transaction::create([
                        'user_id' => $subscription->user_id, // Use subscription's user_id to ensure it's the patient
                        'subscription_id' => $subscription->id,
                        'transaction_id' => isset($paymentResult['transaction_id']) ? $paymentResult['transaction_id'] : null,
                        'amount' => $amount,
                        'status' => 'failed',
                        'is_discounted' => $subscription->is_discounted,
                        'error_message' => isset($paymentResult['message']) ? $paymentResult['message'] : 'Unknown error',
                    ]);

                    // Notify user of failed renewal
                    $user->notify(new SubscriptionRenewalFailedNotification($subscription, $paymentResult['message']));

                    Log::error("Failed to auto-renew subscription #{$subscription->id} for user {$user->name}: {$paymentResult['message']}");
                }
            } catch (\Exception $e) {
                $this->error("  Error processing payment: {$e->getMessage()}");
                $failureCount++;

                // Notify user of failed renewal
                $user->notify(new SubscriptionRenewalFailedNotification($subscription, 'An error occurred while processing your payment'));

                Log::error("Error during auto-renewal of subscription #{$subscription->id}: {$e->getMessage()}");
            }

            $bar->advance();
        }

        $bar->finish();

        $this->newLine(2);
        if (!$dryRun) {
            $this->info("Auto-renewal attempts completed:");
            $this->info("  Successful renewals: {$successCount}");
            $this->info("  Failed renewals: {$failureCount}");
        } else {
            $this->info("Dry run completed. Would have attempted to renew {$count} subscriptions.");
        }

        return 0;
    }
}
