<?php

namespace App\Console\Commands;

use App\Jobs\RenewSubscription;
use App\Models\User;
use Illuminate\Console\Command;

class CheckExpiringSubscriptions extends Command
{
    protected $signature = 'subscriptions:check-expiring';
    protected $description = 'Check for expiring subscriptions and attempt to renew them';

    public function handle()
    {
        // Update this to use the subscriptions table
        // $expiringUsers = User::whereNotNull('subscription_plan_id')
        //     ->whereDate('subscription_ends_at', '=', now()->addDays(3))
        //     ->get();

        // foreach ($expiringUsers as $user) {
        //     RenewSubscription::dispatch($user);
        // }

        // $this->info('Checked ' . $expiringUsers->count() . ' expiring subscriptions.');
    }
}