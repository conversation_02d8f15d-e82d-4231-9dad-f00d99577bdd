<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CheckLogsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check log files for recent entries';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking log files...');
        
        $logPath = storage_path('logs');
        $this->info("Log directory: {$logPath}");
        
        // List all log files
        $logFiles = File::files($logPath);
        $this->info("Found " . count($logFiles) . " log files:");
        
        foreach ($logFiles as $file) {
            $filename = $file->getFilename();
            $size = $file->getSize();
            $lastModified = date('Y-m-d H:i:s', $file->getMTime());
            
            $this->info("- {$filename} ({$size} bytes, last modified: {$lastModified})");
            
            // Show the last 10 lines of each log file
            if ($size > 0) {
                $this->info("  Last 10 lines:");
                $content = file_get_contents($file->getPathname());
                $lines = explode("\n", $content);
                $lastLines = array_slice($lines, -10);
                
                foreach ($lastLines as $line) {
                    if (trim($line)) {
                        $this->line("  " . trim($line));
                    }
                }
            } else {
                $this->warn("  File is empty");
            }
            
            $this->newLine();
        }
        
        // Check if welcome_emails.log exists
        $welcomeEmailsLog = storage_path('logs/welcome_emails.log');
        if (File::exists($welcomeEmailsLog)) {
            $this->info("Welcome emails log exists: {$welcomeEmailsLog}");
            $size = File::size($welcomeEmailsLog);
            $this->info("Size: {$size} bytes");
            
            if ($size > 0) {
                $this->info("Content:");
                $content = file_get_contents($welcomeEmailsLog);
                $this->line($content);
            } else {
                $this->warn("File is empty");
            }
        } else {
            $this->warn("Welcome emails log does not exist yet");
        }
        
        // Check Laravel log
        $laravelLog = storage_path('logs/laravel.log');
        if (File::exists($laravelLog)) {
            $this->info("Laravel log exists: {$laravelLog}");
            $size = File::size($laravelLog);
            $this->info("Size: {$size} bytes");
            
            if ($size > 0) {
                $this->info("Last 20 lines:");
                $content = file_get_contents($laravelLog);
                $lines = explode("\n", $content);
                $lastLines = array_slice($lines, -20);
                
                foreach ($lastLines as $line) {
                    if (trim($line)) {
                        $this->line(trim($line));
                    }
                }
            } else {
                $this->warn("File is empty");
            }
        } else {
            $this->warn("Laravel log does not exist");
        }
    }
}
