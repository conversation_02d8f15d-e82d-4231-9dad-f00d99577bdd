<?php

namespace App\Console\Commands;

use App\Models\BusinessPlanSelfPayment;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupStaleSelfPayments extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'self-payment:cleanup {--hours=24 : Hours after which pending payments are considered stale} {--dry-run : Show what would be cleaned up without actually doing it}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up stale pending self-payments that have been stuck for too long';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $hours = (int) $this->option('hours');
        $dryRun = $this->option('dry-run');
        
        $cutoffTime = Carbon::now()->subHours($hours);
        
        $this->info("Looking for pending self-payments older than {$hours} hours (before {$cutoffTime->format('Y-m-d H:i:s')})...");
        
        // Find stale pending payments
        $stalePayments = BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_PENDING)
            ->where('created_at', '<', $cutoffTime)
            ->with(['businessEmployee', 'businessPlan.business', 'transaction'])
            ->get();
            
        if ($stalePayments->isEmpty()) {
            $this->info('No stale pending self-payments found.');
            return 0;
        }
        
        $this->warn("Found {$stalePayments->count()} stale pending self-payments:");
        
        $table = [];
        foreach ($stalePayments as $payment) {
            $table[] = [
                'ID' => $payment->id,
                'Employee' => $payment->businessEmployee->first_name . ' ' . $payment->businessEmployee->last_name,
                'Business' => $payment->businessPlan->business->name,
                'Amount' => '$' . number_format($payment->amount_paid / 100, 2),
                'Created' => $payment->created_at->format('Y-m-d H:i:s'),
                'Age (hours)' => $payment->created_at->diffInHours(now()),
                'Has Transaction' => $payment->transaction_id ? 'Yes' : 'No',
            ];
        }
        
        $this->table([
            'ID', 'Employee', 'Business', 'Amount', 'Created', 'Age (hours)', 'Has Transaction'
        ], $table);
        
        if ($dryRun) {
            $this->comment('DRY RUN: No changes were made. Remove --dry-run to actually clean up these payments.');
            return 0;
        }
        
        if (!$this->confirm('Do you want to clean up these stale pending payments?')) {
            $this->info('Cleanup cancelled.');
            return 0;
        }
        
        $cleanedCount = 0;
        $transactionCount = 0;
        
        foreach ($stalePayments as $payment) {
            try {
                // Remove associated transaction if exists
                if ($payment->transaction_id) {
                    $transaction = Transaction::find($payment->transaction_id);
                    if ($transaction) {
                        $transaction->delete();
                        $transactionCount++;
                    }
                }
                
                // Delete the stale payment
                $payment->delete();
                $cleanedCount++;
                
                $this->line("✓ Cleaned up payment ID {$payment->id} for {$payment->businessEmployee->first_name} {$payment->businessEmployee->last_name}");
                
            } catch (\Exception $e) {
                $this->error("✗ Failed to clean up payment ID {$payment->id}: {$e->getMessage()}");
            }
        }
        
        $this->info("Cleanup completed:");
        $this->line("  - Removed {$cleanedCount} stale self-payments");
        $this->line("  - Removed {$transactionCount} associated transactions");
        
        return 0;
    }
}
