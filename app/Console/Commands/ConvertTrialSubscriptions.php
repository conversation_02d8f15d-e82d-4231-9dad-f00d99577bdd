<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Subscription;
use App\Jobs\ConvertTrialSubscriptionJob;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ConvertTrialSubscriptions extends Command
{
    protected $signature = 'subscriptions:convert-trials';
    protected $description = 'Queue jobs to convert trial subscriptions that are about to expire';

    public function handle()
    {
        $expiringSoonTrials = Subscription::where('is_trial', true)
            ->where('status', 'active')
            ->where('ends_at', '<=', Carbon::now()->addDay())
            ->get();

        foreach ($expiringSoonTrials as $trial) {
            ConvertTrialSubscriptionJob::dispatch($trial);
        }

        Log::info('Trial conversion jobs queued: ' . $expiringSoonTrials->count());
    }
}