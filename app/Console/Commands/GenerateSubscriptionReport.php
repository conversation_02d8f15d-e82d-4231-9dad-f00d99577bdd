<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class GenerateSubscriptionReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:report 
                            {--period=month : Report period (day, week, month, year)} 
                            {--format=csv : Output format (csv, json)}
                            {--email= : Email address to send the report to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate a report of subscription activity';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $period = $this->option('period');
        $format = $this->option('format');
        $email = $this->option('email');
        
        $this->info("Generating subscription report for period: {$period}");
        
        // Determine date range based on period
        $endDate = now();
        $startDate = match ($period) {
            'day' => now()->subDay(),
            'week' => now()->subWeek(),
            'year' => now()->subYear(),
            default => now()->subMonth(), // Default to month
        };
        
        $this->info("Date range: {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
        
        // Get subscription data
        $newSubscriptions = Subscription::whereBetween('created_at', [$startDate, $endDate])->count();
        $expiredSubscriptions = Subscription::where('status', 'expired')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();
        $cancelledSubscriptions = Subscription::where('status', 'cancelled')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();
        $activeSubscriptions = Subscription::where('status', 'active')->count();
        
        // Get subscription data by plan
        $plans = SubscriptionPlan::all();
        $planStats = [];
        
        foreach ($plans as $plan) {
            $newByPlan = Subscription::where('plan_id', $plan->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
                
            $activeByPlan = Subscription::where('plan_id', $plan->id)
                ->where('status', 'active')
                ->count();
                
            $planStats[$plan->name] = [
                'new' => $newByPlan,
                'active' => $activeByPlan,
                'price' => $plan->price,
                'revenue' => $activeByPlan * $plan->price,
            ];
        }
        
        // Calculate total revenue
        $totalRevenue = array_sum(array_column($planStats, 'revenue'));
        
        // Prepare report data
        $reportData = [
            'period' => $period,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'generated_at' => now()->format('Y-m-d H:i:s'),
            'summary' => [
                'new_subscriptions' => $newSubscriptions,
                'expired_subscriptions' => $expiredSubscriptions,
                'cancelled_subscriptions' => $cancelledSubscriptions,
                'active_subscriptions' => $activeSubscriptions,
                'total_revenue' => $totalRevenue,
            ],
            'plans' => $planStats,
        ];
        
        // Generate report file
        $filename = 'subscription_report_' . $period . '_' . now()->format('Y-m-d') . '.' . $format;
        $content = '';
        
        if ($format === 'json') {
            $content = json_encode($reportData, JSON_PRETTY_PRINT);
        } else {
            // Generate CSV
            $csvData = [];
            
            // Add header row
            $csvData[] = ['Subscription Report', "Period: {$period}", "From: {$startDate->format('Y-m-d')}", "To: {$endDate->format('Y-m-d')}"];
            $csvData[] = []; // Empty row
            
            // Add summary section
            $csvData[] = ['Summary'];
            $csvData[] = ['New Subscriptions', $newSubscriptions];
            $csvData[] = ['Expired Subscriptions', $expiredSubscriptions];
            $csvData[] = ['Cancelled Subscriptions', $cancelledSubscriptions];
            $csvData[] = ['Active Subscriptions', $activeSubscriptions];
            $csvData[] = ['Total Revenue', '$' . number_format($totalRevenue, 2)];
            $csvData[] = []; // Empty row
            
            // Add plan details
            $csvData[] = ['Plan Details'];
            $csvData[] = ['Plan Name', 'New Subscriptions', 'Active Subscriptions', 'Price', 'Revenue'];
            
            foreach ($planStats as $planName => $stats) {
                $csvData[] = [
                    $planName,
                    $stats['new'],
                    $stats['active'],
                    '$' . number_format($stats['price'], 2),
                    '$' . number_format($stats['revenue'], 2),
                ];
            }
            
            // Convert to CSV string
            $csv = fopen('php://temp', 'r+');
            foreach ($csvData as $row) {
                fputcsv($csv, $row);
            }
            rewind($csv);
            $content = stream_get_contents($csv);
            fclose($csv);
        }
        
        // Save the report
        Storage::disk('local')->put('reports/' . $filename, $content);
        $this->info("Report saved to: reports/{$filename}");
        
        // Display summary in console
        $this->newLine();
        $this->info('Subscription Report Summary:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['New Subscriptions', $newSubscriptions],
                ['Expired Subscriptions', $expiredSubscriptions],
                ['Cancelled Subscriptions', $cancelledSubscriptions],
                ['Active Subscriptions', $activeSubscriptions],
                ['Total Revenue', '$' . number_format($totalRevenue, 2)],
            ]
        );
        
        // Email the report if requested
        if ($email) {
            $this->info("Report would be emailed to: {$email}");
            // In a real implementation, you would send the email here
        }
        
        return 0;
    }
}
