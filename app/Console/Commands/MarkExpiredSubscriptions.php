<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Notifications\SubscriptionExpiredNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MarkExpiredSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:mark-expired {--dry-run : Run without making changes} {--no-notify : Do not send notifications to users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark subscriptions as expired if their end date has passed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $noNotify = $this->option('no-notify');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        if ($noNotify) {
            $this->info('Notifications are disabled.');
        }

        $now = now();

        // Find active subscriptions that have passed their end date
        $expiredSubscriptions = Subscription::where('status', 'active')
            ->where('ends_at', '<', $now)
            ->with(['user', 'plan']) // Eager load relationships
            ->get();

        $count = $expiredSubscriptions->count();

        if ($count === 0) {
            $this->info('No expired subscriptions found.');
            return 0;
        }

        $this->info("Found {$count} expired subscriptions.");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $notificationsSent = 0;
        $notificationsFailed = 0;

        foreach ($expiredSubscriptions as $subscription) {
            $this->line("\nProcessing subscription #{$subscription->id} for user {$subscription->user->name}");
            $this->line("  Expired on: {$subscription->ends_at->format('Y-m-d')}");

            if (!$dryRun) {
                $subscription->status = 'expired';
                $subscription->save();

                $this->line("  Status updated to: expired");
                Log::info("Subscription #{$subscription->id} for user {$subscription->user->name} marked as expired");

                // Send notification if enabled
                if (!$noNotify && $subscription->user) {
                    try {
                        $subscription->user->notify(new SubscriptionExpiredNotification($subscription));
                        $this->line("  Notification sent to: {$subscription->user->email}");
                        $notificationsSent++;
                    } catch (\Exception $e) {
                        $this->error("  Failed to send notification: {$e->getMessage()}");
                        Log::error("Failed to send subscription expired notification: {$e->getMessage()}");
                        $notificationsFailed++;
                    }
                }
            } else {
                $this->line("  Would update status to: expired");

                if (!$noNotify && $subscription->user) {
                    $this->line("  Would send notification to: {$subscription->user->email}");
                }
            }

            $bar->advance();
        }

        $bar->finish();

        $this->newLine(2);
        if (!$dryRun) {
            $this->info("Successfully marked {$count} subscriptions as expired.");

            if (!$noNotify) {
                $this->info("Notifications sent: {$notificationsSent}");
                if ($notificationsFailed > 0) {
                    $this->warn("Notifications failed: {$notificationsFailed}");
                }
            }
        } else {
            $this->info("Dry run completed. {$count} subscriptions would be marked as expired.");
        }

        return 0;
    }
}
