<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateSubscriptionAgentData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:migrate-agent-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate agent_id and user_type from meta_data JSON to dedicated columns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //
    }
}
