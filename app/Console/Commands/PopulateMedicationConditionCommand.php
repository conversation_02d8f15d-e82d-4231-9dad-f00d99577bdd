<?php

namespace App\Console\Commands;

use App\Models\Medication;
use App\Models\Condition;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PopulateMedicationConditionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'medication:populate-conditions
                            {--clear : Clear existing medication-condition relationships before populating}
                            {--dry-run : Show what would be created without actually creating records}
                            {--medication= : Populate for a specific medication ID}
                            {--condition= : Populate for a specific condition ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the medication_condition pivot table with medication-condition relationships';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting medication-condition relationship population...');

        // Check if we should clear existing relationships
        if ($this->option('clear')) {
            if ($this->option('dry-run')) {
                $this->warn('DRY RUN: Would clear all existing medication-condition relationships');
            } else {
                $this->warn('Clearing existing medication-condition relationships...');
                DB::table('medication_condition')->truncate();
                $this->info('Existing relationships cleared.');
            }
        }

        // Get medications and conditions based on options
        $medications = $this->getMedications();
        $conditions = $this->getConditions();

        if ($medications->isEmpty()) {
            $this->error('No medications found to process.');
            return 1;
        }

        if ($conditions->isEmpty()) {
            $this->error('No conditions found to process.');
            return 1;
        }

        $this->info("Processing {$medications->count()} medications and {$conditions->count()} conditions...");

        $createdCount = 0;
        $skippedCount = 0;

        // Define medication-condition mappings
        $medicationConditionMappings = $this->getMedicationConditionMappings();

        foreach ($medications as $medication) {
            $this->line("Processing medication: {$medication->name}");

            // Get conditions for this medication based on mappings
            $medicationConditions = $this->getConditionsForMedication($medication, $conditions, $medicationConditionMappings);

            foreach ($medicationConditions as $conditionData) {
                $condition = $conditionData['condition'];
                $isPrimaryUse = $conditionData['is_primary_use'];

                // Check if relationship already exists
                $exists = DB::table('medication_condition')
                    ->where('medication_id', $medication->id)
                    ->where('condition_id', $condition->id)
                    ->exists();

                if ($exists) {
                    $skippedCount++;
                    $this->line("  - Skipped {$condition->name} (already exists)");
                    continue;
                }

                if ($this->option('dry-run')) {
                    $this->line("  - Would create: {$condition->name} (Primary: " . ($isPrimaryUse ? 'Yes' : 'No') . ")");
                    $createdCount++;
                } else {
                    DB::table('medication_condition')->insert([
                        'medication_id' => $medication->id,
                        'condition_id' => $condition->id,
                        'is_primary_use' => $isPrimaryUse,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $createdCount++;
                    $this->line("  - Created: {$condition->name} (Primary: " . ($isPrimaryUse ? 'Yes' : 'No') . ")");
                }
            }
        }

        $this->newLine();
        $this->info('Summary:');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Relationships Created', $createdCount],
                ['Relationships Skipped', $skippedCount],
                ['Total Medications Processed', $medications->count()],
                ['Total Conditions Available', $conditions->count()],
            ]
        );

        if ($this->option('dry-run')) {
            $this->warn('This was a dry run. No actual records were created.');
        } else {
            $this->info('Medication-condition relationships populated successfully!');
        }

        return 0;
    }

    /**
     * Get medications based on command options.
     */
    private function getMedications()
    {
        if ($medicationId = $this->option('medication')) {
            return Medication::where('id', $medicationId)->get();
        }

        return Medication::all();
    }

    /**
     * Get conditions based on command options.
     */
    private function getConditions()
    {
        if ($conditionId = $this->option('condition')) {
            return Condition::where('id', $conditionId)->get();
        }

        return Condition::all();
    }

    /**
     * Get medication-condition mappings.
     * This defines which conditions are associated with which medications.
     * Updated to match actual database content.
     */
    private function getMedicationConditionMappings()
    {
        return [
            // Diabetes medications
            'metformin' => [
                'diabetes' => true,
                'pcos' => false, // Secondary use for PCOS
            ],

            // Erectile dysfunction medications
            'sildenafil' => [
                'erectile dysfunction' => true,
            ],
            'tadalafil' => [
                'erectile dysfunction' => true,
            ],
            'avanafil' => [
                'erectile dysfunction' => true,
            ],
            'vardenafil' => [
                'erectile dysfunction' => true,
            ],

            // Hair loss medications
            'finasteride' => [
                'male pattern baldness' => true,
                'pattern baldness' => true,
            ],
            'dutasteride' => [
                'male pattern baldness' => true,
                'pattern baldness' => true,
            ],
            'minoxidil' => [
                'male pattern baldness' => true,
                'pattern baldness' => true,
            ],

            // Antidepressants - SSRIs
            'sertraline' => [
                'depression' => true,
                'gad' => true,
                'panic disorder' => true,
                'ocd' => true,
                'ptsd' => false,
            ],
            'escitalopram' => [
                'depression' => true,
                'gad' => true,
                'panic disorder' => false,
            ],
            'fluoxetine' => [
                'depression' => true,
                'gad' => false,
                'ocd' => true,
            ],
            'citalopram' => [
                'depression' => true,
                'gad' => false,
            ],
            'paroxetine' => [
                'depression' => true,
                'gad' => true,
                'panic disorder' => true,
                'ocd' => true,
                'ptsd' => false,
            ],

            // Antidepressants - SNRIs
            'duloxetine' => [
                'depression' => true,
                'gad' => true,
                'neuropathic pain' => true,
                'fibromyalgia' => true,
                'chronic pain' => false,
            ],
            'venlafaxine' => [
                'depression' => true,
                'gad' => true,
                'panic disorder' => false,
            ],

            // Antidepressants - Tricyclics
            'amitriptyline' => [
                'depression' => true,
                'neuropathic pain' => true,
                'chronic pain' => true,
                'migraine' => false,
                'sleep disorders' => false,
            ],
            'nortriptyline' => [
                'depression' => true,
                'neuropathic pain' => true,
                'chronic pain' => false,
                'migraine' => false,
            ],

            // Antidepressants - Atypical
            'bupropion' => [
                'depression' => true,
                'adhd' => false,
            ],
            'mirtazapine' => [
                'depression' => true,
                'sleep disorders' => false,
            ],
            'trazodone' => [
                'depression' => true,
                'sleep disorders' => true,
            ],

            // Anxiety medications
            'buspirone' => [
                'gad' => true,
                'anxiety' => true,
            ],

            // Antipsychotics
            'aripiprazole' => [
                'bipolar' => true,
                'depression' => false, // Adjunct therapy
            ],
            'quetiapine' => [
                'bipolar' => true,
                'depression' => false, // Adjunct therapy
                'sleep disorders' => false,
            ],

            // ADHD medications
            'atomoxetine' => [
                'adhd' => true,
            ],

            // Muscle relaxants and pain medications
            'baclofen' => [
                'muscle spasms' => true,
                'chronic pain' => false,
            ],
            'cyclobenzaprine' => [
                'muscle spasms' => true,
                'fibromyalgia' => false,
            ],
            'methocarbamol' => [
                'muscle spasms' => true,
            ],
            'tizanidine' => [
                'muscle spasms' => true,
            ],
            'metaxalone' => [
                'muscle spasms' => true,
            ],
            'chlorzoxazone' => [
                'muscle spasms' => true,
            ],
            'dantrolene' => [
                'muscle spasms' => true,
            ],
            'orphenadrine' => [
                'muscle spasms' => true,
            ],

            // NSAIDs and pain medications
            'celecoxib' => [
                'arthritis' => true,
                'chronic pain' => true,
                'rheumatoid arthritis' => true,
                'psoriatic arthritis' => false,
            ],
            'meloxicam' => [
                'arthritis' => true,
                'chronic pain' => true,
                'rheumatoid arthritis' => true,
            ],
            'nabumetone' => [
                'arthritis' => true,
                'chronic pain' => true,
            ],
            'etodolac' => [
                'arthritis' => true,
                'chronic pain' => true,
            ],
            'acetaminophen' => [
                'chronic pain' => true,
                'migraine' => false,
            ],

            // Migraine medications
            'butalbital' => [
                'migraine' => true,
            ],
            'topiramate' => [
                'epilepsy' => true,
                'migraine' => false, // Prevention
            ],

            // Cardiovascular medications
            'lisinopril' => [
                'hypertension' => true,
                'heart failure' => true,
            ],
            'amlodipine' => [
                'hypertension' => true,
            ],
            'hydrochlorothiazide' => [
                'hypertension' => true,
            ],
            'carvedilol' => [
                'heart failure' => true,
                'hypertension' => false,
            ],
            'diltiazem' => [
                'hypertension' => true,
                'atrial fibrillation' => false,
            ],
            'metoprolol' => [
                'hypertension' => true,
                'heart failure' => false,
            ],
            'propranolol' => [
                'hypertension' => true,
                'essential tremor' => true,
                'migraine' => false, // Prevention
            ],
            'losartan' => [
                'hypertension' => true,
            ],
            'furosemide' => [
                'heart failure' => true,
                'hypertension' => false,
            ],
            'spironolactone' => [
                'heart failure' => true,
                'hypertension' => false,
                'acne' => false, // Off-label use
            ],
            'isosorbide' => [
                'heart failure' => true,
            ],
            'apixaban' => [
                'atrial fibrillation' => true,
            ],

            // Cholesterol medications
            'atorvastatin' => [
                'hypertension' => false, // Cardiovascular protection
            ],
            'simvastatin' => [
                'hypertension' => false, // Cardiovascular protection
            ],

            // Respiratory medications
            'albuterol' => [
                'asthma' => true,
                'copd' => true,
            ],
            'fluticasone' => [
                'asthma' => true,
                'allergic rhinitis' => true,
                'rhinitis' => true,
            ],
            'salmeterol' => [
                'asthma' => true,
                'copd' => true,
            ],
            'ipratropium' => [
                'copd' => true,
                'asthma' => false,
            ],
            'tiotropium' => [
                'copd' => true,
            ],
            'montelukast' => [
                'asthma' => true,
                'allergic rhinitis' => true,
            ],

            // Gastrointestinal medications
            'omeprazole' => [
                'acid reflux' => true,
                'h. pylori' => false,
            ],
            'metoclopramide' => [
                'gastroparesis' => true,
            ],
            'linaclotide' => [
                'ibs' => true,
            ],
            'rifaximin' => [
                'ibs' => true,
            ],

            // Thyroid medications
            'levothyroxine' => [
                'thyroid conditions' => true,
            ],

            // Hormone medications
            'estradiol' => [
                'menopausal symptoms' => true,
            ],

            // Antibiotics
            'amoxicillin' => [
                'bacterial infections' => true,
                'dental prophylaxis' => true,
                'h. pylori' => false,
            ],
            'azithromycin' => [
                'bacterial infections' => true,
            ],
            'doxycycline' => [
                'bacterial infections' => true,
                'acne' => true,
            ],
            'ciprofloxacin' => [
                'bacterial infections' => true,
                'uti' => true,
            ],
            'levofloxacin' => [
                'bacterial infections' => true,
            ],
            'ceftriaxone' => [
                'bacterial infections' => true,
            ],
            'nitrofurantoin' => [
                'uti' => true,
            ],
            'trimethoprim' => [
                'uti' => true,
                'bacterial infections' => false,
            ],
            'metronidazole' => [
                'bacterial infections' => true,
                'h. pylori' => false,
            ],
            'linezolid' => [
                'mrsa' => true,
                'bacterial infections' => true,
            ],
            'vancomycin' => [
                'mrsa' => true,
                'bacterial infections' => true,
            ],

            // Antifungals
            'fluconazole' => [
                'valley fever' => true,
            ],
            'ketoconazole' => [
                'seborrheic dermatitis' => true,
            ],
            'amphotericin' => [
                'valley fever' => true,
            ],
            'voriconazole' => [
                'aspergillosis' => true,
            ],

            // Antivirals
            'acyclovir' => [
                'bacterial infections' => false, // Actually antiviral, but no viral conditions in DB
            ],
            'valacyclovir' => [
                'bacterial infections' => false, // Actually antiviral, but no viral conditions in DB
            ],
            'tenofovir' => [
                'hiv prophylaxis' => true,
            ],

            // Dermatology medications
            'tretinoin' => [
                'acne' => true,
            ],
            'adapalene' => [
                'acne' => true,
            ],
            'hydroquinone' => [
                'acne' => false, // Hyperpigmentation
            ],
            'azelaic acid' => [
                'acne' => true,
                'rosacea' => true,
            ],
            'clobetasol' => [
                'psoriasis' => true,
                'eczema' => true,
            ],
            'hydrocortisone' => [
                'eczema' => true,
                'psoriasis' => false,
            ],
            'mupirocin' => [
                'bacterial infections' => true, // Topical antibiotic
            ],

            // Allergy medications
            'cetirizine' => [
                'allergic rhinitis' => true,
                'allergies' => true,
                'hives' => true,
            ],
            'fexofenadine' => [
                'allergic rhinitis' => true,
                'allergies' => true,
            ],
            'loratadine' => [
                'allergic rhinitis' => true,
                'allergies' => true,
            ],
            'epinephrine' => [
                'anaphylaxis' => true,
            ],
            'prednisone' => [
                'allergic rhinitis' => false,
                'asthma' => false,
                'rheumatoid arthritis' => false,
            ],

            // Neurological medications - Anticonvulsants
            'carbamazepine' => [
                'epilepsy' => true,
                'neuropathic pain' => false,
            ],
            'lamotrigine' => [
                'epilepsy' => true,
                'bipolar' => false,
            ],
            'levetiracetam' => [
                'epilepsy' => true,
            ],
            'phenytoin' => [
                'epilepsy' => true,
            ],
            'valproic acid' => [
                'epilepsy' => true,
                'bipolar' => true,
                'migraine' => false,
            ],
            'oxcarbazepine' => [
                'epilepsy' => true,
            ],
            'brivaracetam' => [
                'epilepsy' => true,
            ],
            'felbamate' => [
                'epilepsy' => true,
            ],
            'ethosuximide' => [
                'epilepsy' => true,
            ],
            'primidone' => [
                'epilepsy' => true,
                'essential tremor' => true,
            ],
            'rufinamide' => [
                'epilepsy' => true,
            ],
            'stiripentol' => [
                'epilepsy' => true,
            ],
            'tiagabine' => [
                'epilepsy' => true,
            ],
            'zonisamide' => [
                'epilepsy' => true,
            ],

            // Parkinson's medications
            'pramipexole' => [
                'parkinson\'s' => true,
            ],

            // ADHD medications
            'clonidine' => [
                'adhd' => true,
                'hypertension' => false,
            ],
            'guanfacine' => [
                'adhd' => true,
                'hypertension' => false,
            ],

            // Bone health
            'alendronate' => [
                'osteopenia' => true,
            ],

            // Immunosuppressants
            'methotrexate' => [
                'rheumatoid arthritis' => true,
                'psoriatic arthritis' => true,
                'psoriasis' => true,
            ],

            // Weight management
            'weight loss kit' => [
                'weight management' => true,
            ],
        ];
    }

    /**
     * Get conditions for a specific medication based on mappings.
     */
    private function getConditionsForMedication($medication, $conditions, $mappings)
    {
        $medicationConditions = [];
        $medicationName = strtolower($medication->name);
        $medicationGenericName = strtolower($medication->generic_name ?? '');

        // Check if we have mappings for this medication
        $conditionMap = null;
        foreach ($mappings as $drugName => $drugConditions) {
            if (str_contains($medicationName, $drugName) || str_contains($medicationGenericName, $drugName)) {
                $conditionMap = $drugConditions;
                break;
            }
        }

        if (!$conditionMap) {
            // If no specific mapping found, create some default relationships
            $this->line("  - No specific mapping found for {$medication->name}, using default conditions");

            // Add some common conditions as secondary uses
            $defaultConditions = ['pain', 'inflammation', 'infection'];
            foreach ($conditions as $condition) {
                $conditionName = strtolower($condition->name);
                foreach ($defaultConditions as $defaultCondition) {
                    if (str_contains($conditionName, $defaultCondition)) {
                        $medicationConditions[] = [
                            'condition' => $condition,
                            'is_primary_use' => false,
                        ];
                        break;
                    }
                }
            }
        } else {
            // Use the specific mappings
            foreach ($conditionMap as $conditionName => $isPrimary) {
                $condition = $conditions->first(function ($c) use ($conditionName) {
                    return str_contains(strtolower($c->name), strtolower($conditionName)) ||
                           str_contains(strtolower($c->therapeutic_use ?? ''), strtolower($conditionName));
                });

                if ($condition) {
                    $medicationConditions[] = [
                        'condition' => $condition,
                        'is_primary_use' => $isPrimary,
                    ];
                }
            }
        }

        return $medicationConditions;
    }
}
