<?php

namespace App\Console\Commands;

use App\Models\Subscription;
use App\Models\Transaction;
use App\Services\LinkTrustService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessRecurringBillingTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'linktrust:track-recurring-billing {--days=1 : Number of days to look back}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Track recurring billing transactions in LinkTrust for affiliate commissions';

    /**
     * The LinkTrust service instance.
     *
     * @var \App\Services\LinkTrustService
     */
    protected $linkTrustService;

    /**
     * Create a new command instance.
     */
    public function __construct(LinkTrustService $linkTrustService)
    {
        parent::__construct();
        $this->linkTrustService = $linkTrustService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $this->info("Processing recurring billing tracking for the last {$days} days...");

        $startDate = Carbon::now()->subDays($days);

        // Get all successful transactions from the last X days that are for recurring billing
        $transactions = Transaction::where('status', 'success')
            ->where('created_at', '>=', $startDate)
            ->whereHas('subscription', function ($query) {
                $query->where(function($q) {
                    $q->whereNotNull('afid')
                      ->orWhereNotNull('click_id');
                }); // Track transactions with either AFID or ClickID
            })
            ->get();

        $this->info("Found {$transactions->count()} transactions to process.");

        $successCount = 0;
        $failCount = 0;

        foreach ($transactions as $transaction) {
            $subscription = $transaction->subscription;

            if (!$subscription || (!$subscription->afid && !$subscription->click_id)) {
                $this->warn("Transaction {$transaction->id} has no valid subscription or tracking IDs.");
                $failCount++;
                continue;
            }

            // Log the tracking IDs we're using
            $this->info("Using AFID: {$subscription->afid}, ClickID: {$subscription->click_id}");

            $this->info("Processing transaction {$transaction->id} for subscription {$subscription->id} with AFID {$subscription->afid}...");

            try {
                // Track the transaction in LinkTrust
                $result = $this->linkTrustService->trackConversion(
                    merchantReferenceId: $transaction->user_id,
                    totalSales: $transaction->amount,
                    transactionId: $transaction->transaction_id
                );

                if ($result['success']) {
                    $this->info("Successfully tracked transaction {$transaction->id} in LinkTrust.");
                    $successCount++;

                    // Store tracking information in transaction meta data
                    $transaction->meta_data = array_merge($transaction->meta_data ?? [], [
                        'linktrust_tracking' => [
                            'tracked_at' => Carbon::now()->toIso8601String(),
                            'success' => true,
                            'tracking_url' => $result['tracking_url'] ?? null,
                        ]
                    ]);
                    $transaction->save();
                } else {
                    $this->error("Failed to track transaction {$transaction->id} in LinkTrust: {$result['message']}");
                    $failCount++;

                    // Store tracking information in transaction meta data
                    $transaction->meta_data = array_merge($transaction->meta_data ?? [], [
                        'linktrust_tracking' => [
                            'tracked_at' => Carbon::now()->toIso8601String(),
                            'success' => false,
                            'error' => $result['message'] ?? 'Unknown error',
                        ]
                    ]);
                    $transaction->save();
                }
            } catch (\Exception $e) {
                $this->error("Exception while tracking transaction {$transaction->id}: {$e->getMessage()}");
                Log::error("LinkTrust recurring billing tracking exception", [
                    'transaction_id' => $transaction->id,
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                $failCount++;
            }
        }

        $this->info("Completed processing recurring billing tracking.");
        $this->info("Success: {$successCount}, Failed: {$failCount}");

        return 0;
    }
}
