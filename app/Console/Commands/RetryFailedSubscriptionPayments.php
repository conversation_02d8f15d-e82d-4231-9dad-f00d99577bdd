<?php

namespace App\Console\Commands;

use App\Models\Transaction;
use App\Models\Subscription;
use App\Notifications\SubscriptionAutoRenewedNotification;
use App\Notifications\SubscriptionRenewalFailedNotification;
use App\Services\Api\AuthorizeNetService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RetryFailedSubscriptionPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:retry-failed-payments
                            {--days=7 : Only retry payments that failed within this many days}
                            {--max-attempts=3 : Maximum number of retry attempts}
                            {--dry-run : Run without making actual charges}
                            {--transaction-id= : Retry a specific transaction}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retry failed subscription payments';

    /**
     * The Authorize.Net service.
     *
     * @var \App\Services\Api\AuthorizeNetService
     */
    protected $authorizeNetService;

    /**
     * Create a new command instance.
     */
    public function __construct(AuthorizeNetService $authorizeNetService)
    {
        parent::__construct();
        $this->authorizeNetService = $authorizeNetService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $days = (int) $this->option('days');
        $maxAttempts = (int) $this->option('max-attempts');
        $transactionId = $this->option('transaction-id');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No charges will be made.');
        }

        // Build the query based on options
        $query = Transaction::where('status', 'failed')
            ->whereNotNull('subscription_id')
            ->where('created_at', '>=', now()->subDays($days))
            ->with(['subscription', 'subscription.user', 'subscription.plan']);

        // If specific transaction ID is provided
        if ($transactionId) {
            $this->info("Looking for specific transaction ID: {$transactionId}");
            $query->where('transaction_id', $transactionId);
        }

        // Get failed transactions
        $failedTransactions = $query->get();

        $count = $failedTransactions->count();

        if ($count === 0) {
            $this->info('No failed transactions found to retry.');
            return 0;
        }

        $this->info("Found {$count} failed transactions to retry.");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $successCount = 0;
        $failureCount = 0;

        foreach ($failedTransactions as $failedTransaction) {
            $subscription = $failedTransaction->subscription;
            
            if (!$subscription) {
                $this->error("\nSkipping transaction #{$failedTransaction->id}: Missing subscription data");
                $bar->advance();
                continue;
            }

            $user = $subscription->user;
            $plan = $subscription->plan;

            if (!$user || !$plan) {
                $this->error("\nSkipping transaction #{$failedTransaction->id}: Missing user or plan data");
                $bar->advance();
                continue;
            }

            // Check if subscription is still active
            if ($subscription->status !== 'active') {
                $this->line("\nSkipping transaction #{$failedTransaction->id}: Subscription is no longer active");
                $bar->advance();
                continue;
            }

            // Count previous retry attempts
            $retryCount = Transaction::where('subscription_id', $subscription->id)
                ->where('status', 'failed')
                ->where('created_at', '>', $failedTransaction->created_at)
                ->count();

            if ($retryCount >= $maxAttempts) {
                $this->line("\nSkipping transaction #{$failedTransaction->id}: Maximum retry attempts reached ({$retryCount}/{$maxAttempts})");
                $bar->advance();
                continue;
            }

            $this->line("\nRetrying payment for subscription #{$subscription->id} for user {$user->name}");
            $this->line("  Plan: {$plan->name}, Amount: \${$failedTransaction->amount}");
            $this->line("  Previous failure: {$failedTransaction->error_message}");
            $this->line("  Retry attempt: " . ($retryCount + 1) . " of {$maxAttempts}");

            // Check if user has a default payment method
            $defaultCreditCard = $user->creditCards()->where('is_default', true)->first();

            if (!$defaultCreditCard) {
                $this->line("  No default payment method found. Skipping retry.");
                $failureCount++;
                $bar->advance();
                continue;
            }

            if ($dryRun) {
                $this->line("  Would attempt to charge credit card ending in {$defaultCreditCard->last_four}");
                $bar->advance();
                continue;
            }

            // Attempt to charge the card
            try {
                // Get customer profile ID
                $customerProfileId = $user->authorize_net_customer_id;
                if (!$customerProfileId) {
                    $this->error("  No Authorize.Net customer profile found for user {$user->id}");
                    $failureCount++;
                    $bar->advance();
                    continue;
                }

                // Process payment using the token/payment profile ID
                $paymentResult = $this->authorizeNetService->processTransaction(
                    $failedTransaction->amount,
                    $customerProfileId,
                    $defaultCreditCard->token,
                    "Subscription Renewal Retry: {$plan->name}",
                    $subscription->user_id,
                    $subscription->id,
                    $subscription->is_discounted
                );

                if ($paymentResult['success']) {
                    // Payment successful, extend the subscription if it hasn't been extended already
                    $newEndDate = $subscription->ends_at;
                    
                    // Only extend if the subscription is about to expire or has expired
                    if ($subscription->ends_at->isPast() || $subscription->ends_at->diffInDays(now()) < 30) {
                        $newEndDate = max($subscription->ends_at, now())->copy()->addMonths($plan->duration_months);
                        $subscription->ends_at = $newEndDate;
                        $subscription->save();
                    }

                    // Create a success transaction record
                    Transaction::create([
                        'user_id' => $subscription->user_id,
                        'subscription_id' => $subscription->id,
                        'transaction_id' => isset($paymentResult['transaction_id']) ? $paymentResult['transaction_id'] : null,
                        'amount' => $failedTransaction->amount,
                        'status' => 'success',
                        'is_discounted' => $subscription->is_discounted,
                    ]);

                    $this->line("  Payment successful! Subscription extended to {$newEndDate->format('Y-m-d')}");
                    $successCount++;

                    // Notify user of successful renewal
                    $user->notify(new SubscriptionAutoRenewedNotification($subscription, $newEndDate));

                    Log::info("Successfully retried payment for subscription #{$subscription->id} for user {$user->name}");
                } else {
                    // Payment failed again
                    $this->error("  Payment retry failed: {$paymentResult['message']}");
                    $failureCount++;

                    // Create a failed transaction record
                    Transaction::create([
                        'user_id' => $subscription->user_id,
                        'subscription_id' => $subscription->id,
                        'transaction_id' => isset($paymentResult['transaction_id']) ? $paymentResult['transaction_id'] : null,
                        'amount' => $failedTransaction->amount,
                        'status' => 'failed',
                        'is_discounted' => $subscription->is_discounted,
                        'error_message' => isset($paymentResult['message']) ? $paymentResult['message'] : 'Unknown error',
                    ]);

                    // Only notify the user if this is the final retry attempt
                    if ($retryCount + 1 >= $maxAttempts) {
                        $user->notify(new SubscriptionRenewalFailedNotification(
                            $subscription, 
                            "We've tried multiple times to process your payment but were unsuccessful. Please update your payment information."
                        ));
                    }

                    Log::error("Failed to retry payment for subscription #{$subscription->id} for user {$user->name}: {$paymentResult['message']}");
                }
            } catch (\Exception $e) {
                $this->error("  Error processing payment retry: {$e->getMessage()}");
                $failureCount++;

                Log::error("Error during payment retry for subscription #{$subscription->id}: {$e->getMessage()}");
            }

            $bar->advance();
        }

        $bar->finish();

        $this->newLine(2);
        if (!$dryRun) {
            $this->info("Payment retry attempts completed:");
            $this->info("  Successful retries: {$successCount}");
            $this->info("  Failed retries: {$failureCount}");
        } else {
            $this->info("Dry run completed. Would have attempted to retry {$count} payments.");
        }

        return 0;
    }
}
