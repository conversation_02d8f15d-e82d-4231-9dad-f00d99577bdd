<?php

namespace App\Console\Commands;

use App\Mail\AchVerificationReminder;
use App\Models\PaymentMethod;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendAchVerificationReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ach:send-reminders {--days=2 : Number of days after initiation to send reminder}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminders for pending ACH verifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $this->info("Sending reminders for ACH verifications initiated {$days} days ago...");

        $cutoffDate = Carbon::now()->subDays($days);
        
        // Find payment methods with pending verification initiated before the cutoff date
        $pendingVerifications = PaymentMethod::where('type', 'ach')
            ->whereJsonContains('meta_data->verification_status', 'pending')
            ->get();
        
        $count = 0;
        foreach ($pendingVerifications as $paymentMethod) {
            // Check if the verification was initiated before the cutoff date
            $initiatedAt = isset($paymentMethod->meta_data['verification_initiated_at']) 
                ? Carbon::parse($paymentMethod->meta_data['verification_initiated_at']) 
                : null;
            
            if (!$initiatedAt || $initiatedAt->isAfter($cutoffDate)) {
                continue;
            }
            
            // Check if a reminder has already been sent today
            $lastReminderSent = isset($paymentMethod->meta_data['last_reminder_sent']) 
                ? Carbon::parse($paymentMethod->meta_data['last_reminder_sent']) 
                : null;
            
            if ($lastReminderSent && $lastReminderSent->isToday()) {
                continue;
            }
            
            // Get the user
            $user = $paymentMethod->user;
            if (!$user) {
                $this->warn("User not found for payment method ID: {$paymentMethod->id}");
                continue;
            }
            
            // Send reminder email
            try {
                Mail::to($user->email)->send(new AchVerificationReminder($user, $paymentMethod));
                
                // Update the payment method with the reminder date
                $paymentMethod->meta_data = array_merge($paymentMethod->meta_data, [
                    'last_reminder_sent' => Carbon::now()->toDateTimeString(),
                    'reminder_count' => ($paymentMethod->meta_data['reminder_count'] ?? 0) + 1
                ]);
                $paymentMethod->save();
                
                $this->info("Reminder sent to {$user->email} for payment method ID: {$paymentMethod->id}");
                $count++;
            } catch (\Exception $e) {
                $this->error("Failed to send reminder to {$user->email}: {$e->getMessage()}");
                Log::error("Failed to send ACH verification reminder: " . $e->getMessage());
            }
        }
        
        $this->info("Sent {$count} reminders for pending ACH verifications.");
    }
}
