<?php

namespace App\Console\Commands;

use App\Mail\AgentRegistrationNotification;
use App\Mail\AgentTemporaryPasswordMail;
use App\Models\Agent;
use App\Models\User;
use App\Notifications\TemporaryPasswordNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendAgentWelcomeEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agents:send-welcome
                            {email? : The email address of the agent to send the welcome email to}
                            {--all : Send welcome emails to all agents}
                            {--reset-password : Generate and send a new temporary password}
                            {--dry-run : Run without actually sending emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send welcome emails to agents';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $all = $this->option('all');
        $resetPassword = $this->option('reset-password');
        $dryRun = $this->option('dry-run');

        if (!$email && !$all) {
            $this->error('Please provide an email address or use the --all option.');
            return Command::FAILURE;
        }

        if ($email && $all) {
            $this->error('Please use either an email address or the --all option, not both.');
            return Command::FAILURE;
        }

        if ($dryRun) {
            $this->info('Running in dry-run mode. No emails will be sent.');
        }

        if ($email) {
            // Send to a specific agent
            $user = User::where('email', $email)->first();

            if (!$user) {
                $this->error("No user found with email: {$email}");
                return Command::FAILURE;
            }

            $agent = Agent::where('user_id', $user->id)->first();

            if (!$agent) {
                $this->error("User {$email} is not an agent.");
                return Command::FAILURE;
            }

            $this->sendWelcomeEmail($user, $agent, $resetPassword, $dryRun);
        } else {
            // Send to all agents
            $agents = Agent::with('user')->get();

            if ($agents->isEmpty()) {
                $this->error('No agents found in the system.');
                return Command::FAILURE;
            }

            $this->info("Found {$agents->count()} agents. Sending welcome emails...");
            $bar = $this->output->createProgressBar($agents->count());
            $bar->start();

            $successCount = 0;
            $failCount = 0;

            foreach ($agents as $agent) {
                if (!$agent->user) {
                    $this->newLine();
                    $this->warn("Agent ID {$agent->id} has no associated user. Skipping.");
                    $failCount++;
                    $bar->advance();
                    continue;
                }

                $result = $this->sendWelcomeEmail($agent->user, $agent, $resetPassword, $dryRun);

                if ($result) {
                    $successCount++;
                } else {
                    $failCount++;
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("Completed sending welcome emails:");
            $this->info("- Success: {$successCount}");
            $this->info("- Failed: {$failCount}");
        }

        return Command::SUCCESS;
    }

    /**
     * Send welcome email to an agent.
     *
     * @param User $user
     * @param Agent $agent
     * @param bool $resetPassword
     * @param bool $dryRun
     * @return bool
     */
    private function sendWelcomeEmail(User $user, Agent $agent, bool $resetPassword, bool $dryRun): bool
    {
        try {
            if ($resetPassword) {
                $tempPassword = Str::random(10);

                if (!$dryRun) {
                    $user->password = bcrypt($tempPassword);
                    $user->save();
                }

                $this->info("Sending welcome email with new temporary password to {$user->email}");

                if (!$dryRun) {
                    // Use the new mailable class for temporary password emails
                    Mail::to($user->email)->send(new AgentTemporaryPasswordMail(
                        user: $user,
                        password: $tempPassword
                    ));
                }
            } else {
                $this->info("Sending welcome email to {$user->email}");

                if (!$dryRun) {
                    Mail::to($user->email)->send(new AgentRegistrationNotification(
                        user: $user,
                        agent: $agent
                    ));
                }
            }

            if (!$dryRun) {
                Log::info("Agent welcome email sent", [
                    'agent_id' => $agent->id,
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'reset_password' => $resetPassword
                ]);
            }

            return true;
        } catch (\Exception $e) {
            $this->newLine();
            $this->error("Failed to send welcome email to {$user->email}: {$e->getMessage()}");

            if (!$dryRun) {
                Log::error("Failed to send agent welcome email", [
                    'agent_id' => $agent->id,
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            return false;
        }
    }
}
