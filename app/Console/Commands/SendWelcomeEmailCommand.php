<?php

namespace App\Console\Commands;

use App\Mail\DoctorWelcomeMail;
use App\Mail\GeneralWelcomeMail;
use App\Mail\PatientWelcomeMail;
use App\Mail\StaffWelcomeMail;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class SendWelcomeEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:send-welcome
                            {email? : The email address of the user to send the welcome email to}
                            {--role= : Send welcome emails to all users of a specific role (patient, doctor, staff, business_admin, business_hr, pharmacist)}
                            {--all : Send welcome emails to all users}
                            {--reset-password : Generate and send a new temporary password}
                            {--dry-run : Run without actually sending emails}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send welcome emails to users (patients, doctors, staff, etc.)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $role = $this->option('role');
        $all = $this->option('all');
        $resetPassword = $this->option('reset-password');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No emails will be sent and no passwords will be changed.');
        }

        // Validate role if provided
        if ($role && !in_array($role, ['patient', 'doctor', 'staff', 'business_admin', 'business_hr', 'pharmacist'])) {
            $this->error('Invalid role. Supported roles: patient, doctor, staff, business_admin, business_hr, pharmacist');
            return Command::FAILURE;
        }

        if ($email) {
            // Send to a specific user
            $user = User::where('email', $email)->first();

            if (!$user) {
                $this->error("No user found with email: {$email}");
                return Command::FAILURE;
            }

            $this->sendWelcomeEmail($user, $resetPassword, $dryRun);
        } elseif ($role) {
            // Send to all users of a specific role
            $users = User::role($role)->get();

            if ($users->isEmpty()) {
                $this->error("No users found with role: {$role}");
                return Command::FAILURE;
            }

            $this->info("Found {$users->count()} users with role: {$role}");

            if (!$dryRun && !$this->confirm("Are you sure you want to send welcome emails to {$users->count()} users?")) {
                $this->info('Operation cancelled.');
                return Command::SUCCESS;
            }

            $successCount = 0;
            $failureCount = 0;

            foreach ($users as $user) {
                if ($this->sendWelcomeEmail($user, $resetPassword, $dryRun)) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }

            $this->info("Welcome emails sent successfully: {$successCount}");
            if ($failureCount > 0) {
                $this->error("Failed to send welcome emails: {$failureCount}");
            }
        } elseif ($all) {
            // Send to all users (excluding agents as they have their own command)
            $users = User::whereDoesntHave('roles', function ($query) {
                $query->where('name', 'agent');
            })->get();

            if ($users->isEmpty()) {
                $this->error('No users found in the system.');
                return Command::FAILURE;
            }

            $this->info("Found {$users->count()} users in the system (excluding agents)");

            if (!$dryRun && !$this->confirm("Are you sure you want to send welcome emails to {$users->count()} users?")) {
                $this->info('Operation cancelled.');
                return Command::SUCCESS;
            }

            $successCount = 0;
            $failureCount = 0;

            foreach ($users as $user) {
                if ($this->sendWelcomeEmail($user, $resetPassword, $dryRun)) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }

            $this->info("Welcome emails sent successfully: {$successCount}");
            if ($failureCount > 0) {
                $this->error("Failed to send welcome emails: {$failureCount}");
            }
        } else {
            $this->error('Please provide either an email address, --role option, or --all flag.');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Send welcome email to a user.
     *
     * @param User $user
     * @param bool $resetPassword
     * @param bool $dryRun
     * @return bool
     */
    private function sendWelcomeEmail(User $user, bool $resetPassword, bool $dryRun): bool
    {
        try {
            $tempPassword = null;

            if ($resetPassword) {
                $tempPassword = Str::random(10);

                if (!$dryRun) {
                    $user->password = bcrypt($tempPassword);
                    $user->save();
                }

                $this->info("Sending welcome email with new temporary password to {$user->email}");
            } else {
                $this->info("Sending welcome email to {$user->email}");
            }

            if (!$dryRun) {
                // Get user's primary role
                $userRole = $user->getRoleNames()->first();
                
                // Send role-specific welcome email
                $mailable = $this->getWelcomeMailableForRole($user, $userRole, $tempPassword);
                
                Mail::to($user->email)->send($mailable);
            }

            $this->info("✓ Welcome email sent to {$user->email} (Role: {$user->getRoleNames()->first()})");
            return true;

        } catch (\Exception $e) {
            $this->error("✗ Failed to send welcome email to {$user->email}: " . $e->getMessage());
            Log::error('Failed to send welcome email', [
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get the appropriate welcome mailable for the user's role.
     *
     * @param User $user
     * @param string $role
     * @param string|null $tempPassword
     * @return mixed
     */
    private function getWelcomeMailableForRole(User $user, string $role, ?string $tempPassword)
    {
        switch ($role) {
            case 'doctor':
                return new DoctorWelcomeMail($user, $tempPassword);
            case 'patient':
                return new PatientWelcomeMail($user, $tempPassword);
            case 'staff':
            case 'business_admin':
            case 'business_hr':
                return new StaffWelcomeMail($user, $tempPassword);
            case 'pharmacist':
            default:
                return new GeneralWelcomeMail($user, $tempPassword);
        }
    }
}
