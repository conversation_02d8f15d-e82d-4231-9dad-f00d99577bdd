<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SetupAgentRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setup-agent-role';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the agent role and permissions for the referral system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up agent role and permissions...');

        // Create agent role if it doesn't exist
        $role = Role::firstOrCreate(['name' => 'agent']);
        $this->info('Agent role created or already exists.');

        // Create permissions
        $permissions = [
            'view_referrals',
            'view_commissions',
            'generate_referral_link',
        ];

        $createdPermissions = [];
        foreach ($permissions as $permissionName) {
            $permission = Permission::firstOrCreate(['name' => $permissionName]);
            $createdPermissions[] = $permission;
            $this->info("Permission '{$permissionName}' created or already exists.");
        }

        // Assign permissions to role
        $role->syncPermissions($createdPermissions);
        $this->info('Permissions assigned to agent role.');

        // Create admin permissions for managing agents
        $adminPermissions = [
            'manage_agents',
            'view_agent_commissions',
            'process_agent_commissions',
        ];

        $createdAdminPermissions = [];
        foreach ($adminPermissions as $permissionName) {
            $permission = Permission::firstOrCreate(['name' => $permissionName]);
            $createdAdminPermissions[] = $permission;
            $this->info("Admin permission '{$permissionName}' created or already exists.");
        }

        // Assign admin permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($createdAdminPermissions);
            $this->info('Agent management permissions assigned to admin role.');
        } else {
            $this->warn('Admin role not found. Skipping admin permission assignment.');
        }

        $this->info('Agent role and permissions setup completed successfully!');
    }
}
