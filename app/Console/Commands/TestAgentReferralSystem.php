<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class TestAgentReferralSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:agent-referral';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run all tests for the agent referral system including commission reports, email notifications, and dashboard analytics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Running agent referral system tests...');

        // Clear configuration cache
        $this->info('Clearing configuration cache...');
        $this->call('config:clear');

        // Run the test scripts
        $this->info('Running agent referral system tests...');
        $process = new \Symfony\Component\Process\Process(['./tests/scripts/run-agent-tests.sh']);

        $process->setTty(true);
        $process->run(function ($type, $buffer) {
            $this->output->write($buffer);
        });

        if ($process->isSuccessful()) {
            $this->info('All agent referral system tests completed successfully!');
        } else {
            $this->error('Some tests failed. Please check the output above for details.');
        }
    }


}
