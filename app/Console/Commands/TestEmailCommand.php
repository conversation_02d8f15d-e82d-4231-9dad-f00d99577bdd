<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test sending an email with the log driver';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing email with log driver...');

        // Log something to make sure logging works
        Log::info('This is a test log message before sending email');

        try {
            // First, let's try a simple email
            Mail::raw('Test email content', function($message) {
                $message->to('<EMAIL>');
                $message->subject('Test Email');
            });

            $this->info('Simple email sent successfully.');

            // Now, let's try sending a welcome email notification
            $user = \App\Models\User::first();
            if ($user) {
                $this->info('Sending welcome email to: ' . $user->email);
                $user->notify(new \App\Notifications\BusinessEmployeeWelcomeNotification(
                    'test-password',
                    'Test Business',
                    'Test Plan'
                ));
                $this->info('Welcome email notification sent.');
            } else {
                $this->error('No users found in the database.');
            }

            $this->info('Mail driver: ' . config('mail.default'));
            $this->info('Mail log channel: ' . config('mail.mailers.log.channel'));

            // Log something after sending email
            Log::info('This is a test log message after sending email');

            $this->info('Check your log files in storage/logs/');

        } catch (\Exception $e) {
            $this->error('Error sending email: ' . $e->getMessage());
        }
    }
}
