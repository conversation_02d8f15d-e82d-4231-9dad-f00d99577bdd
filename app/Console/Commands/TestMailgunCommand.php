<?php

namespace App\Console\Commands;

use App\Mail\TestMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestMailgunCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:test {email : The email address to send the test to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email using Mailgun';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        $this->info("Sending test email to {$email}...");

        try {
            Mail::to($email)->send(new TestMail(
                name: 'Test User',
                messageContent: 'This is a test message to verify that Mailgun is working correctly.'
            ));

            $this->info('Test email sent successfully!');
            $this->info('Please check your inbox (and spam folder) to confirm receipt.');

            $this->info('Current mail configuration:');
            $this->info('- Mail Driver: ' . config('mail.default'));
            $this->info('- From Address: ' . config('mail.from.address'));
            $this->info('- From Name: ' . config('mail.from.name'));

            if (config('mail.default') === 'mailgun') {
                $this->info('- Mailgun Domain: ' . config('services.mailgun.domain'));
                $this->info('- Mailgun Endpoint: ' . config('services.mailgun.endpoint'));
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to send test email: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());

            return Command::FAILURE;
        }
    }
}
