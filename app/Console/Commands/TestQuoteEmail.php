<?php

namespace App\Console\Commands;

use App\Mail\QuoteRequested;
use App\Models\Quote;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestQuoteEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-quote {email? : The email to send the test to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the quote request email with HTML template and PDF attachment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? config('mail.admin_email');

        $this->info('Creating test quote...');

        $quote = new Quote([
            'company_name' => 'Test Company',
            'contact_name' => 'Test Contact',
            'email' => '<EMAIL>',
            'phone' => '************',
            'num_employees' => 10,
            'industry' => 'Technology',
            'current_insurance' => 'Yes',
            'happy_with_coverage' => 'No',
            'coverage_issues' => 'Too expensive'
        ]);

        // Always use log driver for testing as requested
        $originalMailer = config('mail.default');
        config(['mail.default' => 'log']);

        $this->info('Sending test email to: ' . $email);
        $this->info('Using mail driver: log (for testing)');

        try {
            Mail::to($email)->send(new QuoteRequested($quote));
            $this->info('Email sent successfully! Check the log file at: storage/logs/laravel.log');

            // Restore original mailer
            config(['mail.default' => $originalMailer]);

            $this->info('Mail configuration restored to:');
            $this->info('- Mail Driver: ' . config('mail.default'));
            $this->info('- From Address: ' . config('mail.from.address'));
            $this->info('- From Name: ' . config('mail.from.name'));

            $this->info('');
            $this->info('Note: For production, you will need to update the .env file with valid Mailgun credentials:');
            $this->info('MAIL_MAILER=mailgun');
            $this->info('MAIL_FROM_ADDRESS="<EMAIL>"');
            $this->info('MAIL_FROM_NAME="Go MD USA Quotes"');
            $this->info('MAILGUN_DOMAIN="your-actual-mailgun-domain"');
            $this->info('MAILGUN_SECRET="your-actual-mailgun-secret-key"');
            $this->info('MAILGUN_ENDPOINT="api.mailgun.net"');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            // Restore original mailer
            config(['mail.default' => $originalMailer]);

            $this->error('Failed to send email: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
