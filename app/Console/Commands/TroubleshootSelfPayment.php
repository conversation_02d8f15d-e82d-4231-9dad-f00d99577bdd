<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\BusinessEmployee;
use App\Models\BusinessPlanSelfPayment;
use App\Services\BusinessPlanSelfPaymentService;
use Illuminate\Console\Command;

class TroubleshootSelfPayment extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'self-payment:troubleshoot {email : Employee email address} {--fix : Automatically fix common issues}';

    /**
     * The console command description.
     */
    protected $description = 'Troubleshoot self-payment eligibility issues for an employee';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $autoFix = $this->option('fix');

        $this->info("Troubleshooting self-payment eligibility for: {$email}");
        $this->newLine();

        // Find user
        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User not found with email: {$email}");
            return 1;
        }

        $this->info("✓ User found: {$user->fname} {$user->lname} (ID: {$user->id})");
        $this->line("  - Active: " . ($user->active ? 'Yes' : 'No'));

        // Find employee record
        $employee = BusinessEmployee::where('user_id', $user->id)->first();
        if (!$employee) {
            $this->error("BusinessEmployee record not found for user ID: {$user->id}");
            return 1;
        }

        $this->info("✓ BusinessEmployee found: {$employee->first_name} {$employee->last_name} (ID: {$employee->id})");
        $this->line("  - Status: {$employee->status}");
        $this->line("  - Business ID: {$employee->business_id}");
        $this->line("  - Can Make Self Payment: " . ($employee->canMakeSelfPayment() ? 'Yes' : 'No'));

        // Check business and active plan
        $business = $employee->business;
        $this->info("✓ Business: {$business->name} (ID: {$business->id})");

        $activePlan = $business->plans()
            ->where('active', true)
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>=', now());
            })
            ->first();

        if (!$activePlan) {
            $this->error("No active business plan found for business: {$business->name}");
            return 1;
        }

        $this->info("✓ Active business plan found: ID {$activePlan->id}");
        $this->line("  - Plan Quantity: {$activePlan->plan_quantity}");
        $this->line("  - Price per Plan: $" . number_format($activePlan->price_per_plan / 100, 2));
        $this->line("  - Is Active: " . ($activePlan->isActive() ? 'Yes' : 'No'));

        // Check detailed eligibility
        $this->newLine();
        $this->info("Checking detailed eligibility...");
        
        $eligibilityInfo = $activePlan->getEmployeeSelfPayEligibility($employee);
        
        if ($eligibilityInfo['eligible']) {
            $this->info("✓ Employee is eligible for self-payment!");
            return 0;
        }

        // Show specific issue
        $this->error("✗ Employee is NOT eligible for self-payment");
        $this->line("  - Reason: {$eligibilityInfo['reason']}");
        $this->line("  - Message: {$eligibilityInfo['message']}");

        if (isset($eligibilityInfo['existing_payment'])) {
            $payment = $eligibilityInfo['existing_payment'];
            $this->line("  - Existing Payment ID: {$payment['id']}");
            $this->line("  - Payment Status: {$payment['status']}");
            $this->line("  - Payment Amount: $" . number_format($payment['amount'], 2));
            $this->line("  - Created: {$payment['created_at']}");
        }

        // Auto-fix options
        if ($autoFix) {
            $this->newLine();
            $this->info("Attempting to fix issues...");

            $fixed = false;

            // Fix 1: Activate user account
            if (!$user->active) {
                $this->line("Activating user account...");
                $user->update(['active' => true]);
                $this->info("✓ User account activated");
                $fixed = true;
            }

            // Fix 2: Remove pending self-payments
            if ($eligibilityInfo['reason'] === 'existing_payment' && 
                isset($eligibilityInfo['existing_payment']) &&
                $eligibilityInfo['existing_payment']['status'] === BusinessPlanSelfPayment::STATUS_PENDING) {
                
                if ($this->confirm("Remove pending self-payment (ID: {$eligibilityInfo['existing_payment']['id']})?")) {
                    $pendingPayment = BusinessPlanSelfPayment::find($eligibilityInfo['existing_payment']['id']);
                    if ($pendingPayment) {
                        // Remove associated transaction if exists
                        if ($pendingPayment->transaction_id) {
                            $pendingPayment->transaction()->delete();
                        }
                        $pendingPayment->delete();
                        $this->info("✓ Pending self-payment removed");
                        $fixed = true;
                    }
                }
            }

            if ($fixed) {
                // Re-check eligibility
                $this->newLine();
                $this->info("Re-checking eligibility after fixes...");
                
                $selfPaymentService = app(BusinessPlanSelfPaymentService::class);
                $validationResult = $selfPaymentService->validateSelfPayment($employee->fresh());
                
                if ($validationResult['success']) {
                    $this->info("✓ Employee is now eligible for self-payment!");
                    return 0;
                } else {
                    $this->error("✗ Employee is still not eligible: {$validationResult['message']}");
                    return 1;
                }
            }
        } else {
            $this->newLine();
            $this->comment("Run with --fix to automatically resolve common issues");
        }

        return 1;
    }
}
