<?php

namespace App\Console\Commands;

use App\Models\Condition;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdateConditionSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'conditions:update-slugs
                            {--dry-run : Run without making actual changes}
                            {--force : Update all slugs, even if they already exist}
                            {--condition-id= : Update slug for a specific condition ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update slugs for conditions that do not have them';

    /**
     * Create a new command instance.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');
        $conditionId = $this->option('condition-id');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Build the query based on options
        $query = Condition::query();

        // If specific condition ID is provided
        if ($conditionId) {
            $this->info("Looking for specific condition ID: {$conditionId}");
            $query->where('id', $conditionId);
        }
        // If not forcing updates, only get conditions without slugs or with empty slugs
        elseif (!$force) {
            $query->where(function ($q) {
                $q->whereNull('slug')
                  ->orWhere('slug', '');
            });
            $this->info("Looking for conditions without slugs");
        } else {
            $this->info("Will update slugs for all conditions");
        }

        // Execute the query
        $conditions = $query->get();

        $count = $conditions->count();

        if ($count === 0) {
            $this->info('No conditions found that need slug updates.');
            return 0;
        }

        $this->info("Found {$count} conditions that need slug updates.");

        $bar = $this->output->createProgressBar($count);
        $bar->start();

        $updatedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;

        foreach ($conditions as $condition) {
            $this->newLine();
            $this->line("Processing condition #{$condition->id}: {$condition->name}");
            
            // Skip if the condition already has a slug and we're not forcing updates
            if (!$force && !empty($condition->slug)) {
                $this->line("  Already has slug: {$condition->slug}. Skipping.");
                $skippedCount++;
                $bar->advance();
                continue;
            }

            // Generate a slug from the name
            $newSlug = Str::slug($condition->name);
            
            // Check if this slug already exists for another condition
            $existingCondition = Condition::where('slug', $newSlug)
                ->where('id', '!=', $condition->id)
                ->first();
            
            if ($existingCondition) {
                // If the slug already exists, append the ID to make it unique
                $newSlug = $newSlug . '-' . $condition->id;
                $this->line("  Slug {$newSlug} already exists for condition #{$existingCondition->id}. Using {$newSlug} instead.");
            }
            
            $this->line("  Setting slug: {$newSlug}");
            
            if (!$dryRun) {
                try {
                    $condition->slug = $newSlug;
                    $condition->save();
                    $updatedCount++;
                    $this->line("  Slug updated successfully.");
                } catch (\Exception $e) {
                    $this->error("  Error updating slug: {$e->getMessage()}");
                    $errorCount++;
                }
            } else {
                $updatedCount++;
            }
            
            $bar->advance();
        }

        $bar->finish();

        $this->newLine(2);
        if (!$dryRun) {
            $this->info("Slug update completed:");
            $this->info("  Updated: {$updatedCount}");
            $this->info("  Skipped: {$skippedCount}");
            $this->info("  Errors: {$errorCount}");
        } else {
            $this->info("Dry run completed. Would have updated {$updatedCount} conditions.");
        }

        return 0;
    }
}
