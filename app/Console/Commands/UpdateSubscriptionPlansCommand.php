<?php

namespace App\Console\Commands;

use App\Models\SubscriptionPlan;
use Illuminate\Console\Command;

class UpdateSubscriptionPlansCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:update-plans {--show-free-trial=0 : Whether to show free trial in medication ordering flow}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update subscription plans to control free trial visibility';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $showFreeTrial = (bool) $this->option('show-free-trial');

        // Update all active plans
        $plans = SubscriptionPlan::where('status', 'active')
            ->whereIn('type', ['individual', 'family', 'single'])
            ->get();

        foreach ($plans as $plan) {
            $plan->show_free_trial = $showFreeTrial;
            $plan->save();

            $this->info("Updated plan: {$plan->name} (ID: {$plan->id})");
        }

        $this->info("Updated " . count($plans) . " subscription plans.");
        $this->info("Free trial is now " . ($showFreeTrial ? 'visible' : 'hidden') . " in the medication ordering flow.");

        return Command::SUCCESS;
    }
}
