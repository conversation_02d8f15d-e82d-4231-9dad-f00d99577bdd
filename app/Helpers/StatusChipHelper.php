<?php

namespace App\Helpers;

class StatusChipHelper
{
    private static $baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';

    public static function getColorClasses($type, $status)
    {
        if ($type === 'consultation') {
            switch ($status) {
                case 'scheduled': return 'bg-blue-100 text-blue-800';
                case 'in_progress': return 'bg-yellow-100 text-yellow-800';
                case 'completed': return 'bg-green-100 text-green-800';
                case 'cancelled': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        } elseif ($type === 'prescription') {
            switch ($status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'approved': return 'bg-green-100 text-green-800';
                case 'rejected': return 'bg-red-100 text-red-800';
                case 'partially_dispensed': return 'bg-blue-100 text-blue-800';
                case 'dispensed': return 'bg-purple-100 text-purple-800';
                case 'cancelled': return 'bg-gray-100 text-gray-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        } elseif ($type === 'prescription_item') {
            switch ($status) {
                case 'active': return 'bg-green-100 text-green-800';
                case 'discontinued': return 'bg-red-100 text-red-800';
                case 'as_needed': return 'bg-yellow-100 text-yellow-800';
                case 'temporary': return 'bg-blue-100 text-blue-800';
                case 'tapering': return 'bg-purple-100 text-purple-800';
                case 'on_hold': return 'bg-orange-100 text-orange-800';
                case 'scheduled': return 'bg-indigo-100 text-indigo-800';
                case 'long_term': return 'bg-teal-100 text-teal-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        } elseif ($type === 'user') {
            switch ($status) {
                case 'active': return 'bg-green-100 text-green-800';
                case 'inactive': return 'bg-red-100 text-red-800';
                case 'pending_approval': return 'bg-yellow-100 text-yellow-800';
                case 'deleted': return 'bg-gray-100 text-gray-800';
                case 'awaiting_verification': return 'bg-blue-100 text-blue-800';
                case 'payment_due': return 'bg-orange-100 text-orange-800';
                case 'on_hold': return 'bg-purple-100 text-purple-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }
        return 'bg-gray-100 text-gray-800';
    }

    public static function getDisplayText($type, $status)
    {
        $displayTexts = [
            'consultation' => [
                'scheduled' => 'Scheduled',
                'in_progress' => 'In Progress',
                'completed' => 'Completed',
                'cancelled' => 'Cancelled',
            ],
            'prescription' => [
                'pending' => 'Pending',
                'approved' => 'Approved',
                'rejected' => 'Rejected',
                'partially_dispensed' => 'Partially Dispensed',
                'dispensed' => 'Dispensed',
                'cancelled' => 'Cancelled',
            ],
            'prescription_item' => [
                'active' => 'Active',
                'discontinued' => 'Discontinued',
                'as_needed' => 'As Needed',
                'temporary' => 'Temporary',
                'tapering' => 'Tapering',
                'on_hold' => 'On Hold',
                'scheduled' => 'Scheduled',
                'long_term' => 'Long Term',
            ],
            'user' => [
                'active' => 'Active',
                'inactive' => 'Inactive',
                'pending_approval' => 'Pending Approval',
                'deleted' => 'Deleted',
                'awaiting_verification' => 'Awaiting Verification',
                'payment_due' => 'Payment Due',
                'on_hold' => 'On Hold',
            ],
        ];

        if (isset($displayTexts[$type][$status])) {
            return $displayTexts[$type][$status];
        }

        return ucfirst(str_replace('_', ' ', $status));
    }

    public static function getClasses($type, $status)
    {
        return self::$baseClasses . ' ' . self::getColorClasses($type, $status);
    }
}