<?php

namespace App\Http\Controllers\API\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Agent\Goals\CreateGoalRequest;
use App\Http\Requests\API\Agent\Reports\CreateReportRequest;
use App\Http\Requests\API\Agent\Reports\ExportReportRequest;
use App\Models\Agent;
use App\Models\AgentGoal;
use App\Models\AgentCustomReport;
use App\Models\AgentReportExport;
use App\Services\GoalsReportsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GoalsReportsController extends Controller
{
    protected $goalsReportsService;

    public function __construct(GoalsReportsService $goalsReportsService)
    {
        $this->goalsReportsService = $goalsReportsService;
    }

    /**
     * Display a listing of goals.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function goals(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $type = $request->input('type');
        $status = $request->input('status');

        $agent = $this->getAgent();
        $query = AgentGoal::where('agent_id', $agent->id);

        if ($type) {
            $query->where('type', $type);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $goals = $query->orderBy('target_date', 'asc')
                      ->paginate($perPage);

        // Get counts by status
        $activeCount = AgentGoal::where('agent_id', $agent->id)
                               ->where('status', 'active')
                               ->count();

        $achievedCount = AgentGoal::where('agent_id', $agent->id)
                                 ->where('status', 'achieved')
                                 ->count();

        $expiredCount = AgentGoal::where('agent_id', $agent->id)
                                ->where('status', 'expired')
                                ->count();

        return response()->json([
            'data' => $goals->items(),
            'meta' => [
                'current_page' => $goals->currentPage(),
                'from' => $goals->firstItem(),
                'last_page' => $goals->lastPage(),
                'path' => $request->url(),
                'per_page' => $goals->perPage(),
                'to' => $goals->lastItem(),
                'total' => $goals->total(),
                'active_count' => $activeCount,
                'achieved_count' => $achievedCount,
                'expired_count' => $expiredCount
            ]
        ]);
    }

    /**
     * Display the specified goal.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showGoal(string $id)
    {
        $agent = $this->getAgent();
        $goal = AgentGoal::where('agent_id', $agent->id)
                        ->findOrFail($id);

        // Get progress history
        $progressHistory = $this->goalsReportsService->getGoalProgressHistory($goal->id);
        $goal->progress_history = $progressHistory;

        return response()->json([
            'data' => $goal
        ]);
    }

    /**
     * Store a newly created goal.
     *
     * @param  \App\Http\Requests\API\Agent\Goals\CreateGoalRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeGoal(CreateGoalRequest $request)
    {
        $agent = $this->getAgent();

        // Create the goal
        $goal = AgentGoal::create([
            'agent_id' => $agent->id,
            'type' => $request->input('type'),
            'target_value' => $request->input('target_value'),
            'target_date' => $request->input('target_date'),
            'description' => $request->input('description'),
            'status' => 'active',
            'progress_value' => 0,
            'progress_percentage' => 0
        ]);

        return response()->json([
            'message' => 'Goal created successfully',
            'data' => $goal
        ], 201);
    }

    /**
     * Update the specified goal.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateGoal(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'target_value' => 'numeric|min:1',
            'target_date' => 'date|after:today',
            'description' => 'string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();
        $goal = AgentGoal::where('agent_id', $agent->id)
                        ->findOrFail($id);

        // Check if the goal is active
        if ($goal->status !== 'active') {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'Only active goals can be updated'
            ], 422);
        }

        // Update the goal
        if ($request->has('target_value')) {
            $goal->target_value = $request->input('target_value');
            // Recalculate progress percentage
            $goal->progress_percentage = $goal->target_value > 0
                ? round(($goal->progress_value / $goal->target_value) * 100)
                : 0;
        }

        if ($request->has('target_date')) {
            $goal->target_date = $request->input('target_date');
        }

        if ($request->has('description')) {
            $goal->description = $request->input('description');
        }

        $goal->save();

        return response()->json([
            'message' => 'Goal updated successfully',
            'data' => $goal
        ]);
    }

    /**
     * Remove the specified goal.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyGoal(string $id)
    {
        $agent = $this->getAgent();
        $goal = AgentGoal::where('agent_id', $agent->id)
                        ->findOrFail($id);

        $goal->delete();

        return response()->json([
            'message' => 'Goal deleted successfully'
        ]);
    }

    /**
     * Display a listing of custom reports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function reports(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $type = $request->input('type');

        $agent = $this->getAgent();
        $query = AgentCustomReport::where('agent_id', $agent->id);

        if ($type) {
            $query->where('type', $type);
        }

        $reports = $query->orderBy('created_at', 'desc')
                        ->paginate($perPage);

        return response()->json([
            'data' => $reports->items(),
            'meta' => [
                'current_page' => $reports->currentPage(),
                'from' => $reports->firstItem(),
                'last_page' => $reports->lastPage(),
                'path' => $request->url(),
                'per_page' => $reports->perPage(),
                'to' => $reports->lastItem(),
                'total' => $reports->total()
            ]
        ]);
    }

    /**
     * Display the specified custom report.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showReport(string $id)
    {
        $agent = $this->getAgent();
        $report = AgentCustomReport::with('exports')
                                  ->where('agent_id', $agent->id)
                                  ->findOrFail($id);

        return response()->json([
            'data' => $report
        ]);
    }

    /**
     * Store a newly created custom report.
     *
     * @param  \App\Http\Requests\API\Agent\Reports\CreateReportRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeReport(CreateReportRequest $request)
    {
        $agent = $this->getAgent();

        // Create the report
        $report = AgentCustomReport::create([
            'agent_id' => $agent->id,
            'name' => $request->input('name'),
            'type' => $request->input('type'),
            'columns' => json_encode($request->input('columns')),
            'filters' => json_encode($request->input('filters')),
            'sorting' => json_encode($request->input('sorting')),
            'is_scheduled' => $request->input('is_scheduled', false),
            'schedule_frequency' => $request->input('schedule_frequency'),
            'schedule_time' => $request->input('schedule_time')
        ]);

        return response()->json([
            'message' => 'Report created successfully',
            'data' => $report
        ], 201);
    }

    /**
     * Update the specified custom report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateReport(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'columns' => 'array',
            'filters' => 'array',
            'sorting' => 'array',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,true|string|in:daily,weekly,monthly,quarterly',
            'schedule_time' => 'required_if:is_scheduled,true|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();
        $report = AgentCustomReport::where('agent_id', $agent->id)
                                  ->findOrFail($id);

        // Update the report
        if ($request->has('name')) {
            $report->name = $request->input('name');
        }

        if ($request->has('columns')) {
            $report->columns = json_encode($request->input('columns'));
        }

        if ($request->has('filters')) {
            $report->filters = json_encode($request->input('filters'));
        }

        if ($request->has('sorting')) {
            $report->sorting = json_encode($request->input('sorting'));
        }

        if ($request->has('is_scheduled')) {
            $report->is_scheduled = $request->input('is_scheduled');
        }

        if ($request->has('schedule_frequency')) {
            $report->schedule_frequency = $request->input('schedule_frequency');
        }

        if ($request->has('schedule_time')) {
            $report->schedule_time = $request->input('schedule_time');
        }

        $report->save();

        return response()->json([
            'message' => 'Report updated successfully',
            'data' => $report
        ]);
    }

    /**
     * Remove the specified custom report.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyReport(string $id)
    {
        $agent = $this->getAgent();
        $report = AgentCustomReport::where('agent_id', $agent->id)
                                  ->findOrFail($id);

        // Delete associated exports
        AgentReportExport::where('report_id', $report->id)->delete();

        $report->delete();

        return response()->json([
            'message' => 'Report deleted successfully'
        ]);
    }

    /**
     * Generate a report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateReport(Request $request, string $id)
    {
        $agent = $this->getAgent();
        $report = AgentCustomReport::where('agent_id', $agent->id)
                                  ->findOrFail($id);

        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        // Generate the report
        $results = $this->goalsReportsService->generateReport(
            $agent->id,
            $report,
            $dateFrom,
            $dateTo
        );

        // Update last generated timestamp
        $report->last_generated_at = now();
        $report->save();

        return response()->json([
            'data' => $results
        ]);
    }

    /**
     * Export a report.
     *
     * @param  \App\Http\Requests\API\Agent\Reports\ExportReportRequest  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportReport(ExportReportRequest $request, string $id)
    {
        $agent = $this->getAgent();
        $report = AgentCustomReport::where('agent_id', $agent->id)
                                  ->findOrFail($id);

        $format = $request->input('format');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        // Generate the export
        $export = $this->goalsReportsService->exportReport(
            $agent->id,
            $report,
            $format,
            $dateFrom,
            $dateTo
        );

        return response()->json([
            'message' => 'Report exported successfully',
            'data' => $export
        ]);
    }

    /**
     * Download a report export.
     *
     * @param  string  $exportId
     * @return \Illuminate\Http\Response
     */
    public function downloadExport(string $exportId)
    {
        $agent = $this->getAgent();
        $export = AgentReportExport::with('report')
                                  ->findOrFail($exportId);

        // Check if the export belongs to the agent
        if ($export->report->agent_id !== $agent->id) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'You do not have permission to download this export'
            ], 403);
        }

        // Check if the export has expired
        if ($export->expires_at && $export->expires_at < now()) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested export has expired'
            ], 404);
        }

        // Get the file
        $path = $export->file_path;
        $filename = basename($path);

        if (!Storage::exists($path)) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested file is not available'
            ], 404);
        }

        return Storage::download($path, $filename);
    }

    /**
     * Get performance metrics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function performance(Request $request)
    {
        $period = $request->input('period', 'month');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $agent = $this->getAgent();

        $performance = $this->goalsReportsService->getPerformanceMetrics(
            $agent->id,
            $period,
            $startDate,
            $endDate
        );

        return response()->json([
            'data' => $performance
        ]);
    }

    /**
     * Get the authenticated agent.
     *
     * @return \App\Models\Agent
     */
    protected function getAgent()
    {
        $user = Auth::user();
        return Agent::where('user_id', $user->id)->firstOrFail();
    }
}
