<?php

namespace App\Http\Controllers\API\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentMarketingMaterial;
use App\Models\AgentMarketingUsage;
use App\Models\AgentLandingPage;
use App\Models\AgentLandingPageVisit;
use App\Models\AgentLandingPageConversion;
use App\Services\MarketingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class MarketingController extends Controller
{
    protected $marketingService;

    public function __construct(MarketingService $marketingService)
    {
        $this->marketingService = $marketingService;
    }

    /**
     * Display a listing of marketing materials.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function materials(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $type = $request->input('type');
        $search = $request->input('search');

        $query = AgentMarketingMaterial::query()->where('is_active', true);

        if ($type) {
            $query->where('type', $type);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $materials = $query->orderBy('created_at', 'desc')
                          ->paginate($perPage);

        return response()->json([
            'data' => $materials->items(),
            'meta' => [
                'current_page' => $materials->currentPage(),
                'from' => $materials->firstItem(),
                'last_page' => $materials->lastPage(),
                'path' => $request->url(),
                'per_page' => $materials->perPage(),
                'to' => $materials->lastItem(),
                'total' => $materials->total()
            ]
        ]);
    }

    /**
     * Display the specified marketing material.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showMaterial(string $id)
    {
        $material = AgentMarketingMaterial::findOrFail($id);

        if (!$material->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested marketing material is not available'
            ], 404);
        }

        $agent = $this->getAgent();

        // Record a view
        AgentMarketingUsage::create([
            'agent_id' => $agent->id,
            'marketing_material_id' => $material->id,
            'usage_type' => 'view'
        ]);

        // Personalize the content if it's a template
        if ($material->template_content) {
            $material->personalized_content = $this->marketingService->personalizeContent(
                $material->template_content,
                $agent
            );
        }

        // Get usage statistics
        $material->usage_count = AgentMarketingUsage::where('agent_id', $agent->id)
                                                   ->where('marketing_material_id', $material->id)
                                                   ->count();

        $material->last_used_at = AgentMarketingUsage::where('agent_id', $agent->id)
                                                    ->where('marketing_material_id', $material->id)
                                                    ->latest()
                                                    ->value('created_at');

        return response()->json([
            'data' => $material
        ]);
    }

    /**
     * Download a marketing material.
     *
     * @param  string  $id
     * @return \Illuminate\Http\Response
     */
    public function downloadMaterial(string $id)
    {
        $material = AgentMarketingMaterial::findOrFail($id);

        if (!$material->is_active || !$material->file_path) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested file is not available'
            ], 404);
        }

        $agent = $this->getAgent();

        // Record a download
        AgentMarketingUsage::create([
            'agent_id' => $agent->id,
            'marketing_material_id' => $material->id,
            'usage_type' => 'download'
        ]);

        // Get the file
        $path = $material->file_path;
        $filename = basename($path);

        if (!Storage::exists($path)) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested file is not available'
            ], 404);
        }

        return Storage::download($path, $filename);
    }

    /**
     * Share a marketing material.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function shareMaterial(Request $request, string $id)
    {
        $material = AgentMarketingMaterial::findOrFail($id);

        if (!$material->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested marketing material is not available'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|in:facebook,twitter,linkedin,email,other'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();
        $platform = $request->input('platform');

        // Record a share
        $usage = AgentMarketingUsage::create([
            'agent_id' => $agent->id,
            'marketing_material_id' => $material->id,
            'usage_type' => 'share_' . $platform
        ]);

        return response()->json([
            'message' => 'Marketing material shared successfully',
            'data' => $usage
        ]);
    }

    /**
     * Display a listing of landing pages.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function landingPages(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $status = $request->input('status');

        $agent = $this->getAgent();
        $query = AgentLandingPage::where('agent_id', $agent->id);

        if ($status) {
            $query->where('is_active', $status === 'active');
        }

        $landingPages = $query->orderBy('created_at', 'desc')
                             ->paginate($perPage);

        // Add visit and conversion counts
        $landingPages->getCollection()->transform(function ($page) {
            $page->visit_count = $page->visits()->count();
            $page->conversion_count = $page->conversions()->count();
            $page->conversion_rate = $page->visit_count > 0
                ? round(($page->conversion_count / $page->visit_count) * 100, 1)
                : 0;

            return $page;
        });

        return response()->json([
            'data' => $landingPages->items(),
            'meta' => [
                'current_page' => $landingPages->currentPage(),
                'from' => $landingPages->firstItem(),
                'last_page' => $landingPages->lastPage(),
                'path' => $request->url(),
                'per_page' => $landingPages->perPage(),
                'to' => $landingPages->lastItem(),
                'total' => $landingPages->total()
            ]
        ]);
    }

    /**
     * Display the specified landing page.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showLandingPage(string $id)
    {
        $agent = $this->getAgent();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
                                      ->findOrFail($id);

        // Add visit and conversion counts
        $landingPage->visit_count = $landingPage->visits()->count();
        $landingPage->conversion_count = $landingPage->conversions()->count();
        $landingPage->conversion_rate = $landingPage->visit_count > 0
            ? round(($landingPage->conversion_count / $landingPage->visit_count) * 100, 1)
            : 0;

        // Add full URL
        $landingPage->full_url = url('/agent/' . $landingPage->slug);

        // Get recent visits
        $landingPage->recent_visits = $landingPage->visits()
                                                 ->latest()
                                                 ->limit(5)
                                                 ->get();

        // Get recent conversions
        $landingPage->recent_conversions = $landingPage->conversions()
                                                      ->latest()
                                                      ->limit(5)
                                                      ->get();

        return response()->json([
            'data' => $landingPage
        ]);
    }

    /**
     * Store a newly created landing page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeLandingPage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'template' => 'required|string|in:basic,professional,modern',
            'content' => 'required|string',
            'settings' => 'required|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();

        // Generate a unique slug
        $slug = Str::slug($request->input('title'));
        $uniqueSlug = $slug;
        $counter = 1;

        while (AgentLandingPage::where('slug', $uniqueSlug)->exists()) {
            $uniqueSlug = $slug . '-' . $counter;
            $counter++;
        }

        // Create the landing page
        $landingPage = AgentLandingPage::create([
            'agent_id' => $agent->id,
            'title' => $request->input('title'),
            'slug' => $uniqueSlug,
            'template' => $request->input('template'),
            'content' => $request->input('content'),
            'settings' => json_encode($request->input('settings')),
            'is_active' => $request->input('is_active', true)
        ]);

        // Add full URL
        $landingPage->full_url = url('/agent/' . $landingPage->slug);

        return response()->json([
            'message' => 'Landing page created successfully',
            'data' => $landingPage
        ], 201);
    }

    /**
     * Update the specified landing page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLandingPage(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'string|max:255',
            'content' => 'string',
            'settings' => 'array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
                                      ->findOrFail($id);

        // Update the landing page
        if ($request->has('title')) {
            $landingPage->title = $request->input('title');
        }

        if ($request->has('content')) {
            $landingPage->content = $request->input('content');
        }

        if ($request->has('settings')) {
            $landingPage->settings = json_encode($request->input('settings'));
        }

        if ($request->has('is_active')) {
            $landingPage->is_active = $request->input('is_active');
        }

        $landingPage->save();

        // Add full URL
        $landingPage->full_url = url('/agent/' . $landingPage->slug);

        return response()->json([
            'message' => 'Landing page updated successfully',
            'data' => $landingPage
        ]);
    }

    /**
     * Remove the specified landing page.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyLandingPage(string $id)
    {
        $agent = $this->getAgent();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
                                      ->findOrFail($id);

        $landingPage->delete();

        return response()->json([
            'message' => 'Landing page deleted successfully'
        ]);
    }

    /**
     * Get marketing performance metrics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function performance(Request $request)
    {
        $period = $request->input('period', 'month');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $agent = $this->getAgent();

        $performance = $this->marketingService->getPerformanceMetrics(
            $agent->id,
            $period,
            $startDate,
            $endDate
        );

        return response()->json([
            'data' => $performance
        ]);
    }

    /**
     * Get the authenticated agent.
     *
     * @return \App\Models\Agent
     */
    protected function getAgent()
    {
        $user = Auth::user();
        return Agent::where('user_id', $user->id)->firstOrFail();
    }
}
