<?php

namespace App\Http\Controllers\API\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentMessage;
use App\Models\AgentAnnouncement;
use App\Models\AgentAnnouncementRead;
use App\Models\AgentSupportTicket;
use App\Models\AgentSupportTicketReply;
use App\Models\User;
use App\Services\MessagingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MessagingController extends Controller
{
    protected $messagingService;

    public function __construct(MessagingService $messagingService)
    {
        $this->messagingService = $messagingService;
    }

    /**
     * Display a listing of messages (inbox).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function inbox(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');
        $isRead = $request->input('is_read');

        $user = Auth::user();
        $query = AgentMessage::with('sender')
                            ->where('recipient_id', $user->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        if ($isRead !== null) {
            $query->where('is_read', $isRead === 'true' || $isRead === '1');
        }

        $messages = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage);

        // Transform the messages to include only necessary data
        $messages->getCollection()->transform(function ($message) {
            return [
                'id' => $message->id,
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'role' => $message->sender->roles->first() ? $message->sender->roles->first()->name : 'user'
                ],
                'subject' => $message->subject,
                'message_preview' => Str::limit(strip_tags($message->message), 100),
                'is_read' => (bool) $message->is_read,
                'created_at' => $message->created_at,
                'updated_at' => $message->updated_at
            ];
        });

        // Count unread messages
        $unreadCount = AgentMessage::where('recipient_id', $user->id)
                                  ->where('is_read', false)
                                  ->count();

        return response()->json([
            'data' => $messages->items(),
            'meta' => [
                'current_page' => $messages->currentPage(),
                'from' => $messages->firstItem(),
                'last_page' => $messages->lastPage(),
                'path' => $request->url(),
                'per_page' => $messages->perPage(),
                'to' => $messages->lastItem(),
                'total' => $messages->total(),
                'unread_count' => $unreadCount
            ]
        ]);
    }

    /**
     * Display a listing of sent messages.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sent(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');

        $user = Auth::user();
        $query = AgentMessage::with('recipient')
                            ->where('sender_id', $user->id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $messages = $query->orderBy('created_at', 'desc')
                         ->paginate($perPage);

        // Transform the messages to include only necessary data
        $messages->getCollection()->transform(function ($message) {
            return [
                'id' => $message->id,
                'recipient' => [
                    'id' => $message->recipient->id,
                    'name' => $message->recipient->name,
                    'role' => $message->recipient->roles->first() ? $message->recipient->roles->first()->name : 'user'
                ],
                'subject' => $message->subject,
                'message_preview' => Str::limit(strip_tags($message->message), 100),
                'created_at' => $message->created_at,
                'updated_at' => $message->updated_at
            ];
        });

        return response()->json([
            'data' => $messages->items(),
            'meta' => [
                'current_page' => $messages->currentPage(),
                'from' => $messages->firstItem(),
                'last_page' => $messages->lastPage(),
                'path' => $request->url(),
                'per_page' => $messages->perPage(),
                'to' => $messages->lastItem(),
                'total' => $messages->total()
            ]
        ]);
    }

    /**
     * Display the specified message.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showMessage(string $id)
    {
        $user = Auth::user();
        $message = AgentMessage::with(['sender', 'recipient'])
                              ->where(function ($query) use ($user) {
                                  $query->where('sender_id', $user->id)
                                        ->orWhere('recipient_id', $user->id);
                              })
                              ->findOrFail($id);

        // Mark as read if the user is the recipient and the message is unread
        if ($message->recipient_id === $user->id && !$message->is_read) {
            $message->is_read = true;
            $message->save();
        }

        // Transform the message to include sender and recipient data
        $messageData = [
            'id' => $message->id,
            'sender' => [
                'id' => $message->sender->id,
                'name' => $message->sender->name,
                'role' => $message->sender->roles->first() ? $message->sender->roles->first()->name : 'user'
            ],
            'recipient' => [
                'id' => $message->recipient->id,
                'name' => $message->recipient->name,
                'role' => $message->recipient->roles->first() ? $message->recipient->roles->first()->name : 'user'
            ],
            'subject' => $message->subject,
            'message' => $message->message,
            'is_read' => (bool) $message->is_read,
            'created_at' => $message->created_at,
            'updated_at' => $message->updated_at
        ];

        return response()->json([
            'data' => $messageData
        ]);
    }

    /**
     * Send a new message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recipient_id' => 'required|exists:users,id',
            'subject' => 'required|string|max:255',
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Create the message
        $message = AgentMessage::create([
            'sender_id' => $user->id,
            'recipient_id' => $request->input('recipient_id'),
            'subject' => $request->input('subject'),
            'message' => $request->input('message'),
            'is_read' => false
        ]);

        return response()->json([
            'message' => 'Message sent successfully',
            'data' => $message
        ], 201);
    }

    /**
     * Reply to a message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function replyToMessage(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $originalMessage = AgentMessage::findOrFail($id);

        // Ensure the user is the sender or recipient of the original message
        if ($originalMessage->sender_id !== $user->id && $originalMessage->recipient_id !== $user->id) {
            return response()->json([
                'error' => 'Forbidden',
                'message' => 'You do not have permission to reply to this message'
            ], 403);
        }

        // Determine the recipient (the other party in the conversation)
        $recipientId = $originalMessage->sender_id === $user->id
            ? $originalMessage->recipient_id
            : $originalMessage->sender_id;

        // Create the reply
        $reply = AgentMessage::create([
            'sender_id' => $user->id,
            'recipient_id' => $recipientId,
            'subject' => 'Re: ' . $originalMessage->subject,
            'message' => $request->input('message'),
            'is_read' => false
        ]);

        return response()->json([
            'message' => 'Reply sent successfully',
            'data' => $reply
        ], 201);
    }

    /**
     * Mark a message as read.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(string $id)
    {
        $user = Auth::user();
        $message = AgentMessage::where('recipient_id', $user->id)
                              ->findOrFail($id);

        $message->is_read = true;
        $message->save();

        return response()->json([
            'message' => 'Message marked as read',
            'data' => [
                'id' => $message->id,
                'is_read' => true,
                'updated_at' => $message->updated_at
            ]
        ]);
    }

    /**
     * Mark all messages as read.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        $count = AgentMessage::where('recipient_id', $user->id)
                            ->where('is_read', false)
                            ->update(['is_read' => true]);

        return response()->json([
            'message' => 'All messages marked as read',
            'data' => [
                'count' => $count
            ]
        ]);
    }

    /**
     * Delete a message.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMessage(string $id)
    {
        $user = Auth::user();
        $message = AgentMessage::where(function ($query) use ($user) {
                                $query->where('sender_id', $user->id)
                                      ->orWhere('recipient_id', $user->id);
                            })
                            ->findOrFail($id);

        $message->delete();

        return response()->json([
            'message' => 'Message deleted successfully'
        ]);
    }

    /**
     * Display a listing of announcements.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function announcements(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $isRead = $request->input('is_read');

        $agent = $this->getAgent();

        // Get active announcements
        $query = AgentAnnouncement::where('is_active', true)
                                 ->where('publish_at', '<=', now())
                                 ->where(function ($q) {
                                     $q->whereNull('expires_at')
                                       ->orWhere('expires_at', '>=', now());
                                 });

        // Get read announcements for this agent
        $readAnnouncementIds = AgentAnnouncementRead::where('agent_id', $agent->id)
                                                   ->pluck('announcement_id')
                                                   ->toArray();

        if ($isRead !== null) {
            $isReadBool = $isRead === 'true' || $isRead === '1';

            if ($isReadBool) {
                $query->whereIn('id', $readAnnouncementIds);
            } else {
                $query->whereNotIn('id', $readAnnouncementIds);
            }
        }

        $announcements = $query->orderBy('priority', 'desc')
                              ->orderBy('publish_at', 'desc')
                              ->paginate($perPage);

        // Transform the announcements to include read status
        $announcements->getCollection()->transform(function ($announcement) use ($readAnnouncementIds) {
            return [
                'id' => $announcement->id,
                'title' => $announcement->title,
                'content_preview' => Str::limit(strip_tags($announcement->content), 100),
                'priority' => $announcement->priority,
                'is_read' => in_array($announcement->id, $readAnnouncementIds),
                'publish_at' => $announcement->publish_at,
                'expires_at' => $announcement->expires_at,
                'created_at' => $announcement->created_at,
                'updated_at' => $announcement->updated_at
            ];
        });

        // Count unread announcements
        $unreadCount = AgentAnnouncement::where('is_active', true)
                                       ->where('publish_at', '<=', now())
                                       ->where(function ($q) {
                                           $q->whereNull('expires_at')
                                             ->orWhere('expires_at', '>=', now());
                                       })
                                       ->whereNotIn('id', $readAnnouncementIds)
                                       ->count();

        return response()->json([
            'data' => $announcements->items(),
            'meta' => [
                'current_page' => $announcements->currentPage(),
                'from' => $announcements->firstItem(),
                'last_page' => $announcements->lastPage(),
                'path' => $request->url(),
                'per_page' => $announcements->perPage(),
                'to' => $announcements->lastItem(),
                'total' => $announcements->total(),
                'unread_count' => $unreadCount
            ]
        ]);
    }

    /**
     * Display the specified announcement.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showAnnouncement(string $id)
    {
        $announcement = AgentAnnouncement::with('attachments')
                                        ->where('is_active', true)
                                        ->findOrFail($id);

        // Check if the announcement is published and not expired
        if ($announcement->publish_at > now() ||
            ($announcement->expires_at && $announcement->expires_at < now())) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested announcement is not available'
            ], 404);
        }

        $agent = $this->getAgent();

        // Check if the announcement has been read
        $read = AgentAnnouncementRead::where('agent_id', $agent->id)
                                    ->where('announcement_id', $announcement->id)
                                    ->first();

        // Mark as read if not already read
        if (!$read) {
            $read = AgentAnnouncementRead::create([
                'agent_id' => $agent->id,
                'announcement_id' => $announcement->id,
                'read_at' => now()
            ]);
        }

        return response()->json([
            'data' => [
                'id' => $announcement->id,
                'title' => $announcement->title,
                'content' => $announcement->content,
                'priority' => $announcement->priority,
                'is_read' => true,
                'read_at' => $read->read_at,
                'publish_at' => $announcement->publish_at,
                'expires_at' => $announcement->expires_at,
                'attachments' => $announcement->attachments,
                'created_at' => $announcement->created_at,
                'updated_at' => $announcement->updated_at
            ]
        ]);
    }

    /**
     * Mark an announcement as read.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAnnouncementAsRead(string $id)
    {
        $announcement = AgentAnnouncement::where('is_active', true)->findOrFail($id);
        $agent = $this->getAgent();

        $read = AgentAnnouncementRead::updateOrCreate(
            [
                'agent_id' => $agent->id,
                'announcement_id' => $announcement->id
            ],
            [
                'read_at' => now()
            ]
        );

        return response()->json([
            'message' => 'Announcement marked as read',
            'data' => [
                'id' => $announcement->id,
                'is_read' => true,
                'read_at' => $read->read_at
            ]
        ]);
    }

    /**
     * Display a listing of support tickets.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function supportTickets(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $status = $request->input('status');
        $priority = $request->input('priority');

        $agent = $this->getAgent();
        $query = AgentSupportTicket::where('agent_id', $agent->id);

        if ($status) {
            $query->where('status', $status);
        }

        if ($priority) {
            $query->where('priority', $priority);
        }

        $tickets = $query->orderBy('created_at', 'desc')
                        ->paginate($perPage);

        // Transform the tickets to include reply count and preview
        $tickets->getCollection()->transform(function ($ticket) {
            return [
                'id' => $ticket->id,
                'subject' => $ticket->subject,
                'description_preview' => Str::limit(strip_tags($ticket->description), 100),
                'status' => $ticket->status,
                'priority' => $ticket->priority,
                'category' => $ticket->category,
                'reply_count' => $ticket->replies()->count(),
                'created_at' => $ticket->created_at,
                'updated_at' => $ticket->updated_at,
                'resolved_at' => $ticket->resolved_at
            ];
        });

        // Count open and closed tickets
        $openCount = AgentSupportTicket::where('agent_id', $agent->id)
                                      ->where('status', 'open')
                                      ->count();

        $closedCount = AgentSupportTicket::where('agent_id', $agent->id)
                                        ->where('status', 'closed')
                                        ->count();

        return response()->json([
            'data' => $tickets->items(),
            'meta' => [
                'current_page' => $tickets->currentPage(),
                'from' => $tickets->firstItem(),
                'last_page' => $tickets->lastPage(),
                'path' => $request->url(),
                'per_page' => $tickets->perPage(),
                'to' => $tickets->lastItem(),
                'total' => $tickets->total(),
                'open_count' => $openCount,
                'closed_count' => $closedCount
            ]
        ]);
    }

    /**
     * Display the specified support ticket.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showSupportTicket(string $id)
    {
        $agent = $this->getAgent();
        $ticket = AgentSupportTicket::with(['replies.user'])
                                   ->where('agent_id', $agent->id)
                                   ->findOrFail($id);

        // Transform the replies to include user data
        $replies = $ticket->replies->map(function ($reply) {
            return [
                'id' => $reply->id,
                'user' => [
                    'id' => $reply->user->id,
                    'name' => $reply->user->name,
                    'role' => $reply->user->roles->first() ? $reply->user->roles->first()->name : 'user'
                ],
                'message' => $reply->message,
                'is_internal' => (bool) $reply->is_internal,
                'created_at' => $reply->created_at
            ];
        });

        return response()->json([
            'data' => [
                'id' => $ticket->id,
                'subject' => $ticket->subject,
                'description' => $ticket->description,
                'status' => $ticket->status,
                'priority' => $ticket->priority,
                'category' => $ticket->category,
                'resolved_at' => $ticket->resolved_at,
                'created_at' => $ticket->created_at,
                'updated_at' => $ticket->updated_at,
                'replies' => $replies
            ]
        ]);
    }

    /**
     * Create a new support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createSupportTicket(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|string|in:low,medium,high',
            'category' => 'required|string|in:technical,billing,account,other'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();

        // Create the ticket
        $ticket = AgentSupportTicket::create([
            'agent_id' => $agent->id,
            'subject' => $request->input('subject'),
            'description' => $request->input('description'),
            'status' => 'open',
            'priority' => $request->input('priority'),
            'category' => $request->input('category')
        ]);

        return response()->json([
            'message' => 'Support ticket created successfully',
            'data' => $ticket
        ], 201);
    }

    /**
     * Reply to a support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function replySupportTicket(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The given data was invalid',
                'errors' => $validator->errors()
            ], 422);
        }

        $agent = $this->getAgent();
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
                                   ->findOrFail($id);

        // Check if the ticket is open
        if ($ticket->status !== 'open') {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'Cannot reply to a closed ticket'
            ], 422);
        }

        $user = Auth::user();

        // Create the reply
        $reply = AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'message' => $request->input('message'),
            'is_internal' => false
        ]);

        // Update the ticket's updated_at timestamp
        $ticket->touch();

        return response()->json([
            'message' => 'Reply added successfully',
            'data' => [
                'id' => $reply->id,
                'ticket_id' => $ticket->id,
                'user_id' => $user->id,
                'message' => $reply->message,
                'is_internal' => false,
                'created_at' => $reply->created_at,
                'updated_at' => $reply->updated_at
            ]
        ], 201);
    }

    /**
     * Close a support ticket.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function closeSupportTicket(string $id)
    {
        $agent = $this->getAgent();
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
                                   ->findOrFail($id);

        // Check if the ticket is already closed
        if ($ticket->status === 'closed') {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The ticket is already closed'
            ], 422);
        }

        // Update the ticket
        $ticket->status = 'closed';
        $ticket->resolved_at = now();
        $ticket->save();

        $user = Auth::user();

        // Add a system reply
        AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'message' => 'This ticket has been closed by the agent.',
            'is_internal' => false
        ]);

        return response()->json([
            'message' => 'Support ticket closed successfully',
            'data' => [
                'id' => $ticket->id,
                'status' => 'closed',
                'resolved_at' => $ticket->resolved_at,
                'updated_at' => $ticket->updated_at
            ]
        ]);
    }

    /**
     * Reopen a support ticket.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reopenSupportTicket(string $id)
    {
        $agent = $this->getAgent();
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
                                   ->findOrFail($id);

        // Check if the ticket is already open
        if ($ticket->status === 'open') {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The ticket is already open'
            ], 422);
        }

        // Update the ticket
        $ticket->status = 'open';
        $ticket->resolved_at = null;
        $ticket->save();

        $user = Auth::user();

        // Add a system reply
        AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'message' => 'This ticket has been reopened by the agent.',
            'is_internal' => false
        ]);

        return response()->json([
            'message' => 'Support ticket reopened successfully',
            'data' => [
                'id' => $ticket->id,
                'status' => 'open',
                'resolved_at' => null,
                'updated_at' => $ticket->updated_at
            ]
        ]);
    }

    /**
     * Get the authenticated agent.
     *
     * @return \App\Models\Agent
     */
    protected function getAgent()
    {
        $user = Auth::user();
        return Agent::where('user_id', $user->id)->firstOrFail();
    }
}
