<?php

namespace App\Http\Controllers\API\Agent;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Agent\Training\CompleteTrainingRequest;
use App\Http\Requests\API\Agent\Training\SubmitQuizRequest;
use App\Models\Agent;
use App\Models\AgentTrainingMaterial;
use App\Models\AgentTrainingProgress;
use App\Models\AgentCertification;
use App\Services\TrainingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TrainingController extends Controller
{
    protected $trainingService;

    public function __construct(TrainingService $trainingService)
    {
        $this->trainingService = $trainingService;
    }

    /**
     * Display a listing of training materials.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $category = $request->input('category');
        $search = $request->input('search');

        $query = AgentTrainingMaterial::query()->where('is_active', true);

        if ($category) {
            $query->where('type', $category);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $materials = $query->orderBy('order')
                          ->paginate($perPage);

        // Get the agent's progress for these materials
        $agent = $this->getAgent();
        $progressMap = $this->trainingService->getProgressMap($agent->id);

        $materials->getCollection()->transform(function ($material) use ($progressMap) {
            $material->completed = isset($progressMap[$material->id]) && $progressMap[$material->id]['completed'];
            return $material;
        });

        return response()->json([
            'data' => $materials->items(),
            'meta' => [
                'current_page' => $materials->currentPage(),
                'from' => $materials->firstItem(),
                'last_page' => $materials->lastPage(),
                'path' => $request->url(),
                'per_page' => $materials->perPage(),
                'to' => $materials->lastItem(),
                'total' => $materials->total()
            ]
        ]);
    }

    /**
     * Display the specified training material.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $material = AgentTrainingMaterial::findOrFail($id);

        if (!$material->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested training material is not available'
            ], 404);
        }

        // Get the agent's progress for this material
        $agent = $this->getAgent();
        $progress = AgentTrainingProgress::where('agent_id', $agent->id)
                                        ->where('training_material_id', $material->id)
                                        ->first();

        $material->completed = $progress ? $progress->completed : false;
        $material->completion_percentage = $progress ? ($progress->completed ? 100 : 0) : 0;

        // If the material has a quiz, include the questions (but not the answers)
        if ($material->has_quiz && $material->quiz_questions) {
            $quizQuestions = json_decode($material->quiz_questions, true);

            // Remove the correct_answer field from each question
            foreach ($quizQuestions as &$question) {
                unset($question['correct_answer']);
            }

            $material->quiz_questions = $quizQuestions;
        }

        return response()->json([
            'data' => $material
        ]);
    }

    /**
     * Mark a training material as completed.
     *
     * @param  \App\Http\Requests\API\Agent\Training\CompleteTrainingRequest  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function complete(CompleteTrainingRequest $request, string $id)
    {
        $material = AgentTrainingMaterial::findOrFail($id);

        if (!$material->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested training material is not available'
            ], 404);
        }

        // If the material has a quiz, it can't be marked as completed directly
        if ($material->has_quiz) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'This training material has a quiz that must be completed'
            ], 422);
        }

        $agent = $this->getAgent();

        // Mark the material as completed
        $progress = $this->trainingService->markAsCompleted($agent->id, $material->id);

        // Check if any certifications were earned
        $earnedCertifications = $this->trainingService->checkCertifications($agent->id);

        return response()->json([
            'message' => 'Training marked as completed',
            'data' => $progress,
            'earned_certifications' => $earnedCertifications
        ]);
    }

    /**
     * Submit a quiz for a training material.
     *
     * @param  \App\Http\Requests\API\Agent\Training\SubmitQuizRequest  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitQuiz(SubmitQuizRequest $request, string $id)
    {
        $material = AgentTrainingMaterial::findOrFail($id);

        if (!$material->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested training material is not available'
            ], 404);
        }

        // Validate that the material has a quiz
        if (!$material->has_quiz) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'This training material does not have a quiz'
            ], 422);
        }

        $agent = $this->getAgent();
        $answers = $request->input('answers');

        // Grade the quiz and mark as completed if passed
        $result = $this->trainingService->gradeQuiz($agent->id, $material->id, $answers);

        // Check if any certifications were earned
        $earnedCertifications = [];
        if ($result['passed']) {
            $earnedCertifications = $this->trainingService->checkCertifications($agent->id);
        }

        return response()->json([
            'message' => 'Quiz submitted successfully',
            'data' => $result,
            'earned_certifications' => $earnedCertifications
        ]);
    }

    /**
     * Display a listing of certifications.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function certifications(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $status = $request->input('status');

        $agent = $this->getAgent();
        $certifications = $this->trainingService->getAgentCertifications($agent->id, $status, $perPage);

        return response()->json([
            'data' => $certifications->items(),
            'meta' => [
                'current_page' => $certifications->currentPage(),
                'from' => $certifications->firstItem(),
                'last_page' => $certifications->lastPage(),
                'path' => $request->url(),
                'per_page' => $certifications->perPage(),
                'to' => $certifications->lastItem(),
                'total' => $certifications->total()
            ]
        ]);
    }

    /**
     * Display the specified certification.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showCertification(string $id)
    {
        $certification = AgentCertification::with('requirements.trainingMaterial')->findOrFail($id);

        if (!$certification->is_active) {
            return response()->json([
                'error' => 'Not Found',
                'message' => 'The requested certification is not available'
            ], 404);
        }

        $agent = $this->getAgent();
        $certificationDetails = $this->trainingService->getAgentCertificationDetails($agent->id, $certification);

        return response()->json([
            'data' => $certificationDetails
        ]);
    }

    /**
     * Get the agent's training progress.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function progress()
    {
        $agent = $this->getAgent();
        $progress = $this->trainingService->getAgentProgress($agent->id);

        return response()->json([
            'data' => $progress
        ]);
    }

    /**
     * Get the authenticated agent.
     *
     * @return \App\Models\Agent
     */
    protected function getAgent()
    {
        $user = Auth::user();
        return Agent::where('user_id', $user->id)->firstOrFail();
    }
}
