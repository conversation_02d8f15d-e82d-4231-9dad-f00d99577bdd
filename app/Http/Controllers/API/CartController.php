<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\MedicationVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use ProtoneMedia\Splade\Facades\Toast;

class CartController extends Controller
{
    public function addToCart(Request $request, $variantId)
    {
        $variant = MedicationVariant::with(['medicationBase'])->findOrFail($variantId);
        $sessionId = Session::getId();
        $userId = Auth::id();

        // Debug information
        info('Cart Debug - addToCart', [
            'session_id' => $sessionId,
            'user_id' => $userId,
            'is_authenticated' => Auth::check(),
            'auth_user' => Auth::user() ? Auth::user()->id : null,
            'request_user' => $request->user() ? $request->user()->id : null,
        ]);

        // Create query base depending on whether user is logged in
        $query = Cart::where(function($q) use ($sessionId, $userId) {
            $q->where('session_id', $sessionId);
            if ($userId) {
                $q->where(function($subQuery) use ($userId) {
                    $subQuery->whereNull('user_id')
                             ->orWhere('user_id', $userId);
                });
            }
        });

        $existingCartItem = $query->where('medication_variant_id', $variantId)->first();

        if ($existingCartItem) {
            // Update the quantity if the item already exists in the cart
            $existingCartItem->quantity += 1;
            $existingCartItem->save();
        } else {
            $cartItem = Cart::create([
                'session_id' => $sessionId,
                'user_id' => $userId,
                'medication_variant_id' => $variantId,
                'quantity' => 1,
            ]);

            if (!$cartItem) {
                return response()->json([
                    'message' => 'Item could not be added to cart'
                ]);
            }
        }

        // If user logs in, merge their session cart with their user cart
        if ($userId) {
            $this->mergeSessionCartWithUserCart($sessionId, $userId);
        }

        Toast::success('Item added to cart')->autoDismiss(3);
        if ($request->redirectUrl) {
            return redirect($request->redirectUrl);
        }

        // Fetch the cart item with its relationships
        $sessionId = Session::getId();
        $userId = Auth::id();

        $cartItems = Cart::where(function($q) use ($sessionId, $userId) {
                $q->where('session_id', $sessionId);
                if ($userId) {
                    $q->where(function($subQuery) use ($userId) {
                        $subQuery->whereNull('user_id')
                                 ->orWhere('user_id', $userId);
                    });
                }
            })
            ->with(['medicationVariant.medicationBase'])
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'variant_id' => $item->medication_variant_id,
                    'brand_name' => $item->medicationVariant->brand_name,
                    'generic_name' => $item->medicationVariant->medicationBase->generic_name,
                    'strength' => $item->medicationVariant->strength,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->medicationVariant->price,
                    'total_price' => $item->quantity * $item->medicationVariant->price,
                    'requires_prescription' => $item->medicationVariant->medicationBase->requires_prescription
                ];
            });
        // info('Cart items:', ['items' => $cartItems]);

        return response()->json([
            'message' => 'Item added to cart',
            'cartItems' => $cartItems
        ]);
    }

    public function addWeightLossToCart(Request $request)
    {
        $sessionId = Session::getId();
        $userId = Auth::id();
        $buspironeVariant = 26; //Buspar
        $metforminVariant = 83; //Glucophage
        $topiramateVariant = 108; //Topamax

        // Debug information
        info('Cart Debug - addWeightLossToCart', [
            'session_id' => $sessionId,
            'user_id' => $userId,
            'is_authenticated' => Auth::check(),
            'auth_user' => Auth::user() ? Auth::user()->id : null,
            'request_user' => $request->user() ? $request->user()->id : null,
        ]);

        $cartItem = Cart::upsert([
            [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'medication_variant_id' => $buspironeVariant,
                'quantity' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'medication_variant_id' => $metforminVariant,
                'quantity' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'medication_variant_id' => $topiramateVariant,
                'quantity' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ], uniqueBy: ['medication_variant_id'], update: ['quantity']);

        // If user logs in, merge their session cart with their user cart
        if ($userId) {
            $this->mergeSessionCartWithUserCart($sessionId, $userId);
        }

        if (!$cartItem) {
            return response()->json([
                'message' => 'Item could not be added to cart'
            ]);
        }

        Toast::success('Items added to cart')->autoDismiss(3);
        return to_route('rx.plan-options');
    }

    public function removeFromCart(Cart $cart)
    {
        $cart->delete();

        return response()->json([
            'message' => 'Item removed from cart',
            'cartItems' => $this->getCartItems()
        ]);
    }

    public function updateQuantity(Request $request, Cart $cart)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1'
        ]);

        $cart->update(['quantity' => $request->quantity]);

        return response()->json([
            'message' => 'Cart updated',
            'cartItems' => $this->fetchCartItems()
        ]);
    }

    public function getCartItems()
    {
        return response()->json([
            'cartItems' => $this->fetchCartItems()
        ]);
    }

    public function fetchCartItems()
    {
        $sessionId = Session::getId();
        $userId = Auth::id();

        return Cart::with(['medicationVariant' => function($query) {
            $query->with(['medicationBase' => function($q) {
                $q->with(['medicationClass', 'primaryUses.condition']);
            }]);
        }])
        ->where(function($q) use ($sessionId, $userId) {
            $q->where('session_id', $sessionId);
            if ($userId) {
                $q->where(function($subQuery) use ($userId) {
                    $subQuery->whereNull('user_id')
                             ->orWhere('user_id', $userId);
                });
            }
        })
        ->get()
        ->map(function ($item) {
            return [
                'id' => $item->id,
                'variant_id' => $item->medicationVariant->id,
                'brand_name' => $item->medicationVariant->brand_name,
                'generic_name' => $item->medicationVariant->medicationBase->generic_name,
                'strength' => $item->medicationVariant->strength,
                'dosage_form' => $item->medicationVariant->dosage_form,
                'quantity' => $item->quantity,
                'unit_price' => $item->medicationVariant->unit_price,
                'total_price' => $item->medicationVariant->unit_price * $item->quantity,
                'requires_prescription' => $item->medicationVariant->medicationBase->requires_prescription,
            ];
        });
    }

    /**
     * Merge session cart with user cart when user logs in
     *
     * @param string $sessionId
     * @param int $userId
     * @return void
     */
    private function mergeSessionCartWithUserCart(string $sessionId, int $userId): void
    {
        // Find all cart items for this session that don't have a user_id
        $sessionCartItems = Cart::where('session_id', $sessionId)
            ->whereNull('user_id')
            ->get();

        foreach ($sessionCartItems as $item) {
            // Check if user already has this item in their cart
            $existingItem = Cart::where('user_id', $userId)
                ->where('medication_variant_id', $item->medication_variant_id)
                ->first();

            if ($existingItem) {
                // Update quantity of existing item
                $existingItem->quantity += $item->quantity;
                $existingItem->save();
                $item->delete();
            } else {
                // Assign this item to the user
                $item->user_id = $userId;
                $item->save();
            }
        }
    }
}
