<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoriesController extends Controller
{
    public function index()
    {
        $categories = Category::with('children.children.children')->whereNull('parent_id')->get();

        return response()->json($categories);
    }

    public function byCategory($categoryId)
    {
        $categories = Category::where('id', $categoryId)->with('children.children.children')->whereNull('parent_id')->get();

        return response()->json($categories);
    }
}
