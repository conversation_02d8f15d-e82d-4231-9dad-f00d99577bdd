<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\MedicationBase;
use App\Models\MedicationClass;
use App\Models\MedicationVariant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Surfsidemedia\Shoppingcart\Facades\Cart;

class MedicationController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 9);

        $query = MedicationBase::with([
            'medicationVariants',
            'primaryUses' => function($query) {
                $query->with(['condition.categories']);
            },
            'offLabelUses' => function($query) {
                $query->with(['condition.categories']);
            },
            'medicationClass',
            'dosageInformation'
        ])
        ->isActive();

        if ($request->filled('search')) {
            $searchTerm = $request->search;

            $query->where(function($q) use ($searchTerm) {
                $q->where('generic_name', 'like', '%' . $searchTerm . '%')
                  ->orWhereHas('medicationVariants', function($sq) use ($searchTerm) {
                      $sq->where('brand_name', 'like', '%' . $searchTerm . '%');
                  });
            });
        }

        // This parameter is not being used by the frontend, keeping for backward compatibility
        if ($request->filled('parent_category')) {
            $query->whereHas('primaryUses.condition.categories', function($q) use ($request) {
                $q->where('categories.id', $request->parent_category)
                  ->orWhereHas('children', function($sq) use ($request) {
                      $sq->where('categories.id', $request->parent_category);
                  });
            });
        }

        if ($request->filled('category')) {
            $categoryIds = explode(',', $request->category);

            $query->where(function($q) use ($categoryIds) {
                $q->whereHas('primaryUses.condition.categories', function($sq) use ($categoryIds) {
                    $sq->whereIn('categories.id', $categoryIds);
                })->orWhereHas('offLabelUses.condition.categories', function($sq) use ($categoryIds) {
                    $sq->whereIn('categories.id', $categoryIds);
                });
            });
        }

        if ($request->filled('condition')) {
            $conditionId = $request->condition;

            $query->where(function($q) use ($conditionId) {
                $q->whereHas('primaryUses.condition', function($sq) use ($conditionId) {
                    $sq->where('conditions.id', $conditionId);
                })->orWhereHas('offLabelUses.condition', function($sq) use ($conditionId) {
                    $sq->where('conditions.id', $conditionId);
                });
            });
        }

        if ($request->filled('medication_class')) {
            $query->where('medication_class_id', $request->medication_class);
        }

        if ($request->filled('prescription_only')) {
            $query->where('requires_prescription', $request->prescription_only === "true");
        }

        if ($request->filled('controlled')) {
            $query->where('controlled_substance', $request->controlled);
        }

        $medications = $query->orderBy('generic_name')
                            ->paginate($perPage);

        return response()->json($medications);
    }

    public function getFilters()
    {
        $categories = Category::with(['children.conditions', 'conditions'])
            ->whereNull('parent_id')
            ->get();

        $medicationClasses = MedicationClass::whereNull('parent_id')
            ->with('children')
            ->get();

        return response()->json([
            'categories' => $categories,
            'medication_classes' => $medicationClasses
        ]);
    }

    public function genericNameList()
    {
        $medications = MedicationBase::select('id', 'generic_name')
            ->with('medicationVariants')
            ->orderBy('generic_name')
            ->get()
            ->groupBy(function ($item) {
                return strtoupper(substr($item->generic_name, 0, 1));
            });

        $letters = $medications->map(function ($group, $letter) {
            return [
                'letter' => $letter,
                'count' => $group->count(),
                'link' => view('components.letter-link', [
                    'letter' => $letter,
                    'count' => $group->count()
                ])->render()
            ];
        });

        return response()->json([
            'medications' => $medications,
            'letters' => $letters
        ]);
    }

    public function addToCart(Request $request, $medicationId)
    {
        try {
            $medication = MedicationVariant::with('medicationBase')->findOrFail($medicationId);
            // $cartInstance = Cart::instance('shopping');
            // info('Cart before restore:', ['content' => $cartInstance->content()]);
            // $cartInstance->restore('shopping');
            // info('Cart after restore:', ['content' => $cartInstance->content()]);

            // if ($cartInstance->search(function($cartItem) use ($medicationId) {
            //     return $cartItem->id == $medicationId;
            // })->count() > 0) {
            //     return response()->json([
            //         'message' => 'Item already in cart',
            //         'type' => 'info'
            //     ]);
            // }

            info('Cart before add:', ['content' => Cart::instance('shopping')->content()]);
            Cart::add([
                'id' => $medication->id,
                'name' => $medication->medicationBase->generic_name,
                'qty' => 1,
                'price' => 0,
                'options' => [
                    'type' => $medication->medicationBase->type,
                    'requires_prescription' => $medication->medicationBase->requires_prescription,
                ]
            ]);
            info('Cart after add:', ['content' => Cart::instance('shopping')->content()]);

            return response()->json([
                'message' => 'Medication added to cart successfully',
                'type' => 'success',
                'cartCount' => Cart::instance('shopping')->count(),
                'cartContent' => Cart::instance('shopping')->content()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while adding the medication to the cart',
                'type' => 'error',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getVariants($medicationId)
    {
        $variants = MedicationVariant::select('id', 'brand_name', 'strength', 'dosage_form')
            ->where('medication_base_id', $medicationId)
            ->get();

        return response()->json($variants);
    }
}
