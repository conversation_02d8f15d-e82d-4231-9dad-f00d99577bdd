<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Services\PaymentMethodService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class AchVerificationController extends Controller
{
    protected $paymentMethodService;

    public function __construct(PaymentMethodService $paymentMethodService)
    {
        $this->paymentMethodService = $paymentMethodService;
    }

    /**
     * Show the verification form for an ACH payment method.
     */
    public function show(PaymentMethod $paymentMethod)
    {
        // Check if the user has permission to verify this payment method
        if (Auth::id() !== $paymentMethod->user_id && !Auth::user()->hasRole(['admin', 'agent'])) {
            Toast::danger('You do not have permission to verify this payment method.');
            return redirect()->back();
        }

        // Check if this is an ACH payment method
        if (!$paymentMethod->isAch()) {
            Toast::danger('This is not an ACH payment method.');
            return redirect()->back();
        }

        // Get verification status
        $verificationStatus = $this->paymentMethodService->getAchVerificationStatus($paymentMethod);

        return view('payment-methods.verify-ach', [
            'paymentMethod' => $paymentMethod,
            'verificationStatus' => $verificationStatus
        ]);
    }

    /**
     * Process the verification of an ACH payment method.
     */
    public function verify(Request $request, PaymentMethod $paymentMethod)
    {
        // Check if the user has permission to verify this payment method
        if (Auth::id() !== $paymentMethod->user_id && !Auth::user()->hasRole(['admin', 'agent'])) {
            Toast::danger('You do not have permission to verify this payment method.');
            return redirect()->back();
        }

        // Check if this is an ACH payment method
        if (!$paymentMethod->isAch()) {
            Toast::danger('This is not an ACH payment method.');
            return redirect()->back();
        }

        // Validate the request
        $request->validate([
            'amount1' => 'required|numeric|min:0.01|max:1.00',
            'amount2' => 'required|numeric|min:0.01|max:1.00',
        ]);

        // Verify the micro-deposits
        $result = $this->paymentMethodService->verifyAchPaymentMethod(
            $paymentMethod,
            (float) $request->amount1,
            (float) $request->amount2
        );

        if ($result['success']) {
            Toast::success('Bank account verified successfully.');
            return redirect()->route('payment-methods.index');
        } else {
            $message = $result['message'] ?? 'Failed to verify bank account.';
            $attemptsRemaining = $result['attempts_remaining'] ?? null;
            
            if ($attemptsRemaining !== null) {
                $message .= " You have {$attemptsRemaining} attempts remaining.";
            }
            
            Toast::danger($message);
            return redirect()->back()->withInput();
        }
    }

    /**
     * Get the verification status of an ACH payment method.
     */
    public function status(PaymentMethod $paymentMethod)
    {
        // Check if the user has permission to view this payment method
        if (Auth::id() !== $paymentMethod->user_id && !Auth::user()->hasRole(['admin', 'agent'])) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to view this payment method.'
            ], 403);
        }

        // Check if this is an ACH payment method
        if (!$paymentMethod->isAch()) {
            return response()->json([
                'success' => false,
                'message' => 'This is not an ACH payment method.'
            ], 400);
        }

        // Get verification status
        $result = $this->paymentMethodService->getAchVerificationStatus($paymentMethod);

        return response()->json($result);
    }
}
