<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Consultation;
use App\Models\Prescription;
use App\Models\OfflineConversion;
use App\Models\Subscription;
use App\Models\MedicationOrder;
use App\Models\BusinessPlan;
use App\Models\BusinessPlanSelfPayment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class AdminController extends Controller
{
    public function dashboard()
    {
        $totalUsers = User::count();
        $totalDoctors = User::role('doctor')->count();
        $totalPatients = User::role('patient')->count();
        $totalConsultations = Consultation::count();
        $totalPrescriptions = Prescription::count();
        $totalActiveSubscriptions = Subscription::where('status', 'active')->count();
        $totalExpiredSubscriptions = Subscription::whereIn('status', ['active', 'expired'])->where('ends_at', '<', now())->count();
        $totalExpiringSubscriptions = Subscription::where('status', 'active')->whereBetween('ends_at', [now(), now()->addDays(7)])->count();
        $pendingMedicationOrders = MedicationOrder::where('status', 'pending')->count();

        // Business Plan metrics
        $totalBusinessPlans = BusinessPlan::where('active', true)->count();
        $selfPaymentsThisMonth = BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)
            ->whereMonth('paid_at', now()->month)
            ->whereYear('paid_at', now()->year)
            ->count();

        // Get subscription data for the current week
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();

        // Get subscriptions created per day for the current week
        $subscriptionsThisWeek = Subscription::select(DB::raw('DATE(created_at) as date'), DB::raw('count(*) as count'))
            ->whereBetween('created_at', [$startOfWeek, $endOfWeek])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date');

        // Create an array with all days of the week
        $subscriptionChartData = [];
        $subscriptionChartLabels = [];
        $currentDate = $startOfWeek->copy();

        while ($currentDate <= $endOfWeek) {
            $dateString = $currentDate->format('Y-m-d');
            $subscriptionChartLabels[] = $currentDate->format('D');
            $subscriptionChartData[] = $subscriptionsThisWeek->has($dateString) ? $subscriptionsThisWeek[$dateString]->count : 0;
            $currentDate->addDay();
        }

        // Get subscription plan distribution
        $subscriptionsByPlan = Subscription::select('plan_id', DB::raw('count(*) as count'))
            ->where('status', 'active')
            ->with('plan:id,name')
            ->groupBy('plan_id')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->plan ? $item->plan->name : 'Unknown',
                    'count' => $item->count
                ];
            });

        $planChartLabels = $subscriptionsByPlan->pluck('name')->toArray();
        $planChartData = $subscriptionsByPlan->pluck('count')->toArray();

        // Get recent subscriptions
        $recentSubscriptions = Subscription::with(['user', 'plan'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get expired subscriptions
        $expiredSubscriptions = Subscription::with(['user', 'plan'])
            ->whereIn('status', ['active', 'expired'])
            ->where('ends_at', '<', now())
            ->orderBy('ends_at', 'asc')
            ->take(5)
            ->get();

        // Get about to expire subscriptions (within next 7 days)
        $expiringSubscriptions = Subscription::with(['user', 'plan'])
            ->where('status', 'active')
            ->whereBetween('ends_at', [now(), now()->addDays(7)])
            ->orderBy('ends_at', 'asc')
            ->take(5)
            ->get();

        // Get recent medication orders
        $recentMedicationOrders = MedicationOrder::with(['patient', 'doctor'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent consultations
        $recentConsultations = Consultation::with(['patient', 'doctor'])
            ->orderBy('scheduled_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medical questionnaires
        $recentQuestionnaires = \App\Models\MedicalQuestionnaire::with(['user', 'treatment'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medical conditions
        $recentMedicalConditions = \App\Models\MedicalCondition::with('patient')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent allergies
        $recentAllergies = \App\Models\Allergy::with(['user'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Create a new instance of the OfflineConversions table
        $conversions = SpladeTable::for(OfflineConversion::orderBy('created_at', 'DESC'))
            ->column('gclid', label: 'Google Click ID', sortable: true)
            ->column('conversion_name', sortable: true)
            ->column('created_at', label: 'Conversion Time', sortable: true, as: fn ($created_at) => $created_at->format('Y-m-d H:i:s'))
            ->column('conversion_value', sortable: true)
            ->column('conversion_currency', sortable: true)
            ->column('user.email', label: 'Email')
            ->column('is_synced', label: 'Synced', as: fn ($is_synced) => $is_synced ? 'Yes' : 'No')
            ->paginate(10);

        return view('admin.dashboard', compact(
            'conversions',
            'totalUsers',
            'totalDoctors',
            'totalPatients',
            'totalConsultations',
            'totalPrescriptions',
            'totalActiveSubscriptions',
            'totalExpiredSubscriptions',
            'totalExpiringSubscriptions',
            'pendingMedicationOrders',
            'totalBusinessPlans',
            'selfPaymentsThisMonth',
            'subscriptionChartLabels',
            'subscriptionChartData',
            'planChartLabels',
            'planChartData',
            'recentSubscriptions',
            'expiredSubscriptions',
            'expiringSubscriptions',
            'recentMedicationOrders',
            'recentConsultations',
            'recentQuestionnaires',
            'recentMedicalConditions',
            'recentAllergies'
        ));
    }

    // public function users()
    // {
    //     return view('admin.users.index', [
    //         'users' => SpladeTable::for(User::class)
    //             ->column('name', sortable: true)
    //             ->column('email', sortable: true)
    //             ->column('roles', label: 'Role')
    //             ->column('created_at', sortable: true)
    //             ->column('actions')
    //             ->paginate(10),
    //     ]);
    // }

    public function createUser()
    {
        return view('admin.users.create');
    }

    public function storeUser(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|string|in:admin,doctor,patient,pharmacist,staff',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
        ]);

        $user->assignRole($validated['role']);

        Toast::success('User created successfully.');

        return redirect()->route('admin.users');
    }

    public function deleteUser(User $user)
    {
        $user->delete();

        Toast::success('User deleted successfully.');

        return redirect()->route('admin.users');
    }
}
