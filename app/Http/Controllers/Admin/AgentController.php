<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Agents\UploadAgentsAction;
use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentCommission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class AgentController extends Controller
{
    protected $uploadAgentsAction;

    public function __construct(UploadAgentsAction $uploadAgentsAction)
    {
        $this->uploadAgentsAction = $uploadAgentsAction;
    }
    /**
     * Display a listing of agents.
     */
    public function index()
    {
        $agents = SpladeTable::for(Agent::class)
            ->withGlobalSearch(columns: ['id', 'user.name', 'user.email', 'company', 'tier'])
            ->column('id', sortable: true)
            ->column('user.name', label: 'Name', sortable: true)
            ->column('user.email', label: 'Email')
            ->column('company')
            ->column('tier', sortable: true)
            ->column('status', sortable: true)
            ->column('commission_rate', label: 'Commission %', sortable: true)
            ->column('created_at', sortable: true)
            ->column('actions')
            ->paginate(5);

        // Get counts for the dashboard
        $totalAgents = Agent::count();
        $approvedAgents = Agent::where('status', 'approved')->count();
        $pendingAgents = Agent::where('status', 'pending')->count();
        $rejectedAgents = Agent::where('status', 'rejected')->count();

        // Get tier distribution
        $tierCounts = [];
        foreach(Agent::TIERS as $tier => $rate) {
            $tierCounts[$tier] = Agent::where('tier', $tier)->count();
        }

        return view('admin.agents.index', compact(
            'agents',
            'totalAgents',
            'approvedAgents',
            'pendingAgents',
            'rejectedAgents',
            'tierCounts'
        ));
    }

    /**
     * Show the form for editing the specified agent.
     */
    public function edit(Agent $agent)
    {
        $agent->load('user', 'referrer.user');

        return view('admin.agents.edit', compact('agent'));
    }

    /**
     * Update the specified agent in storage.
     */
    public function update(Request $request, Agent $agent)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected',
            'tier' => 'required|in:' . implode(',', array_keys(Agent::TIERS)),
            'commission_rate' => 'required|numeric|min:0|max:100',
        ]);

        $agent->status = $request->status;
        $agent->tier = $request->tier;
        $agent->commission_rate = $request->commission_rate;
        $agent->save();

        Toast::success('Agent updated successfully')->autoDismiss(10);
        return redirect()->route('admin.agents.index');
    }

    /**
     * Display agent commissions.
     */
    public function commissions()
    {
        $commissions = SpladeTable::for(AgentCommission::class)
            ->withGlobalSearch(columns: ['id', 'agent.user.name', 'transaction_id'])
            ->column('id', sortable: true)
            ->column('agent.user.name', label: 'Agent', sortable: true)
            ->column('uplineAgent.user.name', label: 'Upline Agent')
            ->column('transaction_id', sortable: true)
            ->column('total_amount', sortable: true)
            ->column('commission_amount', sortable: true)
            ->column('upline_commission_amount', label: 'Upline Amount', sortable: true)
            ->column('status', sortable: true)
            ->column('created_at', sortable: true)
            ->column('actions')
            ->paginate(15);

        return view('admin.agents.commissions', compact('commissions'));
    }

    /**
     * Mark a commission as paid.
     */
    public function markCommissionPaid(AgentCommission $commission)
    {
        $commission->markAsPaid();

        Toast::success('Commission marked as paid')->autoDismiss(10);
        return back();
    }

    /**
     * Mark a commission as cancelled.
     */
    public function markCommissionCancelled(AgentCommission $commission)
    {
        $commission->markAsCancelled();

        Toast::danger('Commission cancelled')->autoDismiss(10);
        return back();
    }

    /**
     * Get a list of agents for the agent management component.
     */
    public function getAgentsList()
    {
        $agents = Agent::with('user')->latest()->get();
        return response()->json($agents);
    }

    /**
     * Download a CSV template for agent upload.
     */
    public function downloadCsvTemplate()
    {
        $csvContent = $this->uploadAgentsAction->generateCsvTemplate();

        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="agent_template.csv"',
        ]);
    }

    /**
     * Upload agents from a CSV file.
     */
    public function uploadAgents(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        try {
            $result = $this->uploadAgentsAction->handle($request->file('csv_file'));

            if ($result['success']) {
                Toast::success($result['message'])->autoDismiss(10);
            } else {
                Toast::danger($result['message'])->autoDismiss(10);
            }

            return redirect()->route('admin.agents.index');
        } catch (\Exception $e) {
            Log::error('Failed to upload agents: ' . $e->getMessage());
            Toast::danger('Failed to upload agents: ' . $e->getMessage())->autoDismiss(10);
            return back();
        }
    }
}
