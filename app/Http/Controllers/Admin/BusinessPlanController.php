<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessPlan;
use App\Models\BusinessPlanSelfPayment;
use App\Models\BusinessPlanAuditLog;
use App\Models\BusinessEmployee;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class BusinessPlanController extends Controller
{
    /**
     * Display a listing of business plans.
     */
    public function index(Request $request)
    {
        $query = BusinessPlan::with(['business', 'selfPayments', 'completedSelfPayments']);

        // Apply filters
        if ($request->filled('business_name')) {
            $query->whereHas('business', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->business_name . '%');
            });
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('active', true)
                    ->where(function ($q) {
                        $q->whereNull('ends_at')
                            ->orWhere('ends_at', '>=', now());
                    });
            } elseif ($request->status === 'inactive') {
                $query->where('active', false)
                    ->orWhere('ends_at', '<', now());
            }
        }

        $businessPlans = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.business-plans.index', compact('businessPlans'));
    }

    /**
     * Display the specified business plan.
     */
    public function show(BusinessPlan $businessPlan)
    {
        $businessPlan->load([
            'business',
            'selfPayments.businessEmployee.user',
            'selfPayments.transaction',
            'auditLogs.user',
            'auditLogs.businessEmployee'
        ]);

        $selfPaymentStats = [
            'total_self_payments' => $businessPlan->selfPayments()->count(),
            'completed_self_payments' => $businessPlan->completedSelfPayments()->count(),
            'pending_self_payments' => $businessPlan->selfPayments()
                ->where('status', BusinessPlanSelfPayment::STATUS_PENDING)->count(),
            'failed_self_payments' => $businessPlan->selfPayments()
                ->where('status', BusinessPlanSelfPayment::STATUS_FAILED)->count(),
            'total_amount_collected' => $businessPlan->completedSelfPayments()
                ->sum('amount_paid') / 100, // Convert to dollars
        ];

        return view('admin.business-plans.show', compact('businessPlan', 'selfPaymentStats'));
    }

    /**
     * Display self-payments for all business plans.
     */
    public function selfPayments(Request $request)
    {
        $query = BusinessPlanSelfPayment::with([
            'businessPlan.business',
            'businessEmployee.user',
            'transaction'
        ]);

        // Apply filters
        if ($request->filled('business_name')) {
            $query->whereHas('businessPlan.business', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->business_name . '%');
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $selfPayments = $query->orderBy('created_at', 'desc')->paginate(20);

        // Calculate summary statistics
        $stats = [
            'total_payments' => BusinessPlanSelfPayment::count(),
            'completed_payments' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)->count(),
            'pending_payments' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_PENDING)->count(),
            'failed_payments' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_FAILED)->count(),
            'total_amount' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)
                ->sum('amount_paid') / 100,
            'this_month_amount' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount_paid') / 100,
        ];

        return view('admin.business-plans.self-payments', compact('selfPayments', 'stats'));
    }

    /**
     * Display audit logs for business plans.
     */
    public function auditLogs(Request $request)
    {
        $query = BusinessPlanAuditLog::with([
            'businessPlan.business',
            'user',
            'businessEmployee',
            'selfPayment',
            'transaction'
        ]);

        // Apply filters
        if ($request->filled('business_name')) {
            $query->whereHas('businessPlan.business', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->business_name . '%');
            });
        }

        if ($request->filled('action')) {
            $query->where('action', $request->action);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $auditLogs = $query->orderBy('created_at', 'desc')->paginate(25);

        return view('admin.business-plans.audit-logs', compact('auditLogs'));
    }

    /**
     * Display businesses overview.
     */
    public function businesses(Request $request)
    {
        $query = Business::with(['plans' => function ($q) {
            $q->where('active', true)
                ->where(function ($query) {
                    $query->whereNull('ends_at')
                        ->orWhere('ends_at', '>=', now());
                });
        }, 'employees']);

        // Apply filters
        if ($request->filled('name')) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }

        if ($request->filled('has_active_plan')) {
            if ($request->has_active_plan === 'yes') {
                $query->whereHas('plans', function ($q) {
                    $q->where('active', true)
                        ->where(function ($query) {
                            $query->whereNull('ends_at')
                                ->orWhere('ends_at', '>=', now());
                        });
                });
            } elseif ($request->has_active_plan === 'no') {
                $query->whereDoesntHave('plans', function ($q) {
                    $q->where('active', true)
                        ->where(function ($query) {
                            $query->whereNull('ends_at')
                                ->orWhere('ends_at', '>=', now());
                        });
                });
            }
        }

        $businesses = $query->orderBy('name')->paginate(15);

        // Calculate summary statistics
        $stats = [
            'total_businesses' => Business::count(),
            'businesses_with_active_plans' => Business::whereHas('plans', function ($q) {
                $q->where('active', true)
                    ->where(function ($query) {
                        $query->whereNull('ends_at')
                            ->orWhere('ends_at', '>=', now());
                    });
            })->count(),
            'total_employees' => BusinessEmployee::where('status', 'active')->count(),
            'total_self_payments_this_month' => BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->count(),
        ];

        return view('admin.business-plans.businesses', compact('businesses', 'stats'));
    }

    /**
     * Show business plan reports and analytics.
     */
    public function reports(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subMonth()->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // Self-payment trends
        $selfPaymentTrends = BusinessPlanSelfPayment::where('status', BusinessPlanSelfPayment::STATUS_COMPLETED)
            ->whereBetween('paid_at', [$dateFrom, $dateTo])
            ->selectRaw('DATE(paid_at) as date, COUNT(*) as count, SUM(amount_paid) as total_amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top businesses by self-payments
        $topBusinesses = Business::withCount(['plans as self_payments_count' => function ($q) use ($dateFrom, $dateTo) {
            $q->join('business_plan_self_payments', 'business_plans.id', '=', 'business_plan_self_payments.business_plan_id')
                ->where('business_plan_self_payments.status', BusinessPlanSelfPayment::STATUS_COMPLETED)
                ->whereBetween('business_plan_self_payments.paid_at', [$dateFrom, $dateTo]);
        }])
            ->having('self_payments_count', '>', 0)
            ->orderBy('self_payments_count', 'desc')
            ->take(10)
            ->get();

        // Plan quantity changes
        $planChanges = BusinessPlanAuditLog::where('action', BusinessPlanAuditLog::ACTION_QUANTITY_DECREASED)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->with('businessPlan.business')
            ->get();

        return view('admin.business-plans.reports', compact(
            'selfPaymentTrends',
            'topBusinesses',
            'planChanges',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Refund a self-payment (admin only).
     */
    public function refundSelfPayment(BusinessPlanSelfPayment $selfPayment)
    {
        if (!$selfPayment->isCompleted()) {
            Toast::danger('Only completed payments can be refunded.')->autoDismiss(10);
            return back();
        }

        // Mark as refunded
        $selfPayment->update(['status' => BusinessPlanSelfPayment::STATUS_REFUNDED]);

        // Reverse the business plan changes
        $businessPlan = $selfPayment->businessPlan;
        $businessPlan->update([
            'plan_quantity' => $businessPlan->plan_quantity + 1,
            'total_price' => $businessPlan->total_price + $selfPayment->amount_paid,
        ]);

        // Create audit log
        BusinessPlanAuditLog::create([
            'business_plan_id' => $businessPlan->id,
            'user_id' => auth()->id(),
            'business_employee_id' => $selfPayment->business_employee_id,
            'action' => BusinessPlanAuditLog::ACTION_SELF_PAYMENT_REFUNDED,
            'description' => "Admin refunded self-payment #{$selfPayment->id}",
            'old_values' => [
                'plan_quantity' => $businessPlan->plan_quantity - 1,
                'total_price' => $businessPlan->total_price - $selfPayment->amount_paid,
            ],
            'new_values' => [
                'plan_quantity' => $businessPlan->plan_quantity,
                'total_price' => $businessPlan->total_price,
            ],
            'source' => BusinessPlanAuditLog::SOURCE_ADMIN,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'self_payment_id' => $selfPayment->id,
        ]);

        Toast::success('Self-payment has been refunded and business plan restored.')->autoDismiss(10);
        return back();
    }
}
