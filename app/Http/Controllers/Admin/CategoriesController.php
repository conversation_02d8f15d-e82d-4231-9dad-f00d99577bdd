<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class CategoriesController extends Controller
{
    public function index()
    {
        return view('admin.categories.index', [
            'categories' => SpladeTable::for(Category::class)
                ->column('name', sortable: true)
                ->column('level', sortable: true)
                ->column('parent.name', label: 'Parent Category')
                ->column('actions')
                ->withGlobalSearch(columns: ['name'])
                ->paginate(10)
        ]);
    }

    public function create()
    {
        return view('admin.categories.create', [
            'parents' => Category::whereNull('parent_id')->get()
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:categories,id',
            'level' => 'required|integer'
        ]);

        Category::create($validated);

        Toast::success('Category created successfully.')->autoDismiss(3);
        return redirect()->route('admin.categories.index');
    }

    public function edit(Category $category)
    {
        return view('admin.categories.edit', [
            'category' => $category,
            'parents' => Category::where('id', '!=', $category->id)
                ->whereNull('parent_id')
                ->orWhere('level', '<', $category->level)
                ->get()
        ]);
    }

    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => [
                'nullable',
                'exists:categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value === $category->id) {
                        $fail('Category cannot be its own parent.');
                    }
                }
            ],
            'level' => 'required|integer|min:0'
        ]);

        $category->update($validated);

        Toast::success('Category updated successfully.');
        return redirect()->route('admin.categories.index');
    }

    public function destroy(Category $category)
    {
        $category->delete();

        Toast::success('Category deleted successfully.')->autoDismiss(3);

        return redirect()->route('admin.categories.index');
    }
}
