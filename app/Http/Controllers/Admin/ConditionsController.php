<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Condition;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class ConditionsController extends Controller
{
    public function index()
    {
        return view('admin.conditions.index', [
            'conditions' => SpladeTable::for(Condition::class)
                ->column('name', sortable: true)
                ->column('therapeutic_use')
                ->column('categories', label: 'Categories')
                ->column('actions')
                ->withGlobalSearch(columns: ['name'])
                ->paginate(10)
        ]);
    }

    public function create()
    {
        return view('admin.conditions.create', [
            'categories' => Category::with('children.children.children')->whereNull('parent_id')->get()
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'therapeutic_use' => 'required|string',
            'categories' => 'required|array',
            'categories.*' => 'exists:categories,id'
        ]);

        $condition = Condition::create([
            'name' => $validated['name'],
            'therapeutic_use' => $validated['therapeutic_use']
        ]);

        $condition->categories()->attach($validated['categories']);

        Toast::success('Condition created successfully.')->autoDismiss(3);
        return to_route('admin.conditions.index');
    }

    public function edit(Condition $condition)
    {
        return view('admin.conditions.edit', [
            'condition' => $condition->load('categories'),
            'categories' => Category::with('children.children.children')->whereNull('parent_id')->get()
        ]);
    }

    public function update(Request $request, Condition $condition)
    {
        info($request);
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'therapeutic_use' => 'required|string',
            'categories' => 'required|array',
            'categories.*' => 'exists:categories,id'
        ]);

        $condition->update([
            'name' => $validated['name'],
            'therapeutic_use' => $validated['therapeutic_use']
        ]);

        $condition->categories()->sync($validated['categories']);

        Toast::success('Condition updated successfully.')->autoDismiss(3);
        return to_route('admin.conditions.index');
    }

    public function destroy(Condition $condition)
    {
        $condition->categories()->detach();
        $condition->delete();

        Toast::success('Condition deleted successfully.')->autoDismiss(3);
        return to_route('admin.conditions.index');
    }
}
