<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class ConsultationController extends Controller
{
    /**
     * Display a listing of the consultations.
     */
    public function index()
    {
        return view('admin.consultations.index', [
            'consultations' => SpladeTable::for(Consultation::with(['patient', 'doctor']))
                ->column('id', sortable: true)
                ->column('patient.name', label: 'Patient', sortable: true)
                ->column('doctor.name', label: 'Doctor', sortable: true)
                ->column('scheduled_at', sortable: true)
                ->column('status', sortable: true)
                ->column('type', sortable: true)
                ->column('actions')
                ->selectFilter('status', Consultation::STATUSES)
                ->paginate(10),
        ]);
    }

    /**
     * Show the form for creating a new consultation.
     */
    public function create()
    {
        return view('admin.consultations.create');
    }

    /**
     * Store a newly created consultation in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:users,id',
            'doctor_id' => 'nullable|exists:users,id',
            'scheduled_at' => 'required|date',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
            'status' => 'required|in:scheduled,completed,cancelled,pending',
        ]);

        Consultation::create($validated);

        Toast::success('Consultation created successfully.');

        return redirect()->route('admin.consultations.index');
    }

    /**
     * Display the specified consultation.
     */
    public function show(Consultation $consultation)
    {
        $consultation->load(['patient', 'doctor']);
        
        return view('admin.consultations.show', compact('consultation'));
    }

    /**
     * Show the form for editing the specified consultation.
     */
    public function edit(Consultation $consultation)
    {
        return view('admin.consultations.edit', compact('consultation'));
    }

    /**
     * Update the specified consultation in storage.
     */
    public function update(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:users,id',
            'doctor_id' => 'nullable|exists:users,id',
            'scheduled_at' => 'required|date',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
            'status' => 'required|in:scheduled,completed,cancelled,pending',
        ]);

        $consultation->update($validated);

        Toast::success('Consultation updated successfully.');

        return redirect()->route('admin.consultations.index');
    }

    /**
     * Remove the specified consultation from storage.
     */
    public function destroy(Consultation $consultation)
    {
        $consultation->delete();

        Toast::success('Consultation deleted successfully.');

        return redirect()->route('admin.consultations.index');
    }
}
