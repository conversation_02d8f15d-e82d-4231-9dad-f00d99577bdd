<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Discount;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class DiscountController extends Controller
{
    public function index()
    {
        return view('admin.discounts.index', [
            'discounts' => SpladeTable::for(Discount::class)
                ->column('name')
                ->column('type')
                ->column('value')
                ->column('start_date')
                ->column('end_date')
                ->column(
                    key: 'status',
                    sortable: true,
                    as: fn ($discount) => config('status.discount')[$discount],
                )
                ->column('action')
                // ->bulkAction(
                //     label: 'Activate',
                //     each: fn (Discount $discount) => info($discount),
                //     before: fn () => Toast::info('Activating discount')->autoDismiss(10),
                //     after: fn () => Toast::info('Activated')->autoDismiss(10)
                // )
                ->paginate(10),
        ]);
    }

    public function create()
    {
        $subscriptionPlans = SubscriptionPlan::all();
        return view('admin.discounts.create-or-edit', compact('subscriptionPlans'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|string|max:255',
            'subscription_plan_ids' => 'required|array',
            'subscription_plan_ids.*' => 'exists:subscription_plans,id',
        ]);

        $discount = Discount::create($validated);
        $discount->subscriptionPlans()->attach($validated['subscription_plan_ids']);

        Toast::success('Discount created successfully.');
        return redirect()->route('admin.discounts.index');
    }

    public function edit(Discount $discount)
    {
        $subscriptionPlans = SubscriptionPlan::all();
        $selectedPlanIds = $discount->subscriptionPlans->pluck('id');
        return view('admin.discounts.edit', compact('discount', 'subscriptionPlans', 'selectedPlanIds'));
    }

    public function update(Request $request, Discount $discount)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'is_active' => 'boolean',
            'subscription_plan_ids' => 'required|array',
            'subscription_plan_ids.*' => 'exists:subscription_plans,id',
        ]);

        $subscriptionPlanIds = $validated['subscription_plan_ids'];
        unset($validated['subscription_plan_ids']);

        $discount->update($validated);
        $discount->subscriptionPlans()->sync($subscriptionPlanIds);

        Toast::success('Discount updated successfully.');
        return redirect()->route('admin.discounts.index');
    }

    public function destroy(Discount $discount)
    {
        $discount->delete();
        Toast::success('Discount deleted successfully.');
        return redirect()->route('admin.discounts.index');
    }
}