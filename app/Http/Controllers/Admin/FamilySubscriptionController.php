<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\FamilyMember\AdminAddDependentRequest;
use App\Http\Requests\Admin\FamilyMember\AdminOverrideAgeRestrictionRequest;
use App\Models\FamilyMember;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Services\FamilySubscriptionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class FamilySubscriptionController extends Controller
{
    public function __construct(protected FamilySubscriptionService $familySubscriptionService)
    {
    }

    /**
     * Display a listing of all family subscriptions.
     */
    public function index()
    {
        // Get all family plans
        $familyPlans = SubscriptionPlan::where('type', 'family')->pluck('id');

        // Get all primary subscriptions for family plans
        $query = Subscription::whereIn('plan_id', $familyPlans)
            ->where('is_primary_account', true)
            ->whereHas('plan') // Ensure plan relationship exists
            ->whereHas('user') // Ensure user relationship exists
            ->with(['user', 'plan']);

        $subscriptions = SpladeTable::for($query)
            ->column('id', sortable: true)
            ->column('user.name', label: 'Primary Account Holder', sortable: true)
            ->column('plan.name', label: 'Plan', sortable: true)
            ->column('starts_at', label: 'Start Date', sortable: true)
            ->column('ends_at', label: 'End Date', sortable: true)
            ->column('status', label: 'Status', sortable: true)
            ->column('dependent_count', label: 'Dependents', as: function ($subscription) {
                // Add null check to prevent errors
                if (!$subscription) {
                    Log::warning('Null subscription object in FamilySubscriptionController table');
                    return 'N/A';
                }
                if (!$subscription->plan) {
                    Log::warning('Subscription missing plan relationship', ['subscription_id' => $subscription->id]);
                    return 'N/A';
                }
                return $subscription->getDependentCount() . '/5';
            })
            ->column('actions', label: 'Actions')
            ->paginate(10)
            ->defaultSort('created_at', 'desc')
            ->searchInput('user.name')
            ->searchInput('plan.name')
            ->selectFilter('status', [
                'active' => 'Active',
                'inactive' => 'Inactive',
                'cancelled' => 'Cancelled',
                'expired' => 'Expired',
            ]);

        return view('admin.family-subscriptions.index', [
            'subscriptions' => $subscriptions,
        ]);
    }

    /**
     * Display the specified family subscription with its dependents.
     */
    public function show(Subscription $subscription)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        $subscription->load(['user', 'plan']);

        // Get all family members for this subscription
        $query = FamilyMember::where('subscription_id', $subscription->id)
            ->with(['dependentUser', 'primaryUser']);

        $familyMembers = SpladeTable::for($query)
            ->column('id', sortable: true)
            ->column('dependentUser.name', label: 'Name', sortable: true)
            ->column('dependentUser.email', label: 'Email', sortable: true)
            ->column('relationship_type', label: 'Relationship', sortable: true)
            ->column('date_of_birth', label: 'Date of Birth', sortable: true)
            ->column('age_category', label: 'Age Category', as: function ($familyMember) {
                if (!$familyMember->date_of_birth) return 'Unknown';

                $age = $familyMember->date_of_birth->age;
                if ($age >= 24) {
                    return '<span class="badge badge-warning">Older (24+)</span>';
                } elseif ($age >= 18) {
                    return '<span class="badge badge-info">Adult (18-23)</span>';
                } else {
                    return '<span class="badge badge-secondary">Minor (<18)</span>';
                }
            })
            ->column('subscription_status', label: 'Status', as: function ($familyMember) {
                $dependentSubscription = Subscription::where('user_id', $familyMember->dependent_user_id)
                    ->where('primary_subscription_id', $familyMember->subscription_id)
                    ->first();

                if (!$dependentSubscription) return 'N/A';

                $statusClasses = [
                    'active' => 'badge-success',
                    'inactive' => 'badge-warning',
                    'cancelled' => 'badge-error',
                    'expired' => 'badge-error',
                    'pending_payment' => 'badge-info',
                ];

                $class = $statusClasses[$dependentSubscription->status] ?? 'badge-secondary';
                return '<span class="badge ' . $class . '">' . ucfirst($dependentSubscription->status) . '</span>';
            })
            ->column('actions', label: 'Actions')
            ->paginate(10)
            ->withGlobalSearch(columns: ['dependentUser.name', 'dependentUser.email']);

        return view('admin.family-subscriptions.show', [
            'subscription' => $subscription,
            'familyMembers' => $familyMembers,
            'canAddMore' => !$subscription->hasReachedMaxDependents(),
            'canAddOlderDependent' => !$subscription->hasReachedMaxOlderDependents(),
            'canAddYoungerDependent' => !$subscription->hasReachedMaxYoungerDependents(),
            'dependentCount' => $subscription->getDependentCount(),
            'youngerDependentCount' => $subscription->getYoungerDependentCount(),
            'adultDependentCount' => $subscription->getAdultDependentCount(),
            'olderDependentCount' => $subscription->getOlderDependentCount(),
            'maxDependents' => 5,
            'maxYoungerDependents' => 4,
            'maxOlderDependents' => 1,
        ]);
    }

    /**
     * Show the form for adding a dependent to a family subscription.
     */
    public function createDependent(Subscription $subscription)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        return view('admin.family-subscriptions.create-dependent', [
            'subscription' => $subscription,
            'canAddOlderDependent' => !$subscription->hasReachedMaxOlderDependents(),
            'canAddYoungerDependent' => !$subscription->hasReachedMaxYoungerDependents(),
            'youngerDependentCount' => $subscription->getYoungerDependentCount(),
            'adultDependentCount' => $subscription->getAdultDependentCount(),
            'olderDependentCount' => $subscription->getOlderDependentCount(),
            'maxYoungerDependents' => 4,
            'maxOlderDependents' => 1,
            'relationshipTypes' => [
                'spouse' => 'Spouse/Partner',
                'child' => 'Child',
                'other' => 'Other',
            ],
            'overrideAgeRestrictions' => false,
        ]);
    }

    /**
     * Store a newly created dependent in storage.
     */
    public function storeDependent(AdminAddDependentRequest $request, Subscription $subscription)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        try {
            $result = $this->familySubscriptionService->addDependentToFamily(
                $subscription,
                $request->validated(),
                $request->override_age_restrictions ?? false
            );

            // Log the admin action
            Log::info("Admin added dependent to family subscription #{$subscription->id} for user {$subscription->user->name}. Override age restrictions: " . ($request->override_age_restrictions ? 'Yes' : 'No'));

            Toast::success('Family member added successfully.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.show', $subscription);
        } catch (\Exception $e) {
            Toast::danger('Error adding family member: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Remove a dependent from a family subscription.
     */
    public function removeDependent(Subscription $subscription, FamilyMember $familyMember)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        // Ensure the family member belongs to this subscription
        if ($familyMember->subscription_id !== $subscription->id) {
            Toast::danger('This family member does not belong to the specified subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.show', $subscription);
        }

        try {
            // Store dependent name for the success message
            $dependentName = $familyMember->dependentUser->name;

            $this->familySubscriptionService->removeDependentFromFamily(
                $subscription,
                $familyMember->dependentUser
            );

            // Log the admin action
            Log::info("Admin removed dependent {$dependentName} from family subscription #{$subscription->id} for user {$subscription->user->name}");

            Toast::success("Family member {$dependentName} removed successfully.")->autoDismiss(5);
        } catch (\Exception $e) {
            Toast::danger('Error removing family member: ' . $e->getMessage())->autoDismiss(10);
        }

        return redirect()->route('admin.family-subscriptions.show', $subscription);
    }

    /**
     * Show the form for overriding age restrictions.
     */
    public function showOverrideForm(Subscription $subscription)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        return view('admin.family-subscriptions.override-age-restrictions', [
            'subscription' => $subscription,
            'currentMaxOlderDependents' => $subscription->getFeature('admin_overrides.max_older_dependents', 1),
            'currentMaxYoungerDependents' => $subscription->getFeature('admin_overrides.max_younger_dependents', 4),
            'currentMaxTotalDependents' => $subscription->getFeature('admin_overrides.max_total_dependents', 5),
        ]);
    }

    /**
     * Apply age restriction overrides.
     */
    public function applyOverrides(AdminOverrideAgeRestrictionRequest $request, Subscription $subscription)
    {
        // Ensure this is a primary family subscription
        if (!$subscription->isPrimaryAccount() || !$subscription->plan->isFamilyPlan()) {
            Toast::danger('This is not a primary family subscription.')->autoDismiss(5);
            return redirect()->route('admin.family-subscriptions.index');
        }

        // Store the overrides in the subscription's meta_data
        $subscription->setFeature('admin_overrides.max_older_dependents', (int) $request->max_older_dependents);
        $subscription->setFeature('admin_overrides.max_younger_dependents', (int) $request->max_younger_dependents);
        $subscription->setFeature('admin_overrides.max_total_dependents', (int) $request->max_total_dependents);
        $subscription->setFeature('admin_overrides.reason', $request->reason);
        $subscription->setFeature('admin_overrides.applied_at', now()->toDateTimeString());
        $subscription->setFeature('admin_overrides.applied_by', auth()->user()->name);

        // Log the admin action
        Log::info("Admin overrode age restrictions for family subscription #{$subscription->id}. New limits: {$request->max_total_dependents} total, {$request->max_older_dependents} older, {$request->max_younger_dependents} younger. Reason: {$request->reason}");

        Toast::success('Age restrictions successfully overridden.')->autoDismiss(5);
        return redirect()->route('admin.family-subscriptions.show', $subscription);
    }

    /**
     * Generate a report of family plan usage.
     */
    public function report(Request $request)
    {
        // Get date range from request or use default (last 30 days)
        $startDate = $request->input('start_date')
            ? Carbon::parse($request->input('start_date'))
            : Carbon::now()->subDays(30)->startOfDay();

        $endDate = $request->input('end_date')
            ? Carbon::parse($request->input('end_date'))->endOfDay()
            : Carbon::now()->endOfDay();

        // Get all family plans
        $familyPlans = SubscriptionPlan::where('type', 'family')->pluck('id');

        // Get all primary subscriptions for family plans (for statistics)
        $familySubscriptionsCollection = Subscription::whereIn('plan_id', $familyPlans)
            ->where('is_primary_account', true)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'plan'])
            ->get();

        // Create query builder for SpladeTable (without ->get())
        $familySubscriptionsQuery = Subscription::whereIn('plan_id', $familyPlans)
            ->where('is_primary_account', true)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'plan']);

        // Calculate statistics
        $totalFamilyPlans = $familySubscriptionsCollection->count();
        $activeFamilyPlans = $familySubscriptionsCollection->where('status', 'active')->count();
        $totalDependents = 0;
        $olderDependents = 0;
        $youngerDependents = 0;
        $adultDependents = 0;
        $minorDependents = 0;

        foreach ($familySubscriptionsCollection as $subscription) {
            // Add null checks to prevent errors
            if (!$subscription || !$subscription->plan) {
                continue;
            }

            $totalDependents += $subscription->getDependentCount();
            $olderDependents += $subscription->getOlderDependentCount();
            $youngerDependents += $subscription->getYoungerDependentCount();
            $adultDependents += $subscription->getAdultDependentCount();
            $minorDependents += $youngerDependents - $adultDependents;
        }

        // Calculate averages
        $avgDependentsPerPlan = $totalFamilyPlans > 0 ? round($totalDependents / $totalFamilyPlans, 1) : 0;

        // Get distribution of dependents per plan
        $dependentDistribution = [
            '0' => 0,
            '1' => 0,
            '2' => 0,
            '3' => 0,
            '4' => 0,
            '5' => 0,
            '6+' => 0,
        ];

        foreach ($familySubscriptionsCollection as $subscription) {
            // Add null checks to prevent errors
            if (!$subscription || !$subscription->plan) {
                continue;
            }

            $count = $subscription->getDependentCount();
            if ($count > 5) {
                $dependentDistribution['6+']++;
            } else {
                $dependentDistribution[(string)$count]++;
            }
        }

        return view('admin.family-subscriptions.report', [
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
            'totalFamilyPlans' => $totalFamilyPlans,
            'activeFamilyPlans' => $activeFamilyPlans,
            'totalDependents' => $totalDependents,
            'olderDependents' => $olderDependents,
            'youngerDependents' => $youngerDependents,
            'adultDependents' => $adultDependents,
            'minorDependents' => $minorDependents,
            'avgDependentsPerPlan' => $avgDependentsPerPlan,
            'dependentDistribution' => $dependentDistribution,
            'subscriptions' => SpladeTable::for($familySubscriptionsQuery)
                ->column('id', sortable: true)
                ->column('user.name', label: 'Primary Account Holder', sortable: true)
                ->column('plan.name', label: 'Plan', sortable: true)
                ->column('created_at', label: 'Created Date', sortable: true)
                ->column('status', label: 'Status', sortable: true)
                ->column('dependent_count', label: 'Dependents', as: function ($subscription) {
                    // Add null check to prevent errors
                    if (!$subscription || !$subscription->plan) {
                        return 'N/A';
                    }
                    return $subscription->getDependentCount() . '/5';
                })
                ->column('actions', label: 'Actions')
                ->paginate(10)
                ->defaultSort('created_at', 'desc'),
        ]);
    }
}
