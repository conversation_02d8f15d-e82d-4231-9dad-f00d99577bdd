<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Condition;
use App\Models\MedicationBase;
use App\Models\MedicationClass;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationBasesController extends Controller
{
    public function index()
    {
        return view('admin.medication-bases.index', [
            'medications' => SpladeTable::for(MedicationBase::class)
                ->withGlobalSearch(columns: ['generic_name'])
                ->column('generic_name', sortable: true)
                ->column('type', sortable: true)
                ->column('medicationClass.name', label: 'Class')
                ->column('requires_prescription', label: 'Prescription Required')
                ->column('controlled_substance', label: 'Controlled')
                ->column('status', sortable: true)
                ->column('actions')
                ->paginate(10)
        ]);
    }

    public function create()
    {
        return view('admin.medication-bases.create', [
            'medicationClasses' => MedicationClass::all()->sortBy('name'),
            'conditions' => Condition::all()->sortBy('name'),
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'generic_name' => 'required|string|max:255|unique:medication_bases',
            'medication_class_id' => 'required|exists:medication_classes,id',
            'type' => 'required|string',
            'description' => 'required|string',
            'requires_prescription' => 'boolean',
            'controlled_substance' => 'boolean',
            'contraindications' => 'nullable|string',
            'side_effects' => 'nullable|string',
            'interactions' => 'nullable|string',
            'pregnancy_category' => 'required|string|size:1',
            'breastfeeding_safe' => 'boolean',
            'black_box_warning' => 'nullable|string',
            'status' => 'required|in:active,discontinued,recalled',
            'primary_uses' => 'array',
            'primary_uses.*.condition_id' => 'required|exists:conditions,id',
            'primary_uses.*.primary_treatment' => 'required|string',
            'primary_uses.*.mechanism_of_action' => 'required|string',
            'off_label_uses' => 'array',
            'off_label_uses.*.condition_id' => 'exists:conditions,id',
            'off_label_uses.*.primary_treatment' => 'string',
            'off_label_uses.*.mechanism_of_action' => 'string',
            'dosage_information' => 'array',
            'dosage_information.*.use_type' => 'required|string',
            'dosage_information.*.starting_dose' => 'required|string',
            'dosage_information.*.other_dosages' => 'nullable|string',
        ]);

        $medication = MedicationBase::create($validated);

        if (!empty($validated['primary_uses'])) {
            foreach ($validated['primary_uses'] as $use) {
                $medication->primaryUses()->create($use);
            }
        }

        if (!empty($validated['off_label_uses'])) {
            foreach ($validated['off_label_uses'] as $use) {
                $medication->offLabelUses()->create($use);
            }
        }

        if (!empty($validated['dosage_information'])) {
            foreach ($validated['dosage_information'] as $dosage) {
                $medication->dosageInformation()->create($dosage);
            }
        }

        Toast::success('Medication created successfully.')->autoDismiss(3);
        return redirect()->route('admin.medication-bases.index');
    }

    public function edit(MedicationBase $medicationBase)
    {
        $medicationBase->load(['primaryUses', 'offLabelUses', 'dosageInformation']);
        
        return view('admin.medication-bases.edit', [
            'medication' => $medicationBase,
            'medicationClasses' => MedicationClass::all()->sortBy('name'),
            'conditions' => Condition::all()->sortBy('name'),
        ]);
    }

    public function update(Request $request, MedicationBase $medicationBase)
    {
        $validated = $request->validate([
            'generic_name' => 'required|string|max:255|unique:medication_bases,generic_name,' . $medicationBase->id,
            'medication_class_id' => 'required|exists:medication_classes,id',
            'type' => 'required|string',
            'description' => 'required|string',
            'requires_prescription' => 'boolean',
            'controlled_substance' => 'boolean',
            'contraindications' => 'nullable|string',
            'side_effects' => 'nullable|string',
            'interactions' => 'nullable|string',
            'pregnancy_category' => 'required|string|size:1',
            'breastfeeding_safe' => 'boolean',
            'black_box_warning' => 'nullable|string',
            // 'status' => 'required|in:active,discontinued,recalled',
            'primary_uses' => 'array',
            'primary_uses.*.id' => 'nullable|exists:primary_uses,id',
            'primary_uses.*.condition_id' => 'required|exists:conditions,id',
            'primary_uses.*.primary_treatment' => 'required|string',
            'primary_uses.*.mechanism_of_action' => 'required|string',
            'off_label_uses' => 'array',
            'off_label_uses.*.id' => 'nullable|exists:off_label_uses,id',
            'off_label_uses.*.condition_id' => 'exists:conditions,id',
            'off_label_uses.*.primary_treatment' => 'string',
            'off_label_uses.*.mechanism_of_action' => 'string',
            'dosage_information' => 'array',
            'dosage_information.*.id' => 'nullable|exists:dosage_information,id',
            'dosage_information.*.use_type' => 'required|string',
            'dosage_information.*.starting_dose' => 'required|string',
            'dosage_information.*.other_dosages' => 'nullable|string',
        ]);

        $medicationBase->update($validated);

        // Update or create primary uses
        $primaryUseIds = [];
        foreach ($validated['primary_uses'] as $use) {
            if (isset($use['id'])) {
                $medicationBase->primaryUses()->where('id', $use['id'])->update($use);
                $primaryUseIds[] = $use['id'];
            } else {
                $newUse = $medicationBase->primaryUses()->create($use);
                $primaryUseIds[] = $newUse->id;
            }
        }
        $medicationBase->primaryUses()->whereNotIn('id', $primaryUseIds)->delete();

        // Update or create off-label uses
        if (!empty($validated['off_label_uses'])) {
            $offLabelUseIds = [];
            foreach ($validated['off_label_uses'] as $use) {
                if (isset($use['id'])) {
                    $medicationBase->offLabelUses()->where('id', $use['id'])->update($use);
                    $offLabelUseIds[] = $use['id'];
                } else {
                    $newUse = $medicationBase->offLabelUses()->create($use);
                    $offLabelUseIds[] = $newUse->id;
                }
            }
            $medicationBase->offLabelUses()->whereNotIn('id', $offLabelUseIds)->delete();
        }

        if (!empty($validated['dosage_information'])) {
            // Update or create dosage information
            $dosageIds = [];
            foreach ($validated['dosage_information'] as $dosage) {
                if (isset($dosage['id'])) {
                    $medicationBase->dosageInformation()->where('id', $dosage['id'])->update($dosage);
                    $dosageIds[] = $dosage['id'];
                } else {
                    $newDosage = $medicationBase->dosageInformation()->create($dosage);
                    $dosageIds[] = $newDosage->id;
                }
            }
            $medicationBase->dosageInformation()->whereNotIn('id', $dosageIds)->delete();
        }

        Toast::success('Medication updated successfully.')->autoDismiss(3);
        return redirect()->route('admin.medication-bases.index');
    }

    public function destroy(MedicationBase $medicationBase)
    {
        $medicationBase->primaryUses()->delete();
        $medicationBase->offLabelUses()->delete();
        $medicationBase->dosageInformation()->delete();
        $medicationBase->delete();

        Toast::success('Medication deleted successfully.')->autoDismiss(3);
        return redirect()->route('admin.medication-bases.index');
    }
}
