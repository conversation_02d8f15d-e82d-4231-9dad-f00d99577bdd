<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Condition;
use App\Models\Medication;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationConditionMappingController extends Controller
{
    /**
     * Display a listing of the medication-condition mappings.
     */
    public function index()
    {
        return view('admin.medication-condition-mapping.index', [
            'medications' => SpladeTable::for(Medication::class)
                ->withGlobalSearch(columns: ['name', 'generic_name'])
                ->column('name', sortable: true, searchable: true)
                ->column('generic_name', sortable: true, searchable: true)
                ->column('type')
                ->column(
                    key: 'conditions_count',
                    label: 'Mapped Conditions',
                    as: fn ($medication) => $medication->conditions()->count()
                )
                ->column('actions')
                ->paginate(10),
        ]);
    }

    /**
     * Show the form for editing the medication-condition mappings.
     */
    public function edit(Medication $medication)
    {
        $medication->load('conditions');
        $conditions = Condition::orderBy('name')->get();
        $mappedConditionIds = $medication->conditions->pluck('id')->toArray();
        
        return view('admin.medication-condition-mapping.edit', [
            'medication' => $medication,
            'conditions' => $conditions,
            'mappedConditionIds' => $mappedConditionIds,
        ]);
    }

    /**
     * Update the medication-condition mappings.
     */
    public function update(Request $request, Medication $medication)
    {
        $validated = $request->validate([
            'condition_ids' => 'array',
            'condition_ids.*' => 'exists:conditions,id',
            'is_primary_use' => 'array',
            'is_primary_use.*' => 'boolean',
        ]);

        // Get the condition IDs from the request
        $conditionIds = $validated['condition_ids'] ?? [];
        
        // Prepare the pivot data with is_primary_use values
        $pivotData = [];
        foreach ($conditionIds as $conditionId) {
            $isPrimaryUse = isset($validated['is_primary_use'][$conditionId]) && $validated['is_primary_use'][$conditionId];
            $pivotData[$conditionId] = ['is_primary_use' => $isPrimaryUse];
        }
        
        // Sync the conditions with the pivot data
        $medication->conditions()->sync($pivotData);

        Toast::success('Medication-condition mappings updated successfully.')->autoDismiss(3);
        return redirect()->route('admin.medication-condition-mapping.index');
    }
}
