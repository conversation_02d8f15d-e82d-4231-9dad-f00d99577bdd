<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;
use Spatie\Permission\Models\Role;

class MedicationOrderController extends Controller
{
    /**
     * Display a listing of all medication orders.
     */
    public function index()
    {
        $orders = SpladeTable::for(MedicationOrder::class)
            ->column('id', label: 'Order #')
            ->column('patient_id', label: 'Patient', as: fn ($patient_id) => User::find($patient_id)->name)
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status')
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id) => $doctor_id ? User::find($doctor_id)->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('admin.medication-orders.show', $order))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('admin.medication-orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Display the specified medication order.
     */
    public function show(MedicationOrder $medicationOrder)
    {
        $medicationOrder->load(['items.medication', 'patient', 'doctor', 'prescription.items']);

        return view('admin.medication-orders.show', [
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for assigning a doctor to the medication order.
     */
    public function assignDoctorForm(MedicationOrder $medicationOrder)
    {
        if ($medicationOrder->status !== 'pending') {
            Toast::warning('This order already has a doctor assigned.');
            return redirect()->route('admin.medication-orders.show', $medicationOrder);
        }

        $doctorRole = Role::where('name', 'doctor')->first();
        $doctors = User::role($doctorRole)->where('status', 'active')->get();

        return view('admin.medication-orders.assign-doctor', [
            'order' => $medicationOrder,
            'doctors' => $doctors,
        ]);
    }

    /**
     * Assign a doctor to the medication order.
     */
    public function assignDoctor(Request $request, MedicationOrder $medicationOrder)
    {
        $request->validate([
            'doctor_id' => 'required|exists:users,id',
        ]);

        if ($medicationOrder->status !== 'pending') {
            Toast::warning('This order already has a doctor assigned.');
            return redirect()->route('admin.medication-orders.show', $medicationOrder);
        }

        $doctor = User::findOrFail($request->doctor_id);

        // Ensure the selected user has the doctor role
        if (!$doctor->hasRole('doctor')) {
            Toast::warning('The selected user is not a doctor.');
            return redirect()->back()->withInput();
        }

        $medicationOrder->assignDoctor($doctor);

        Toast::success('Doctor assigned successfully.');
        return redirect()->route('admin.medication-orders.show', $medicationOrder);
    }

    /**
     * Remove the specified medication order from storage.
     */
    public function destroy(MedicationOrder $medicationOrder)
    {
        // Only allow deletion of pending orders
        if ($medicationOrder->status !== MedicationOrder::STATUS_PENDING) {
            Toast::warning('Only pending orders can be deleted.');
            return redirect()->route('admin.medication-orders.index');
        }

        // Delete the medication order items first
        $medicationOrder->items()->delete();

        // Delete the medication order
        $medicationOrder->delete();

        Toast::success('Medication order deleted successfully.');
        return redirect()->route('admin.medication-orders.index');
    }
}
