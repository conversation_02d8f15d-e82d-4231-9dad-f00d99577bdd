<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\MedicationVariantRequest;
use App\Models\MedicationBase;
use App\Models\MedicationVariant;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationVariantsController extends Controller
{
    public function index()
    {
        return view('admin.medication-variants.index', [
            'variants' => SpladeTable::for(MedicationVariant::class)
                ->withGlobalSearch(columns: ['brand_name', 'manufacturer', 'strength', 'dosage_form'])
                ->column('brand_name', sortable: true, searchable: true)
                ->column('manufacturer', sortable: true, searchable: true)
                ->column('medicationBase.generic_name', label: 'Generic Name', searchable: true)
                ->column('strength', sortable: true)
                ->column('dosage_form', sortable: true)
                ->column('route_of_administration')
                ->column('unit_price', sortable: true)
                ->column('is_usual_dosage', label: 'Usual Dosage')
                ->column('actions')
                ->paginate(10),
        ]);
    }

    public function create()
    {
        $medicationBases = MedicationBase::select('id', 'generic_name')->get()->sortBy('generic_name');
        
        return view('admin.medication-variants.create', [
            'medicationBases' => $medicationBases,
            'dosageForms' => [
                'tablet' => 'Tablet',
                'capsule' => 'Capsule',
                'liquid' => 'Liquid',
                'injection' => 'Injection',
                'topical' => 'Topical',
                'inhaler' => 'Inhaler',
            ],
            'routes' => [
                'oral' => 'Oral',
                'intravenous' => 'Intravenous',
                'intramuscular' => 'Intramuscular',
                'topical' => 'Topical',
                'inhalation' => 'Inhalation',
                'sublingual' => 'Sublingual',
            ],
        ]);
    }

    public function store(MedicationVariantRequest $request)
    {
        try {
            $validated = $request->validated();
            
            // Convert price to decimal
            $validated['unit_price'] = number_format((float)$validated['unit_price'], 2, '.', '');
            
            MedicationVariant::create($validated);
            
            Toast::success('Medication variant created successfully.');
            return redirect()->route('admin.medication-variants.index');
        } catch (\Exception $e) {
            Toast::danger('Error creating medication variant: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function edit(MedicationVariant $medicationVariant)
    {
        $medicationBases = MedicationBase::select('id', 'generic_name')->get()->sortBy('generic_name');
        
        return view('admin.medication-variants.edit', [
            'variant' => $medicationVariant,
            'medicationBases' => $medicationBases,
            'dosageForms' => [
                'tablet' => 'Tablet',
                'capsule' => 'Capsule',
                'liquid' => 'Liquid',
                'injection' => 'Injection',
                'topical' => 'Topical',
                'inhaler' => 'Inhaler',
            ],
            'routes' => [
                'oral' => 'Oral',
                'intravenous' => 'Intravenous',
                'intramuscular' => 'Intramuscular',
                'topical' => 'Topical',
                'inhalation' => 'Inhalation',
                'sublingual' => 'Sublingual',
            ],
        ]);
    }

    public function update(MedicationVariantRequest $request, MedicationVariant $medicationVariant)
    {
        try {
            $validated = $request->validated();
            
            // Convert price to decimal
            $validated['unit_price'] = number_format((float)$validated['unit_price'], 2, '.', '');
            
            $medicationVariant->update($validated);
            
            Toast::success('Medication variant updated successfully.');
            return redirect()->route('admin.medication-variants.index');
        } catch (\Exception $e) {
            Toast::danger('Error updating medication variant: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function destroy(MedicationVariant $medicationVariant)
    {
        try {
            $medicationVariant->delete();
            Toast::success('Medication variant deleted successfully.');
            return redirect()->route('admin.medication-variants.index');
        } catch (\Exception $e) {
            Toast::danger('Error deleting medication variant: ' . $e->getMessage());
            return back();
        }
    }

    public function show(MedicationVariant $medicationVariant)
    {
        return view('admin.medication-variants.show', [
            'variant' => $medicationVariant->load('medicationBase'),
        ]);
    }
}
