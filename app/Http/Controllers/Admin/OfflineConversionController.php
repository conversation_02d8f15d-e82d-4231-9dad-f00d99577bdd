<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OfflineConversion;
use App\Tables\OfflineConversions;

class OfflineConversionController extends Controller
{
    public function index()
    {
        return view('admin.offline-conversions.index', ['conversions' => OfflineConversions::class]);
    }

    // public function export()
    // {
    //     $conversions = OfflineConversion::where('is_synced', false)->get();
    
    //     $csv = "Parameters:TimeZone=UTC\n";
    //     $csv .= "Google Click ID,Conversion Name,Conversion Time,Conversion Value,Conversion Currency,Ad User Data,Ad Personalization\n";
    
    //     foreach ($conversions as $conversion) {
    //         $csv .= implode(',', [
    //             $conversion->gclid ?? '',
    //             $conversion->conversion_name,
    //             $conversion->created_at->format('Y-m-d H:i:s'),
    //             $conversion->conversion_value,
    //             $conversion->conversion_currency,
    //             '', // Ad User Data - left empty
    //             ''  // Ad Personalization - left empty
    //         ]) . "\n";
    //     }
    
    //     // Mark as synced
    //     $conversions->each->update(['is_synced' => true]);
    
    //     // Set headers for CSV download
    //     $headers = [
    //         'Content-Type' => 'text/csv',
    //         'Content-Disposition' => 'attachment; filename="offline_conversions_' . now()->format('Y-m-d_His') . '.csv"',
    //         'Pragma' => 'no-cache',
    //         'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
    //         'Expires' => '0'
    //     ];

    //     // Use Splade's download method
    //     return response($csv, 200, $headers)
    //         ->header('X-Splade-Download', true);
    // }
}
