<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DoctorPatientAssignment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use ProtoneMedia\Splade\Facades\Toast;
use App\Policies\AssignPatientPolicy;

class PatientAssignmentController extends Controller
{
    public function assignForm(Request $request, User $patient)
    {
        $doctors = User::role('doctor')->get();

        return view('admin.assign-patient', compact('patient', 'doctors'));
    }

    public function assign(Request $request, User $patient)
    {
        $request->validate([
            'doctor_id' => 'required|exists:users,id',
        ]);

        DoctorPatientAssignment::create([
            'doctor_id' => $request->doctor_id,
            'patient_id' => $patient->id,
            'assigned_by' => auth()->id(),
        ]);

        Toast::success('Patient assigned to doctor successfully.')->autoDismiss(10);

        return redirect()->route('admin.users.patients');
    }
}
