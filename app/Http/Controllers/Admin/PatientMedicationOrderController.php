<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Medication;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class PatientMedicationOrderController extends Controller
{
    /**
     * Display a listing of the patient's medication orders.
     */
    public function index(User $patient)
    {
        // Create a query builder for the medication orders
        $query = MedicationOrder::where('patient_id', $patient->id);

        $orders = SpladeTable::for($query)
            ->column('id', label: 'Order #')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status', as: fn ($status) => MedicationOrder::STATUSES[$status] ?? $status)
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $order) => $order->doctor ? $order->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('admin.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $order]))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('admin.patients.medication-orders.index', [
            'patient' => $patient,
            'orders' => $orders,
        ]);
    }

    /**
     * Show the form for creating a new medication order.
     */
    public function create(User $patient)
    {
        $medications = Medication::where('status', 'active')->orderBy('name')->get();

        return view('admin.patients.medication-orders.create', [
            'patient' => $patient,
            'medications' => $medications,
        ]);
    }

    /**
     * Store a newly created medication order in storage.
     */
    public function store(Request $request, User $patient)
    {
        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        // Create the medication order
        $order = MedicationOrder::create([
            'patient_id' => $patient->id,
            'status' => 'pending',
            'patient_notes' => $request->patient_notes,
        ]);

        // Create the medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $order->id,
                'medication_id' => $medication['medication_id'],
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order created successfully.');
        return redirect()->route('admin.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $order]);
    }

    /**
     * Display the specified medication order.
     */
    public function show(User $patient, MedicationOrder $medicationOrder)
    {
        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        $medicationOrder->load(['items.medication', 'doctor', 'prescription.items']);

        return view('admin.patients.medication-orders.show', [
            'patient' => $patient,
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for editing the specified medication order.
     */
    public function edit(User $patient, MedicationOrder $medicationOrder)
    {
        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        $medications = Medication::where('status', 'active')->orderBy('name')->get();

        return view('admin.patients.medication-orders.edit', [
            'patient' => $patient,
            'order' => $medicationOrder,
            'medications' => $medications,
        ]);
    }

    /**
     * Update the specified medication order in storage.
     */
    public function update(Request $request, User $patient, MedicationOrder $medicationOrder)
    {
        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        // Update the medication order
        $medicationOrder->update([
            'patient_notes' => $request->patient_notes,
        ]);

        // Delete existing items
        $medicationOrder->items()->delete();

        // Create new medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $medicationOrder->id,
                'medication_id' => $medication['medication_id'],
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order updated successfully.');
        return redirect()->route('admin.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $medicationOrder]);
    }
}
