<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreditCardRequest;
use App\Models\CreditCard;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use App\Services\AuthorizeNetService;
use App\Services\CreditCardService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentController extends Controller
{
    public function __construct(
        private CreditCardService $creditCardService,
        private AuthorizeNetService $authorizeNetService
    ) {}

    public function chargeCcForm(User $patient)
    {
        $plans = SubscriptionPlan::get();
        return view('admin.payment.charge-cc', compact('patient', 'plans'));
    }
    public function chargeCc(CreditCardRequest $request, User $patient)
    {
        if (is_null($request->plan)) {
            Toast::danger('Please select a plan');
            return back();
        }

        $ccExisted = CreditCard::where('user_id', $patient->id)
            ->where('last_four', $request->credit_card_number)
            ->where('expiration_month', $request->date('expiration_date')->format('m'))
            ->where('expiration_year', $request->date('expiration_date')->format('Y'))
            ->where('cvv', $request->cvv)
            ->first();

        DB::beginTransaction();

        try {
            if (!$ccExisted) {
                $creditCard = $this->creditCardService->addCreditCard(
                    $patient,
                    $request->input('credit_card_number'),
                    $request->date('expiration_date')->format('m'),
                    $request->date('expiration_date')->format('Y'),
                    $request->input('cvv')
                );

                // Log before checking properties
                Log::debug('Credit Card Response:', [
                    'is_null' => is_null($creditCard),
                    'class' => $creditCard ? get_class($creditCard) : 'null',
                    'attributes' => $creditCard ? $creditCard->toArray() : []
                ]);

                // Guard against null
                if (!$creditCard) {
                    throw new \Exception('Failed to process credit card');
                }
            }

            $plan = SubscriptionPlan::find($request->plan);
            $paymentResult = $this->authorizeNetService->chargeCreditCard($plan->price, $patient);

            if ($paymentResult['success']) {
                // Check if the patient has insurance
                $hasInsurance = $request->has('has_insurance');

                $metaData = [
                    'plan_id' => $plan->id,
                    'amount' => $plan->price,
                    'has_insurance' => $hasInsurance,
                ];

                // Calculate the price based on insurance
                $price = $hasInsurance && $plan->active_discount
                    ? $plan->active_discount->getDiscountedAmount($plan->price)
                    : $plan->price;

                $subscription = $patient->subscriptions()->create([
                    'plan_id' => $request->plan,
                    'starts_at' => now(),
                    'ends_at' => now()->addMonths($plan->duration_months),
                    'status' => 'active',
                    'is_discounted' => $hasInsurance,
                    'discounted_price' => $hasInsurance ? $price : null,
                    'meta_data' => $metaData,
                ]);
                Log::info('Subscription created: ' . json_encode($subscription));

                // Update the transaction record with the subscription ID
                Transaction::where('transaction_id', $paymentResult['transaction_id'])
                    ->update([
                        'subscription_id' => $subscription->id,
                        'is_discounted' => $hasInsurance, // Only set to true if patient has insurance
                    ]);

                DB::commit();

                Toast::info('Payment successful.')->autoDismiss();
                Log::info('Payment process completed successfully for user: ' . $patient->id);
                return back();
            } else {
                Log::error('Payment failed for user: ' . $patient->id . '. Error: ' . $paymentResult['error']);
                Toast::danger('Payment failed: ' . $paymentResult['error'])->autoDismiss();
                return back()->with('error', 'Payment failed: ' . $paymentResult['error']);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Exception in payment process: ' . $e->getMessage());
            Toast::danger('An error occurred during payment processing. Please try again or contact support.')->autoDismiss();
            return back();
        }
    }
}
