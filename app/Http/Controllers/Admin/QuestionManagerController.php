<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\{Question, QuestionOption, Service};
use Illuminate\Http\Request;

class QuestionManagerController extends Controller
{
    public function index()
    {
        $services = Service::orderBy('name')->get();
        return view('admin.question-manager', compact('services'));
    }

    public function getQuestions($serviceId)
    {
        $questions = Question::where('service_id', $serviceId)
            ->with('options')
            ->orderBy('order')
            ->get();

        return response()->json($questions);
    }

    public function createQuestion(Request $request)
    {
        $validated = $request->validate([
            'service_id' => 'required|exists:services,id',
            'question' => 'required|string|max:255',
            'type' => 'required|in:text,textarea,select,radio,checkbox',
            'required' => 'boolean',
        ]);

        $maxOrder = Question::where('service_id', $validated['service_id'])->max('order') ?? 0;

        $question = Question::create([
            'service_id' => $validated['service_id'],
            'question' => $validated['question'],
            'type' => $validated['type'],
            'required' => $validated['required'],
            'order' => $maxOrder + 1,
        ]);

        return response()->json($question);
    }

    
    public function editQuestion(Question $question)
    {
        // $question = Question::orderBy('name')->get();
        info($question);
        return view('admin.edit-question', compact('question'));
    }

    public function updateQuestion(Request $request, Question $question)
    {
        $validated = $request->validate([
            'question' => 'required|string|max:255',
            'type' => 'required|in:text,textarea,select,radio,checkbox',
            'required' => 'boolean',
        ]);

        $question->update($validated);

        return response()->json($question);
    }

    public function deleteQuestion(Question $question)
    {
        $question->delete();
        return response()->json(['success' => true]);
    }

    public function createOption(Request $request, Question $question)
    {
        $validated = $request->validate([
            'option_value' => 'required|string|max:255',
        ]);

        $maxOrder = $question->options()->max('order') ?? 0;

        $option = $question->options()->create([
            'option_value' => $validated['option_value'],
            'order' => $maxOrder + 1,
        ]);

        return response()->json($option);
    }

    public function deleteOption(QuestionOption $option)
    {
        $option->delete();
        return response()->json(['success' => true]);
    }

    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'questions' => 'required|array',
            'questions.*.id' => 'required|exists:questions,id',
            'questions.*.order' => 'required|integer|min:1',
        ]);

        foreach ($validated['questions'] as $questionData) {
            Question::where('id', $questionData['id'])->update(['order' => $questionData['order']]);
        }

        return response()->json(['success' => true]);
    }

    public function updateOptionOrder(Request $request, Question $question)
    {
        $validated = $request->validate([
            'options' => 'required|array',
            'options.*.id' => 'required|exists:question_options,id',
            'options.*.order' => 'required|integer|min:1',
        ]);

        foreach ($validated['options'] as $optionData) {
            QuestionOption::where('id', $optionData['id'])->update(['order' => $optionData['order']]);
        }

        return response()->json(['success' => true]);
    }

    public function showQuestionsByService()
    {
        $questionsByService = $this->getQuestionsByService();
        return view('admin.tree', compact('questionsByService'));
    }

    public function getQuestionsByService()
    {
        $services = Service::with(['questions' => function ($query) {
            $query->whereNull('parent_question_id')->with('options', 'children.options', 'children.children.options');
        }])->get();

        $questionsByService = [];

        foreach ($services as $service) {
            $questionsByService[$service->name] = $service->questions->map(function ($question) {
                return $this->formatQuestionTree($question);
            });
        }

        return $questionsByService;
    }

    public function showForm()
    {
        $questionTree = $this->getQuestionTree();

        return view('admin.tree', compact('questionTree'));
    }
    
    public function getQuestionTree()
    {
        $rootQuestion = Question::with(['options', 'children.options', 'children.children.options'])
            ->whereNull('parent_question_id')
            ->first();

        return $this->formatQuestionTree($rootQuestion);
    }

    public function exportQuestionTree()
    {
        $questionTree = [
            'question' => 'Is there a specific mental health medication you are interested in?',
            'help' => 'It is helpful for the provider to know if you have a specific treatment preference.',
            'type' => 'select',
            'options' => [
                'yes' => [
                    'question' => 'What medication are you interested in?',
                    'type' => 'select',
                    'options' => [
                        'bupropion' => 'bupropion (wellbutrin)',
                        'buspirone' => 'buspirone (BuSpar)',
                        'citalopram' => 'citalopram (Celexa)',
                        'duloxetine' => 'duloxetine (Cymbalta)',
                        'escitalopram' => 'escitalopram (Lexapro)',
                        'flouxetine' => 'flouxetine (prozac)',
                        'propranolol' => 'propranolol',
                        'sertraline' => 'sertraline (zoloft)',
                        'venlafaxine' => 'venlafaxine (effexor)',
                        'other' => [
                            'question' => 'What other medication are you interested in?',
                            'type' => 'textarea'
                        ]
                    ]
                ],
                'no' => null
            ]
        ];

        return $questionTree;
    }
    
    private function formatQuestionTree($question)
    {
        $formattedQuestion = [
            'question' => $question->question,
            'help' => $question->additional_text,
            'type' => $question->type,
            'options' => []
        ];

        foreach ($question->options as $option) {
            $childQuestion = $question->children->where('id', $option->option_value)->first();
            
            if ($childQuestion) {
                $formattedQuestion['options'][$option->option_value] = $this->formatQuestionTree($childQuestion);
            } else {
                $formattedQuestion['options'][$option->option_value] = $option->label;
            }
        }

        return $formattedQuestion;
    }
}