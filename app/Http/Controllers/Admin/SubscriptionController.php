<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Api\AuthorizeNetService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class SubscriptionController extends Controller
{
    public function index()
    {
        return view('admin.subscriptions.index', [
            'subscriptions' => SpladeTable::for(Subscription::with(['user', 'plan'])->orderByDesc('created_at'))
                ->column('id')
                ->column('user.name', label: 'User')
                ->column('plan.name', label: 'Plan')
                ->column('starts_at', label: 'Start Date')
                ->column('ends_at', label: 'End Date')
                ->column('status', label: 'Status')
                ->column('is_discounted', label: 'Discounted', as: function ($isDiscounted) {
                    return $isDiscounted
                        ? '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes (Insurance)</span>'
                        : '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">No</span>';
                })
                ->column('discounted_price', label: 'Discounted Price')
                ->column('actions')
                ->paginate(10)
                ->defaultSort('created_at', 'desc')
                ->searchInput('user.name')
                ->searchInput('plan.name')
                ->selectFilter('status', [
                    'active' => 'Active',
                    'inactive' => 'Inactive',
                    'cancelled' => 'Cancelled',
                    'expired' => 'Expired',
                ])
                ->selectFilter('is_discounted', [
                    '1' => 'Yes (Insurance)',
                    '0' => 'No',
                ]),
        ]);
    }

    public function show(Subscription $subscription)
    {
        $subscription->load(['user', 'plan', 'transactions']);
        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Activate an inactive subscription
     */
    public function activate(Subscription $subscription)
    {
        if ($subscription->status !== 'active') {
            $subscription->status = 'active';
            $subscription->save();

            Toast::success('Subscription activated successfully.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        Toast::danger('Subscription is already active.')
            ->autoDismiss(5);

        return redirect()->route('admin.subscriptions.show', $subscription);
    }

    /**
     * Deactivate an active subscription
     */
    public function deactivate(Subscription $subscription)
    {
        if ($subscription->status === 'active') {
            $subscription->status = 'inactive';
            $subscription->save();

            Toast::success('Subscription deactivated successfully.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        Toast::danger('Subscription is not active.')
            ->leftTop()
            ->autoDismiss(5);

        return redirect()->route('admin.subscriptions.show', $subscription);
    }

    /**
     * Cancel a subscription
     */
    public function cancel(Subscription $subscription)
    {
        if ($subscription->status !== 'cancelled') {
            $subscription->status = 'cancelled';
            $subscription->cancelled_at = now();
            $subscription->save();

            Toast::success('Subscription cancelled successfully.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        Toast::danger('Subscription is already cancelled.')
            ->autoDismiss(5);

        return redirect()->route('admin.subscriptions.show', $subscription);
    }

    /**
     * Administratively renew a subscription without payment
     *
     * This method allows administrators to extend a subscription's end date
     * without charging the customer. It's intended for customer service,
     * promotional extensions, or correcting subscription issues.
     */
    public function renew(Subscription $subscription, Request $request)
    {
        // Validate the renewal duration
        $request->validate([
            'duration' => 'required|integer|min:1|max:24',
        ]);

        // Set the new end date based on the current end date or now, whichever is later
        $startFrom = max($subscription->ends_at, now());
        $newEndDate = $startFrom->copy()->addMonths((int) $request->duration);

        $subscription->status = 'active';
        $subscription->cancelled_at = null;
        $subscription->ends_at = $newEndDate;
        $subscription->save();

        // Log this administrative action
        Log::info("Admin manually renewed subscription #{$subscription->id} for user {$subscription->user->name} until {$newEndDate->format('Y-m-d')} without payment");

        Toast::success("Subscription administratively renewed until {$newEndDate->format('M d, Y')} (no payment processed)")
            ->autoDismiss(5);

        return redirect()->route('admin.subscriptions.show', $subscription);
    }

    /**
     * Renew a subscription with payment
     *
     * This method charges the customer's default credit card and extends their subscription.
     */
    public function renewWithPayment(Subscription $subscription, Request $request, AuthorizeNetService $authorizeNetService)
    {
        // Validate the renewal duration
        $request->validate([
            'duration' => 'required|integer|min:1|max:12',
        ]);

        $user = $subscription->user;
        $plan = $subscription->plan;

        if (!$user || !$plan) {
            Toast::danger('Missing user or plan data for this subscription.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        // Check if user has a default payment method
        $defaultCreditCard = $user->creditCards()->where('is_default', true)->first();

        if (!$defaultCreditCard) {
            Log::warning("No default credit card found for user {$user->id}");

            Toast::danger('No default payment method found for this user. Please add a credit card and set it as default.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        // Get customer profile ID
        $customerProfileId = $user->authorize_net_customer_id;
        if (!$customerProfileId) {
            Log::warning("No Authorize.Net customer profile found for user {$user->id}");

            Toast::danger('No payment profile found for this user. Please create a payment profile first.')
                ->autoDismiss(5);

            return redirect()->route('admin.subscriptions.show', $subscription);
        }

        // Calculate the amount to charge
        $amount = $subscription->is_discounted && $subscription->discounted_price
            ? $subscription->discounted_price
            : $plan->price;

        // Create a pending transaction record
        // Make sure to use the subscription owner's user_id, not the admin's
        $transaction = Transaction::create([
            'user_id' => $subscription->user_id, // Use subscription's user_id to ensure it's the patient
            'subscription_id' => $subscription->id,
            'amount' => $amount,
            'currency' => 'USD',
            'payment_method' => 'credit_card',
            'status' => 'pending',
            'is_discounted' => $subscription->is_discounted,
        ]);

        // Log the admin who initiated this transaction
        Log::info("Admin user #{auth()->id()} initiated payment for subscription #{$subscription->id}");

        try {
            // Process the payment
            $paymentResult = $authorizeNetService->processTransaction(
                $amount,
                $customerProfileId,
                $defaultCreditCard->token,
                "Subscription Renewal: {$plan->name}",
                $subscription->user_id, // Pass the patient's user ID, not the admin's
                $subscription->id,
                $subscription->is_discounted
            );

            if ($paymentResult['success']) {
                // Check if a transaction with this ID already exists
                $transactionId = isset($paymentResult['transaction_id']) ? $paymentResult['transaction_id'] : null;
                $existingTransaction = null;

                if ($transactionId) {
                    $existingTransaction = Transaction::where('transaction_id', $transactionId)->first();
                }

                if ($existingTransaction) {
                    // If a transaction with this ID already exists, use that one and delete our pending one
                    Log::warning("Transaction ID {$transactionId} already exists in the database. Using existing transaction record.");
                    $transaction->delete();
                    $transaction = $existingTransaction;

                    // Make sure the existing transaction is marked as successful
                    if ($transaction->status !== 'success') {
                        $transaction->status = 'success';
                        $transaction->save();
                    }
                } else {
                    // Update transaction record with result
                    $transaction->transaction_id = $transactionId;
                    $transaction->status = 'success';
                    $transaction->save();
                }

                // Calculate new end date
                $startFrom = max($subscription->ends_at, now());
                $newEndDate = $startFrom->copy()->addMonths((int) $request->duration);

                // Update subscription
                $subscription->ends_at = $newEndDate;
                $subscription->status = 'active';
                $subscription->cancelled_at = null;
                $subscription->save();

                Log::info("Admin manually renewed subscription #{$subscription->id} for user {$user->name} until {$newEndDate->format('Y-m-d')} with payment of $" . $amount);

                Toast::success("Payment successful! Subscription renewed until {$newEndDate->format('M d, Y')}")
                    ->autoDismiss(5);
            } else {
                // Update transaction record with failure
                $transaction->status = 'failed';
                $transaction->error_message = isset($paymentResult['message']) ? $paymentResult['message'] : 'Unknown error';
                $transaction->save();

                $errorMessage = isset($paymentResult['message']) ? $paymentResult['message'] : 'Unknown error';
                Log::error("Payment failed for subscription #{$subscription->id}: {$errorMessage}");

                Toast::danger("Payment failed: {$errorMessage}")
                    ->autoDismiss(5);
            }
        } catch (\Exception $e) {
            // Update transaction record with failure
            $transaction->status = 'failed';
            $transaction->error_message = $e->getMessage();
            $transaction->save();

            Log::error("Error processing payment for subscription #{$subscription->id}: {$e->getMessage()}");

            Toast::danger("Error processing payment: {$e->getMessage()}")
                ->autoDismiss(5);
        }

        return redirect()->route('admin.subscriptions.show', $subscription);
    }
}
