<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class SubscriptionPlanController extends Controller
{
    public function index()
    {
        return view('admin.subscription-plans.index', [
            'plans' => SpladeTable::for(SubscriptionPlan::class)
                ->column('name')
                ->column('price')
                ->column('current_price', 'Current Price')
                ->column('duration_months')
                ->column('service_limit')
                ->column('is_active', label: 'Active')
                ->column('is_featured', label: 'Featured')
                ->column('display_order', label: 'Order')
                ->column('status')
                ->column('action')
                ->paginate(10),
        ]);
    }

    public function create()
    {
        return view('admin.subscription-plans.create-or-edit');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'duration_months' => 'required|integer|min:1',
            'service_limit' => 'nullable|integer|min:1',
            'status' => 'required|in:active,inactive',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'display_order' => 'integer|min:0',
        ]);

        // Set default values for checkboxes if not present
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        SubscriptionPlan::create($validated);

        Toast::success('Subscription plan created successfully.');
        return redirect()->route('admin.subscription-plans.index');
    }

    public function edit(SubscriptionPlan $subscriptionPlan)
    {
        return view('admin.subscription-plans.create-or-edit', compact('subscriptionPlan'));
    }

    public function update(Request $request, SubscriptionPlan $subscriptionPlan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'price' => 'required|numeric|min:0',
            'duration_months' => 'required|integer|min:1',
            'service_limit' => 'nullable|integer|min:1',
            'status' => 'required|in:active,inactive',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'display_order' => 'integer|min:0',
        ]);

        // Set default values for checkboxes if not present
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        $subscriptionPlan->update($validated);

        Toast::success('Subscription plan updated successfully.');
        return redirect()->route('admin.subscription-plans.index');
    }

    public function createDiscount(SubscriptionPlan $subscriptionPlan)
    {
        return view('admin.subscription-plans.create-discount', compact('subscriptionPlan'));
    }

    public function storeDiscount(Request $request, SubscriptionPlan $subscriptionPlan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        $subscriptionPlan->discounts()->create($validated);

        Toast::success('Discount added successfully.');
        return redirect()->route('admin.subscription-plans.index');
    }

    /**
     * Toggle the active status of a subscription plan.
     */
    public function toggleActive(SubscriptionPlan $subscriptionPlan)
    {
        $subscriptionPlan->update([
            'is_active' => !$subscriptionPlan->is_active
        ]);

        $status = $subscriptionPlan->is_active ? 'activated' : 'deactivated';
        Toast::success("Plan {$status} successfully.");
        return back();
    }

    /**
     * Toggle the featured status of a subscription plan.
     */
    public function toggleFeatured(SubscriptionPlan $subscriptionPlan)
    {
        $subscriptionPlan->update([
            'is_featured' => !$subscriptionPlan->is_featured
        ]);

        $status = $subscriptionPlan->is_featured ? 'featured' : 'unfeatured';
        Toast::success("Plan {$status} successfully.");
        return back();
    }

    /**
     * Update the display order of subscription plans.
     */
    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'plans' => 'required|array',
            'plans.*.id' => 'required|exists:subscription_plans,id',
            'plans.*.order' => 'required|integer|min:0',
        ]);

        foreach ($validated['plans'] as $plan) {
            SubscriptionPlan::where('id', $plan['id'])->update(['display_order' => $plan['order']]);
        }

        Toast::success('Plan order updated successfully.');
        return response()->json(['success' => true]);
    }
}
