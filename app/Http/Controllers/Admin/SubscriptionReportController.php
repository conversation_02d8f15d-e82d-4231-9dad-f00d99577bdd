<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\SpladeTable;

class SubscriptionReportController extends Controller
{
    /**
     * Display the subscription renewal dashboard.
     */
    public function index(Request $request)
    {
        // Get date range from request or use default (last 30 days)
        $startDate = $request->input('start_date') 
            ? Carbon::parse($request->input('start_date')) 
            : Carbon::now()->subDays(30)->startOfDay();
            
        $endDate = $request->input('end_date') 
            ? Carbon::parse($request->input('end_date'))->endOfDay() 
            : Carbon::now()->endOfDay();

        // Get subscription renewal transactions
        $transactions = Transaction::where('status', 'success')
            ->whereNotNull('subscription_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'subscription', 'subscription.plan'])
            ->get();

        // Get failed renewal transactions
        $failedTransactions = Transaction::where('status', 'failed')
            ->whereNotNull('subscription_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'subscription', 'subscription.plan'])
            ->get();

        // Calculate statistics
        $totalRenewals = $transactions->count();
        $totalRevenue = $transactions->sum('amount');
        $totalFailures = $failedTransactions->count();
        $failureRate = $totalRenewals + $totalFailures > 0 
            ? round(($totalFailures / ($totalRenewals + $totalFailures)) * 100, 2) 
            : 0;

        // Group by day for chart data
        $renewalsByDay = $transactions->groupBy(function ($transaction) {
            return $transaction->created_at->format('Y-m-d');
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'revenue' => $group->sum('amount')
            ];
        });

        // Prepare chart data
        $chartDates = [];
        $chartCounts = [];
        $chartRevenue = [];

        // Fill in all dates in the range
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $chartDates[] = $dateString;
            
            if (isset($renewalsByDay[$dateString])) {
                $chartCounts[] = $renewalsByDay[$dateString]['count'];
                $chartRevenue[] = $renewalsByDay[$dateString]['revenue'];
            } else {
                $chartCounts[] = 0;
                $chartRevenue[] = 0;
            }
            
            $currentDate->addDay();
        }

        // Get upcoming renewals
        $upcomingRenewals = Subscription::where('status', 'active')
            ->where('ends_at', '>=', Carbon::now())
            ->where('ends_at', '<=', Carbon::now()->addDays(30))
            ->with(['user', 'plan'])
            ->orderBy('ends_at')
            ->get();

        // Get recent transactions for the table
        $recentTransactions = Transaction::whereNotNull('subscription_id')
            ->with(['user', 'subscription', 'subscription.plan'])
            ->orderByDesc('created_at')
            ->limit(100);

        return view('admin.reports.subscriptions', [
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
            'totalRenewals' => $totalRenewals,
            'totalRevenue' => $totalRevenue,
            'totalFailures' => $totalFailures,
            'failureRate' => $failureRate,
            'chartDates' => json_encode($chartDates),
            'chartCounts' => json_encode($chartCounts),
            'chartRevenue' => json_encode($chartRevenue),
            'upcomingRenewals' => $upcomingRenewals,
            'transactions' => SpladeTable::for($recentTransactions)
                ->column('id', sortable: true)
                ->column('user.name', label: 'User', sortable: true)
                ->column('subscription.plan.name', label: 'Plan')
                ->column('amount', sortable: true)
                ->column('status', sortable: true)
                ->column('created_at', label: 'Date', sortable: true)
                ->column('transaction_id', label: 'Transaction ID')
                ->column('error_message', label: 'Error Message')
                ->paginate(10)
                ->defaultSort('created_at', 'desc')
                ->searchInput('user.name')
                ->selectFilter('status', [
                    'success' => 'Success',
                    'failed' => 'Failed',
                    'pending' => 'Pending',
                ]),
        ]);
    }

    /**
     * Export subscription renewal data.
     */
    public function export(Request $request)
    {
        // Get date range from request
        $startDate = $request->input('start_date') 
            ? Carbon::parse($request->input('start_date')) 
            : Carbon::now()->subDays(30)->startOfDay();
            
        $endDate = $request->input('end_date') 
            ? Carbon::parse($request->input('end_date'))->endOfDay() 
            : Carbon::now()->endOfDay();

        // Get subscription renewal transactions
        $transactions = Transaction::whereNotNull('subscription_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['user', 'subscription', 'subscription.plan'])
            ->get();

        // Generate CSV
        $filename = 'subscription_renewals_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($transactions) {
            $file = fopen('php://output', 'w');
            
            // Add CSV header
            fputcsv($file, [
                'Transaction ID',
                'Date',
                'User ID',
                'User Name',
                'User Email',
                'Subscription ID',
                'Plan',
                'Amount',
                'Status',
                'Error Message'
            ]);
            
            // Add data rows
            foreach ($transactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->created_at->format('Y-m-d H:i:s'),
                    $transaction->user_id,
                    $transaction->user ? $transaction->user->name : 'N/A',
                    $transaction->user ? $transaction->user->email : 'N/A',
                    $transaction->subscription_id,
                    $transaction->subscription && $transaction->subscription->plan 
                        ? $transaction->subscription->plan->name 
                        : 'N/A',
                    $transaction->amount,
                    $transaction->status,
                    $transaction->error_message
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
