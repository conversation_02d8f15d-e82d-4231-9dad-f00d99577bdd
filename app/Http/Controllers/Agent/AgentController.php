<?php

namespace App\Http\Controllers\Agent;

use App\Actions\Agents\CalculateCommissionAction;
use App\Actions\Agents\GenerateReferralLinkAction;
use App\Actions\Agents\GetDirectReferralsAction;
use App\Actions\Agents\RegisterAgentAction;
use App\Actions\Agents\ValidateReferralTokenAction;
use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentCommission;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class AgentController extends Controller
{
    protected $registerAgentAction;
    protected $generateReferralLinkAction;
    protected $validateReferralTokenAction;
    protected $calculateCommissionAction;
    protected $getDirectReferralsAction;

    public function __construct(
        RegisterAgentAction $registerAgentAction,
        GenerateReferralLinkAction $generateReferralLinkAction,
        ValidateReferralTokenAction $validateReferralTokenAction,
        CalculateCommissionAction $calculateCommissionAction,
        GetDirectReferralsAction $getDirectReferralsAction
    ) {
        $this->registerAgentAction = $registerAgentAction;
        $this->generateReferralLinkAction = $generateReferralLinkAction;
        $this->validateReferralTokenAction = $validateReferralTokenAction;
        $this->calculateCommissionAction = $calculateCommissionAction;
        $this->getDirectReferralsAction = $getDirectReferralsAction;
    }

    /**
     * Display the agent dashboard.
     */
    public function dashboard(Request $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        // Get profile completion data from middleware
        $showProfileCompletionModal = $request->get('show_profile_completion_modal', false);
        $missingProfileFields = $request->get('missing_profile_fields', []);

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        // Generate referral link
        $referralResult = $this->generateReferralLinkAction->execute([
            'agent_id' => $agent->id
        ]);

        // Get agent's referrals
        $referrals = $agent->referrals()->with('user')->get();

        // Get agent's commissions
        $commissions = $agent->commissions()
            ->with(['transaction', 'subscription'])
            ->latest()
            ->paginate(10);

        // Get upline commissions
        $uplineCommissions = $agent->uplineCommissions()
            ->with(['agent.user', 'transaction', 'subscription'])
            ->latest()
            ->paginate(10);

        // Calculate total earnings
        $totalEarnings = $agent->commissions()->sum('commission_amount');
        $totalUplineEarnings = $agent->uplineCommissions()->sum('upline_commission_amount');

        // Get recent patient enrollments
        $recentPatientEnrollments = Subscription::where('agent_id', $agent->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get patient IDs enrolled by this agent
        $patientIds = Subscription::where('agent_id', $agent->id)
            ->pluck('user_id')
            ->toArray();

        // Get recent medical questionnaires from enrolled patients
        $recentQuestionnaires = \App\Models\MedicalQuestionnaire::whereIn('user_id', $patientIds)
            ->with(['user', 'treatment'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medical conditions from enrolled patients
        $recentMedicalConditions = \App\Models\MedicalCondition::whereIn('patient_id', $patientIds)
            ->with('patient')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medication orders from enrolled patients
        $recentMedicationOrders = \App\Models\MedicationOrder::whereIn('patient_id', $patientIds)
            ->with(['patient', 'items'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get counts for direct referrals
        $directPatientCount = $agent->referredPatients()
            ->whereHas('roles', function ($query) {
                $query->where('name', 'patient');
            })
            ->count();

        $directBusinessCount = $agent->referredBusinesses()->count();

        return view('agent.dashboard', compact(
            'agent',
            'referralResult',
            'referrals',
            'commissions',
            'uplineCommissions',
            'totalEarnings',
            'totalUplineEarnings',
            'recentPatientEnrollments',
            'recentQuestionnaires',
            'recentMedicalConditions',
            'recentMedicationOrders',
            'directPatientCount',
            'directBusinessCount',
            'showProfileCompletionModal',
            'missingProfileFields'
        ));
    }

    /**
     * Show the agent registration form.
     */
    public function showRegistrationForm(Request $request)
    {
        $referralToken = $request->query('agent_ref');
        $specifiedTier = $request->query('tier') ?? 'AGENT';
        $referringAgent = null;
        $allowedTiers = [];
        $selectedTier = 'AGENT'; // Default tier is AGENT

        if ($referralToken) {
            $result = $this->validateReferralTokenAction->execute([
                'token' => $referralToken,
                'tier' => $specifiedTier
            ]);

            if ($result['success']) {
                $referringAgent = $result['referring_agent'];
                $allowedTiers = $result['allowed_tiers'];

                // If a specific tier was specified and valid, use it as the selected tier
                if (isset($result['selected_tier'])) {
                    $selectedTier = $result['selected_tier'];
                    // If we have a valid selected tier, only show that tier as an option
                    $allowedTiers = $result['selected_tiers'];
                }
            }
        } else {
            // No referral, only include AGENT tier and other non-SFMO tiers for reference
            $allowedTiers = array_filter(Agent::TIERS, function($key) {
                return $key !== 'SFMO';
            }, ARRAY_FILTER_USE_KEY);
        }

        $default = ['referral_token' => $referralToken, 'selectedTier' => $selectedTier, 'tier' => $specifiedTier];

        return view('agent.register', compact('referringAgent', 'allowedTiers', 'referralToken', 'selectedTier', 'default'));
    }

    /**
     * Register a new agent.
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'company' => 'nullable|string|max:255',
            'experience' => 'required|string|in:new,1-3,3-5,5+',
            'tier' => 'required|string|in:' . implode(',', array_keys(Agent::TIERS)),
            'referral_token' => 'nullable|string'
        ]);

        $referringAgent = null;
        if ($request->referral_token) {
            $result = $this->validateReferralTokenAction->execute([
                'token' => $request->referral_token
            ]);

            if ($result['success']) {
                $referringAgent = $result['referring_agent'];
            }
        }

        $result = $this->registerAgentAction->execute([
            'name' => $request->name,
            'email' => $request->email,
            'password' => $request->password,
            'fname' => $request->fname,
            'lname' => $request->lname,
            'phone' => $request->phone,
            'company' => $request->company,
            'experience' => $request->experience,
            'tier' => $request->tier,
            'referring_agent_id' => $referringAgent ? $referringAgent->id : null
        ]);

        if ($result['success']) {
            Auth::login($result['user']);
            Toast::success($result['message'])->autoDismiss(10);
            return redirect()->route('agent.dashboard');
        } else {
            Toast::danger($result['message'])->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Show agent's referrals.
     */
    public function referrals()
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        $referrals = $agent->referrals()->with('user')->paginate(15);

        // Generate referral links for the referrals view
        $referralLinks = $agent->generateTierSpecificReferralUrls();

        return view('agent.referrals', compact('agent', 'referrals', 'referralLinks'));
    }

    /**
     * Show agent's commissions.
     */
    public function commissions()
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        $commissions = $agent->commissions()
            ->with(['transaction', 'subscription'])
            ->latest()
            ->paginate(15);

        $uplineCommissions = $agent->uplineCommissions()
            ->with(['agent.user', 'transaction', 'subscription'])
            ->latest()
            ->paginate(15);

        $totalEarnings = $agent->commissions()->sum('commission_amount');
        $totalUplineEarnings = $agent->uplineCommissions()->sum('upline_commission_amount');

        return view('agent.commissions', compact(
            'agent',
            'commissions',
            'uplineCommissions',
            'totalEarnings',
            'totalUplineEarnings'
        ));
    }

    /**
     * Show agent's direct referrals (patients and businesses).
     */
    public function directReferrals()
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        $result = $this->getDirectReferralsAction->execute([
            'agent' => $agent,
            'per_page' => 10
        ]);

        return view('agent.direct-referrals', [
            'agent' => $agent,
            'patients' => $result['patients'],
            'businesses' => $result['businesses'],
            'totalPatients' => $result['total_patients'],
            'totalBusinesses' => $result['total_businesses']
        ]);
    }

    /**
     * Display the agent help page.
     */
    public function help()
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        return view('agent.help', compact('agent'));
    }

    /**
     * Show enrollments made by referred agents.
     */
    public function referredEnrollments()
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('home');
        }

        // Get all agents referred by the current agent
        $referredAgents = $agent->referrals()->with('user')->get();

        // Get all agent IDs for querying enrollments
        $referredAgentIds = $referredAgents->pluck('id')->toArray();

        // If there are no referred agents, return early with empty collections
        if (empty($referredAgentIds)) {
            return view('agent.referred-enrollments', [
                'agent' => $agent,
                'referredAgents' => $referredAgents,
                'patientEnrollments' => collect([]),
                'businessEnrollments' => collect([]),
                'totalPatientEnrollments' => 0,
                'totalBusinessEnrollments' => 0,
                'totalCommissionAmount' => 0
            ]);
        }

        // Get patient enrollments from referred agents
        $patientEnrollments = Subscription::whereIn('agent_id', $referredAgentIds)
            ->where('user_type', 'patient')
            ->with(['user', 'plan', 'agent.user'])
            ->latest()
            ->paginate(10, ['*'], 'patient_page');

        // Get business enrollments from referred agents
        $businessEnrollments = Subscription::whereIn('agent_id', $referredAgentIds)
            ->where('user_type', 'business_admin')
            ->with(['user', 'plan', 'agent.user'])
            ->latest()
            ->paginate(10, ['*'], 'business_page');

        // Calculate total enrollments and commissions
        $totalPatientEnrollments = Subscription::whereIn('agent_id', $referredAgentIds)
            ->where('user_type', 'patient')
            ->count();

        $totalBusinessEnrollments = Subscription::whereIn('agent_id', $referredAgentIds)
            ->where('user_type', 'business_admin')
            ->count();

        // Get total commission amount from referred agents' enrollments
        $totalCommissionAmount = AgentCommission::whereIn('agent_id', $referredAgentIds)
            ->sum('commission_amount');

        return view('agent.referred-enrollments', [
            'agent' => $agent,
            'referredAgents' => $referredAgents,
            'patientEnrollments' => $patientEnrollments,
            'businessEnrollments' => $businessEnrollments,
            'totalPatientEnrollments' => $totalPatientEnrollments,
            'totalBusinessEnrollments' => $totalBusinessEnrollments,
            'totalCommissionAmount' => $totalCommissionAmount
        ]);
    }
}
