<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\Commission;
use App\Models\Referral;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\Splade\Facades\Toast;

class AnalyticsController extends Controller
{
    /**
     * Display the agent analytics dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to view analytics.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Get basic statistics
        $totalCommissions = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->sum('amount');
        
        $totalReferrals = Referral::where('referring_agent_id', $agent->id)->count();
        
        $successfulReferrals = Referral::where('referring_agent_id', $agent->id)
            ->where('status', 'converted')
            ->count();
        
        $conversionRate = $totalReferrals > 0 
            ? round(($successfulReferrals / $totalReferrals) * 100, 2) 
            : 0;
        
        // Get monthly commission data for the chart
        $monthlyCommissions = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
        
        // Format data for Chart.js
        $labels = [];
        $data = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthYear = $date->format('M Y');
            $labels[] = $monthYear;
            
            $found = false;
            foreach ($monthlyCommissions as $commission) {
                if ($commission->month == $date->month && $commission->year == $date->year) {
                    $data[] = $commission->total;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $data[] = 0;
            }
        }
        
        // Get referral source breakdown
        $referralSources = Referral::where('referring_agent_id', $agent->id)
            ->selectRaw('source, COUNT(*) as count')
            ->groupBy('source')
            ->get();
        
        // Get tier comparison data
        $tierData = Agent::where('tier', $agent->tier)
            ->join('commissions', 'agents.id', '=', 'commissions.agent_id')
            ->where('commissions.status', 'paid')
            ->where('commissions.created_at', '>=', Carbon::now()->subMonths(3))
            ->selectRaw('AVG(commissions.amount) as avg_commission, COUNT(DISTINCT agents.id) as agent_count')
            ->first();
        
        $avgTierCommission = $tierData->avg_commission ?? 0;
        $agentCount = $tierData->agent_count ?? 0;
        
        // Get referral network data
        $referralNetwork = Agent::where('referring_agent_id', $agent->id)
            ->with('user:id,name')
            ->get(['id', 'user_id', 'tier', 'status', 'created_at']);
        
        return view('agent.analytics.dashboard', compact(
            'totalCommissions',
            'totalReferrals',
            'successfulReferrals',
            'conversionRate',
            'labels',
            'data',
            'referralSources',
            'avgTierCommission',
            'agentCount',
            'referralNetwork'
        ));
    }
    
    /**
     * Display detailed commission analytics.
     *
     * @return \Illuminate\View\View
     */
    public function commissionAnalytics()
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to view commission analytics.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Get commission data by source
        $commissionsBySource = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->selectRaw('source, SUM(amount) as total')
            ->groupBy('source')
            ->get();
        
        // Get commission trend data
        $commissionTrend = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
        
        // Calculate growth rate
        $currentMonthTotal = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('amount');
        
        $previousMonthTotal = Commission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->whereMonth('created_at', Carbon::now()->subMonth()->month)
            ->whereYear('created_at', Carbon::now()->subMonth()->year)
            ->sum('amount');
        
        $growthRate = $previousMonthTotal > 0 
            ? round((($currentMonthTotal - $previousMonthTotal) / $previousMonthTotal) * 100, 2) 
            : 0;
        
        return view('agent.analytics.commissions', compact(
            'commissionsBySource',
            'commissionTrend',
            'currentMonthTotal',
            'previousMonthTotal',
            'growthRate'
        ));
    }
    
    /**
     * Display detailed referral analytics.
     *
     * @return \Illuminate\View\View
     */
    public function referralAnalytics()
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to view referral analytics.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Get referral data by status
        $referralsByStatus = Referral::where('referring_agent_id', $agent->id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();
        
        // Get referral trend data
        $referralTrend = Referral::where('referring_agent_id', $agent->id)
            ->where('created_at', '>=', Carbon::now()->subMonths(12))
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
        
        // Get conversion rate trend
        $conversionTrend = [];
        foreach ($referralTrend as $trend) {
            $total = Referral::where('referring_agent_id', $agent->id)
                ->whereMonth('created_at', $trend->month)
                ->whereYear('created_at', $trend->year)
                ->count();
            
            $converted = Referral::where('referring_agent_id', $agent->id)
                ->where('status', 'converted')
                ->whereMonth('created_at', $trend->month)
                ->whereYear('created_at', $trend->year)
                ->count();
            
            $conversionTrend[] = [
                'month' => Carbon::createFromDate($trend->year, $trend->month, 1)->format('M Y'),
                'rate' => $total > 0 ? round(($converted / $total) * 100, 2) : 0,
            ];
        }
        
        return view('agent.analytics.referrals', compact(
            'referralsByStatus',
            'referralTrend',
            'conversionTrend'
        ));
    }
}
