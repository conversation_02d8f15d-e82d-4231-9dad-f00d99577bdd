<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentAnnouncement;
use App\Models\AgentAnnouncementRead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class AnnouncementController extends Controller
{
    /**
     * Display the announcements page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get active announcements for this agent's tier
        $announcements = AgentAnnouncement::active()
            ->forTier($agent->tier)
            ->orderBy('publish_at', 'desc')
            ->paginate(10);

        // Get read announcement IDs
        $readAnnouncementIds = AgentAnnouncementRead::where('agent_id', $agent->id)
            ->pluck('announcement_id')
            ->toArray();

        // Count unread announcements
        $unreadCount = $announcements->filter(function($announcement) use ($readAnnouncementIds) {
            return !in_array($announcement->id, $readAnnouncementIds);
        })->count();

        return view('agent.announcements.index', compact(
            'agent',
            'announcements',
            'readAnnouncementIds',
            'unreadCount'
        ));
    }

    /**
     * Display a specific announcement.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the announcement
        $announcement = AgentAnnouncement::active()
            ->forTier($agent->tier)
            ->findOrFail($id);

        // Mark as read if not already read
        $read = AgentAnnouncementRead::where('agent_id', $agent->id)
            ->where('announcement_id', $announcement->id)
            ->first();

        if (!$read) {
            AgentAnnouncementRead::create([
                'agent_id' => $agent->id,
                'announcement_id' => $announcement->id,
                'read_at' => now(),
            ]);
        }

        return view('agent.announcements.show', compact(
            'agent',
            'announcement'
        ));
    }

    /**
     * Mark all announcements as read.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAllAsRead()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get active announcements for this agent's tier
        $announcements = AgentAnnouncement::active()
            ->forTier($agent->tier)
            ->get();

        // Get read announcement IDs
        $readAnnouncementIds = AgentAnnouncementRead::where('agent_id', $agent->id)
            ->pluck('announcement_id')
            ->toArray();

        // Mark unread announcements as read
        foreach ($announcements as $announcement) {
            if (!in_array($announcement->id, $readAnnouncementIds)) {
                AgentAnnouncementRead::create([
                    'agent_id' => $agent->id,
                    'announcement_id' => $announcement->id,
                    'read_at' => now(),
                ]);
            }
        }

        Toast::success('All announcements marked as read!')->autoDismiss(10);

        return redirect()->route('agent.announcements.index');
    }

    /**
     * Display the archived announcements page.
     *
     * @return \Illuminate\View\View
     */
    public function archived()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get expired announcements for this agent's tier
        $announcements = AgentAnnouncement::where('publish_at', '<=', now())
            ->where('expires_at', '<', now())
            ->forTier($agent->tier)
            ->orderBy('expires_at', 'desc')
            ->paginate(10);

        return view('agent.announcements.archived', compact(
            'agent',
            'announcements'
        ));
    }
}
