<?php

namespace App\Http\Controllers\Agent;

use App\Actions\UploadEmployees;
use App\Http\Controllers\Controller;
use App\Http\Requests\Agent\BusinessEmployeeUpdateRequest;
use App\Models\Business;
use App\Models\BusinessEmployee;
use App\Models\Subscription;
use App\Models\User;
use App\Services\BusinessEmployeeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use ProtoneMedia\Splade\Facades\Toast;
use Spatie\Permission\Models\Role;

class BusinessController extends Controller
{
    protected $businessEmployeeService;
    protected $uploadEmployeesAction;

    public function __construct(
        BusinessEmployeeService $businessEmployeeService,
        UploadEmployees $uploadEmployeesAction
    ) {
        $this->businessEmployeeService = $businessEmployeeService;
        $this->uploadEmployeesAction = $uploadEmployeesAction;
    }

    /**
     * Display a list of businesses that the agent has enrolled.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get businesses where the agent is the referring agent
        $directlyReferredBusinesses = Business::where('referring_agent_id', $user->agent->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get business IDs from subscriptions where the agent is listed as the enrolling agent
        $enrolledBusinessIds = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->with('user')
            ->get()
            ->pluck('user.business_id')
            ->filter() // Remove null values
            ->unique() // Remove duplicates
            ->toArray();

        // Get businesses from those IDs, excluding ones already in the first collection
        $enrolledBusinesses = Business::whereIn('id', $enrolledBusinessIds)
            ->whereNotIn('id', $directlyReferredBusinesses->pluck('id'))
            ->orderBy('created_at', 'desc')
            ->get();

        // Merge the collections
        $businesses = $directlyReferredBusinesses->merge($enrolledBusinesses);

        return view('business.agent.businesses', [
            'businesses' => $businesses,
            'agent' => $user->agent
        ]);
    }

    /**
     * Display employees for a specific business that the agent has enrolled.
     */
    public function showEmployees(Request $request, Business $business)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        $employees = $business->employees()->withTrashed()->get();
        $availableSeats = $business->available_seats;
        $totalSeats = $business->total_seats;

        return view('business.agent.business-employees', [
            'business' => $business,
            'employees' => $employees,
            'availableSeats' => $availableSeats,
            'totalSeats' => $totalSeats
        ]);
    }

    /**
     * Show the form for uploading employees for a business that the agent has enrolled.
     */
    public function showEmployeeUploadForm(Request $request, Business $business)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        $availableSeats = $business->available_seats;

        return view('business.agent.business-employees-upload', [
            'business' => $business,
            'availableSeats' => $availableSeats
        ]);
    }

    /**
     * Upload employees from a CSV file.
     */
    public function uploadEmployees(Request $request, Business $business)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        try {
            // Use the UploadEmployees action to handle the CSV upload
            $result = $this->uploadEmployeesAction->handle($business, $request->file('csv_file'));

            if ($result['success']) {
                if ($result['count'] > 0) {
                    Toast::success($result['message'])->autoDismiss(10);
                } else {
                    Toast::info($result['message'])->autoDismiss(10);
                }
                return redirect()->route('agent.businesses.employees', $business->id);
            } else {
                // Handle error from the action
                Log::error('Employee upload failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'error_type' => $result['error_type'] ?? 'Unknown type'
                ]);
                Toast::danger($result['message'])->autoDismiss(10);
                return back();
            }
        } catch (\Exception $e) {
            // Handle unexpected exceptions
            Log::error('Unexpected error during employee upload', [
                'exception' => get_class($e),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Create a user-friendly error message
            $errorMessage = 'An unexpected error occurred while uploading employees. Please try again or contact support.';

            // For development environments, include more details
            if (config('app.debug')) {
                $errorMessage .= ' Error: ' . $e->getMessage();
            }

            Toast::danger($errorMessage)->autoDismiss(10);
            return back();
        }
    }
    /**
     * Download a CSV template for employee upload.
     */
    public function downloadTemplate()
    {
        $csvContent = $this->uploadEmployeesAction->generateCsvTemplate();

        return \Illuminate\Support\Facades\Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="agent_employee_template.csv"',
        ]);
    }

    /**
     * Send a welcome email to a specific employee.
     */
    public function sendWelcomeEmail(Request $request, Business $business, BusinessEmployee $employee)
    {
        // Add debug logging at the start of the method
        \Illuminate\Support\Facades\Log::info('sendWelcomeEmail method called', [
            'business_id' => $business->id,
            'employee_id' => $employee->id,
            'employee_email' => $employee->email
        ]);

        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            \Illuminate\Support\Facades\Log::warning('Unauthorized access - not an agent');
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        \Illuminate\Support\Facades\Log::info('Agent authorization check', [
            'isReferringAgent' => $isReferringAgent,
            'hasEnrolledBusiness' => $hasEnrolledBusiness
        ]);

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            \Illuminate\Support\Facades\Log::warning('Unauthorized access - agent not associated with business');
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        // Check if the employee belongs to the business
        if ($employee->business_id !== $business->id) {
            \Illuminate\Support\Facades\Log::warning('Employee does not belong to the business', [
                'employee_business_id' => $employee->business_id,
                'business_id' => $business->id
            ]);
            Toast::danger('This employee does not belong to the specified business.')->autoDismiss(10);
            return back();
        }

        try {
            // Find the user associated with this employee
            $employeeUser = User::find($employee->user_id);

            \Illuminate\Support\Facades\Log::info('Looking up employee user', [
                'employee_user_id' => $employee->user_id,
                'found_user' => $employeeUser ? true : false
            ]);

            if (!$employeeUser) {
                \Illuminate\Support\Facades\Log::warning('No user account found for employee');
                Toast::danger('No user account found for this employee.')->autoDismiss(10);
                return back();
            }

            // Generate a new temporary password
            $password = Str::random(10);
            $employeeUser->password = Hash::make($password);
            $employeeUser->save();

            \Illuminate\Support\Facades\Log::info('Generated new password for employee');

            // Get subscription plan if available
            $subscriptionPlan = null;
            $subscription = $employeeUser->subscriptions()->first();
            if ($subscription && $subscription->plan) {
                $subscriptionPlan = $subscription->plan->name;
            }

            \Illuminate\Support\Facades\Log::info('About to send welcome email', [
                'employee_email' => $employeeUser->email,
                'business_name' => $business->name,
                'subscription_plan' => $subscriptionPlan
            ]);

            // Log the current mail configuration
            \Illuminate\Support\Facades\Log::info('Mail configuration', [
                'driver' => config('mail.default'),
                'from_address' => config('mail.from.address'),
                'from_name' => config('mail.from.name'),
                'log_channel' => config('mail.mailers.log.channel')
            ]);

            // Send a test email using the Mail facade
            \Illuminate\Support\Facades\Mail::raw('Test email before sending notification', function($message) use ($employeeUser) {
                $message->to($employeeUser->email);
                $message->subject('Test Email');
            });

            \Illuminate\Support\Facades\Log::info('Test email sent via Mail facade');

            // Send the welcome email
            $employeeUser->notify(new \App\Notifications\BusinessEmployeeWelcomeNotification(
                $password,
                $business->name,
                $subscriptionPlan
            ));

            \Illuminate\Support\Facades\Log::info('Welcome email notification sent');

            Toast::success("Welcome email sent to {$employee->email} with new login credentials.")->autoDismiss(10);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to send welcome email: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
                'employee_id' => $employee->id,
                'business_id' => $business->id
            ]);

            Toast::danger('Failed to send welcome email: ' . $e->getMessage())->autoDismiss(10);
        }

        return back();
    }

    /**
     * Show the form for editing an employee.
     */
    public function editEmployee(Request $request, Business $business, BusinessEmployee $employee)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        // Ensure the employee belongs to this business
        if ($employee->business_id !== $business->id) {
            Toast::danger('Employee not found in this business.')->autoDismiss(10);
            return redirect()->route('agent.businesses.employees', $business->id);
        }

        // Format the employee data for the form
        $employeeData = $employee->toArray();

        // Format the date of birth for HTML date input (Y-m-d format)
        if ($employee->dob) {
            $employeeData['dob'] = $employee->dob->format('Y-m-d');
        }

        return view('business.agent.business-employee-edit', [
            'business' => $business,
            'employee' => $employeeData
        ]);
    }

    /**
     * Update an employee's information.
     */
    public function updateEmployee(BusinessEmployeeUpdateRequest $request, Business $business, BusinessEmployee $employee)
    {
        $user = $request->user();

        if (!$user->hasRole('agent') || !$user->agent) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check if the agent is the referring agent for this business
        $isReferringAgent = $business->referring_agent_id === $user->agent->id;

        // Check if the agent has enrolled this business (via subscription)
        $hasEnrolledBusiness = Subscription::where('agent_id', $user->agent->id)
            ->where('user_type', 'business_admin')
            ->whereHas('user', function($query) use ($business) {
                $query->where('business_id', $business->id);
            })
            ->exists();

        // Allow access if the agent is either the referring agent or has enrolled the business
        if (!$isReferringAgent && !$hasEnrolledBusiness) {
            Toast::danger('Unauthorized access. You can only manage businesses you have enrolled.')->autoDismiss(10);
            return redirect()->route('agent.businesses');
        }

        // Ensure the employee belongs to this business
        if ($employee->business_id !== $business->id) {
            Toast::danger('Employee not found in this business.')->autoDismiss(10);
            return redirect()->route('agent.businesses.employees', $business->id);
        }

        try {
            DB::beginTransaction();

            // Update employee information
            $employeeData = $request->only([
                'first_name', 'last_name', 'email', 'phone', 'mobile_phone',
                'dob', 'address1', 'address2', 'city', 'state', 'zip', 'status'
            ]);

            $employee->update($employeeData);

            // Update user information if the employee has a user account
            if ($employee->user_id) {
                $user = User::find($employee->user_id);
                if ($user) {
                    $userData = [
                        'name' => $request->first_name . ' ' . $request->last_name,
                        'email' => $request->email,
                        'fname' => $request->first_name,
                        'lname' => $request->last_name,
                        'phone' => $request->phone,
                        'mobile_phone' => $request->mobile_phone,
                        'dob' => $request->dob,
                        'address1' => $request->address1,
                        'address2' => $request->address2,
                        'city' => $request->city,
                        'state' => $request->state,
                        'zip' => $request->zip,
                    ];

                    // Update password if provided
                    if ($request->filled('password')) {
                        $userData['password'] = Hash::make($request->password);
                    }

                    $user->update($userData);
                }
            }

            DB::commit();

            Toast::success('Employee information updated successfully.')->autoDismiss(10);
            return redirect()->route('agent.businesses.employees', $business->id);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update employee: ' . $e->getMessage());

            Toast::danger('Failed to update employee: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }
}
