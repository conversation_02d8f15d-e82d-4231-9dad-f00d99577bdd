<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentCommission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class CommissionReportController extends Controller
{
    /**
     * Display the commission reports page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->first();

        if (!$agent) {
            Toast::danger('You must be an agent to view commission reports.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get commission data for the agent
        $commissions = AgentCommission::where('agent_id', $agent->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate summary statistics
        $totalCommission = AgentCommission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->sum('commission_amount');

        $pendingCommission = AgentCommission::where('agent_id', $agent->id)
            ->where('status', 'pending')
            ->sum('commission_amount');

        $commissionsByMonth = AgentCommission::where('agent_id', $agent->id)
            ->where('status', 'paid')
            ->selectRaw('MONTH(created_at) as month, YEAR(created_at) as year, SUM(commission_amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        return view('agent.commissions.reports', compact(
            'commissions',
            'totalCommission',
            'pendingCommission',
            'commissionsByMonth'
        ));
    }

    /**
     * Export commission report as CSV.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportCsv(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->first();

        if (!$agent) {
            Toast::danger('You must be an agent to export commission reports.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get filter parameters
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $status = $request->input('status');

        // Build query
        $query = AgentCommission::where('agent_id', $agent->id);

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $commissions = $query->orderBy('created_at', 'desc')->get();

        // Generate CSV
        $filename = 'commission_report_' . date('Y-m-d') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($commissions) {
            $file = fopen('php://output', 'w');

            // Add headers
            fputcsv($file, [
                'ID',
                'Date',
                'Source',
                'Amount',
                'Status',
                'Description'
            ]);

            // Add data
            foreach ($commissions as $commission) {
                fputcsv($file, [
                    $commission->id,
                    $commission->created_at->format('Y-m-d'),
                    $commission->subscription_id ? 'Subscription' : 'Transaction',
                    $commission->commission_amount,
                    $commission->status,
                    'Commission at ' . $commission->agent_rate . '% rate'
                ]);
            }

            fclose($file);
        };

        Toast::success('Commission report exported successfully.')->autoDismiss(10);
        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export commission report as PDF.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportPdf(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->first();

        if (!$agent) {
            Toast::danger('You must be an agent to export commission reports.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get filter parameters
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $status = $request->input('status');

        // Build query
        $query = AgentCommission::where('agent_id', $agent->id);

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $commissions = $query->orderBy('created_at', 'desc')->get();

        // Generate PDF
        $pdf = app()->make('dompdf.wrapper');
        $pdf->loadView('agent.commissions.pdf', compact('commissions', 'agent'));

        Toast::success('Commission report exported successfully.')->autoDismiss(10);
        return $pdf->download('commission_report_' . date('Y-m-d') . '.pdf');
    }
}
