<?php

namespace App\Http\Controllers\Agent;

use App\Actions\Subscriptions\CreatePendingSubscription;
use App\Http\Controllers\Controller;
use App\Mail\BusinessEnrollmentNotification;
use App\Mail\PatientEnrollmentNotification;
use App\Models\Agent;
use App\Models\Business;
use App\Models\BusinessContact;
use App\Models\BusinessEmployee;
use App\Models\BusinessPlan;
use App\Models\CreditCard;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Services\AgentReferralService;
use App\Services\BusinessPlanService;
use App\Services\CreditCardService;
use App\Support\SubscriptionGroups;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use ProtoneMedia\Splade\Facades\Toast;

class EnrollmentController extends Controller
{
    protected $agentReferralService;
    protected $businessPlanService;
    protected $createPendingSubscription;

    public function __construct(
        AgentReferralService $agentReferralService,
        BusinessPlanService $businessPlanService,
        CreatePendingSubscription $createPendingSubscription
    ) {
        $this->agentReferralService = $agentReferralService;
        $this->businessPlanService = $businessPlanService;
        $this->createPendingSubscription = $createPendingSubscription;
    }

    /**
     * Show the enrollment form.
     */
    public function index(Request $request)
    {
        $agent = Auth::user()->agent;

        if (!$agent || $agent->status !== 'approved') {
            Toast::danger('Your agent account must be approved to enroll patients or businesses.')->autoDismiss(10);
            return redirect()->route('agent.dashboard');
        }

        // Get premium subscription plans (group_id = 3)
        $subscriptionPlans = SubscriptionPlan::premium()
            ->where('is_active', true)
            ->orderBy('price')
            ->get();

        // Set active tab if provided in request
        $activeTab = $request->query('tab', 'patient');

        return view('agent.enrollment.index', [
            'agent' => $agent,
            'subscriptionPlans' => $subscriptionPlans,
            'activeTab' => $activeTab
        ]);
    }

    /**
     * Enroll a new patient.
     */
    public function enrollPatient(Request $request)
    {
        $agent = Auth::user()->agent;

        if (!$agent || $agent->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'Your agent account must be approved to enroll patients.'
            ], 403);
        }

        $request->validate([
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'max:20'],
            'gender' => ['required', 'string', 'in:M,F,O'],
            'dob' => ['required', 'date'],
            'address1' => ['required', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'plan_id' => [
                'required',
                'exists:subscription_plans,id',
                function ($attribute, $value, $fail) {
                    $plan = SubscriptionPlan::find($value);
                    if (!$plan || !$plan->isPremium()) {
                        $fail('The selected plan is not valid. Please select a Premium Plan.');
                    }
                },
            ],
        ]);

        try {
            DB::beginTransaction();

            // Create the user
            $user = User::create([
                'name' => $request->fname . ' ' . $request->lname,
                'email' => $request->email,
                'password' => Hash::make(str_replace(['-', ' '], '', $request->dob)), // Default password is DOB without dashes
                'fname' => $request->fname,
                'lname' => $request->lname,
                'gender' => $request->gender,
                'dob' => $request->dob,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'phone' => $request->phone,
                'status' => 'active',
            ]);

            // Assign patient role
            $user->assignRole('patient');

            // Get the subscription plan
            $plan = SubscriptionPlan::findOrFail($request->plan_id);

            // Create subscription with pending_payment status using the action
            $subscriptionResult = $this->createPendingSubscription->execute([
                'user' => $user,
                'plan' => $plan,
                'session_data' => [
                    'source' => 'agent_enrollment'
                ]
            ]);

            if (!$subscriptionResult['success']) {
                throw new \Exception($subscriptionResult['error']);
            }

            $subscription = $subscriptionResult['subscription'];

            // Update subscription with agent-specific data
            $subscription->update([
                'agent_id' => $agent->id,
                'user_type' => 'patient',
                'meta_data' => array_merge($subscription->meta_data ?? [], [
                    'referral_code' => $agent->referral_code,
                    'plan_group' => 'Premium Plans'
                ])
            ]);

            // Attach agent to subscription for commission tracking
            $this->agentReferralService->attachAgentToSubscription($subscription, $agent);

            // Send enrollment email notification
            try {
                Mail::to($user->email)->send(new PatientEnrollmentNotification(
                    patient: $user,
                    subscription: $subscription,
                    agent: Auth::user()
                ));

                Log::info('Patient enrollment email sent', [
                    'patient_id' => $user->id,
                    'email' => $user->email
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send patient enrollment email: ' . $e->getMessage(), [
                    'patient_id' => $user->id,
                    'email' => $user->email
                ]);
                // Continue with the process even if email fails
            }

            DB::commit();

            Toast::success('Patient enrolled successfully! Payment is required to activate the subscription. An email with details has been sent to the patient.')->autoDismiss(10);
            return to_route('agent.patients.edit', $user);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Patient enrollment failed: ' . $e->getMessage());

            Toast::danger('Failed to enroll patient: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to enroll patient: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Enroll a new business.
     */
    public function enrollBusiness(Request $request)
    {
        $agent = Auth::user()->agent;

        if (!$agent || $agent->status !== 'approved') {
            Toast::danger('Your agent account must be approved to enroll businesses.')->autoDismiss(10);
            return redirect()->route('agent.dashboard');
        }

        $request->validate([
            'business_name' => ['required', 'string', 'max:255'],
            'business_email' => ['required', 'string', 'email', 'max:255'],
            'business_phone' => ['required', 'string', 'max:20'],
            'address1' => ['required', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'owner_fname' => ['required', 'string', 'max:255'],
            'owner_lname' => ['required', 'string', 'max:255'],
            'admin_email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'admin_phone' => ['required', 'string', 'max:20'],
            'hr_fname' => ['nullable', 'string', 'max:255'],
            'hr_lname' => ['nullable', 'string', 'max:255'],
            'hr_contact_email' => ['nullable', 'string', 'email', 'max:255'],
            'hr_contact_phone' => ['nullable', 'string', 'max:20'],
            'employee_count' => ['required', 'integer', 'min:1'],
            'plan_price' => ['required', 'numeric', 'min:20'],
        ]);

        try {
            DB::beginTransaction();

            // Create the business
            $business = Business::create([
                'name' => $request->business_name,
                'email' => $request->business_email,
                'phone' => $request->business_phone,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'referring_agent_id' => $agent->id,
            ]);

            // Create admin user
            $adminUser = User::create([
                'name' => $request->owner_fname . ' ' . $request->owner_lname,
                'email' => $request->admin_email,
                'password' => Hash::make($request->admin_phone), // Use phone as default password
                'fname' => $request->owner_fname,
                'lname' => $request->owner_lname,
                'phone' => $request->admin_phone,
                'business_id' => $business->id,
                'status' => 'active',
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
            ]);

            // Assign business_admin role
            $adminUser->assignRole('business_admin');

            // Create business contact
            BusinessContact::create([
                'business_id' => $business->id,
                'owner_fname' => $request->owner_fname,
                'owner_lname' => $request->owner_lname,
                'billing_contact_email' => $request->admin_email,
                'billing_contact_phone' => $request->admin_phone,
                'hr_fname' => $request->hr_fname,
                'hr_lname' => $request->hr_lname,
                'hr_contact_email' => $request->hr_contact_email,
                'hr_contact_phone' => $request->hr_contact_phone,
            ]);

            // Create HR user if HR contact email is provided
            if ($request->hr_contact_email) {
                // Create HR name from provided fields or default to "HR Contact"
                $hrName = ($request->hr_fname && $request->hr_lname)
                    ? $request->hr_fname . ' ' . $request->hr_lname
                    : 'HR Contact';

                $hrUser = User::create([
                    'name' => $hrName,
                    'email' => $request->hr_contact_email,
                    'password' => Hash::make($request->hr_contact_phone ?: $request->admin_phone),
                    'phone' => $request->hr_contact_phone ?: $request->admin_phone,
                    'business_id' => $business->id,
                    'status' => 'active',
                    'fname' => $request->hr_fname,
                    'lname' => $request->hr_lname,
                ]);

                // Assign business_hr role
                $hrUser->assignRole('business_hr');
            }

            // Create business plan using the service
            $planData = [
                'employee_count' => $request->employee_count,
                'price_per_plan' => 2000, // $20.00 per plan stored as 2000 cents
                'duration_months' => 1, // Default to 1 month
                'afid' => $request->afid ?? null,
                'click_id' => $request->click_id ?? null,
                'active' => false,
                // Note: total_price is calculated in the service
            ];

            $planResult = $this->businessPlanService->createPlan($business, $planData);

            if (!$planResult['success']) {
                throw new \Exception($planResult['message']);
            }

            $businessPlan = $planResult['business_plan'];

            // Get a premium business plan ID (using the first premium plan)
            $premiumPlan = SubscriptionPlan::premium()->where('is_active', true)->first();

            // Create subscription with pending_payment status using the action
            $subscriptionResult = $this->createPendingSubscription->execute([
                'user' => $adminUser,
                'plan' => $premiumPlan,
                'session_data' => [
                    'source' => 'agent_business_enrollment'
                ]
            ]);

            if (!$subscriptionResult['success']) {
                throw new \Exception($subscriptionResult['error']);
            }

            $subscription = $subscriptionResult['subscription'];

            // Update subscription with business-specific data
            $subscription->update([
                'ends_at' => $businessPlan->ends_at, // Use the same end date as the business plan
                'agent_id' => $agent->id,
                'user_type' => 'business_admin',
                'meta_data' => array_merge($subscription->meta_data ?? [], [
                    'business_id' => $business->id,
                    'business_plan_id' => $businessPlan->id,
                    'referral_code' => $agent->referral_code,
                    'plan_group' => $premiumPlan->group_name,
                    'enrollment_type' => 'business'
                ])
            ]);

            // Attach agent to subscription for commission tracking
            $this->agentReferralService->attachAgentToSubscription($subscription, $agent);

            // Send enrollment email notification
            try {
                Mail::to($adminUser->email)->send(new BusinessEnrollmentNotification(
                    business: $business,
                    admin: $adminUser,
                    businessPlan: $businessPlan,
                    subscription: $subscription,
                    agent: Auth::user()
                ));

                Log::info('Business enrollment email sent', [
                    'business_id' => $business->id,
                    'admin_id' => $adminUser->id,
                    'email' => $adminUser->email
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send business enrollment email: ' . $e->getMessage(), [
                    'business_id' => $business->id,
                    'admin_id' => $adminUser->id,
                    'email' => $adminUser->email
                ]);
                // Continue with the process even if email fails
            }

            DB::commit();

            Toast::success('Business enrolled successfully! Payment is required to activate the subscription. An email with details has been sent to the business administrator.')->autoDismiss(10);
            return redirect()->route('agent.patients.payment-methods.index', $adminUser);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Business enrollment failed: ' . $e->getMessage());

            Toast::danger('Failed to enroll business: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Display a list of enrollments made by the agent.
     */
    public function enrollments()
    {
        $agent = Auth::user()->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('agent.dashboard');
        }

        // Get patient enrollments using the new dedicated columns
        $patientEnrollments = \App\Models\Subscription::where('agent_id', $agent->id)
            ->where('user_type', 'patient')
            ->with('user', 'plan')
            ->latest()
            ->paginate(10, ['*'], 'patient_page');

        // Get business enrollments using the new dedicated columns
        $businessEnrollments = \App\Models\Subscription::where('agent_id', $agent->id)
            ->where('user_type', 'business_admin')
            ->with('user', 'plan')
            ->latest()
            ->paginate(10, ['*'], 'business_page');

        // Get commissions related to these enrollments
        $commissions = $agent->commissions()
            ->with(['transaction', 'subscription'])
            ->latest()
            ->paginate(10, ['*'], 'commission_page');

        return view('agent.enrollment.list', compact('patientEnrollments', 'businessEnrollments', 'commissions'));
    }

    /**
     * Activate a subscription by charging the default credit card.
     */
    public function activateSubscription(Subscription $subscription)
    {
        $agent = Auth::user()->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('agent.dashboard');
        }

        // Check if the subscription belongs to this agent
        if ($subscription->agent_id !== $agent->id) {
            Toast::danger('Unauthorized access.');
            return back();
        }

        // Check if the subscription is in pending_payment status
        if ($subscription->status !== Subscription::STATUS_PENDING_PAYMENT) {
            Toast::danger('This subscription cannot be activated. It must be in pending payment status.');
            return back();
        }

        // Get the user associated with the subscription
        $user = $subscription->user;

        // Check if the user has a default payment method
        $defaultPaymentMethod = $user->defaultPaymentMethod();

        if (!$defaultPaymentMethod) {
            Toast::danger('No default payment method found for this user. Please add a credit card and set it as default.');
            return redirect()->route('agent.patients.payment-methods.index', $user);
        }

        try {
            // Get the subscription plan
            $plan = $subscription->plan;

            // Use the ProcessSubscriptionPayment action to charge the card
            $paymentResult = app(\App\Actions\Subscriptions\ProcessSubscriptionPayment::class)->execute([
                'user' => $user,
                'plan' => $plan,
                'subscription' => $subscription, // Pass the existing subscription
                'use_existing_card' => true,
                'credit_card_id' => $defaultPaymentMethod->id,
                'session_data' => [
                    'source' => 'agent_activation',
                    'agent_id' => $agent->id
                ],
            ]);

            if ($paymentResult['success']) {
                // The subscription should already be activated by the payment process

                // If this is a business subscription, also activate the business plan
                if ($subscription->user_type === 'business_admin' && $subscription->hasFeature('business_plan_id')) {
                    $businessPlanId = $subscription->getFeature('business_plan_id');
                    $businessPlan = BusinessPlan::find($businessPlanId);

                    if ($businessPlan) {
                        // Activate the business plan
                        $businessPlan->update(['active' => true]);

                        // Get the business
                        $business = $businessPlan->business;

                        // Activate pending employees if there are available seats
                        $availableSeats = $business->available_seats;
                        if ($availableSeats > 0) {
                            $pendingEmployees = $business->employees()
                                ->where('status', 'pending')
                                ->orderBy('created_at')
                                ->limit($availableSeats)
                                ->get();

                            foreach ($pendingEmployees as $employee) {
                                $employee->update(['status' => 'active']);

                                // Also activate the employee's subscription if it exists
                                if ($employee->user_id) {
                                    Subscription::where('user_id', $employee->user_id)
                                        ->where('user_type', 'business_employee')
                                        ->where('status', 'pending_activation')
                                        ->update(['status' => 'active']);
                                }
                            }

                            $activatedCount = $pendingEmployees->count();
                            Log::info("Activated $activatedCount pending employees", [
                                'business_id' => $business->id,
                                'business_plan_id' => $businessPlanId
                            ]);
                        }

                        Log::info('Business plan activated', [
                            'business_plan_id' => $businessPlanId,
                            'subscription_id' => $subscription->id,
                            'agent_id' => $agent->id
                        ]);

                        Toast::success('Subscription and business plan activated successfully!');
                    } else {
                        Toast::success('Subscription activated successfully! (Business plan not found)');
                    }
                } else {
                    Toast::success('Subscription activated successfully!');
                }
            } else {
                Toast::danger('Payment failed: ' . ($paymentResult['error'] ?? 'Unknown error'));
            }

            return back();

        } catch (\Exception $e) {
            Log::error('Subscription activation failed: ' . $e->getMessage(), [
                'subscription_id' => $subscription->id,
                'user_id' => $user->id,
                'agent_id' => $agent->id
            ]);

            Toast::danger('Failed to activate subscription: ' . $e->getMessage());
            return back();
        }
    }
}
