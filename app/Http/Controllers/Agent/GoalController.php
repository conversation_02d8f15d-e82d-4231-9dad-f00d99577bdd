<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentGoal;
use App\Models\Commission;
use App\Models\Referral;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\Splade\Facades\Toast;

class GoalController extends Controller
{
    /**
     * Display the goals dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get active goals
        $activeGoals = AgentGoal::where('agent_id', $agent->id)
            ->where('status', 'active')
            ->orderBy('target_date')
            ->get();

        // Get completed goals
        $completedGoals = AgentGoal::where('agent_id', $agent->id)
            ->where('status', 'achieved')
            ->orderBy('achieved_at', 'desc')
            ->limit(5)
            ->get();

        // Get current stats
        $currentMonth = now()->startOfMonth();
        $currentYear = now()->startOfYear();

        $monthlyCommissions = Commission::where('agent_id', $agent->id)
            ->where('created_at', '>=', $currentMonth)
            ->sum('amount');

        $yearlyCommissions = Commission::where('agent_id', $agent->id)
            ->where('created_at', '>=', $currentYear)
            ->sum('amount');

        $monthlyReferrals = Referral::where('referring_agent_id', $agent->id)
            ->where('created_at', '>=', $currentMonth)
            ->count();

        $yearlyReferrals = Referral::where('referring_agent_id', $agent->id)
            ->where('created_at', '>=', $currentYear)
            ->count();

        return view('agent.goals.index', compact(
            'agent',
            'activeGoals',
            'completedGoals',
            'monthlyCommissions',
            'yearlyCommissions',
            'monthlyReferrals',
            'yearlyReferrals'
        ));
    }

    /**
     * Show the form for creating a new goal.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        return view('agent.goals.create', compact('agent'));
    }

    /**
     * Store a newly created goal.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Validate the request
        $request->validate([
            'type' => 'required|in:commissions,referrals',
            'target_value' => 'required|numeric|min:1',
            'target_date' => 'required|date|after:today',
            'description' => 'nullable|string',
        ]);

        // Create the goal
        $goal = AgentGoal::create([
            'agent_id' => $agent->id,
            'type' => $request->type,
            'target_value' => $request->target_value,
            'target_date' => $request->target_date,
            'description' => $request->description,
            'status' => 'active',
        ]);

        Toast::success('Goal created successfully!')->autoDismiss(10);

        return redirect()->route('agent.goals.index');
    }

    /**
     * Display a specific goal.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the goal
        $goal = AgentGoal::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Get progress data
        $progressData = $this->getGoalProgressData($goal);

        return view('agent.goals.show', compact(
            'agent',
            'goal',
            'progressData'
        ));
    }

    /**
     * Show the form for editing a goal.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the goal
        $goal = AgentGoal::where('agent_id', $agent->id)
            ->where('status', 'active')
            ->findOrFail($id);

        return view('agent.goals.edit', compact(
            'agent',
            'goal'
        ));
    }

    /**
     * Update a goal.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the goal
        $goal = AgentGoal::where('agent_id', $agent->id)
            ->where('status', 'active')
            ->findOrFail($id);

        // Validate the request
        $request->validate([
            'target_value' => 'required|numeric|min:1',
            'target_date' => 'required|date|after:today',
            'description' => 'nullable|string',
        ]);

        // Update the goal
        $goal->update([
            'target_value' => $request->target_value,
            'target_date' => $request->target_date,
            'description' => $request->description,
        ]);

        Toast::success('Goal updated successfully!')->autoDismiss(10);

        return redirect()->route('agent.goals.show', $goal->id);
    }

    /**
     * Delete a goal.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the goal
        $goal = AgentGoal::where('agent_id', $agent->id)
            ->where('status', 'active')
            ->findOrFail($id);

        // Delete the goal
        $goal->delete();

        Toast::success('Goal deleted successfully!')->autoDismiss(10);

        return redirect()->route('agent.goals.index');
    }

    /**
     * Get goal progress data.
     *
     * @param  \App\Models\AgentGoal  $goal
     * @return array
     */
    protected function getGoalProgressData(AgentGoal $goal)
    {
        $startDate = $goal->created_at->startOfDay();
        $endDate = $goal->target_date->endOfDay();
        $today = now()->endOfDay();

        // Calculate total days and days elapsed
        $totalDays = $startDate->diffInDays($endDate);
        $daysElapsed = $startDate->diffInDays($today);
        $daysElapsed = min($daysElapsed, $totalDays);

        // Calculate expected progress based on time
        $expectedProgress = $totalDays > 0 ? ($daysElapsed / $totalDays) * $goal->target_value : 0;

        // Get actual progress
        $actualProgress = 0;

        if ($goal->type === 'commissions') {
            $actualProgress = Commission::where('agent_id', $goal->agent_id)
                ->where('created_at', '>=', $startDate)
                ->where('created_at', '<=', min($today, $endDate))
                ->sum('amount');
        } elseif ($goal->type === 'referrals') {
            $actualProgress = Referral::where('referring_agent_id', $goal->agent_id)
                ->where('created_at', '>=', $startDate)
                ->where('created_at', '<=', min($today, $endDate))
                ->count();
        }

        // Calculate progress percentage
        $progressPercentage = $goal->target_value > 0 ? min(100, round(($actualProgress / $goal->target_value) * 100)) : 0;

        // Check if goal is achieved
        if ($actualProgress >= $goal->target_value && $goal->status === 'active') {
            $goal->update([
                'status' => 'achieved',
                'achieved_at' => now(),
            ]);

            // Notify the agent
            $goal->agent->user->notify(new \App\Notifications\GoalAchievedNotification($goal));
        }

        // Get monthly data for chart
        $monthlyData = $this->getMonthlyProgressData($goal, $startDate, $endDate);

        return [
            'actual_progress' => $actualProgress,
            'expected_progress' => $expectedProgress,
            'progress_percentage' => $progressPercentage,
            'days_elapsed' => $daysElapsed,
            'total_days' => $totalDays,
            'time_percentage' => $totalDays > 0 ? round(($daysElapsed / $totalDays) * 100) : 0,
            'monthly_data' => $monthlyData,
        ];
    }

    /**
     * Get monthly progress data for chart.
     *
     * @param  \App\Models\AgentGoal  $goal
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    protected function getMonthlyProgressData(AgentGoal $goal, $startDate, $endDate)
    {
        $months = [];
        $currentDate = $startDate->copy()->startOfMonth();

        while ($currentDate->lte($endDate)) {
            $months[] = $currentDate->format('M Y');
            $currentDate->addMonth();
        }

        $monthlyData = [
            'labels' => $months,
            'actual' => [],
            'target' => [],
        ];

        $currentDate = $startDate->copy()->startOfMonth();
        $cumulativeTarget = 0;
        $cumulativeActual = 0;

        foreach ($months as $index => $month) {
            $monthEnd = $currentDate->copy()->endOfMonth();

            // Calculate target for this month (proportional to days in goal period)
            $daysInMonth = 0;
            $tempDate = $currentDate->copy();
            while ($tempDate->lte($monthEnd) && $tempDate->lte($endDate)) {
                if ($tempDate->gte($startDate)) {
                    $daysInMonth++;
                }
                $tempDate->addDay();
            }

            $totalDays = $startDate->diffInDays($endDate);
            $monthlyTarget = $totalDays > 0 ? ($daysInMonth / $totalDays) * $goal->target_value : 0;
            $cumulativeTarget += $monthlyTarget;

            // Get actual progress for this month
            $monthlyActual = 0;

            if ($goal->type === 'commissions') {
                $monthlyActual = Commission::where('agent_id', $goal->agent_id)
                    ->where('created_at', '>=', max($currentDate, $startDate))
                    ->where('created_at', '<=', min($monthEnd, $endDate))
                    ->sum('amount');
            } elseif ($goal->type === 'referrals') {
                $monthlyActual = Referral::where('referring_agent_id', $goal->agent_id)
                    ->where('created_at', '>=', max($currentDate, $startDate))
                    ->where('created_at', '<=', min($monthEnd, $endDate))
                    ->count();
            }

            $cumulativeActual += $monthlyActual;

            $monthlyData['actual'][] = round($cumulativeActual, 2);
            $monthlyData['target'][] = round($cumulativeTarget, 2);

            $currentDate->addMonth();
        }

        return $monthlyData;
    }
}
