<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentLandingPage;
use App\Models\AgentMarketingUsage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use ProtoneMedia\Splade\Facades\Toast;

class LandingPageController extends Controller
{
    /**
     * Display a landing page.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $landingPage = AgentLandingPage::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Record visit
        $landingPage->visits()->create([
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'referrer' => request()->header('referer'),
        ]);

        // Store agent ID in cookie for attribution
        Cookie::queue('referring_agent_id', $landingPage->agent_id, 43200); // 30 days

        return view('agent.landing-pages.show', compact('landingPage'));
    }

    /**
     * Handle form submission from a landing page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $slug
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submit(Request $request, $slug)
    {
        $landingPage = AgentLandingPage::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'nullable|string',
        ]);

        // Record conversion
        $landingPage->conversions()->create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'message' => $request->message,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Check if this came from a marketing material usage
        $usageId = $request->query('usage');
        if ($usageId) {
            $usage = AgentMarketingUsage::find($usageId);
            if ($usage) {
                $usage->increment('conversion_count');
            }
        }

        // Notify the agent
        $landingPage->agent->user->notify(new \App\Notifications\LandingPageConversionNotification($landingPage, $request->only(['name', 'email', 'phone', 'message'])));

        Toast::success('Thank you for your submission! An agent will contact you shortly.')->autoDismiss(10);

        return redirect()->route('agent.landing-page.thank-you', $slug);
    }

    /**
     * Display the thank you page after form submission.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function thankYou($slug)
    {
        $landingPage = AgentLandingPage::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return view('agent.landing-pages.thank-you', compact('landingPage'));
    }

    /**
     * Track a click from a marketing material.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $code
     * @return \Illuminate\Http\RedirectResponse
     */
    public function trackClick(Request $request, $code)
    {
        $agent = Agent::where('referral_code', $code)->firstOrFail();

        // Check if this is from a marketing material usage
        $usageId = $request->query('usage');
        if ($usageId) {
            $usage = AgentMarketingUsage::find($usageId);
            if ($usage) {
                $usage->increment('click_count');
            }
        }

        // Store agent ID in cookie for attribution
        Cookie::queue('referring_agent_id', $agent->id, 43200); // 30 days

        // Redirect to the appropriate page
        $redirectUrl = $request->query('redirect', route('home'));

        return redirect($redirectUrl);
    }
}
