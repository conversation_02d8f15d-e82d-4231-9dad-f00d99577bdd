<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentMarketingMaterial;
use App\Models\AgentMarketingUsage;
use App\Models\AgentLandingPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use ProtoneMedia\Splade\Facades\Toast;

class MarketingController extends Controller
{
    /**
     * Display the marketing dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get marketing usage statistics
        $usageStats = AgentMarketingUsage::where('agent_id', $agent->id)
            ->selectRaw('marketing_material_id, COUNT(*) as usage_count, SUM(click_count) as total_clicks, SUM(conversion_count) as total_conversions')
            ->groupBy('marketing_material_id')
            ->get()
            ->keyBy('marketing_material_id');

        // Get landing page statistics
        $landingPages = AgentLandingPage::where('agent_id', $agent->id)
            ->withCount(['visits', 'conversions'])
            ->get();

        // Calculate overall conversion rate
        $totalClicks = $usageStats->sum('total_clicks');
        $totalConversions = $usageStats->sum('total_conversions');
        $conversionRate = $totalClicks > 0 ? round(($totalConversions / $totalClicks) * 100, 2) : 0;

        return view('agent.marketing.dashboard', compact(
            'agent',
            'usageStats',
            'landingPages',
            'totalClicks',
            'totalConversions',
            'conversionRate'
        ));
    }

    /**
     * Display the marketing materials library.
     *
     * @return \Illuminate\View\View
     */
    public function library()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get marketing materials by type
        $materials = AgentMarketingMaterial::where('is_active', true)
            ->get()
            ->groupBy('type');

        // Get usage statistics
        $usageStats = AgentMarketingUsage::where('agent_id', $agent->id)
            ->selectRaw('marketing_material_id, COUNT(*) as usage_count')
            ->groupBy('marketing_material_id')
            ->pluck('usage_count', 'marketing_material_id')
            ->toArray();

        return view('agent.marketing.library', compact(
            'agent',
            'materials',
            'usageStats'
        ));
    }

    /**
     * Display a specific marketing material.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentMarketingMaterial::findOrFail($id);

        // Get usage statistics for this material
        $usageStats = AgentMarketingUsage::where('agent_id', $agent->id)
            ->where('marketing_material_id', $material->id)
            ->selectRaw('usage_type, COUNT(*) as count, SUM(click_count) as clicks, SUM(conversion_count) as conversions')
            ->groupBy('usage_type')
            ->get();

        // Generate personalized content
        $personalizedContent = $this->personalizeContent($material, $agent);

        return view('agent.marketing.show', compact(
            'agent',
            'material',
            'usageStats',
            'personalizedContent'
        ));
    }

    /**
     * Share a marketing material to social media.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function share(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentMarketingMaterial::findOrFail($id);

        // Validate the request
        $request->validate([
            'platform' => 'required|in:facebook,twitter,linkedin,email'
        ]);

        // Generate personalized content
        $personalizedContent = $this->personalizeContent($material, $agent);

        // Record usage
        $usage = AgentMarketingUsage::create([
            'agent_id' => $agent->id,
            'marketing_material_id' => $material->id,
            'usage_type' => 'share_' . $request->platform,
            'click_count' => 0,
            'conversion_count' => 0
        ]);

        // Generate sharing URL
        $shareUrl = $this->getSharingUrl(
            $request->platform,
            $personalizedContent,
            route('referral.track', ['code' => $agent->referral_code, 'usage' => $usage->id])
        );

        Toast::success('Content shared successfully!')->autoDismiss(10);

        return redirect($shareUrl);
    }

    /**
     * Download a marketing material.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentMarketingMaterial::findOrFail($id);

        // Record usage
        AgentMarketingUsage::create([
            'agent_id' => $agent->id,
            'marketing_material_id' => $material->id,
            'usage_type' => 'download',
            'click_count' => 0,
            'conversion_count' => 0
        ]);

        // If it's a file, download it
        if ($material->file_path) {
            return Storage::download($material->file_path, $material->title . '.' . pathinfo($material->file_path, PATHINFO_EXTENSION));
        }

        // If it's a template, generate a PDF
        $personalizedContent = $this->personalizeContent($material, $agent);

        $pdf = app()->make('dompdf.wrapper');
        $pdf->loadHTML($personalizedContent);

        return $pdf->download($material->title . '.pdf');
    }

    /**
     * Display the landing pages management page.
     *
     * @return \Illuminate\View\View
     */
    public function landingPages()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get landing pages
        $landingPages = AgentLandingPage::where('agent_id', $agent->id)
            ->withCount(['visits', 'conversions'])
            ->get();

        return view('agent.marketing.landing-pages', compact(
            'agent',
            'landingPages'
        ));
    }

    /**
     * Show the form for creating a new landing page.
     *
     * @return \Illuminate\View\View
     */
    public function createLandingPage()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get templates
        $templates = [
            'basic' => 'Basic Landing Page',
            'professional' => 'Professional Landing Page',
            'promotional' => 'Promotional Landing Page'
        ];

        return view('agent.marketing.create-landing-page', compact(
            'agent',
            'templates'
        ));
    }

    /**
     * Store a newly created landing page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeLandingPage(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Validate the request
        $request->validate([
            'title' => 'required|string|max:255',
            'template' => 'required|in:basic,professional,promotional',
            'content' => 'required|string',
            'settings' => 'nullable|array'
        ]);

        // Generate slug
        $slug = Str::slug($request->title) . '-' . Str::random(5);

        // Create landing page
        $landingPage = AgentLandingPage::create([
            'agent_id' => $agent->id,
            'title' => $request->title,
            'slug' => $slug,
            'content' => $request->content,
            'settings' => $request->settings ?? [],
            'is_active' => true
        ]);

        Toast::success('Landing page created successfully!')->autoDismiss(10);

        return redirect()->route('agent.marketing.landing-pages');
    }

    /**
     * Show the form for editing a landing page.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function editLandingPage($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
            ->where('id', $id)
            ->firstOrFail();

        // Get templates
        $templates = [
            'basic' => 'Basic Landing Page',
            'professional' => 'Professional Landing Page',
            'promotional' => 'Promotional Landing Page'
        ];

        return view('agent.marketing.edit-landing-page', compact(
            'agent',
            'landingPage',
            'templates'
        ));
    }

    /**
     * Update a landing page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateLandingPage(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
            ->where('id', $id)
            ->firstOrFail();

        // Validate the request
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'settings' => 'nullable|array',
            'is_active' => 'boolean'
        ]);

        // Update landing page
        $landingPage->update([
            'title' => $request->title,
            'content' => $request->content,
            'settings' => $request->settings ?? [],
            'is_active' => $request->has('is_active')
        ]);

        Toast::success('Landing page updated successfully!')->autoDismiss(10);

        return redirect()->route('agent.marketing.landing-pages');
    }

    /**
     * Delete a landing page.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteLandingPage($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $landingPage = AgentLandingPage::where('agent_id', $agent->id)
            ->where('id', $id)
            ->firstOrFail();

        // Delete landing page
        $landingPage->delete();

        Toast::success('Landing page deleted successfully!')->autoDismiss(10);

        return redirect()->route('agent.marketing.landing-pages');
    }

    /**
     * Personalize content for an agent.
     *
     * @param  \App\Models\AgentMarketingMaterial  $material
     * @param  \App\Models\Agent  $agent
     * @return string
     */
    protected function personalizeContent(AgentMarketingMaterial $material, Agent $agent)
    {
        $content = $material->template_content ?? '';

        // Replace placeholders with agent information
        $replacements = [
            '[AGENT_NAME]' => $agent->user->name,
            '[AGENT_EMAIL]' => $agent->user->email,
            '[AGENT_PHONE]' => $agent->user->phone ?? 'Contact me',
            '[COMPANY_NAME]' => $agent->company ?? 'Our Company',
            '[REFERRAL_CODE]' => $agent->referral_code,
            '[REFERRAL_LINK]' => route('referral.landing', ['code' => $agent->referral_code])
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }

    /**
     * Get the sharing URL for a platform.
     *
     * @param  string  $platform
     * @param  string  $content
     * @param  string  $url
     * @return string
     */
    protected function getSharingUrl($platform, $content, $url)
    {
        $title = substr($content, 0, 100);

        switch ($platform) {
            case 'facebook':
                return 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($url);

            case 'twitter':
                return 'https://twitter.com/intent/tweet?text=' . urlencode($title) . '&url=' . urlencode($url);

            case 'linkedin':
                return 'https://www.linkedin.com/sharing/share-offsite/?url=' . urlencode($url);

            case 'email':
                return 'mailto:?subject=' . urlencode($title) . '&body=' . urlencode($content . "\n\n" . $url);

            default:
                return $url;
        }
    }
}
