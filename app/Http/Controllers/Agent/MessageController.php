<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentMessage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class MessageController extends Controller
{
    /**
     * Display the agent's inbox.
     *
     * @return \Illuminate\View\View
     */
    public function inbox()
    {
        $user = Auth::user();

        // Get received messages
        $messages = AgentMessage::where('recipient_id', $user->id)
            ->with('sender:id,name')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Get unread count
        $unreadCount = AgentMessage::where('recipient_id', $user->id)
            ->where('is_read', false)
            ->count();

        return view('agent.messages.inbox', compact(
            'messages',
            'unreadCount'
        ));
    }

    /**
     * Display the agent's sent messages.
     *
     * @return \Illuminate\View\View
     */
    public function sent()
    {
        $user = Auth::user();

        // Get sent messages
        $messages = AgentMessage::where('sender_id', $user->id)
            ->with('recipient:id,name')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('agent.messages.sent', compact('messages'));
    }

    /**
     * Show a specific message.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $user = Auth::user();

        // Get the message
        $message = AgentMessage::where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->with(['sender:id,name', 'recipient:id,name'])
            ->findOrFail($id);

        // Mark as read if the user is the recipient
        if ($message->recipient_id === $user->id && !$message->is_read) {
            $message->markAsRead();
        }

        return view('agent.messages.show', compact('message'));
    }

    /**
     * Show the form for composing a new message.
     *
     * @return \Illuminate\View\View
     */
    public function compose()
    {
        $user = Auth::user();

        // Get potential recipients (all agents and admins)
        $recipients = User::whereHas('roles', function($query) {
                $query->whereIn('name', ['agent', 'admin']);
            })
            ->where('id', '!=', $user->id)
            ->orderBy('name')
            ->get(['id', 'name']);

        return view('agent.messages.compose', compact('recipients'));
    }

    /**
     * Send a new message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function send(Request $request)
    {
        $user = Auth::user();

        // Validate the request
        $request->validate([
            'recipient_id' => 'required|exists:users,id',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Create the message
        $message = AgentMessage::create([
            'sender_id' => $user->id,
            'recipient_id' => $request->recipient_id,
            'subject' => $request->subject,
            'message' => $request->message,
            'is_read' => false,
        ]);

        // Notify the recipient
        $recipient = User::find($request->recipient_id);
        $recipient->notify(new \App\Notifications\NewMessageNotification($message));

        Toast::success('Message sent successfully!')->autoDismiss(10);

        return redirect()->route('agent.messages.sent');
    }

    /**
     * Reply to a message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reply(Request $request, $id)
    {
        $user = Auth::user();

        // Get the original message
        $originalMessage = AgentMessage::where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->findOrFail($id);

        // Validate the request
        $request->validate([
            'message' => 'required|string',
        ]);

        // Create the reply
        $reply = AgentMessage::create([
            'sender_id' => $user->id,
            'recipient_id' => $originalMessage->sender_id === $user->id
                ? $originalMessage->recipient_id
                : $originalMessage->sender_id,
            'subject' => 'Re: ' . $originalMessage->subject,
            'message' => $request->message,
            'is_read' => false,
        ]);

        // Notify the recipient
        $recipient = User::find($reply->recipient_id);
        $recipient->notify(new \App\Notifications\NewMessageNotification($reply));

        Toast::success('Reply sent successfully!')->autoDismiss(10);

        return redirect()->route('agent.messages.show', $reply->id);
    }

    /**
     * Delete a message.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete($id)
    {
        $user = Auth::user();

        // Get the message
        $message = AgentMessage::where(function($query) use ($user) {
                $query->where('recipient_id', $user->id)
                      ->orWhere('sender_id', $user->id);
            })
            ->findOrFail($id);

        // Delete the message
        $message->delete();

        Toast::success('Message deleted successfully!')->autoDismiss(10);

        return redirect()->route('agent.messages.inbox');
    }

    /**
     * Mark a message as read.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead($id)
    {
        $user = Auth::user();

        // Get the message
        $message = AgentMessage::where('recipient_id', $user->id)
            ->findOrFail($id);

        // Mark as read
        $message->markAsRead();

        Toast::success('Message marked as read!')->autoDismiss(10);

        return redirect()->back();
    }

    /**
     * Mark all messages as read.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAllAsRead()
    {
        $user = Auth::user();

        // Mark all messages as read
        AgentMessage::where('recipient_id', $user->id)
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        Toast::success('All messages marked as read!')->autoDismiss(10);

        return redirect()->back();
    }
}
