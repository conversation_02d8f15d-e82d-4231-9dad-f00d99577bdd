<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class NotificationSettingsController extends Controller
{
    /**
     * Display the notification settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to view notification settings.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Get notification settings for the agent
        $settings = NotificationSetting::firstOrCreate(
            ['agent_id' => $agent->id],
            [
                'referral_notifications' => true,
                'commission_notifications' => true,
                'payment_notifications' => true,
                'status_notifications' => true,
                'notification_frequency' => 'immediate',
            ]
        );
        
        return view('agent.notifications.settings', compact('settings'));
    }
    
    /**
     * Update the notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to update notification settings.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Validate the request
        $validated = $request->validate([
            'referral_notifications' => 'boolean',
            'commission_notifications' => 'boolean',
            'payment_notifications' => 'boolean',
            'status_notifications' => 'boolean',
            'notification_frequency' => 'required|in:immediate,daily,weekly',
        ]);
        
        // Update or create notification settings
        NotificationSetting::updateOrCreate(
            ['agent_id' => $agent->id],
            $validated
        );
        
        Toast::success('Notification settings updated successfully.')->autoDismiss(10);
        return redirect()->route('agent.notifications.settings');
    }
    
    /**
     * Test notification delivery.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function testNotification()
    {
        $agent = Agent::where('user_id', Auth::id())->first();
        
        if (!$agent) {
            Toast::danger('You must be an agent to test notifications.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }
        
        // Send a test notification
        $user = Auth::user();
        $user->notify(new \App\Notifications\TestNotification($agent));
        
        Toast::success('Test notification sent successfully. Please check your email.')->autoDismiss(10);
        return redirect()->route('agent.notifications.settings');
    }
}
