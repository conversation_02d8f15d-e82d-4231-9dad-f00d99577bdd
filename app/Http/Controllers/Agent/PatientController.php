<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\MedicalCondition;
use App\Models\MedicalQuestionnaire;
use App\Models\MedicationOrder;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class PatientController extends Controller
{
    /**
     * Display a listing of patients enrolled by the agent.
     */
    public function index()
    {
        $agent = Auth::user()->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('agent.dashboard');
        }

        // Get patient IDs enrolled by this agent
        $patientIds = Subscription::where('agent_id', $agent->id)
            ->where('user_type', 'patient')
            ->pluck('user_id')
            ->toArray();

        $patients = SpladeTable::for(User::whereIn('id', $patientIds))
            ->column('name', sortable: true)
            ->column('email', sortable: true)
            ->column('created_at', label: 'Registered', sortable: true)
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc');

        return view('agent.patients.index', [
            'patients' => $patients,
        ]);
    }

    /**
     * Display the specified patient.
     */
    public function show(User $patient)
    {
        $agent = Auth::user()->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.');
            return redirect()->route('agent.dashboard');
        }

        // Check if this patient was enrolled by the agent
        $subscription = Subscription::where('agent_id', $agent->id)
            ->where('user_id', $patient->id)
            ->where('user_type', 'patient')
            ->first();

        if (!$subscription) {
            Toast::danger('You do not have permission to view this patient.');
            return redirect()->route('agent.patients.index');
        }

        // Get patient's medical conditions
        $medicalConditions = MedicalCondition::where('patient_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get patient's medical questionnaires
        $questionnaires = MedicalQuestionnaire::where('user_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get patient's medication orders
        $medicationOrders = MedicationOrder::where('patient_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('agent.patients.show', [
            'patient' => $patient,
            'subscription' => $subscription,
            'medicalConditions' => $medicalConditions,
            'questionnaires' => $questionnaires,
            'medicationOrders' => $medicationOrders,
        ]);
    }
}
