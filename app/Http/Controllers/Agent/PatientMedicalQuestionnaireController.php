<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use App\Traits\HandlesMedicalQuestionnaires;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class PatientMedicalQuestionnaireController extends Controller
{
    use HandlesMedicalQuestionnaires;

    /**
     * Display a listing of the patient's medical questionnaires.
     */
    public function index(User $patient)
    {
        // Check if the agent has permission to view this patient's questionnaires
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to view this patient\'s medical questionnaires.')->autoDismiss(3);
            return redirect()->back();
        }

        $questionnaires = MedicalQuestionnaire::where('user_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('agent.patients.medical-questionnaires.index', [
            'patient' => $patient,
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Show the form for creating a new medical questionnaire.
     */
    public function create(User $patient)
    {
        // Check if the agent has permission to create a questionnaire for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medical questionnaire for this patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Get form data
        $formData = $this->getFormData($patient);

        return view('agent.patients.medical-questionnaires.create', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Store a newly created medical questionnaire.
     */
    public function store(Request $request, User $patient)
    {
        // Check if the agent has permission to create a questionnaire for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medical questionnaire for this patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Store the questionnaire
        $this->storeQuestionnaire($request, $patient);

        Toast::success('Medical questionnaire created successfully.')->autoDismiss(3);
        return redirect()->route('agent.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Show the form for editing a medical questionnaire.
     */
    public function edit(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the agent has permission to edit this patient's questionnaire
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to edit this patient\'s medical questionnaire.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Get form data
        $formData = $this->getFormData($patient);
        $formData['questionnaire'] = $questionnaire;

        return view('agent.patients.medical-questionnaires.edit', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Update a medical questionnaire.
     */
    public function update(Request $request, User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the agent has permission to update this patient's questionnaire
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to update this patient\'s medical questionnaire.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Update the questionnaire
        $this->updateQuestionnaire($request, $patient, $questionnaire);

        Toast::success('Medical questionnaire updated successfully.')->autoDismiss(3);
        return redirect()->route('agent.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Display the specified medical questionnaire.
     */
    public function show(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the agent has permission to view this patient's questionnaire
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to view this patient\'s medical questionnaire.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Load related data
        $questionnaire->load(['user', 'treatment']);

        return view('agent.patients.medical-questionnaires.show', [
            'patient' => $patient,
            'questionnaire' => $questionnaire
        ]);
    }
}
