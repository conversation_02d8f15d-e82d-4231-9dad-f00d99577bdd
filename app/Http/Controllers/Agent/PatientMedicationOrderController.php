<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Medication;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class PatientMedicationOrderController extends Controller
{
    /**
     * Display a listing of the patient's medication orders.
     */
    public function index(User $patient)
    {
        // Check if the agent has permission to view this patient's orders
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to view this patient\'s medication orders.');
            return redirect()->back();
        }

        // Create a query builder for the medication orders
        $query = MedicationOrder::where('patient_id', $patient->id);

        $orders = SpladeTable::for($query)
            ->column('id', label: 'Order #')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status', as: fn ($status) => MedicationOrder::STATUSES[$status] ?? $status)
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $order) => $order->doctor ? $order->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('agent.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $order]))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('agent.patients.medication-orders.index', [
            'patient' => $patient,
            'orders' => $orders,
        ]);
    }

    /**
     * Show the form for creating a new medication order.
     */
    public function create(User $patient)
    {
        // Check if the agent has permission to create an order for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medication order for this patient.');
            return redirect()->back();
        }

        $medications = Medication::where('status', 'approved')->orderBy('name')->get();

        return view('agent.patients.medication-orders.create', [
            'patient' => $patient,
            'medications' => $medications,
        ]);
    }

    /**
     * Store a newly created medication order in storage.
     */
    public function store(Request $request, User $patient)
    {
        // Check if the agent has permission to create an order for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medication order for this patient.');
            return redirect()->back();
        }

        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required_if:medications.*.custom_medication_name,null',
            'medications.*.custom_medication_name' => 'required_if:medications.*.medication_id,null|string|max:255',
            'medications.*.custom_medication_details' => 'nullable|string|max:1000',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
            'authorization_confirmed' => 'accepted'
        ]);

        // Validate that each medication has either medication_id or custom_medication_name
        // foreach ($request->medications as $index => $medication) {
        //     if (empty($medication['medication_id']) && empty($medication['custom_medication_name'])) {
        //         return back()->withErrors([
        //             "medications.{$index}.medication_id" => 'Either a medication must be selected or a custom medication name must be provided.'
        //         ])->withInput();
        //     }
        // }

        // Create the medication order
        $order = MedicationOrder::create([
            'patient_id' => $patient->id,
            'status' => 'pending',
            'patient_notes' => $request->patient_notes,
        ]);

        // Create the medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $order->id,
                'medication_id' => !empty($medication['medication_id']) ? $medication['medication_id'] : null,
                'custom_medication_name' => $medication['custom_medication_name'] ?? null,
                'custom_medication_details' => $medication['custom_details'] ?? null,
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order created successfully.');
        return redirect()->route('agent.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $order]);
    }

    /**
     * Display the specified medication order.
     */
    public function show(User $patient, MedicationOrder $medicationOrder)
    {
        // Check if the agent has permission to view this patient's order
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to view this patient\'s medication order.');
            return redirect()->back();
        }

        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        $medicationOrder->load(['items.medication', 'doctor', 'prescription.items']);

        return view('agent.patients.medication-orders.show', [
            'patient' => $patient,
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for editing the specified medication order.
     */
    public function edit(User $patient, MedicationOrder $medicationOrder)
    {
        // Check if the agent has permission to edit this patient's order
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to edit this patient\'s medication order.');
            return redirect()->back();
        }

        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        // Only allow editing of pending orders
        if ($medicationOrder->status !== 'pending') {
            Toast::warning('Only pending orders can be edited.');
            return redirect()->route('agent.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $medicationOrder]);
        }

        $medications = Medication::where('status', 'approved')->orderBy('name')->get();

        return view('agent.patients.medication-orders.edit', [
            'patient' => $patient,
            'order' => $medicationOrder,
            'medications' => $medications,
        ]);
    }

    /**
     * Update the specified medication order in storage.
     */
    public function update(Request $request, User $patient, MedicationOrder $medicationOrder)
    {
        // Check if the agent has permission to update this patient's order
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to update this patient\'s medication order.');
            return redirect()->back();
        }

        // Ensure the order belongs to the patient
        if ($medicationOrder->patient_id !== $patient->id) {
            Toast::danger('This medication order does not belong to the specified patient.');
            return redirect()->back();
        }

        // Only allow updating of pending orders
        if ($medicationOrder->status !== 'pending') {
            Toast::warning('Only pending orders can be updated.');
            return redirect()->route('agent.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $medicationOrder]);
        }

        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'nullable|exists:medications,id',
            'medications.*.custom_name' => 'nullable|string|max:255',
            'medications.*.custom_details' => 'nullable|string|max:1000',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
            'authorization_confirmed' => 'required|accepted',
        ]);

        // Validate that each medication has either medication_id or custom_name
        foreach ($request->medications as $index => $medication) {
            if (empty($medication['medication_id']) && empty($medication['custom_name'])) {
                return back()->withErrors([
                    "medications.{$index}.custom_name" => 'Either a medication must be selected or a custom medication name must be provided.'
                ])->withInput();
            }
        }

        // Update the medication order
        $medicationOrder->update([
            'patient_notes' => $request->patient_notes,
        ]);

        // Delete existing items
        $medicationOrder->items()->delete();

        // Create new medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $medicationOrder->id,
                'medication_id' => !empty($medication['medication_id']) ? $medication['medication_id'] : null,
                'custom_medication_name' => $medication['custom_name'] ?? null,
                'custom_medication_details' => $medication['custom_details'] ?? null,
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order updated successfully.');
        return redirect()->route('agent.patients.medication-orders.show', ['patient' => $patient, 'medicationOrder' => $medicationOrder]);
    }

    /**
     * Check if a patient was enrolled by an agent
     *
     * @param User $patient
     * @param \App\Models\Agent $agent
     * @return bool
     */
    private function isPatientEnrolledByAgent(User $patient, $agent)
    {
        // Check if the patient has any subscriptions with the agent's ID in the agent_id field
        return $patient->subscriptions()
            ->where('agent_id', $agent->id)
            ->exists();
    }
}
