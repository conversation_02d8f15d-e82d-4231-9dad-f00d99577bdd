<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class ProfileController extends Controller
{
    /**
     * Show the agent profile edit form.
     */
    public function edit(Request $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.')->autoDismiss(5);
            return redirect()->route('agent.dashboard');
        }

        return view('agent.profile.edit', compact('user', 'agent'));
    }

    /**
     * Update the agent profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $agent = $user->agent;

        if (!$agent) {
            Toast::danger('Agent profile not found.')->autoDismiss(5);
            return redirect()->route('agent.dashboard');
        }

        $validated = $request->validate([
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'required|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
            'gender' => 'nullable|in:M,F,O',
            'dob' => 'nullable|date|before:today',
            'address1' => 'nullable|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:2',
            'zip' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'image' => 'nullable|image|max:2048',
            // Agent-specific fields
            'company' => 'required|string|max:255',
            'experience' => 'required|string|in:new,1-3,3-5,5+',
            'npn' => 'required|string|max:50',
            'agree_to_terms' => 'required|accepted',
        ]);

        // Update name
        $validated['name'] = $validated['fname'] . ' ' . $validated['lname'];

        // Set terms agreement timestamp
        if ($validated['agree_to_terms']) {
            $validated['terms_agreed_at'] = now();
        }

        // Handle image upload
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            // Delete old image if it exists
            if ($user->image && Storage::disk('public')->exists($user->image)) {
                Storage::disk('public')->delete($user->image);
            }

            $imagePath = $request->file('image')->store('profile_images', 'public');
            $validated['image'] = $imagePath;
        } else {
            // Remove image from validated data if no file was uploaded
            unset($validated['image']);
        }

        // Separate user and agent data
        $userData = collect($validated)->except(['company', 'experience', 'npn', 'agree_to_terms'])->toArray();
        $agentData = collect($validated)->only(['company', 'experience', 'npn'])->toArray();

        $user->update($userData);
        $agent->update($agentData);

        Toast::success('Profile updated successfully!')->autoDismiss(5);

        return redirect()->route('agent.profile.edit');
    }
}
