<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentCustomReport;
use App\Models\AgentReportExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use League\Csv\Writer;
use ProtoneMedia\Splade\Facades\Toast;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get custom reports
        $reports = AgentCustomReport::where('agent_id', $agent->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Get recent exports
        $recentExports = AgentReportExport::where('agent_id', $agent->id)
            ->with('report')
            ->orderBy('generated_at', 'desc')
            ->limit(5)
            ->get();

        return view('agent.reports.index', compact(
            'agent',
            'reports',
            'recentExports'
        ));
    }

    /**
     * Show the form for creating a new custom report.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        return view('agent.reports.create', compact('agent'));
    }

    /**
     * Store a newly created custom report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:commission,referral,performance',
            'filters' => 'nullable|array',
            'columns' => 'required|array',
            'sorting' => 'nullable|array',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,1|in:daily,weekly,monthly',
            'schedule_time' => 'required_if:is_scheduled,1',
        ]);

        // Create the report
        $report = AgentCustomReport::create([
            'agent_id' => $agent->id,
            'name' => $request->name,
            'type' => $request->type,
            'filters' => $request->filters ?? [],
            'columns' => $request->columns,
            'sorting' => $request->sorting ?? [],
            'is_scheduled' => $request->has('is_scheduled'),
            'schedule_frequency' => $request->schedule_frequency,
            'schedule_time' => $request->schedule_time,
        ]);

        Toast::success('Custom report created successfully!')->autoDismiss(10);

        return redirect()->route('agent.reports.show', $report->id);
    }

    /**
     * Display a specific custom report.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the report
        $report = AgentCustomReport::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Get report data
        $reportData = $report->generateData();

        // Get recent exports
        $recentExports = AgentReportExport::where('agent_id', $agent->id)
            ->where('report_id', $report->id)
            ->orderBy('generated_at', 'desc')
            ->limit(5)
            ->get();

        return view('agent.reports.show', compact(
            'agent',
            'report',
            'reportData',
            'recentExports'
        ));
    }

    /**
     * Show the form for editing a custom report.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the report
        $report = AgentCustomReport::where('agent_id', $agent->id)
            ->findOrFail($id);

        return view('agent.reports.edit', compact(
            'agent',
            'report'
        ));
    }

    /**
     * Update a custom report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the report
        $report = AgentCustomReport::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'filters' => 'nullable|array',
            'columns' => 'required|array',
            'sorting' => 'nullable|array',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,1|in:daily,weekly,monthly',
            'schedule_time' => 'required_if:is_scheduled,1',
        ]);

        // Update the report
        $report->update([
            'name' => $request->name,
            'filters' => $request->filters ?? [],
            'columns' => $request->columns,
            'sorting' => $request->sorting ?? [],
            'is_scheduled' => $request->has('is_scheduled'),
            'schedule_frequency' => $request->schedule_frequency,
            'schedule_time' => $request->schedule_time,
        ]);

        Toast::success('Custom report updated successfully!')->autoDismiss(10);

        return redirect()->route('agent.reports.show', $report->id);
    }

    /**
     * Delete a custom report.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the report
        $report = AgentCustomReport::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Delete the report
        $report->delete();

        Toast::success('Custom report deleted successfully!')->autoDismiss(10);

        return redirect()->route('agent.reports.index');
    }

    /**
     * Export a custom report.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the report
        $report = AgentCustomReport::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Validate the request
        $request->validate([
            'format' => 'required|in:csv,xlsx,pdf',
        ]);

        // Generate report data
        $reportData = $report->generateData();

        // Create export record
        $export = AgentReportExport::create([
            'agent_id' => $agent->id,
            'report_id' => $report->id,
            'report_type' => $report->type,
            'format' => $request->format,
            'generated_at' => now(),
        ]);

        // Generate export file
        $filePath = $this->generateExportFile($report, $reportData, $request->format, $export->id);

        // Update export record with file path
        $export->update([
            'file_path' => $filePath,
        ]);

        if ($request->has('download')) {
            return Storage::download($filePath, $report->name . '.' . $request->format);
        }

        Toast::success('Report exported successfully!')->autoDismiss(10);

        return redirect()->route('agent.reports.show', $report->id);
    }

    /**
     * Download an export file.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the export
        $export = AgentReportExport::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Check if file exists
        if (!Storage::exists($export->file_path)) {
            Toast::danger('Export file not found!')->autoDismiss(10);
            return redirect()->back();
        }

        return Storage::download($export->file_path, $export->report->name . '.' . $export->format);
    }

    /**
     * Generate an export file.
     *
     * @param  \App\Models\AgentCustomReport  $report
     * @param  \Illuminate\Support\Collection  $data
     * @param  string  $format
     * @param  int  $exportId
     * @return string
     */
    protected function generateExportFile(AgentCustomReport $report, $data, $format, $exportId)
    {
        $fileName = Str::slug($report->name) . '-' . date('Y-m-d-His') . '.' . $format;
        $filePath = 'exports/' . $report->agent_id . '/' . $fileName;

        switch ($format) {
            case 'csv':
                return $this->generateCsvFile($report, $data, $filePath);

            case 'xlsx':
                return $this->generateXlsxFile($report, $data, $filePath);

            case 'pdf':
                return $this->generatePdfFile($report, $data, $filePath);

            default:
                return $this->generateCsvFile($report, $data, $filePath);
        }
    }

    /**
     * Generate a CSV export file.
     *
     * @param  \App\Models\AgentCustomReport  $report
     * @param  \Illuminate\Support\Collection  $data
     * @param  string  $filePath
     * @return string
     */
    protected function generateCsvFile(AgentCustomReport $report, $data, $filePath)
    {
        $csv = Writer::createFromString('');

        // Add headers
        $headers = [];
        foreach ($report->columns as $column) {
            $headers[] = $this->getColumnLabel($column);
        }
        $csv->insertOne($headers);

        // Add data rows
        foreach ($data as $row) {
            $csvRow = [];
            foreach ($report->columns as $column) {
                $csvRow[] = $row->{$column} ?? '';
            }
            $csv->insertOne($csvRow);
        }

        // Save the file
        Storage::put($filePath, $csv->getContent());

        return $filePath;
    }

    /**
     * Generate an XLSX export file.
     *
     * @param  \App\Models\AgentCustomReport  $report
     * @param  \Illuminate\Support\Collection  $data
     * @param  string  $filePath
     * @return string
     */
    protected function generateXlsxFile(AgentCustomReport $report, $data, $filePath)
    {
        // For simplicity, we'll just generate a CSV file for now
        // In a real implementation, you would use a library like PhpSpreadsheet
        return $this->generateCsvFile($report, $data, str_replace('.xlsx', '.csv', $filePath));
    }

    /**
     * Generate a PDF export file.
     *
     * @param  \App\Models\AgentCustomReport  $report
     * @param  \Illuminate\Support\Collection  $data
     * @param  string  $filePath
     * @return string
     */
    protected function generatePdfFile(AgentCustomReport $report, $data, $filePath)
    {
        // Generate PDF using a view
        $pdf = app()->make('dompdf.wrapper');
        $pdf->loadView('agent.reports.pdf', [
            'report' => $report,
            'data' => $data,
            'columns' => $report->columns,
            'columnLabels' => array_map([$this, 'getColumnLabel'], $report->columns),
        ]);

        // Save the file
        Storage::put($filePath, $pdf->output());

        return $filePath;
    }

    /**
     * Get a human-readable label for a column.
     *
     * @param  string  $column
     * @return string
     */
    protected function getColumnLabel($column)
    {
        $labels = [
            'id' => 'ID',
            'created_at' => 'Date',
            'amount' => 'Amount',
            'status' => 'Status',
            'source' => 'Source',
            'description' => 'Description',
            'agent_id' => 'Agent ID',
            'upline_agent_id' => 'Upline Agent ID',
            'referring_agent_id' => 'Referring Agent ID',
            'referred_agent_id' => 'Referred Agent ID',
            'referred_user_id' => 'Referred User ID',
            'referred_user_name' => 'Referred User Name',
            'referred_user_email' => 'Referred User Email',
        ];

        return $labels[$column] ?? Str::title(str_replace('_', ' ', $column));
    }
}
