<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentSupportTicket;
use App\Models\AgentSupportTicketReply;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class SupportTicketController extends Controller
{
    /**
     * Display the support tickets page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get open tickets
        $openTickets = AgentSupportTicket::where('agent_id', $agent->id)
            ->open()
            ->with(['assignedTo:id,name', 'latestReply'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get resolved tickets
        $resolvedTickets = AgentSupportTicket::where('agent_id', $agent->id)
            ->resolved()
            ->with(['assignedTo:id,name', 'latestReply'])
            ->orderBy('resolved_at', 'desc')
            ->limit(5)
            ->get();

        return view('agent.support.index', compact(
            'agent',
            'openTickets',
            'resolvedTickets'
        ));
    }

    /**
     * Show the form for creating a new support ticket.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        return view('agent.support.create', compact('agent'));
    }

    /**
     * Store a newly created support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Validate the request
        $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
        ]);

        // Create the ticket
        $ticket = AgentSupportTicket::create([
            'agent_id' => $agent->id,
            'subject' => $request->subject,
            'description' => $request->description,
            'status' => 'open',
        ]);

        // Notify admins
        $admins = User::role('admin')->get();
        foreach ($admins as $admin) {
            $admin->notify(new \App\Notifications\NewSupportTicketNotification($ticket));
        }

        Toast::success('Support ticket created successfully!')->autoDismiss(10);

        return redirect()->route('agent.support.show', $ticket->id);
    }

    /**
     * Display a specific support ticket.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the ticket
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
            ->with(['assignedTo:id,name', 'replies.user:id,name'])
            ->findOrFail($id);

        // Get replies
        $replies = $ticket->replies()
            ->with('user:id,name')
            ->orderBy('created_at', 'asc')
            ->get();

        return view('agent.support.show', compact(
            'agent',
            'ticket',
            'replies'
        ));
    }

    /**
     * Add a reply to a support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reply(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the ticket
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Validate the request
        $request->validate([
            'message' => 'required|string',
        ]);

        // Create the reply
        $reply = AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'is_internal' => false,
        ]);

        // Update ticket status if it was resolved
        if ($ticket->status === 'resolved') {
            $ticket->update([
                'status' => 'open',
                'resolved_at' => null,
            ]);
        }

        // Notify assigned admin if any
        if ($ticket->assigned_to) {
            $admin = User::find($ticket->assigned_to);
            $admin->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply));
        } else {
            // Notify all admins
            $admins = User::role('admin')->get();
            foreach ($admins as $admin) {
                $admin->notify(new \App\Notifications\SupportTicketReplyNotification($ticket, $reply));
            }
        }

        Toast::success('Reply added successfully!')->autoDismiss(10);

        return redirect()->route('agent.support.show', $ticket->id);
    }

    /**
     * Close a support ticket.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function close($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the ticket
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Update ticket status
        $ticket->update([
            'status' => 'closed',
            'resolved_at' => now(),
        ]);

        // Add a system reply
        AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => Auth::id(),
            'message' => 'This ticket has been closed by the agent.',
            'is_internal' => false,
        ]);

        Toast::success('Ticket closed successfully!')->autoDismiss(10);

        return redirect()->route('agent.support.index');
    }

    /**
     * Reopen a support ticket.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reopen($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get the ticket
        $ticket = AgentSupportTicket::where('agent_id', $agent->id)
            ->findOrFail($id);

        // Update ticket status
        $ticket->update([
            'status' => 'open',
            'resolved_at' => null,
        ]);

        // Add a system reply
        AgentSupportTicketReply::create([
            'ticket_id' => $ticket->id,
            'user_id' => Auth::id(),
            'message' => 'This ticket has been reopened by the agent.',
            'is_internal' => false,
        ]);

        Toast::success('Ticket reopened successfully!')->autoDismiss(10);

        return redirect()->route('agent.support.show', $ticket->id);
    }

    /**
     * Display all resolved tickets.
     *
     * @return \Illuminate\View\View
     */
    public function resolved()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get resolved tickets
        $resolvedTickets = AgentSupportTicket::where('agent_id', $agent->id)
            ->resolved()
            ->with(['assignedTo:id,name', 'latestReply'])
            ->orderBy('resolved_at', 'desc')
            ->paginate(15);

        return view('agent.support.resolved', compact(
            'agent',
            'resolvedTickets'
        ));
    }
}
