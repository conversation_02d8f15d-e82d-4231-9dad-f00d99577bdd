<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\AgentTrainingMaterial;
use App\Models\AgentTrainingProgress;
use App\Models\AgentCertification;
use App\Models\AgentEarnedCertification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class TrainingController extends Controller
{
    /**
     * Display the training dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get required training materials
        $requiredTraining = AgentTrainingMaterial::where('is_required', true)
            ->orderBy('order')
            ->get();

        // Get optional training materials
        $optionalTraining = AgentTrainingMaterial::where('is_required', false)
            ->orderBy('order')
            ->get();

        // Get completed training IDs
        $completedTrainingIds = AgentTrainingProgress::where('agent_id', $agent->id)
            ->where('completed', true)
            ->pluck('training_material_id')
            ->toArray();

        // Calculate progress percentage
        $totalRequired = $requiredTraining->count();
        $completedRequired = 0;

        if ($totalRequired > 0) {
            $completedRequired = $requiredTraining->filter(function($material) use ($completedTrainingIds) {
                return in_array($material->id, $completedTrainingIds);
            })->count();
        }

        $progressPercentage = $totalRequired > 0
            ? round(($completedRequired / $totalRequired) * 100)
            : 100;

        // Get available and earned certifications
        $availableCertifications = AgentCertification::all();
        $earnedCertifications = AgentEarnedCertification::where('agent_id', $agent->id)
            ->with('certification')
            ->get();

        return view('agent.training.dashboard', compact(
            'agent',
            'requiredTraining',
            'optionalTraining',
            'completedTrainingIds',
            'progressPercentage',
            'availableCertifications',
            'earnedCertifications'
        ));
    }

    /**
     * Display a specific training material.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentTrainingMaterial::findOrFail($id);

        // Get or create progress record
        $progress = AgentTrainingProgress::firstOrCreate(
            [
                'agent_id' => $agent->id,
                'training_material_id' => $material->id
            ]
        );

        // Get related certifications
        $relatedCertifications = $material->certifications;

        return view('agent.training.show', compact(
            'agent',
            'material',
            'progress',
            'relatedCertifications'
        ));
    }

    /**
     * Mark a training material as completed.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markCompleted(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentTrainingMaterial::findOrFail($id);

        // Update progress record
        $progress = AgentTrainingProgress::updateOrCreate(
            [
                'agent_id' => $agent->id,
                'training_material_id' => $material->id
            ],
            [
                'completed' => true,
                'completed_at' => now(),
                'score' => $request->input('score')
            ]
        );

        // Check if any certifications can be awarded
        $this->checkCertificationEligibility($agent);

        Toast::success('Training marked as completed!')->autoDismiss(10);

        return redirect()->route('agent.training.dashboard');
    }

    /**
     * Submit a quiz for a training material.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitQuiz(Request $request, $id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $material = AgentTrainingMaterial::findOrFail($id);

        // Validate the quiz answers
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required'
        ]);

        // Calculate score (this would be more complex in a real implementation)
        $answers = $request->input('answers');
        $correctAnswers = json_decode($material->correct_answers ?? '[]', true);
        $totalQuestions = count($correctAnswers);
        $correctCount = 0;

        foreach ($answers as $questionId => $answer) {
            if (isset($correctAnswers[$questionId]) && $correctAnswers[$questionId] == $answer) {
                $correctCount++;
            }
        }

        $score = $totalQuestions > 0 ? round(($correctCount / $totalQuestions) * 100) : 0;
        $passed = $score >= 70; // 70% passing threshold

        // Update progress record
        $progress = AgentTrainingProgress::updateOrCreate(
            [
                'agent_id' => $agent->id,
                'training_material_id' => $material->id
            ],
            [
                'completed' => $passed,
                'completed_at' => $passed ? now() : null,
                'score' => $score
            ]
        );

        if ($passed) {
            // Check if any certifications can be awarded
            $this->checkCertificationEligibility($agent);

            Toast::success("Quiz completed successfully with a score of {$score}%!")->autoDismiss(10);
        } else {
            Toast::danger("Quiz score of {$score}% did not meet the passing threshold of 70%. Please try again.")->autoDismiss(10);
        }

        return redirect()->route('agent.training.show', $id);
    }

    /**
     * Check if the agent is eligible for any certifications.
     *
     * @param  \App\Models\Agent  $agent
     * @return void
     */
    protected function checkCertificationEligibility(Agent $agent)
    {
        // Get all certifications
        $certifications = AgentCertification::with('requirements')->get();

        // Get completed training IDs
        $completedTrainingIds = AgentTrainingProgress::where('agent_id', $agent->id)
            ->where('completed', true)
            ->pluck('training_material_id')
            ->toArray();

        // Get already earned certification IDs
        $earnedCertificationIds = AgentEarnedCertification::where('agent_id', $agent->id)
            ->pluck('certification_id')
            ->toArray();

        foreach ($certifications as $certification) {
            // Skip if already earned
            if (in_array($certification->id, $earnedCertificationIds)) {
                continue;
            }

            // Get required training material IDs for this certification
            $requiredTrainingIds = $certification->requirements->pluck('training_material_id')->toArray();

            // Check if all required training is completed
            $allCompleted = true;
            foreach ($requiredTrainingIds as $requiredId) {
                if (!in_array($requiredId, $completedTrainingIds)) {
                    $allCompleted = false;
                    break;
                }
            }

            // Award certification if eligible
            if ($allCompleted && count($requiredTrainingIds) >= $certification->required_training_count) {
                AgentEarnedCertification::create([
                    'agent_id' => $agent->id,
                    'certification_id' => $certification->id,
                    'earned_at' => now(),
                    'expires_at' => $certification->validity_period ? now()->addMonths($certification->validity_period) : null,
                    'status' => 'active'
                ]);

                // Notify the agent
                $agent->user->notify(new \App\Notifications\CertificationEarnedNotification($certification));
            }
        }
    }

    /**
     * Display the certifications page.
     *
     * @return \Illuminate\View\View
     */
    public function certifications()
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();

        // Get available certifications
        $availableCertifications = AgentCertification::with('trainingMaterials')->get();

        // Get earned certifications
        $earnedCertifications = AgentEarnedCertification::where('agent_id', $agent->id)
            ->with('certification')
            ->get();

        // Get completed training IDs
        $completedTrainingIds = AgentTrainingProgress::where('agent_id', $agent->id)
            ->where('completed', true)
            ->pluck('training_material_id')
            ->toArray();

        return view('agent.training.certifications', compact(
            'agent',
            'availableCertifications',
            'earnedCertifications',
            'completedTrainingIds'
        ));
    }

    /**
     * Download a certification.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadCertification($id)
    {
        $agent = Agent::where('user_id', Auth::id())->firstOrFail();
        $earnedCertification = AgentEarnedCertification::where('agent_id', $agent->id)
            ->where('id', $id)
            ->with('certification')
            ->firstOrFail();

        // Generate PDF certificate (this would be more complex in a real implementation)
        $pdf = app()->make('dompdf.wrapper');
        $pdf->loadView('agent.training.certificate', compact('agent', 'earnedCertification'));

        return $pdf->download('certificate_' . $earnedCertification->certification->name . '.pdf');
    }
}
