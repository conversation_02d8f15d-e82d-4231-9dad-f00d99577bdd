<?php

namespace App\Http\Controllers\Agents;

use App\Actions\Agents\RegisterAgentAction;
use App\Http\Controllers\Controller;
use App\Mail\AdminAgentRegistrationNotification;
use App\Mail\AgentRegistrationNotification;
use App\Models\Agent;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use ProtoneMedia\Splade\Facades\Toast;

class AgentPartnerProgramController extends Controller
{
    public function __construct(protected RegisterAgentAction $registerAgentAction) {}

    public function index(Request $request)
    {
        // Get tier from URL parameter, default to MGA if not specified
        $tier = $request->query('tier', 'MGA');

        // Validate tier is one of the allowed values
        if (!in_array($tier, array_keys(Agent::TIERS))) {
            $tier = 'MGA'; // Default to MGA if invalid tier provided
        }

        return view('front.agents.agent-partner-program', [
            'tier' => $tier
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'company' => $request->agent_type === 'agency' ? 'required|string|max:255' : 'nullable|string|max:255',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip' => 'required|string|max:20',
            'npn' => 'nullable|string|max:50',
            'agent_type' => 'required|string|in:agent,agency',
            'password' => 'required|string|min:8|confirmed',
            'experience' => 'required|string',
            'terms' => 'required|accepted',
        ]);

        try {
            DB::beginTransaction();

            // Get tier from request or use MGA as default
            $tier = $request->input('tier', 'MGA');

            // Validate tier is one of the allowed values
            if (!in_array($tier, array_keys(Agent::TIERS))) {
                $tier = 'MGA'; // Default to MGA if invalid tier provided
            }

            $name = $request->first_name . ' ' . $request->last_name;

            // Use the RegisterAgentAction to create the agent
            $result = $this->registerAgentAction->execute([
                'name' => $name,
                'email' => $request->email,
                'password' => $request->password,
                'fname' => $request->first_name,
                'lname' => $request->last_name,
                'phone' => $request->phone,
                'company' => $request->company,
                'experience' => $request->experience,
                'tier' => $tier,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'npn' => $request->npn,
                'referring_agent_id' => null // No referrer for direct applications
            ]);

            if (!$result['success']) {
                throw new Exception($result['message']);
            }

            DB::commit();

            // Send email notification to the agent
            try {
                Mail::to($result['user']->email)->send(new AgentRegistrationNotification(
                    user: $result['user'],
                    agent: $result['agent']
                ));

                Log::info('Agent registration email sent', [
                    'agent_id' => $result['agent']->id,
                    'user_id' => $result['user']->id,
                    'email' => $result['user']->email
                ]);
            } catch (Exception $e) {
                // Log the error but don't fail the registration process
                Log::error('Failed to send agent registration email: ' . $e->getMessage(), [
                    'agent_id' => $result['agent']->id,
                    'user_id' => $result['user']->id,
                    'email' => $result['user']->email
                ]);
            }

            // Send notification to admin
            try {
                // Get admin email from config, or use a default
                $adminEmail = config('mail.admin_email', '<EMAIL>');

                Mail::to($adminEmail)->send(new AdminAgentRegistrationNotification(
                    user: $result['user'],
                    agent: $result['agent']
                ));

                Log::info('Admin agent registration notification sent', [
                    'agent_id' => $result['agent']->id,
                    'admin_email' => $adminEmail
                ]);
            } catch (Exception $e) {
                // Log the error but don't fail the registration process
                Log::error('Failed to send admin agent registration notification: ' . $e->getMessage(), [
                    'agent_id' => $result['agent']->id,
                    'error' => $e->getMessage()
                ]);
            }

            // Automatically log in the agent
            Auth::login($result['user']);

            Toast::success('Thank You for Applying! You have been automatically logged in.')->autoDismiss(5);
            return redirect()->route('agent.dashboard');

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Agent partner program registration failed: ' . $e->getMessage());
            Toast::danger('Something went wrong: ' . $e->getMessage())->autoDismiss(5);
            return redirect()->back()->withInput();
        }
    }

    public function thanks()
    {
        return view('front.agents.agent-partner-thanks');
    }
}
