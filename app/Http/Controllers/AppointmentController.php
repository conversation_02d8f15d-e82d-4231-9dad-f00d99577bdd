<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\User;
use App\Models\Business;
use App\Models\BusinessEmployee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;
use Illuminate\Support\Facades\Gate;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    /**
     * Display a listing of the appointments.
     * For business admins, show all employee appointments.
     * For employees, show only their appointments.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Check if user is a business admin
        if ($user->hasRole('business_admin') || $user->hasRole('business_hr')) {
            $business = Business::find($user->business_id);
            
            if (!$business) {
                Toast::danger('Business not found.');
                return redirect()->route('dashboard');
            }
            
            // Get all employees of this business
            $employeeIds = BusinessEmployee::where('business_id', $business->id)
                ->whereNotNull('user_id')
                ->pluck('user_id')
                ->toArray();
            
            // Get consultations for all employees
            $consultations = Consultation::whereIn('patient_id', $employeeIds)
                ->with(['patient', 'doctor'])
                ->latest('scheduled_at');
                
            return view('appointments.index', [
                'consultations' => SpladeTable::for($consultations)
                    ->column('patient.name', label: 'Employee', sortable: true)
                    ->column('doctor.name', label: 'Doctor', sortable: true)
                    ->column('scheduled_at', label: 'Date & Time', sortable: true)
                    ->column('status', sortable: true)
                    ->column('type', sortable: true)
                    ->column('actions')
                    ->selectFilter('status', Consultation::STATUSES)
                    ->paginate(10),
                'isBusinessAdmin' => true
            ]);
        } 
        // Check if user is an employee
        elseif ($user->hasRole('employee') || $user->hasRole('patient')) {
            // Get consultations for this employee/patient
            $consultations = $user->patientConsultations()
                ->with(['doctor'])
                ->latest('scheduled_at');
                
            return view('appointments.index', [
                'consultations' => SpladeTable::for($consultations)
                    ->column('doctor.name', label: 'Doctor', sortable: true)
                    ->column('scheduled_at', label: 'Date & Time', sortable: true)
                    ->column('status', sortable: true)
                    ->column('type', sortable: true)
                    ->column('actions')
                    ->selectFilter('status', Consultation::STATUSES)
                    ->paginate(10),
                'isBusinessAdmin' => false
            ]);
        } else {
            Toast::danger('You do not have permission to view appointments.');
            return redirect()->route('dashboard');
        }
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create(Request $request)
    {
        $user = $request->user();
        
        // If business admin is creating appointment for an employee
        if ($user->hasRole('business_admin') || $user->hasRole('business_hr')) {
            $business = Business::find($user->business_id);
            
            if (!$business) {
                Toast::danger('Business not found.');
                return redirect()->route('dashboard');
            }
            
            // Get all active employees of this business
            $employees = BusinessEmployee::where('business_id', $business->id)
                ->where('status', 'active')
                ->whereNotNull('user_id')
                ->with('user')
                ->get()
                ->pluck('user.name', 'user_id');
            
            // Get all available doctors
            $doctors = User::role('doctor')->where('status', 'active')->pluck('name', 'id');
            
            return view('appointments.create', [
                'employees' => $employees,
                'doctors' => $doctors,
                'isBusinessAdmin' => true
            ]);
        } 
        // If employee is creating their own appointment
        elseif ($user->hasRole('employee') || $user->hasRole('patient')) {
            // Get all available doctors
            $doctors = User::role('doctor')->where('status', 'active')->pluck('name', 'id');
            
            return view('appointments.create', [
                'doctors' => $doctors,
                'isBusinessAdmin' => false
            ]);
        } else {
            Toast::danger('You do not have permission to create appointments.');
            return redirect()->route('dashboard');
        }
    }

    /**
     * Store a newly created appointment in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();
        
        // Validate common fields
        $validatedData = $request->validate([
            'doctor_id' => 'required|exists:users,id',
            'scheduled_at' => 'required|date|after:now',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
        ]);
        
        // If business admin is creating appointment for an employee
        if ($user->hasRole('business_admin') || $user->hasRole('business_hr')) {
            // Additional validation for employee selection
            $request->validate([
                'patient_id' => 'required|exists:users,id',
            ]);
            
            $patientId = $request->input('patient_id');
            
            // Verify the patient belongs to this business
            $businessEmployee = BusinessEmployee::where('business_id', $user->business_id)
                ->where('user_id', $patientId)
                ->first();
                
            if (!$businessEmployee) {
                Toast::danger('Selected employee does not belong to your business.');
                return back()->withInput();
            }
        } 
        // If employee is creating their own appointment
        elseif ($user->hasRole('employee') || $user->hasRole('patient')) {
            $patientId = $user->id;
        } else {
            Toast::danger('You do not have permission to create appointments.');
            return redirect()->route('dashboard');
        }
        
        // Create the consultation
        $consultation = Consultation::create([
            'patient_id' => $patientId,
            'doctor_id' => $validatedData['doctor_id'],
            'scheduled_at' => $validatedData['scheduled_at'],
            'type' => $validatedData['type'],
            'patient_complaint' => $validatedData['notes'] ?? null,
            'status' => 'scheduled',
        ]);
        
        Toast::success('Appointment scheduled successfully.');
        
        return redirect()->route('appointments.index');
    }

    /**
     * Display the specified appointment.
     */
    public function show(Request $request, Consultation $appointment)
    {
        $user = $request->user();
        
        // Check if user has permission to view this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            Toast::danger('You do not have permission to view this appointment.');
            return redirect()->route('appointments.index');
        }
        
        // Load related data
        $appointment->load(['patient', 'doctor']);
        
        return view('appointments.show', [
            'appointment' => $appointment,
            'isBusinessAdmin' => $user->hasRole('business_admin') || $user->hasRole('business_hr')
        ]);
    }

    /**
     * Show the form for editing the specified appointment.
     */
    public function edit(Request $request, Consultation $appointment)
    {
        $user = $request->user();
        
        // Check if user has permission to edit this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            Toast::danger('You do not have permission to edit this appointment.');
            return redirect()->route('appointments.index');
        }
        
        // Only allow editing if appointment is scheduled (not in progress, completed, or cancelled)
        if ($appointment->status !== 'scheduled') {
            Toast::danger('Only scheduled appointments can be edited.');
            return redirect()->route('appointments.show', $appointment);
        }
        
        // Get all available doctors
        $doctors = User::role('doctor')->where('status', 'active')->pluck('name', 'id');
        
        return view('appointments.edit', [
            'appointment' => $appointment,
            'doctors' => $doctors,
            'isBusinessAdmin' => $user->hasRole('business_admin') || $user->hasRole('business_hr')
        ]);
    }

    /**
     * Update the specified appointment in storage.
     */
    public function update(Request $request, Consultation $appointment)
    {
        $user = $request->user();
        
        // Check if user has permission to update this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            Toast::danger('You do not have permission to update this appointment.');
            return redirect()->route('appointments.index');
        }
        
        // Only allow updating if appointment is scheduled (not in progress, completed, or cancelled)
        if ($appointment->status !== 'scheduled') {
            Toast::danger('Only scheduled appointments can be updated.');
            return redirect()->route('appointments.show', $appointment);
        }
        
        // Validate the request
        $validatedData = $request->validate([
            'doctor_id' => 'required|exists:users,id',
            'scheduled_at' => 'required|date|after:now',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
        ]);
        
        // Update the appointment
        $appointment->update([
            'doctor_id' => $validatedData['doctor_id'],
            'scheduled_at' => $validatedData['scheduled_at'],
            'type' => $validatedData['type'],
            'patient_complaint' => $validatedData['notes'] ?? $appointment->patient_complaint,
        ]);
        
        Toast::success('Appointment updated successfully.');
        
        return redirect()->route('appointments.show', $appointment);
    }

    /**
     * Cancel the specified appointment.
     */
    public function cancel(Request $request, Consultation $appointment)
    {
        $user = $request->user();
        
        // Check if user has permission to cancel this appointment
        if (!$this->canAccessAppointment($user, $appointment)) {
            Toast::danger('You do not have permission to cancel this appointment.');
            return redirect()->route('appointments.index');
        }
        
        // Only allow cancelling if appointment is scheduled (not in progress or completed)
        if ($appointment->status !== 'scheduled') {
            Toast::danger('Only scheduled appointments can be cancelled.');
            return redirect()->route('appointments.show', $appointment);
        }
        
        // Update the appointment status to cancelled
        $appointment->update([
            'status' => 'cancelled'
        ]);
        
        Toast::success('Appointment cancelled successfully.');
        
        return redirect()->route('appointments.index');
    }

    /**
     * Check if a user can access an appointment.
     */
    private function canAccessAppointment($user, $appointment)
    {
        // If user is the patient, they can access their own appointment
        if ($user->id === $appointment->patient_id) {
            return true;
        }
        
        // If user is a business admin, check if the patient belongs to their business
        if ($user->hasRole('business_admin') || $user->hasRole('business_hr')) {
            $businessEmployee = BusinessEmployee::where('business_id', $user->business_id)
                ->where('user_id', $appointment->patient_id)
                ->first();
                
            return $businessEmployee !== null;
        }
        
        return false;
    }
}
