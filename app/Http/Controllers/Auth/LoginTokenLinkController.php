<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;
use ProtoneMedia\Splade\Facades\Toast;

class LoginTokenLinkController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, $token)
    {
        [$id, $tokenHash] = explode('|', $token, 2);

        $tokenModel = PersonalAccessToken::find($id);

        if (!$tokenModel || !$tokenModel->tokenable ||
            !hash_equals($tokenModel->token, hash('sha256', $tokenHash)) ||
            $tokenModel->created_at->addHours(24) < now()) {
            Toast::danger('Invalid or expired login link');
            return to_route('login');
        }

        $user = $tokenModel->tokenable;

        $tokenModel->delete();

        Auth::login($user);

        return redirect()->intended('/dashboard');
    }
}
