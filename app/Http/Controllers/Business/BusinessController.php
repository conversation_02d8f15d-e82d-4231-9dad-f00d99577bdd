<?php

namespace App\Http\Controllers\Business;

use App\Actions\Business\RegisterBusinessAction;
use App\Concerns\HandlesMultiStepForms;
use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Http\Requests\Business\BusinessRegistrationRequest;
use App\Models\Business;
use App\Models\BusinessContact;
use App\Models\BusinessEmployee;
use App\Models\BusinessPlan;
use App\Models\Consultation;
use App\Models\CreditCard;
use App\Models\Transaction;
use App\Models\User;
use App\Services\BusinessTrialService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;
use Spatie\Permission\Models\Role;

class BusinessController extends Controller
{
    use HandlesMultiStepForms, TracksLinkTrust;

    public function __construct(
        protected BusinessTrialService $businessTrialService,
        protected RegisterBusinessAction $registerBusinessAction
    ) {}

    /**
     * Display the business registration form.
     */
    public function create(Request $request, $step = 1)
    {
        $default = [
            'plan_quantity' => 5,
            'plan_blocks' => 1,
            'step' => (int) $step,
        ];

        // Preserve agent referral information across steps
        $referringAgentId = Cookie::get('referring_agent_id') ?? session('referring_agent_id') ?? null;
        if ($referringAgentId) {
            $default['referring_agent_id'] = $referringAgentId;
            Log::info('Business registration step with referring agent', [
                'step' => $step,
                'referring_agent_id' => $referringAgentId
            ]);
        }

        if ($step == 2) {
            if (
                ! isset($default['billing_contact_email']) &&
                $request->session()->has('business_registration')
            ) {
                $businessRegistration = $request->session()->get('business_registration');
                $default['billing_contact_email'] ??= $businessRegistration['business_email'];
                $default['billing_contact_phone'] ??= $businessRegistration['phone'];
            }
        }

        // Get stored form data using the trait method
        $default = array_merge($default, $this->getStoredFormData('business_registration', []));

        return view('business.registration.create', [
            'default' => $default,
            'states' => config('states'),
            'steps' => ['Business Information', 'Contact Information', 'Plan Selection', 'Terms & Conditions'],
        ]);
    }

    public function store(BusinessRegistrationRequest $request)
    {
        // If not on the final step, store the data and redirect to the next step
        if ($request->step < 4) {
            // Use the trait method to store step data and redirect
            return $this->storeStepAndRedirect(
                $request,
                'business_registration',
                $request->step,
                4,
                'business.register'
            );
        }

        // Get all form data from the session
        $formData = $this->getStoredFormData('business_registration', []);

        // Merge with the current request data
        $formData = array_merge($formData, $request->except('step'));

        // Add tracking IDs
        $formData['click_id'] = Cookie::get('LTClickID') ??
                               session('LTClickID') ??
                               request()->query('ClickID');

        $formData['afid'] = Cookie::get('AFID') ??
                           session('AFID') ??
                           request()->query('AFID');

        // Add referring agent ID
        $formData['referring_agent_id'] = Cookie::get('referring_agent_id') ??
                                         session('referring_agent_id') ??
                                         null;

        // Log the referring agent ID for debugging
        if ($formData['referring_agent_id']) {
            Log::info('Business registration with referring agent', [
                'referring_agent_id' => $formData['referring_agent_id']
            ]);
        }

        // Execute the business registration action
        $result = $this->registerBusinessAction->execute($formData);

        if ($result['success']) {
            // Clear the stored form data
            $this->clearStoredFormData('business_registration');

            // Log in the admin user
            Auth::login($result['admin_user']);

            // Log information about the created subscription
            if (isset($result['subscription']) && $result['subscription']) {
                Log::info('Business registration completed with subscription', [
                    'business_id' => $result['business']->id,
                    'admin_user_id' => $result['admin_user']->id,
                    'subscription_id' => $result['subscription']->id,
                    'business_plan_id' => $result['business_plan']->id
                ]);
            } else {
                Log::warning('Business registration completed without subscription', [
                    'business_id' => $result['business']->id,
                    'admin_user_id' => $result['admin_user']->id,
                    'business_plan_id' => $result['business_plan']->id
                ]);
            }

            // Track the business registration in LinkTrust
            $this->trackLinkTrustPurchase(
                $result['transaction']->amount / 100, // Convert cents to dollars
                (string)$result['admin_user']->id,
                $result['transaction_id']
            );

            Toast::success($result['message'])->autoDismiss(3);
            return redirect()->route('business.dashboard');
        } else {
            // Track the failed registration in LinkTrust if we have a user
            if (isset($result['admin_user'])) {
                $adminUser = $result['admin_user'];
                $customerInfo = [
                    'name' => $adminUser->name,
                    'email' => $adminUser->email,
                    'phone' => $adminUser->phone ?? ''
                ];

                $transactionId = 'FAILED-REG-' . time();
                $this->trackLinkTrustFailedTransaction(
                    (string)$adminUser->id,
                    $transactionId,
                    $customerInfo
                );
            }

            Log::error('Business registration failed: ' . ($result['exception'] ? $result['exception']->getMessage() : 'Unknown error'), [
                'trace' => $result['exception'] ? $result['exception']->getTraceAsString() : null,
            ]);

            Toast::danger($result['message'])->autoDismiss(3);
            return back()->withInput();
        }
    }

    /**
     * Display the business dashboard.
     */
    public function dashboard(Request $request)
    {
        $user = $request->user();
        $business = $user->business;

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(5);
            return redirect()->route('dashboard');
        }

        $businessAdmin = User::where('business_id', $user->business_id)->role('business_admin')->first();
        $businessAdminId = $businessAdmin ? $businessAdmin->id : null;

        // Load business plans and employees relationships
        $business->load(['plans', 'employees']);

        // Get employee IDs for this business
        $employeeIds = BusinessEmployee::where('business_id', $business->id)
            ->whereNotNull('user_id')
            ->pluck('user_id')
            ->toArray();

        // Count active employees
        $activeEmployeesCount = BusinessEmployee::where('business_id', $business->id)
            ->where('status', 'active')
            ->whereNotNull('user_id')
            ->count();

        // Get upcoming appointments
        $upcomingAppointmentsCount = Consultation::whereIn('patient_id', $employeeIds)
            ->where('status', 'scheduled')
            ->where('scheduled_at', '>=', now())
            ->count();

        // Get recent appointments
        $recentAppointments = Consultation::whereIn('patient_id', $employeeIds)
            ->with(['patient', 'doctor'])
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        // Get user's credit cards
        $creditCards = $user->creditCards ?? collect();

        // Calculate trial days remaining
        $trialDaysRemaining = $business->trial_days_remaining;

        // Calculate seats
        $totalSeats = $business->total_seats;
        $availableSeats = $business->available_seats;

        // Get active employees
        $activeEmployees = $business->employees()
            ->where('status', 'active')
            ->count();

        return view('business.dashboard_new', compact(
            'business',
            'businessAdminId',
            'activeEmployeesCount',
            'upcomingAppointmentsCount',
            'recentAppointments',
            'trialDaysRemaining',
            'totalSeats',
            'availableSeats',
            'activeEmployees',
            'creditCards'
        ));
    }

    /**
     * Display the business profile.
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(5);
            return redirect()->route('dashboard');
        }

        return view('business.profile', compact('business'));
    }

    /**
     * Update the business profile.
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(5);
            return redirect()->route('dashboard');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:businesses,email,' . $business->id,
            'phone' => 'required|string|max:20',
            'address1' => 'nullable|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'zip' => 'nullable|string|max:20',
        ]);

        $business->update($validated);

        Toast::success('Business profile updated successfully.')->autoDismiss(5);
        return redirect()->route('business.profile');
    }

    /**
     * Display the help page.
     */
    public function help()
    {
        return view('business.help');
    }

    /**
     * Update business information.
     */
    public function update(Request $request, Business $business)
    {
        // Validate the request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:businesses,email,' . $business->id,
            'phone' => 'required|string|max:20',
            'address1' => 'nullable|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'zip' => 'nullable|string|max:20',
        ]);

        try {
            $business->update($validated);
            Toast::success('Business information updated successfully.')->autoDismiss(5);
            return [
                'success' => true,
                'message' => 'Business information updated successfully.'
            ];
        } catch (\Exception $e) {
            Log::error('Failed to update business: ' . $e->getMessage());
            Toast::danger('Failed to update business information.')->autoDismiss(5);
            return [
                'success' => false,
                'message' => 'Failed to update business information.'
            ];
        }
    }
}
