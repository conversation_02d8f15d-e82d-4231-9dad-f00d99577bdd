<?php

namespace App\Http\Controllers\Business;

use App\Actions\Business\AddCreditCardAction;
use App\Actions\Business\GetBusinessAdminAction;
use App\Actions\Business\SetDefaultCreditCardAction;
use App\Concerns\BusinessUserRelationship;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreditCardRequest;
use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class CreditCardController extends Controller
{
    use BusinessUserRelationship;

    public function __construct(
        private AddCreditCardAction $addCreditCardAction,
        private SetDefaultCreditCardAction $setDefaultCreditCardAction,
        private GetBusinessAdminAction $getBusinessAdminAction
    ) {}

    /**
     * Get all credit cards for the authenticated user or business admin
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, get the business_admin's credit cards
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            }
        }

        $query = $targetUser->creditCards();

        // Include trashed cards if requested
        if ($request->has('with_trashed') && $request->with_trashed) {
            $query = $targetUser->allCreditCards();
        }

        $creditCards = $query->get()->map(function ($card) {
            return [
                'id' => $card->id,
                'brand' => $card->brand,
                'last_four' => substr($card->last_four, -4),
                'cc' => substr($card->last_four, -4),
                'expiration_month' => $card->expiration_month,
                'expiration_year' => $card->expiration_year,
                'is_default' => (bool) $card->is_default,
                'deleted_at' => $card->deleted_at,
                'is_deleted' => $card->deleted_at !== null,
            ];
        });

        return response()->json($creditCards);
    }

    /**
     * Store a new credit card for the authenticated user or business admin
     */
    public function store(CreditCardRequest $request)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, add the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        try {
            $result = $this->addCreditCardAction->execute([
                'user' => $targetUser,
                'card_number' => $request->card_number,
                'expiration_month' => $request->expiration_month,
                'expiration_year' => $request->expiration_year,
                'cvv' => $request->cvv
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            $creditCard = $result['card'];
            $isNew = $result['is_new'];

            if ($isNew) {
                Log::info('New credit card added for user: ' . $targetUser->id);
            } else {
                Log::info('Existing credit card used for user: ' . $targetUser->id);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'card' => [
                    'id' => $creditCard->id,
                    'brand' => $creditCard->brand,
                    'last_four' => substr($creditCard->last_four, -4),
                    'expiration_month' => $creditCard->expiration_month,
                    'expiration_year' => $creditCard->expiration_year,
                    'is_default' => (bool) $creditCard->is_default,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to add credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set a credit card as default for the authenticated user or business admin
     */
    public function setDefault(CreditCard $creditCard)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, set the default credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            $result = $this->setDefaultCreditCardAction->execute([
                'user' => $targetUser,
                'credit_card' => $creditCard
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            Log::error('Error setting default credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update default credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Soft delete a credit card for the authenticated user or business admin
     */
    public function destroy(CreditCard $creditCard)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, delete the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            // Check if this is the default card
            if ($creditCard->is_default) {
                // Find another card to set as default
                $anotherCard = $targetUser->creditCards()->where('id', '!=', $creditCard->id)->first();
                if ($anotherCard) {
                    $this->setDefaultCreditCardAction->execute([
                        'user' => $targetUser,
                        'credit_card' => $anotherCard
                    ]);
                }
            }

            $creditCard->delete(); // This will now soft delete the card
            return response()->json([
                'success' => true,
                'message' => 'Credit card removed successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Permanently delete a credit card for the authenticated user or business admin
     */
    public function forceDelete($id)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, permanently delete the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            $creditCard->forceDelete();
            return response()->json([
                'success' => true,
                'message' => 'Credit card permanently deleted'
            ]);
        } catch (\Exception $e) {
            Log::error('Error permanently deleting credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to permanently delete credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore a soft-deleted credit card for the authenticated user or business admin
     */
    public function restore($id)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, restore the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            $creditCard->restore();
            return response()->json([
                'success' => true,
                'message' => 'Credit card restored successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error restoring credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore credit card: ' . $e->getMessage()
            ], 500);
        }
    }
}
