<?php

namespace App\Http\Controllers\Business;

use App\Actions\Business\GetBusinessAdminAction;
use App\Actions\PaymentMethods\AddPaymentMethod;
use App\Actions\PaymentMethods\GetPaymentMethods;
use App\Actions\PaymentMethods\RemovePaymentMethod;
use App\Actions\PaymentMethods\SetDefaultPaymentMethod;
use App\Concerns\BusinessUserRelationship;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreditCardRequest;
use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class CreditCardController extends Controller
{
    use BusinessUserRelationship;

    public function __construct(
        private AddPaymentMethod $addPaymentMethod,
        private SetDefaultPaymentMethod $setDefaultPaymentMethod,
        private GetPaymentMethods $getPaymentMethods,
        private RemovePaymentMethod $removePaymentMethod,
        private GetBusinessAdminAction $getBusinessAdminAction
    ) {}

    /**
     * Get all payment methods for the authenticated user or business admin
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, get the business_admin's payment methods
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            }
        }

        // Get payment methods using the action
        $result = $this->getPaymentMethods->execute(['user' => $targetUser]);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
                'credit_cards' => []
            ], 500);
        }

        // Transform the data to match the expected format
        $creditCards = collect($result['credit_cards'])->map(function ($card) {
            return [
                'id' => $card['id'],
                'brand' => $card['brand'],
                'last_four' => $card['cc'],
                'cc' => $card['cc'],
                'expiration_month' => $card['expiration_month'],
                'expiration_year' => $card['expiration_year'],
                'is_default' => (bool) $card['is_default'],
                'deleted_at' => null, // Payment methods use soft deletes differently
                'is_deleted' => false,
                'source' => $card['source'] ?? 'payment_methods'
            ];
        });

        return response()->json($creditCards);
    }

    /**
     * Store a new credit card for the authenticated user or business admin
     */
    public function store(CreditCardRequest $request)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, add the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        try {
            $result = $this->addPaymentMethod->execute([
                'user' => $targetUser,
                'card_number' => $request->card_number,
                'expiration_month' => $request->expiration_month,
                'expiration_year' => $request->expiration_year,
                'cvv' => $request->cvv
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            $paymentMethod = $result['card'];
            $isNew = $result['is_new'];

            if ($isNew) {
                Log::info('New payment method added for user: ' . $targetUser->id);
            } else {
                Log::info('Existing payment method used for user: ' . $targetUser->id);
            }

            // Handle both PaymentMethod and CreditCard models for backward compatibility
            if ($paymentMethod instanceof PaymentMethod) {
                $responseCard = [
                    'id' => $paymentMethod->id,
                    'brand' => $paymentMethod->cc_brand,
                    'last_four' => substr($paymentMethod->cc_last_four, -4),
                    'expiration_month' => $paymentMethod->cc_expiration_month,
                    'expiration_year' => $paymentMethod->cc_expiration_year,
                    'is_default' => (bool) $paymentMethod->is_default,
                ];
            } else {
                // Legacy CreditCard model
                $responseCard = [
                    'id' => $paymentMethod->id,
                    'brand' => $paymentMethod->brand,
                    'last_four' => substr($paymentMethod->last_four, -4),
                    'expiration_month' => $paymentMethod->expiration_month,
                    'expiration_year' => $paymentMethod->expiration_year,
                    'is_default' => (bool) $paymentMethod->is_default,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'card' => $responseCard
            ]);
        } catch (\Exception $e) {
            Log::error('Error adding credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to add credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set a payment method as default for the authenticated user or business admin
     */
    public function setDefault($paymentMethod)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, set the default credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        // Ensure the payment method belongs to the target user
        if ($paymentMethod->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Payment method does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's payment methods
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s payment methods'
            ], 403);
        }

        try {
            $result = $this->setDefaultPaymentMethod->execute([
                'user' => $targetUser,
                'payment_method' => $paymentMethod
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            Log::error('Error setting default payment method: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update default payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Soft delete a payment method for the authenticated user or business admin
     */
    public function destroy($paymentMethod)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, delete the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        // Ensure the payment method belongs to the target user
        if ($paymentMethod->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Payment method does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's payment methods
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s payment methods'
            ], 403);
        }

        try {
            // Use the RemovePaymentMethod action for consistent handling
            $result = $this->removePaymentMethod->execute([
                'user' => $targetUser,
                'payment_method' => $paymentMethod
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            Log::error('Error removing payment method: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove payment method: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Permanently delete a credit card for the authenticated user or business admin
     */
    public function forceDelete($id)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, permanently delete the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            $creditCard->forceDelete();
            return response()->json([
                'success' => true,
                'message' => 'Credit card permanently deleted'
            ]);
        } catch (\Exception $e) {
            Log::error('Error permanently deleting credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to permanently delete credit card: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore a soft-deleted credit card for the authenticated user or business admin
     */
    public function restore($id)
    {
        $user = Auth::user();
        $targetUser = $user;

        // If user is business_hr, restore the credit card for the business_admin
        if ($user->hasRole('business_hr')) {
            $result = $this->getBusinessAdminAction->execute(['user' => $user]);
            if ($result['success']) {
                $targetUser = $result['business_admin'];
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 403);
            }
        }

        $creditCard = CreditCard::withTrashed()->findOrFail($id);

        // Ensure the credit card belongs to the target user
        if ($creditCard->user_id !== $targetUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - Credit card does not belong to the target user'
            ], 403);
        }

        // Ensure the current user can manage the target user's credit cards
        if (!$this->canManageCreditCards($targetUser)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized - You do not have permission to manage this user\'s credit cards'
            ], 403);
        }

        try {
            $creditCard->restore();
            return response()->json([
                'success' => true,
                'message' => 'Credit card restored successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error restoring credit card: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore credit card: ' . $e->getMessage()
            ], 500);
        }
    }
}
