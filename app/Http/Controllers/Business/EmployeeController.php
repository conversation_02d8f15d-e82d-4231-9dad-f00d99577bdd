<?php

namespace App\Http\Controllers\Business;

use App\Actions\UploadEmployees;
use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessEmployee;
use App\Models\User;
use App\Services\BusinessEmployeeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use League\Csv\Reader;
use League\Csv\Writer;
use ProtoneMedia\Splade\Facades\Toast;
use Spatie\Permission\Models\Role;

class EmployeeController extends Controller
{
    protected $businessEmployeeService;
    protected $uploadEmployeesAction;

    public function __construct(
        BusinessEmployeeService $businessEmployeeService,
        UploadEmployees $uploadEmployeesAction
    ) {
        $this->businessEmployeeService = $businessEmployeeService;
        $this->uploadEmployeesAction = $uploadEmployeesAction;
    }

    /**
     * Display a listing of the employees.
     */
    public function index(Request $request)
    {
        $business = Auth::user()->business;
        return $business->employees()->withTrashed()->get();
    }

    /**
     * Show the form for creating a new employee.
     */
    public function create()
    {
        return view('business.employees.create');
    }

    /**
     * Store a newly created employee in storage.
     */
    public function store(Request $request)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'mobile_phone' => ['nullable', 'string', 'max:20'],
            'dob' => ['nullable', 'date'],
            'address1' => ['nullable', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'state' => ['nullable', 'string', 'max:255'],
            'zip' => ['nullable', 'string', 'max:20'],
        ]);

        try {
            DB::beginTransaction();

            $result = $this->businessEmployeeService->enrollEmployee($business, $request->all());

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            DB::commit();

            Toast::success('Employee added successfully!')->autoDismiss(10);
            return redirect()->route('business.employees.index');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to add employee: ' . $e->getMessage());

            Toast::danger('Failed to add employee: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Display the specified employee.
     */
    public function show(BusinessEmployee $employee)
    {
        $user = Auth::user();
        $business = Business::find($user->business_id);

        if (!$business || $employee->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.employees.index');
        }

        return view('business.employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified employee.
     */
    public function edit(BusinessEmployee $employee)
    {
        $user = Auth::user();
        $business = Business::find($user->business_id);

        if (!$business || $employee->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.employees.index');
        }

        return view('business.employees.edit', compact('employee'));
    }

    /**
     * Update the specified employee in storage.
     */
    public function update(Request $request, BusinessEmployee $employee)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business || $employee->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.employees.index');
        }

        $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'mobile_phone' => ['nullable', 'string', 'max:20'],
            'dob' => ['nullable', 'date'],
            'address1' => ['nullable', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'state' => ['nullable', 'string', 'max:255'],
            'zip' => ['nullable', 'string', 'max:20'],
        ]);

        try {
            $employee->update($request->all());

            // Update user information if exists
            if ($employee->user_id) {
                $user = User::find($employee->user_id);
                if ($user) {
                    $user->update([
                        'name' => $request->first_name . ' ' . $request->last_name,
                        'email' => $request->email,
                        'fname' => $request->first_name,
                        'lname' => $request->last_name,
                        'phone' => $request->phone,
                        'mobile_phone' => $request->mobile_phone,
                        'dob' => $request->dob,
                        'address1' => $request->address1,
                        'address2' => $request->address2,
                        'city' => $request->city,
                        'state' => $request->state,
                        'zip' => $request->zip,
                    ]);
                }
            }

            Toast::success('Employee updated successfully!')->autoDismiss(10);
            return redirect()->route('business.employees.index');

        } catch (\Exception $e) {
            Log::error('Failed to update employee: ' . $e->getMessage());

            Toast::danger('Failed to update employee: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Terminate the specified employee.
     */
    public function terminate(Request $request, BusinessEmployee $employee)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business || $employee->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.employees.index');
        }

        $request->validate([
            'termination_reason' => ['nullable', 'string', 'max:255'],
        ]);

        try {
            $result = $this->businessEmployeeService->terminateEmployee(
                $employee,
                $request->termination_reason
            );

            if (!$result['success']) {
                throw new \Exception($result['message'] ?? 'Failed to terminate employee');
            }

            Toast::success('Employee terminated successfully.')->autoDismiss(10);
            return redirect()->route('business.employees.index');

        } catch (\Exception $e) {
            Log::error('Failed to terminate employee: ' . $e->getMessage());

            Toast::danger('Failed to terminate employee: ' . $e->getMessage())->autoDismiss(10);
            return back();
        }
    }

    /**
     * Download a CSV template for employee upload.
     */
    public function downloadTemplate()
    {
        $csvContent = $this->uploadEmployeesAction->generateCsvTemplate();

        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="employee_template.csv"',
        ]);
    }

    /**
     * Upload employees from a CSV file.
     */
    public function upload(Request $request)
    {
        $user = $request->user();

        // Get the business for the current user
        $business = Business::find($user->business_id);

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048',
        ]);

        try {
            // Use the UploadEmployees action to handle the CSV upload
            $result = $this->uploadEmployeesAction->handle($business, $request->file('csv_file'));

            if ($result['success']) {
                if ($result['count'] > 0) {
                    Toast::success($result['message'])->autoDismiss(10);
                } else {
                    Toast::info($result['message'])->autoDismiss(10);
                }
            } else {
                Toast::danger($result['message'])->autoDismiss(10);
            }

            return redirect()->route('business.employees.index');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to upload employees: ' . $e->getMessage());

            Toast::danger('Failed to upload employees: ' . $e->getMessage())->autoDismiss(10);
            return back();
        }
    }

    /**
     * Create a user account for an employee.
     */
    public function createUser(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:business_employees,id',
        ]);

        try {
            $employee = BusinessEmployee::findOrFail($request->employee_id);

            // Check if employee belongs to the current business
            $user = Auth::user();
            if ($employee->business_id !== $user->business_id) {
                throw new \Exception('Unauthorized access');
            }

            // Check if user already exists
            if ($employee->user_id) {
                throw new \Exception('User account already exists for this employee');
            }

            DB::beginTransaction();

            // Create user
            $newUser = User::create([
                'business_id' => $employee->business_id,
                'name' => $employee->first_name . ' ' . $employee->last_name,
                'email' => $employee->email,
                'fname' => $employee->first_name,
                'lname' => $employee->last_name,
                'phone' => $employee->phone,
                'mobile_phone' => $employee->mobile_phone,
                'dob' => $employee->dob,
                'address1' => $employee->address1,
                'address2' => $employee->address2,
                'city' => $employee->city,
                'state' => $employee->state,
                'zip' => $employee->zip,
                'password' => bcrypt(Str::random(12)),
                'status' => 'active',
            ]);

            // Assign patient role
            $patientRole = Role::findByName('patient', 'web');
            $newUser->assignRole($patientRole);

            // Update employee
            $employee->update([
                'user_id' => $newUser->id,
                'status' => 'active',
            ]);

            DB::commit();

            Toast::success('User account created successfully.')->autoDismiss(5);
            return redirect()->route('business.employees.show', $employee->id);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create user: ' . $e->getMessage());

            Toast::danger('Failed to create user: ' . $e->getMessage())->autoDismiss(5);
            return back();
        }
    }

    /**
     * Send a login link to an employee.
     */
    public function sendLoginLink(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:business_employees,id',
        ]);

        try {
            $employee = BusinessEmployee::with('user')->findOrFail($request->employee_id);

            // Check if employee belongs to the current business
            $user = Auth::user();
            if ($employee->business_id !== $user->business_id) {
                throw new \Exception('Unauthorized access');
            }

            // Check if user exists
            if (!$employee->user_id || !$employee->user) {
                throw new \Exception('No user account exists for this employee');
            }

            // Generate and send login link
            $token = Str::random(60);
            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $employee->user->email],
                ['token' => $token, 'created_at' => now()]
            );

            // In a real app, you would send an email with the login link
            // For now, we'll just log it
            $loginUrl = url(route('password.reset', [
                'token' => $token,
                'email' => $employee->user->email,
            ], false));

            Log::info('Login link generated: ' . $loginUrl);

            Toast::success('Login link sent successfully.')->autoDismiss(5);
            return redirect()->route('business.employees.show', $employee->id);

        } catch (\Exception $e) {
            Log::error('Failed to send login link: ' . $e->getMessage());

            Toast::danger('Failed to send login link: ' . $e->getMessage())->autoDismiss(5);
            return back();
        }
    }
}
