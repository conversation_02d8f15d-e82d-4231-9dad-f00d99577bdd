<?php

namespace App\Http\Controllers\Business;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentMethodController extends Controller
{
    /**
     * Display the payment methods page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        $role = $user->roles->first()->name ?? 'business_admin';
        
        return view('business.payment-methods.index', [
            'user' => $user,
            'role' => $role
        ]);
    }
}
