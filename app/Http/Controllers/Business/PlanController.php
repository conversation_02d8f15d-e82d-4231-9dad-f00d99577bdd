<?php

namespace App\Http\Controllers\Business;

use App\Actions\Business\AddBusinessPlan;
use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessPlan;
use App\Models\PaymentMethod;
use App\Services\PaymentProcessorService;
use App\Services\BusinessPlanService;
use App\Http\Requests\Business\AddBusinessPlanRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PlanController extends Controller
{
    public function __construct(
        protected BusinessPlanService $businessPlanService,
        protected AddBusinessPlan $addBusinessPlanAction,
        protected PaymentProcessorService $paymentProcessorService
    ) {}

    /**
     * Display a listing of the business plans.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        $activePlan = $business->plans()
            ->where('active', true)
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>=', now());
            })
            ->first();

        $previousPlans = $business->plans()
            ->where(function ($query) {
                $query->where('active', false)
                    ->orWhere(function ($q) {
                        $q->where('ends_at', '<', now());
                    });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('business.plans.index', compact('business', 'activePlan', 'previousPlans'));
    }

    /**
     * Show the form for creating a new plan.
     */
    public function create()
    {
        return view('business.plans.create');
    }

    /**
     * Store a newly created plan in storage.
     */
    public function store(AddBusinessPlanRequest $request)
    {
        $business = Auth::user()->business;

        if (!$business) {
            Toast::danger('Business not found.')->autoDismiss(3);
            return redirect()->route('dashboard');
        }

        // Get validated data
        $data = $request->validated();

        // Prepare data for the action
        $actionData = [
            'business' => $business,
            'plan_quantity' => $data['plan_quantity'],
            'price_per_plan' => $data['price_per_plan'],
            'duration_months' => $data['duration_months'],
        ];

        $result = $this->addBusinessPlanAction->execute($actionData);

        if ($result['success']) {
            // Log information about the created plan and subscription
            $plan = $result['plan'];
            $subscription = $result['subscription'];

            if ($subscription) {
                Log::info('Business plan and subscription created successfully', [
                    'business_id' => $business->id,
                    'plan_id' => $plan->id,
                    'subscription_id' => $subscription->id
                ]);
            } else {
                Log::info('Business plan created successfully, but no subscription was created', [
                    'business_id' => $business->id,
                    'plan_id' => $plan->id
                ]);
            }

            Toast::success($result['message'])->autoDismiss(3);
        } else {
            Log::error('Failed to create business plan: ' . ($result['error'] ?? 'Unknown error'));
            Toast::danger($result['message'])->autoDismiss(3);
        }

        return back();
    }

    /**
     * Display the specified plan.
     */
    public function show(BusinessPlan $plan)
    {
        $user = Auth::user();
        $business = Business::find($user->business_id);

        if (!$business || $plan->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.plans.index');
        }

        return view('business.plans.show', compact('plan'));
    }

    /**
     * Show the form for renewing a plan.
     */
    public function renew(BusinessPlan $plan)
    {
        $user = Auth::user();
        $business = Business::find($user->business_id);

        if (!$business || $plan->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.plans.index');
        }

        return view('business.plans.renew', compact('plan'));
    }

    /**
     * Process plan renewal.
     */
    public function processRenewal(Request $request, BusinessPlan $plan)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business || $plan->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.plans.index');
        }

        $request->validate([
            'employee_count' => ['required', 'integer', 'min:1'],
            'price_per_plan' => ['required', 'numeric', 'min:20'],
            'duration_months' => ['required', 'integer', 'min:1', 'max:12'],
        ]);

        try {
            DB::beginTransaction();

            // Convert price to cents
            $pricePerPlan = $request->price_per_plan * 100;

            $renewalData = [
                'employee_count' => $request->employee_count,
                'price_per_plan' => $pricePerPlan,
                'duration_months' => $request->duration_months,
            ];

            $result = $this->businessPlanService->renewPlan($plan, $renewalData);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            DB::commit();

            Toast::success('Business plan renewed successfully!')->autoDismiss(10);
            return redirect()->route('business.plans.index');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to renew business plan: ' . $e->getMessage());

            Toast::danger('Failed to renew business plan: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Cancel the specified plan.
     */
    public function cancel(Request $request, BusinessPlan $plan)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business || $plan->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return redirect()->route('business.plans.index');
        }

        try {
            $result = $this->businessPlanService->cancelPlan($plan);

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            Toast::success('Business plan cancelled successfully.')->autoDismiss(10);
            return redirect()->route('business.plans.index');

        } catch (\Exception $e) {
            Log::error('Failed to cancel business plan: ' . $e->getMessage());

            Toast::danger('Failed to cancel business plan: ' . $e->getMessage())->autoDismiss(10);
            return back();
        }
    }

    /**
     * Activate a business plan with payment.
     */
    public function activate(Request $request, BusinessPlan $plan)
    {
        $user = $request->user();
        $business = Business::find($user->business_id);

        if (!$business || $plan->business_id !== $business->id) {
            Toast::danger('Unauthorized access.')->autoDismiss(10);
            return back();
        }

        // Validate the request
        $request->validate([
            'payment_method_id' => 'required|integer|exists:payment_methods,id',
        ]);

        try {
            // Get the payment method
            $paymentMethod = PaymentMethod::find($request->payment_method_id);

            if (!$paymentMethod || $paymentMethod->user_id !== $user->id) {
                Toast::danger('Invalid payment method.')->autoDismiss(10);
                return back();
            }

            // Process payment based on payment method type
            $result = match($paymentMethod->type) {
                'credit_card' => $this->processCardPayment($plan, $paymentMethod),
                'ach' => $this->processAchPayment($plan, $paymentMethod),
                'invoice' => $this->processInvoicePayment($plan, $paymentMethod),
                default => throw new \Exception('Unsupported payment method type.')
            };

            if (!$result['success']) {
                throw new \Exception($result['message']);
            }

            // Activate the plan
            $plan->update(['active' => true]);

            Toast::success('Business plan activated successfully.')->autoDismiss(10);
        } catch (\Exception $e) {
            Log::error('Failed to activate business plan: ' . $e->getMessage());

            Toast::danger('Failed to activate business plan: ' . $e->getMessage())->autoDismiss(10);
        }

        return back();
    }

    /**
     * Process credit card payment for a plan.
     */
    private function processCardPayment(BusinessPlan $plan, PaymentMethod $paymentMethod)
    {
        $user = $paymentMethod->user;

        // Find any existing subscription that might be linked to this business plan
        $subscription = $user->subscriptions()
            ->whereJsonContains('meta_data->business_plan_id', $plan->id)
            ->first();

        $subscriptionId = $subscription ? $subscription->id : null;

        return $this->paymentProcessorService->processCreditCardPayment(
            $user,
            $paymentMethod,
            $plan->total_price / 100, // Convert cents to dollars
            'Business Plan Payment - Plan #' . $plan->id,
            $subscriptionId, // Use linked subscription ID if found, otherwise null
            false // Not a discounted payment
        );
    }

    /**
     * Process ACH payment for a plan.
     */
    private function processAchPayment(BusinessPlan $plan, PaymentMethod $paymentMethod)
    {
        $user = $paymentMethod->user;

        // Find any existing subscription that might be linked to this business plan
        $subscription = $user->subscriptions()
            ->whereJsonContains('meta_data->business_plan_id', $plan->id)
            ->first();

        $subscriptionId = $subscription ? $subscription->id : null;

        return $this->paymentProcessorService->processAchPayment(
            $user,
            $paymentMethod,
            $plan->total_price / 100, // Convert cents to dollars
            'Business Plan Payment - Plan #' . $plan->id,
            $subscriptionId, // Use linked subscription ID if found, otherwise null
            false // Not a discounted payment
        );
    }

    /**
     * Process invoice payment for a plan.
     */
    private function processInvoicePayment(BusinessPlan $plan, PaymentMethod $paymentMethod)
    {
        $user = $paymentMethod->user;

        // Find any existing subscription that might be linked to this business plan
        $subscription = $user->subscriptions()
            ->whereJsonContains('meta_data->business_plan_id', $plan->id)
            ->first();

        $subscriptionId = $subscription ? $subscription->id : null;

        return $this->paymentProcessorService->processInvoicePayment(
            $user,
            $paymentMethod,
            $plan->total_price / 100, // Convert cents to dollars
            'Business Plan Payment - Plan #' . $plan->id,
            $subscriptionId, // Use linked subscription ID if found, otherwise null
            false // Not a discounted payment
        );
    }
}
