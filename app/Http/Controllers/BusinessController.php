<?php

namespace App\Http\Controllers;

use App\Events\QuoteRequested;
use App\Http\Requests\StoreQuoteRequest;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class BusinessController extends Controller
{
    public function index()
    {
        return view('front.business.index');
    }

    public function requestQuote(StoreQuoteRequest $request)
    {
        $validated = $request->validated();

        // Create the quote record
        $quote = Quote::create($validated);

        // Check if this is a download-only request or a full quote request
        $isDownloadOnly = $request->input('action') === 'download';

        // Only send email notification for full quote requests
        if (!$isDownloadOnly) {
            // Trigger the quote requested event to send email
            event(new QuoteRequested($quote));

            // Show success message for quote requests
            Toast::info('Your quote request has been submitted. We will contact you shortly.')->autoDismiss();
        } else {
            // Show a different message for download-only requests
            Toast::info('Thank you for your interest. Your brochure is downloading.')->autoDismiss();
        }

        // Handle download action if requested
        if ($isDownloadOnly) {
            // Directly return the file download
            $path = storage_path('app/GoMDUSA_Group_Health_Plan_Information_2025_$20_per_Member.pdf');

            // Use the original filename from the storage path
            $originalFilename = 'GoMDUSA_Group_Health_Plan_Information_2025_$20_per_Member.pdf';
            $downloadFilename = 'GoMDUSA_Group_Health_Plan_Information_2025.pdf';

            // Check if file exists
            if (!file_exists($path)) {
                Toast::danger('PDF file not found. Please contact support.')->autoDismiss();
                return to_route('business');
            }

            // Return the file as a download response
            return Response::download($path, $originalFilename, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $originalFilename . '"'
            ]);
        }

        // Return to business page if no download requested
        return to_route('business');
    }

    // No longer needed - direct download is handled in the main controller
}
