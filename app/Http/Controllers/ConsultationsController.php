<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class ConsultationsController extends Controller
{
    public function create()
    {
        $patients = User::role('patient')->get();
        return view('doctor.consultations.create', compact('patients'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:users,id',
            'scheduled_at' => 'required|date|after:now',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
        ]);

        $consultation = Consultation::create([
            'patient_id' => $validated['patient_id'],
            'doctor_id' => auth()->id(),
            'scheduled_at' => $validated['scheduled_at'],
            'type' => $validated['type'],
            'notes' => $validated['notes'] ?? null,
            'status' => 'scheduled',
        ]);

        Toast::success('Consultation scheduled successfully.');

        return redirect()->route('doctor.consultations');
    }

    public function start(Consultation $consultation)
    {
        if ($consultation->status !== 'scheduled') {
            Toast::error('This consultation cannot be started.');
            return redirect()->route('doctor.consultations');
        }

        $consultation->update(['status' => 'in_progress']);

        // Here you would typically redirect to a video/audio/chat interface
        // For this example, we'll just show a view with consultation details
        return view('doctor.consultations.in-progress', compact('consultation'));
    }

    public function end(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'notes' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
        ]);

        $consultation->update([
            'status' => 'completed',
            'notes' => $validated['notes'],
            'diagnosis' => $validated['diagnosis'],
            'treatment_plan' => $validated['treatment_plan'],
        ]);

        Toast::success('Consultation completed successfully.');

        return redirect()->route('doctor.consultations');
    }

    public function schedule()
    {
        $consultations = Consultation::where('doctor_id', auth()->id())
            ->whereBetween('scheduled_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->get();

        return view('doctor.schedule', compact('consultations'));
    }
}
