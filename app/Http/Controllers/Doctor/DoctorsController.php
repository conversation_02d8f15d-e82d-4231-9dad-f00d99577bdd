<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use App\Models\DoctorPatientAssignment;
use App\Models\Medication;
use App\Models\MedicationOrder;
use App\Models\MedicalRecord;
use App\Models\Prescription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class DoctorsController extends Controller
{
    public function dashboard(Request $request)
    {
        $patients = $request->user()
            ->doctorAssignments()
            ->count();

        $currentWeekConsultations = Consultation::where('doctor_id', auth()->id())
            ->whereBetween('scheduled_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->count();

        $scheduledConsultations = Consultation::where('doctor_id', auth()->id())
            ->where('status', 'scheduled')
            ->count();

        $completedConsultations = Consultation::where('doctor_id', auth()->id())
            ->where('status', 'completed')
            ->count();

        $prescriptionsWritten = Prescription::where('doctor_id', auth()->id())
            ->count();

        $medicationOrders = $request->user()
            ->assignedMedicationOrders()
            ->count();

        // Get recently assigned medication orders
        $recentlyAssignedOrders = $request->user()
            ->assignedMedicationOrders()
            ->with('patient')
            ->orderBy('assigned_at', 'desc')
            ->take(5)
            ->get();

        // Get recently assigned patients
        $recentlyAssignedPatients = $request->user()
            ->doctorAssignments()
            ->with(['patient', 'assignedBy'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get patient IDs assigned to this doctor
        $patientIds = $request->user()->assignedPatients()->pluck('users.id')->toArray();

        // Get recent medical questionnaires from assigned patients
        $recentQuestionnaires = \App\Models\MedicalQuestionnaire::whereIn('user_id', $patientIds)
            ->with(['user', 'treatment'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medical conditions from assigned patients
        $recentMedicalConditions = \App\Models\MedicalCondition::whereIn('patient_id', $patientIds)
            ->with('patient')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medication orders from assigned patients
        $recentPatientMedicationOrders = \App\Models\MedicationOrder::whereIn('patient_id', $patientIds)
            ->with(['patient', 'items.medication'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent allergies from assigned patients
        $recentPatientAllergies = \App\Models\Allergy::whereIn('user_id', $patientIds)
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        return view('doctor.dashboard', compact(
            'patients',
            'currentWeekConsultations',
            'scheduledConsultations',
            'completedConsultations',
            'prescriptionsWritten',
            'medicationOrders',
            'recentlyAssignedOrders',
            'recentlyAssignedPatients',
            'recentQuestionnaires',
            'recentMedicalConditions',
            'recentPatientMedicationOrders',
            'recentPatientAllergies'
        ));
    }

    public function consultations()
    {
        return view('doctor.consultations.index', [
            'consultations' => SpladeTable::for(Consultation::where('doctor_id', auth()->id()))
                // ->column('id', sortable: true)
                ->column('patient_name', label: 'Patient')
                ->column('scheduled_at', sortable: true)
                ->column('status')
                ->column('doctor_notes', label: 'Notes')
                ->column('actions')
                ->selectFilter('status', Consultation::STATUSES)
                ->paginate(10),
        ]);
    }

    public function showConsultation(Consultation $consultation)
    {
        return view('doctor.consultations.show', compact('consultation'));
    }

    public function updateConsultation(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,completed,cancelled',
            'doctor_notes' => 'nullable|string',
        ]);

        $consultation->update($validated);

        Toast::success('Consultation updated successfully.');

        return redirect()->route('doctor.consultations.list');
    }

    public function prescriptions()
    {
        return view('doctor.prescriptions.index', [
            'prescriptions' => SpladeTable::for(Prescription::where('doctor_id', auth()->id()))
                ->column('id', sortable: true)
                ->column('patient_name', label: 'Patient')
                ->column('created_at', sortable: true)
                ->column('status')
                ->column('actions')
                ->paginate(10),
        ]);
    }

    public function patientPrescriptions(User $patient)
    {
        return view('doctor.patients.prescriptions', [
            'patient' => $patient,
            'prescriptions' => SpladeTable::for($patient->prescriptionsAsPatient())
                ->column('created_at', label: 'Date', sortable: true, as: fn ($created_at) => $created_at->format('M d, Y g:i A'))
                ->column('notes')
                ->column('status')
                ->column('actions')
                ->paginate(10),
        ]);
    }

    public function createPrescription(User $patient)
    {
        $medications = Medication::all();

        return view('doctor.patients.create-prescription', compact('patient', 'medications'));
    }

    public function storePrescription(Request $request, User $patient)
    {
        $validated = $request->validate([
            'medications' => 'required|array',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.dosage' => 'required|string',
            'medications.*.frequency' => 'required|string',
            'medications.*.duration' => 'required|integer',
            'medications.*.quantity' => 'required|integer',
            'notes' => 'nullable|string',
        ]);

        $prescription = Prescription::create([
            'user_id' => $patient->id,
            'doctor_id' => auth()->id(),
            'notes' => $validated['notes'] ?? null,
        ]);

        $prescription->items()->createMany($validated['medications']);
        // foreach ($validated['medications'] as $medication) {
        //     $prescription->items()->attach($medication['medication_id'], [
        //         'dosage' => $medication['dosage'],
        //         'frequency' => $medication['frequency'],
        //         'duration' => $medication['duration'],
        //         'quantity' => $medication['quantity'],
        //     ]);
        // }

        Toast::success('Prescription created successfully.');

        return redirect()->route('doctor.patients.index');
    }

    public function showPrescription(Prescription $prescription)
    {
        return view('doctor.prescriptions.show', compact('prescription'));
    }

    public function editPrescription(Prescription $prescription)
    {
        return view('doctor.prescriptions.edit', [
            'prescription' => $prescription->load('medications'),
            'medications' => Medication::pluck('name', 'id'),
        ]);
    }

    public function patients(Request $request)
    {
        // $patient = $request->user()->assignedPatients()->active()->get();
        // dd($patient);
        return view('doctor.patients.index', [
            'patients' => SpladeTable::for(
                $request->user()->assignedPatients()->active()
                    ->with([
                        'prescriptionsAsPatient' => function ($query) {
                            $query->where('doctor_id', auth()->id());
                        },
                        'medicationOrders',
                        'patientConsultations' => function ($query) {
                            $query->where('doctor_id', auth()->id());
                        },
                        'preferredMedications',
                        'services'
                    ])
            )
                ->column('actions', alignment: 'right')
                ->column(
                    key: 'status',
                    sortable: true,
                    as: fn ($status) => config('status.user')[$status] ?? $status
                )
                ->column('consultations')
                ->column('name', sortable: true)
                ->column('email', sortable: true)
                ->column('phone', sortable: true)
                ->column('mobile_phone', sortable: true, hidden: true)
                ->column('gender', sortable: true, hidden: true)
                ->column('preferredMedications')
                ->column('medicationOrders')
                ->column(
                    key: 'dob',
                    label: 'Age',
                    sortable: true,
                    as: fn ($dob) => $dob ? Carbon::parse($dob)->age : null
                )
                ->column(
                    key: 'dob',
                    label: 'Birthday',
                    sortable: true,
                    hidden: true,
                    as: fn ($dob) =>  $dob ? Carbon::parse($dob)->format('F j, Y') : ''
                )
                ->column('address1', sortable: true, hidden: true)
                ->column('address2', sortable: true, hidden: true)
                ->column('city', sortable: true, hidden: true)
                ->column('state', sortable: true)
                ->column('zip', sortable: true, hidden: true)
                ->column(
                    key: 'prescriptionsAsPatient',
                    label: 'Prescriptions',
                    as: fn ($prescriptions) => $prescriptions->count()
                )
                ->column(
                    key: 'services.name',
                    label: 'Services',
                )
                ->column(
                    key: 'created_at',
                    label: 'Created At',
                    sortable: true,
                    as: fn ($created_at) => $created_at->format('m/d/Y'),
                    hidden: true,
                )
                ->column(
                    key: 'updated_at',
                    label: 'Updated At',
                    sortable: true,
                    as: fn ($updated_at) => $updated_at->format('m/d/Y'),
                    hidden: true,
                )
                ->selectFilter('status', config('status.user'))
                ->withGlobalSearch(columns: ['name', 'email'])
                ->defaultSortDesc('updated_at')
                ->paginate(10),
        ]);
    }

    public function showPatient(Request $request, User $patient)
    {
        // fix this later
        // if (Gate::denies('viewPatient', [$request->user(), $patient])) {
        //     abort(403, 'You are not authorized to view this patient.');
        // }

        $medicalRecords = $patient->medicalRecords()->latest()->get();
        $consultations = $patient->patientConsultations()->where('doctor_id', auth()->id())->latest()->get();
        $prescriptions = $patient->prescriptionsAsPatient()->where('doctor_id', auth()->id())->latest()->get();
        $medicalQuestionnaires = $patient->medicalQuestionnaires()->latest()->get();
        $patient->load(['allergies', 'healthQuestion', 'painAssessment', 'preferredMedications']);

        // Get patient's medication orders
        $medicationOrders = MedicationOrder::where('patient_id', $patient->id)
            ->with(['items.medication', 'prescription.items'])
            ->latest()
            ->get();

        // Get assignment information
        $assignment = DoctorPatientAssignment::where('doctor_id', auth()->id())
            ->where('patient_id', $patient->id)
            ->with('assignedBy')
            ->first();

        return view('doctor.patients.show', compact(
            'patient',
            'medicalRecords',
            'consultations',
            'prescriptions',
            'medicalQuestionnaires',
            'medicationOrders',
            'assignment'
        ));
    }

    public function schedule()
    {
        $consultations = Consultation::where('doctor_id', auth()->id())
            ->whereBetween('scheduled_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->get();

        return view('doctor.schedule', compact('consultations'));
    }

    public function createConsultation(User $patient)
    {
        return view('doctor.patients.schedule-consultation', compact('patient'));
    }

    public function storeConsultation(Request $request, User $patient)
    {
        $validated = $request->validate([
            'scheduled_at' => 'required|date|after:now',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
        ]);

        $patient->patientConsultations()->create([
            'doctor_id' => auth()->id(),
            'scheduled_at' => $validated['scheduled_at'],
            'type' => $validated['type'],
            'notes' => $validated['notes'] ?? null,
            'status' => 'scheduled',
        ]);

        Toast::success('Consultation scheduled successfully.');

        return redirect()->route('doctor.patients.index');
    }

    public function startConsultation(Consultation $consultation)
    {
        if ($consultation->status !== 'scheduled') {
            Toast::danger('This consultation cannot be started.');
            return redirect()->route('doctor.schedule');
        }

        $consultation->update(['status' => 'in_progress']);

        // Here you would typically redirect to a video/audio/chat interface
        // For this example, we'll just show a view with consultation details
        return view('doctor.consultations.in-progress', compact('consultation'));
    }

    public function endConsultation(Request $request, Consultation $consultation)
    {
        $validated = $request->validate([
            'patient_complaint' => 'required|string',
            'doctor_notes' => 'required|string',
            'diagnosis' => 'required|string',
            'treatment_plan' => 'required|string',
        ]);

        $consultation->update([
            'status' => 'completed',
            'patient_complaint' => 'required|string',
            'doctor_notes' => $validated['doctor_notes'],
            'diagnosis' => $validated['diagnosis'],
            'treatment_plan' => $validated['treatment_plan'],
        ]);

        Toast::success('Consultation completed successfully.');

        return redirect()->route('doctor.schedule');
    }

    public function medicalRecords(User $patient)
    {
        $medicalRecords = $patient->medicalRecords()->latest()->get();
        return view('doctor.medical-records.index', compact('patient', 'medicalRecords'));
    }

    public function storeMedicalRecord(Request $request, User $patient)
    {
        $validated = $request->validate([
            'record_type' => 'required|string',
            'description' => 'required|string',
            'date' => 'required|date|after:1900',
            'file' => 'nullable|file|max:10240', // 10MB max
        ]);

        $medicalRecord = new MedicalRecord($validated);
        $medicalRecord->doctor_id = auth()->id();

        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('medical_records');
            $medicalRecord->file_path = $path;
        }

        $patient->medicalRecords()->save($medicalRecord);

        Toast::success('Medical record added successfully.');

        return redirect()->route('doctor.patients.show', $patient);
    }
}
