<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\Prescription;
use App\Models\PrescriptionItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationOrderController extends Controller
{
    /**
     * Display a listing of the doctor's assigned medication orders.
     */
    public function index()
    {
        $orders = SpladeTable::for(MedicationOrder::where('doctor_id', Auth::id()))
            ->column('id', label: 'Order #')
            ->column('patient_id', label: 'Patient', as: fn ($patient_id, $order) => $order->patient ? $order->patient->name : 'Unknown')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('doctor.medication-orders.show', $order))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('doctor.medication-orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Display the specified medication order.
     */
    public function show(MedicationOrder $medicationOrder)
    {
        // Ensure the doctor can only view their assigned orders
        if ($medicationOrder->doctor_id !== Auth::id()) {
            Toast::warning('You do not have permission to view this order.');
            return redirect()->route('doctor.medication-orders.index');
        }

        $medicationOrder->load([
            'items.medication',
            'patient.allergies',
            'patient.healthQuestion',
            'patient.preferredMedications',
            'prescription.items'
        ]);

        return view('doctor.medication-orders.show', [
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for creating a prescription for the medication order.
     */
    public function createPrescription(MedicationOrder $medicationOrder)
    {
        // Ensure the doctor can only create prescriptions for their assigned orders
        if ($medicationOrder->doctor_id !== Auth::id()) {
            Toast::warning('You do not have permission to create a prescription for this order.');
            return redirect()->route('doctor.medication-orders.index');
        }

        // Ensure the order is in the assigned status
        if ($medicationOrder->status !== 'assigned') {
            Toast::warning('You can only create prescriptions for assigned orders.');
            return redirect()->route('doctor.medication-orders.show', $medicationOrder);
        }

        $medicationOrder->load(['items.medication', 'patient']);

        return view('doctor.medication-orders.create-prescription', [
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Store a newly created prescription for the medication order.
     */
    public function storePrescription(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the doctor can only create prescriptions for their assigned orders
        if ($medicationOrder->doctor_id !== Auth::id()) {
            Toast::warning('You do not have permission to create a prescription for this order.');
            return redirect()->route('doctor.medication-orders.index');
        }

        // Ensure the order is in the assigned status
        if ($medicationOrder->status !== 'assigned') {
            Toast::warning('You can only create prescriptions for assigned orders.');
            return redirect()->route('doctor.medication-orders.show', $medicationOrder);
        }

        $request->validate([
            'doctor_notes' => 'nullable|string|max:1000',
            'items' => 'required|array|min:1',
            'items.*.medication_order_item_id' => 'required|exists:medication_order_items,id',
            'items.*.status' => 'required|in:approved,rejected',
            'items.*.rejection_reason' => 'required_if:items.*.status,rejected|nullable|string|max:1000',
            'items.*.dosage' => 'required_if:items.*.status,approved|nullable|string|max:255',
            'items.*.frequency' => 'required_if:items.*.status,approved|nullable|string|max:255',
            'items.*.duration' => 'required_if:items.*.status,approved|nullable|integer',
            'items.*.quantity' => 'required_if:items.*.status,approved|nullable|integer',
        ]);

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Update the medication order
            $medicationOrder->update([
                'doctor_notes' => $request->doctor_notes,
            ]);

            // Create a new prescription
            $prescription = Prescription::create([
                'user_id' => $medicationOrder->patient_id,
                'doctor_id' => Auth::id(),
                'status' => 'pending',
                'notes' => $request->doctor_notes,
            ]);

            // Process each medication order item
            $allRejected = true;

            foreach ($request->items as $item) {
                $orderItem = MedicationOrderItem::findOrFail($item['medication_order_item_id']);

                // Update the order item status
                $orderItem->update([
                    'status' => $item['status'],
                    'rejection_reason' => $item['status'] === 'rejected' ? $item['rejection_reason'] : null,
                ]);

                // If the item is approved, create a prescription item
                if ($item['status'] === 'approved') {
                    $allRejected = false;

                    $prescriptionItem = [
                        'prescription_id' => $prescription->id,
                        'dosage' => $item['dosage'],
                        'frequency' => $item['frequency'],
                        'duration' => $item['duration'],
                        'quantity' => $item['quantity'],
                    ];

                    // If it's a custom medication, use the custom name
                    if ($orderItem->isCustomMedication()) {
                        $prescriptionItem['custom_medication_name'] = $orderItem->custom_medication_name;
                        $prescriptionItem['custom_medication_details'] = $orderItem->custom_medication_details;
                    } else {
                        $prescriptionItem['medication_id'] = $orderItem->medication_id;
                    }

                    PrescriptionItem::create($prescriptionItem);
                }
            }

            // Update the medication order status
            if ($allRejected) {
                $medicationOrder->reject('All medication requests were rejected.');
                $prescription->delete(); // No need for a prescription if all items are rejected
            } else {
                $medicationOrder->linkPrescription($prescription);
            }

            DB::commit();

            Toast::success('Prescription created successfully.');
            return redirect()->route('doctor.medication-orders.show', $medicationOrder);
        } catch (\Exception $e) {
            DB::rollBack();
            Toast::danger('An error occurred while creating the prescription.');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Reject the medication order without creating a prescription.
     */
    public function reject(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the doctor can only reject their assigned orders
        if ($medicationOrder->doctor_id !== Auth::id()) {
            Toast::warning('You do not have permission to reject this order.');
            return redirect()->route('doctor.medication-orders.index');
        }

        // Ensure the order is in the assigned status
        if ($medicationOrder->status !== 'assigned') {
            Toast::warning('You can only reject assigned orders.');
            return redirect()->route('doctor.medication-orders.show', $medicationOrder);
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $medicationOrder->reject($request->rejection_reason);

        Toast::success('Medication order rejected successfully.');
        return redirect()->route('doctor.medication-orders.show', $medicationOrder);
    }
}
