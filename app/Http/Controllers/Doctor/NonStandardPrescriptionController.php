<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Models\Medication;
use App\Models\NonStandardPrescriptionDetail;
use App\Models\Prescription;
use App\Models\PrescriptionItem;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use ProtoneMedia\Splade\Facades\Toast;

class NonStandardPrescriptionController extends Controller
{
    public function create(User $patient)
    {
        $medications = Medication::all();
        return view('doctor.patients.create-non-standard-prescription', compact('patient', 'medications'));
    }

    public function store(Request $request, User $patient)
    {
        $validatedData = $request->validate([
            // 'user_id' => 'required|exists:users,id',
            // 'doctor_id' => 'required|exists:users,id',
            // 'status' => 'required|in:pending,approved,rejected',
            'notes' => ['nullable', 'string'],
            'compounding_instructions' => ['nullable', 'string'],
            'special_instructions' => ['nullable', 'string'],
            'medications' => ['required', 'array', 'min:1'],
            'medications.*.isCustom' => ['required', 'boolean'],
            'medications.*.medication_id' => [
                'nullable',
                function ($attribute, $value, $fail) use ($request) {
                    $index = explode('.', $attribute)[1];
                    $isCustom = $request->input("medications.$index.isCustom");
                    if (!$isCustom && $value === null) {
                        $fail('The medication id is required for standard medications.');
                    }
                    if (!$isCustom && !Rule::exists('medications', 'id')->passes($attribute, $value)) {
                        $fail('The selected medication does not exist.');
                    }
                },
            ],
            'medications.*.custom_medication_name' => ['required_if:medications.*.isCustom,true', 'string'],
            'medications.*.custom_medication_details' => ['nullable', 'string'],
            'medications.*.dosage' => ['required', 'string'],
            'medications.*.frequency' => ['required', 'string'],
            'medications.*.duration' => ['required', 'string'],
            'medications.*.quantity' => ['required', 'string'],
        ]);

        $prescription = Prescription::create([
            'user_id' => $patient->id,
            'doctor_id' => auth()->id(),
            // 'status' => $validatedData['status'],
            'notes' => $validated['notes'] ?? null,
            'is_non_standard' => true,
        ]);

        NonStandardPrescriptionDetail::create([
            'prescription_id' => $prescription->id,
            'compounding_instructions' => $validatedData['compounding_instructions'],
            'special_instructions' => $validatedData['special_instructions'],
        ]);

        foreach ($validatedData['medications'] as $medicationData) {
            PrescriptionItem::create([
                'prescription_id' => $prescription->id,
                'medication_id' => $medicationData['isCustom'] ? null : $medicationData['medication_id'],
                'custom_medication_name' => $medicationData['isCustom'] ? $medicationData['custom_medication_name'] : null,
                'custom_medication_details' => $medicationData['isCustom'] ? $medicationData['custom_medication_details'] : null,
                'dosage' => $medicationData['dosage'],
                'frequency' => $medicationData['frequency'],
                'duration' => $medicationData['duration'],
                'quantity' => $medicationData['quantity'],
            ]);
        }

        Toast::success('Non-standard prescription created successfully')->autoDismiss();

        return redirect()->route('doctor.patients.index');
    }
}
