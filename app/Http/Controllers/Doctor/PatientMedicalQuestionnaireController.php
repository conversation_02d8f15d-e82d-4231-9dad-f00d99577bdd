<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use App\Traits\HandlesMedicalQuestionnaires;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class PatientMedicalQuestionnaireController extends Controller
{
    use HandlesMedicalQuestionnaires;

    /**
     * Display a listing of the patient's medical questionnaires.
     */
    public function index()
    {
        // Get patient IDs assigned to this doctor
        $patientIds = Auth::user()->assignedPatients()->pluck('users.id')->toArray();
        
        $questionnaires = MedicalQuestionnaire::whereIn('user_id', $patientIds)
            ->with(['user', 'treatment'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('doctor.patients.medical-questionnaires.index', [
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Display the specified medical questionnaire.
     */
    public function show(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the doctor is assigned to this patient
        $isAssigned = Auth::user()->assignedPatients()->where('users.id', $patient->id)->exists();
        if (!$isAssigned) {
            Toast::danger('You are not assigned to this patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Load related data
        $questionnaire->load(['user', 'treatment']);

        return view('doctor.patients.medical-questionnaires.show', [
            'patient' => $patient,
            'questionnaire' => $questionnaire
        ]);
    }
}
