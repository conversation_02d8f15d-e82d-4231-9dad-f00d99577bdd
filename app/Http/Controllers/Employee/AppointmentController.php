<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class AppointmentController extends Controller
{
    /**
     * Display a listing of the employee's appointments.
     */
    public function index()
    {
        $appointments = SpladeTable::for(Consultation::where('patient_id', Auth::id()))
            ->column('scheduled_at', label: 'Date & Time', sortable: true)
            ->column('type', label: 'Type')
            ->column('status', label: 'Status')
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $consultation) => $consultation->doctor ? $consultation->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('scheduled_at', 'desc');

        return view('employee.appointments.index', [
            'appointments' => $appointments,
        ]);
    }

    /**
     * Show the form for creating a new appointment.
     */
    public function create()
    {
        // Get the employee record for the current user
        $employee = Auth::user()->businessEmployee;

        // Define appointment types
        $appointmentTypes = [
            'initial_consultation' => 'Initial Consultation',
            'follow_up' => 'Follow-up Visit',
            'urgent_care' => 'Urgent Care',
            'prescription_refill' => 'Prescription Refill',
            'specialist_consultation' => 'Specialist Consultation',
        ];

        return view('employee.appointments.create', [
            'appointmentTypes' => $appointmentTypes,
            'employee' => $employee,
        ]);
    }

    /**
     * Store a newly created appointment in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|string',
            'scheduled_at' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Create the appointment without a doctor (admin will assign later)
            $appointment = Consultation::create([
                'patient_id' => Auth::id(),
                'doctor_id' => null, // No doctor assigned initially
                'type' => $request->type,
                'status' => 'pending', // Use pending status until a doctor is assigned
                'scheduled_at' => $request->scheduled_at,
                'notes' => $request->notes,
            ]);

            DB::commit();

            Toast::success('Appointment request submitted successfully. An administrator will assign a doctor to your appointment.');
            return redirect()->route('employee.appointments.index');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to schedule appointment: ' . $e->getMessage());
            Toast::danger('Failed to schedule appointment: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Display the specified appointment.
     */
    public function show(Consultation $appointment)
    {
        // Ensure the user can only view their own appointments
        if ($appointment->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to view this appointment.');
            return redirect()->route('employee.appointments.index');
        }

        return view('employee.appointments.show', [
            'appointment' => $appointment,
        ]);
    }

    /**
     * Show the form for editing the specified appointment.
     */
    public function edit(Consultation $appointment)
    {
        // Ensure the user can only edit their own appointments
        if ($appointment->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to edit this appointment.');
            return redirect()->route('employee.appointments.index');
        }

        // Only allow editing of scheduled appointments
        if ($appointment->status !== 'scheduled') {
            Toast::warning('Only scheduled appointments can be modified.');
            return redirect()->route('employee.appointments.show', $appointment);
        }

        // Define appointment types
        $appointmentTypes = [
            'initial_consultation' => 'Initial Consultation',
            'follow_up' => 'Follow-up Visit',
            'urgent_care' => 'Urgent Care',
            'prescription_refill' => 'Prescription Refill',
            'specialist_consultation' => 'Specialist Consultation',
        ];

        return view('employee.appointments.edit', [
            'appointment' => $appointment,
            'appointmentTypes' => $appointmentTypes,
        ]);
    }

    /**
     * Update the specified appointment in storage.
     */
    public function update(Request $request, Consultation $appointment)
    {
        // Ensure the user can only update their own appointments
        if ($appointment->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this appointment.');
            return redirect()->route('employee.appointments.index');
        }

        // Only allow updating of scheduled appointments
        if ($appointment->status !== 'scheduled') {
            Toast::warning('Only scheduled appointments can be modified.');
            return redirect()->route('employee.appointments.show', $appointment);
        }

        $request->validate([
            'type' => 'required|string',
            'scheduled_at' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            // Preserve the existing doctor_id if there is one
            $appointment->update([
                'type' => $request->type,
                'scheduled_at' => $request->scheduled_at,
                'notes' => $request->notes,
                // If status was 'scheduled', keep it that way, otherwise set to 'pending'
                'status' => $appointment->status === 'scheduled' ? 'scheduled' : 'pending',
            ]);

            Toast::success('Appointment updated successfully.');
            return redirect()->route('employee.appointments.show', $appointment);
        } catch (\Exception $e) {
            Log::error('Failed to update appointment: ' . $e->getMessage());
            Toast::danger('Failed to update appointment: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Cancel the specified appointment.
     */
    public function cancel(Consultation $appointment)
    {
        // Ensure the user can only cancel their own appointments
        if ($appointment->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to cancel this appointment.');
            return redirect()->route('employee.appointments.index');
        }

        // Only allow cancellation of scheduled appointments
        if ($appointment->status !== 'scheduled') {
            Toast::warning('Only scheduled appointments can be cancelled.');
            return redirect()->route('employee.appointments.show', $appointment);
        }

        try {
            $appointment->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            Toast::success('Appointment cancelled successfully.');
            return redirect()->route('employee.appointments.index');
        } catch (\Exception $e) {
            Log::error('Failed to cancel appointment: ' . $e->getMessage());
            Toast::danger('Failed to cancel appointment: ' . $e->getMessage());
            return back();
        }
    }
}
