<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\MedicalQuestionnaire;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function __invoke(Request $request)
    {
        $user = Auth::user();
        $employee = $user->businessEmployee;
        $business = $employee->business;

        // Get profile completion data from middleware
        $showProfileCompletionModal = $request->get('show_profile_completion_modal', false);
        $missingProfileFields = $request->get('missing_profile_fields', []);

        // Get medical questionnaire completion data from middleware
        $showMedicalQuestionnaireModal = $request->get('show_medical_questionnaire_modal', false);

        // Get latest measurement
        $latestMeasurement = Auth::user()->measurements()->latest('measured_at')->first();

        // Count active conditions
        $activeConditionsCount = Auth::user()->medicalConditions()->where('status', 'active')->count();

        // Get appointments, medications, prescriptions
        $appointments = $user->patientConsultations ?? collect([]);
        $medications = $user->medications ?? collect([]);
        $prescriptions = $user->prescriptionsAsPatient ?? collect([]);

        // Recent activities
        $recentActivities = $user->activities()->latest()->take(5)->get();

        // Get the latest medical questionnaire
        $latestQuestionnaire = MedicalQuestionnaire::where('user_id', $user->id)
            ->latest()
            ->first();

        return view('employee.dashboard', compact(
            'employee',
            'business',
            'appointments',
            'medications',
            'prescriptions',
            'latestMeasurement',
            'activeConditionsCount',
            'recentActivities',
            'latestQuestionnaire',
            'showProfileCompletionModal',
            'missingProfileFields',
            'showMedicalQuestionnaireModal'
        ));
    }
}
