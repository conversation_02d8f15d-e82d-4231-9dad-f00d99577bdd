<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\MedicalCondition;
use App\Models\MedicationOrder;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use App\Traits\HandlesMedicalQuestionnaires;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class MedicalQuestionnaireController extends Controller
{
    use HandlesMedicalQuestionnaires;

    /**
     * Display a listing of the user's medical questionnaires.
     */
    public function index()
    {
        $user = Auth::user();
        $questionnaires = MedicalQuestionnaire::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        $latestQuestionnaire = $questionnaires->first();

        return view('employee.medical-questionnaires.index', [
            'questionnaires' => $questionnaires,
            'latestQuestionnaire' => $latestQuestionnaire
        ]);
    }

    /**
     * Show the form for creating a new medical questionnaire.
     */
    public function create()
    {
        $user = Auth::user();

        // Get the user's medical conditions
        $hasMedicalConditions = MedicalCondition::where('patient_id', $user->id)->exists();

        // Get the user's medication orders
        $hasMedicationOrders = MedicationOrder::where('patient_id', $user->id)->exists();
        $medicationOrders = MedicationOrder::where('patient_id', $user->id)
            ->with(['items.medication'])
            ->get();

        // Define the categories for the questionnaire
        $categories = collect([
            'general_health',
            'medications',
            'allergies',
            'medical_history',
            'family_history',
            'lifestyle',
            'mental_health',
            'reproductive_health',
        ]);

        // Get user's medications and allergies
        $userMedications = $user->userReportedMedication()->get()->toArray();
        $userAllergies = $user->allergies()->get()->toArray();

        // If there are no medications or allergies, provide empty arrays with structure
        if (empty($userMedications)) {
            $userMedications = [
                [
                    'medication_name' => '',
                    'type' => '',
                    'dosage' => '',
                    'frequency' => '',
                    'duration' => '',
                    'reason' => '',
                    'side_effects' => ''
                ]
            ];
        }

        if (empty($userAllergies)) {
            $userAllergies = [
                [
                    'allergen' => '',
                    'type' => '',
                    'reaction' => '',
                    'severity' => '',
                    'diagnosed_at' => ''
                ]
            ];
        }

        // Default form data
        $formData = [
            'height_feet' => 5,
            'height_inches' => 0,
            'weight' => 150,
            'blood_pressure_systolic' => 120,
            'blood_pressure_diastolic' => 80,
            'heart_rate' => 70,
            'temperature' => 98.6,
            'medical_history' => 'N/A',
            'surgical_history' => 'N/A',
            'family_history' => 'N/A',
            'social_history' => 'N/A',
            'tobacco_use' => 'no',
            'alcohol_use' => 'no',
            'drug_use' => 'no',
            'exercise_frequency' => 'never',
            'diet' => 'N/A',
            'occupation' => 'N/A',
            'stress_level' => 'low',
            'sleep_patterns' => 'N/A',
            'health_concerns' => 'N/A',
            'symptoms' => 'N/A',
            'symptom_duration' => 'N/A',
            'previous_treatments' => 'N/A',
            'treatment_goals' => 'N/A',
            'quality_of_life' => 'N/A',
            'treatment_preference' => 'balanced',
            'medication_preference' => 'open',
            'additional_info' => 'N/A',
            'concerns' => 'N/A',
            'medications' => $userMedications,
            'allergies' => $userAllergies,
        ];

        return view('employee.medical-questionnaires.create', [
            'formData' => $formData,
            'hasMedicalConditions' => $hasMedicalConditions,
            'hasMedicationOrders' => $hasMedicationOrders,
            'medicationOrders' => $medicationOrders,
            'categories' => $categories
        ]);
    }

    /**
     * Store a newly created medical questionnaire.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // Validate only the fields we need for employee questionnaires
        $request->validate([
            'allergies.*.allergen' => 'required',
            'medications.*.medication_name' => 'required',
            'height_feet' => 'required|numeric|min:0|max:8',
            'height_inches' => 'required|numeric|min:0|max:11',
            'weight' => 'required|numeric|min:0',
            'blood_pressure_systolic' => 'required|numeric|min:0|max:300',
            'blood_pressure_diastolic' => 'required|numeric|min:0|max:200',
            'heart_rate' => 'required|numeric|min:0|max:250',
            'temperature' => 'required|numeric|min:90|max:110',
            'medical_history' => 'required|string',
            'surgical_history' => 'required|string',
            'family_history' => 'required|string',
            'social_history' => 'required|string',
            'tobacco_use' => 'required|string',
            'alcohol_use' => 'required|string',
            'drug_use' => 'required|string',
            'exercise_frequency' => 'required|string',
            'diet' => 'required|string',
            'occupation' => 'required|string',
            'stress_level' => 'required|string',
            'sleep_patterns' => 'required|string',
            'health_concerns' => 'required|string',
            'symptoms' => 'required|string',
            'symptom_duration' => 'required|string',
            'previous_treatments' => 'required|string',
            'treatment_goals' => 'required|string',
            'quality_of_life' => 'required|string',
            'treatment_preference' => 'required|string',
            'medication_preference' => 'required|string',
            'additional_info' => 'required|string',
            'concerns' => 'required|string',
        ]);

        // Create a new questionnaire
        $questionnaire = new MedicalQuestionnaire();
        $questionnaire->user_id = $user->id;
        $questionnaire->cart_session_id = $request->cart_session_id ?? 'general-' . uniqid();
        $questionnaire->status = 'completed';

        // Fill the questionnaire with the request data
        $questionnaire->fill($request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]));

        // Store all form data in the data column
        $data = $request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]);

        // Ensure specific fields are included in the data
        $specificFields = [
            'surgical_history',
            'family_history',
            'social_history',
            'tobacco_use',
            'alcohol_use',
            'drug_use',
            'exercise_frequency',
            'stress_level',
            'diet',
            'sleep_patterns',
            'occupation'
        ];

        foreach ($specificFields as $field) {
            if ($request->has($field)) {
                $data[$field] = $request->input($field);
            }
        }

        $questionnaire->data = $data;
        $questionnaire->save();

        // Update user's medications
        if ($request->has('medications')) {
            // Delete existing medications
            $user->userReportedMedication()->delete();

            // Create new medications
            foreach ($request->medications as $medication) {
                if (!empty($medication['medication_name'])) {
                    $user->userReportedMedication()->create($medication);
                }
            }
        }

        // Update user's allergies
        if ($request->has('allergies')) {
            // Delete existing allergies
            $user->allergies()->delete();

            // Create new allergies
            foreach ($request->allergies as $allergy) {
                if (!empty($allergy['allergen'])) {
                    $user->allergies()->create($allergy);
                }
            }
        }

        Toast::success('Medical questionnaire submitted successfully.')->autoDismiss(3);
        return redirect()->route('employee.medical-questionnaires.index');
    }

    /**
     * Show the form for editing the specified medical questionnaire.
     */
    public function edit(MedicalQuestionnaire $questionnaire)
    {
        $user = Auth::user();

        // Ensure the user can only edit their own questionnaires
        if ($questionnaire->user_id !== $user->id) {
            Toast::danger('You do not have permission to edit this questionnaire.')->autoDismiss(3);
            return redirect()->route('employee.medical-questionnaires.index');
        }

        // Define the categories for the questionnaire
        $categories = collect([
            'general_health',
            'medications',
            'allergies',
            'medical_history',
            'family_history',
            'lifestyle',
            'mental_health',
            'reproductive_health',
        ]);

        // Get the user's medical conditions
        $hasMedicalConditions = MedicalCondition::where('patient_id', Auth::id())->exists();

        // Get the user's medication orders
        $hasMedicationOrders = MedicationOrder::where('patient_id', Auth::id())->exists();
        $medicationOrders = MedicationOrder::where('patient_id', Auth::id())
            ->with(['items.medication'])
            ->get();

        // Get user's medications and allergies
        $userMedications = $user->userReportedMedication()->get()->toArray();
        $userAllergies = $user->allergies()->get()->toArray();

        // If there are no medications or allergies, provide empty arrays with structure
        if (empty($userMedications)) {
            $userMedications = [
                [
                    'medication_name' => '',
                    'type' => '',
                    'dosage' => '',
                    'frequency' => '',
                    'duration' => '',
                    'reason' => '',
                    'side_effects' => ''
                ]
            ];
        }

        if (empty($userAllergies)) {
            $userAllergies = [
                [
                    'allergen' => '',
                    'type' => '',
                    'reaction' => '',
                    'severity' => '',
                    'diagnosed_at' => ''
                ]
            ];
        }

        // Prepare the form data
        $formData = $questionnaire->data ?? [];

        // Merge the questionnaire attributes with the form data
        foreach ($questionnaire->getAttributes() as $key => $value) {
            if (!in_array($key, ['id', 'created_at', 'updated_at', 'user_id', 'cart_session_id', 'status', 'data'])) {
                $formData[$key] = $value;
            }
        }

        // Ensure these specific fields are included
        $specificFields = [
            'surgical_history',
            'family_history',
            'social_history',
            'tobacco_use',
            'alcohol_use',
            'drug_use',
            'exercise_frequency',
            'stress_level',
            'diet',
            'sleep_patterns',
            'occupation'
        ];

        foreach ($specificFields as $field) {
            if (isset($questionnaire->$field)) {
                $formData[$field] = $questionnaire->$field;
            } elseif (isset($questionnaire->data[$field])) {
                $formData[$field] = $questionnaire->data[$field];
            }
        }

        // Set default values for all fields if they're empty
        if (empty($formData['height_feet'])) $formData['height_feet'] = 5;
        if (empty($formData['height_inches'])) $formData['height_inches'] = 0;
        if (empty($formData['weight'])) $formData['weight'] = 150;
        if (empty($formData['blood_pressure_systolic'])) $formData['blood_pressure_systolic'] = 120;
        if (empty($formData['blood_pressure_diastolic'])) $formData['blood_pressure_diastolic'] = 80;
        if (empty($formData['heart_rate'])) $formData['heart_rate'] = 70;
        if (empty($formData['temperature'])) $formData['temperature'] = 98.6;
        if (empty($formData['medical_history'])) $formData['medical_history'] = 'N/A';
        if (empty($formData['surgical_history'])) $formData['surgical_history'] = 'N/A';
        if (empty($formData['family_history'])) $formData['family_history'] = 'N/A';
        if (empty($formData['social_history'])) $formData['social_history'] = 'N/A';
        if (empty($formData['tobacco_use'])) $formData['tobacco_use'] = 'no';
        if (empty($formData['alcohol_use'])) $formData['alcohol_use'] = 'no';
        if (empty($formData['drug_use'])) $formData['drug_use'] = 'no';
        if (empty($formData['exercise_frequency'])) $formData['exercise_frequency'] = 'never';
        if (empty($formData['diet'])) $formData['diet'] = 'N/A';
        if (empty($formData['occupation'])) $formData['occupation'] = 'N/A';
        if (empty($formData['stress_level'])) $formData['stress_level'] = 'low';
        if (empty($formData['sleep_patterns'])) $formData['sleep_patterns'] = 'N/A';
        if (empty($formData['health_concerns'])) $formData['health_concerns'] = 'N/A';
        if (empty($formData['symptoms'])) $formData['symptoms'] = 'N/A';
        if (empty($formData['symptom_duration'])) $formData['symptom_duration'] = 'N/A';
        if (empty($formData['previous_treatments'])) $formData['previous_treatments'] = 'N/A';
        if (empty($formData['treatment_goals'])) $formData['treatment_goals'] = 'N/A';
        if (empty($formData['quality_of_life'])) $formData['quality_of_life'] = 'N/A';
        if (empty($formData['treatment_preference'])) $formData['treatment_preference'] = 'balanced';
        if (empty($formData['medication_preference'])) $formData['medication_preference'] = 'open';
        if (empty($formData['additional_info'])) $formData['additional_info'] = 'N/A';
        if (empty($formData['concerns'])) $formData['concerns'] = 'N/A';

        // Add medications and allergies
        $formData['medications'] = $userMedications;
        $formData['allergies'] = $userAllergies;

        return view('employee.medical-questionnaires.edit', [
            'questionnaire' => $questionnaire,
            'formData' => $formData,
            'hasMedicalConditions' => $hasMedicalConditions,
            'hasMedicationOrders' => $hasMedicationOrders,
            'medicationOrders' => $medicationOrders,
            'categories' => $categories
        ]);
    }

    /**
     * Update the specified medical questionnaire in storage.
     */
    public function update(Request $request, MedicalQuestionnaire $questionnaire)
    {
        $user = Auth::user();

        // Ensure the user can only update their own questionnaires
        if ($questionnaire->user_id !== $user->id) {
            Toast::danger('You do not have permission to update this questionnaire.')->autoDismiss(3);
            return redirect()->route('employee.medical-questionnaires.index');
        }

        // Validate only the fields we need for employee questionnaires
        $request->validate([
            'allergies.*.allergen' => 'required',
            'medications.*.medication_name' => 'required',
            'height_feet' => 'required|numeric|min:0|max:8',
            'height_inches' => 'required|numeric|min:0|max:11',
            'weight' => 'required|numeric|min:0',
            'blood_pressure_systolic' => 'required|numeric|min:0|max:300',
            'blood_pressure_diastolic' => 'required|numeric|min:0|max:200',
            'heart_rate' => 'required|numeric|min:0|max:250',
            'temperature' => 'required|numeric|min:90|max:110',
            'medical_history' => 'required|string',
            'surgical_history' => 'required|string',
            'family_history' => 'required|string',
            'social_history' => 'required|string',
            'tobacco_use' => 'required|string',
            'alcohol_use' => 'required|string',
            'drug_use' => 'required|string',
            'exercise_frequency' => 'required|string',
            'diet' => 'required|string',
            'occupation' => 'required|string',
            'stress_level' => 'required|string',
            'sleep_patterns' => 'required|string',
            'health_concerns' => 'required|string',
            'symptoms' => 'required|string',
            'symptom_duration' => 'required|string',
            'previous_treatments' => 'required|string',
            'treatment_goals' => 'required|string',
            'quality_of_life' => 'required|string',
            'treatment_preference' => 'required|string',
            'medication_preference' => 'required|string',
            'additional_info' => 'required|string',
            'concerns' => 'required|string',
        ]);

        // Update the questionnaire
        $questionnaire->fill($request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]));

        // Store all form data in the data column
        $data = $request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]);

        // Ensure specific fields are included in the data
        $specificFields = [
            'surgical_history',
            'family_history',
            'social_history',
            'tobacco_use',
            'alcohol_use',
            'drug_use',
            'exercise_frequency',
            'stress_level',
            'diet',
            'sleep_patterns',
            'occupation'
        ];

        foreach ($specificFields as $field) {
            if ($request->has($field)) {
                $data[$field] = $request->input($field);
            }
        }

        $questionnaire->data = $data;
        $questionnaire->save();

        // Update user's medications
        if ($request->has('medications')) {
            // Delete existing medications
            $user->userReportedMedication()->delete();

            // Create new medications
            foreach ($request->medications as $medication) {
                if (!empty($medication['medication_name'])) {
                    $user->userReportedMedication()->create($medication);
                }
            }
        }

        // Update user's allergies
        if ($request->has('allergies')) {
            // Delete existing allergies
            $user->allergies()->delete();

            // Create new allergies
            foreach ($request->allergies as $allergy) {
                if (!empty($allergy['allergen'])) {
                    $user->allergies()->create($allergy);
                }
            }
        }

        Toast::success('Medical questionnaire updated successfully.')->autoDismiss(3);
        return redirect()->route('employee.medical-questionnaires.index');
    }
}
