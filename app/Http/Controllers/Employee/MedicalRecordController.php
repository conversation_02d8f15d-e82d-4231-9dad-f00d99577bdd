<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Allergy;
use App\Models\Category;
use App\Models\MedicalCondition;
use App\Models\MedicalRecord;
use App\Models\MedicalSurgicalHistory;
use App\Models\User;
use App\Models\UserMeasurement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicalRecordController extends Controller
{
    public function index()
    {
        $user = User::with(['measurements', 'medicalConditions', 'allergies', 'medicalSurgicalHistory'])->find(Auth::id());

        $categories = Category::whereNull('parent_id')
            ->with(['children.children.conditions', 'children.conditions'])
            ->orderBy('name')
            ->get();

        $selectOptions = [];

        foreach ($categories as $topCategory) {
            $group = [
                'text' => $topCategory->name,
                'children' => []
            ];

            // Add conditions from level 2 categories
            foreach ($topCategory->children as $secondLevel) {
                // Add conditions directly attached to level 2
                foreach ($secondLevel->conditions as $condition) {
                    $group['children'][] = [
                        'id' => $condition->id,
                        'text' => "{$secondLevel->name}: {$condition->name}",
                        'category_id' => $secondLevel->id
                    ];
                }

                // Add conditions from level 3 categories
                foreach ($secondLevel->children as $thirdLevel) {
                    foreach ($thirdLevel->conditions as $condition) {
                        $group['children'][] = [
                            'id' => $condition->id,
                            'text' => "{$secondLevel->name} > {$thirdLevel->name}: {$condition->name}",
                            'category_id' => $thirdLevel->id
                        ];
                    }
                }
            }

            // Only add groups that have conditions
            if (count($group['children']) > 0) {
                $selectOptions[] = $group;
            }
        }

        // Get the user's uploaded medical records
        $medicalRecords = SpladeTable::for(MedicalRecord::where('patient_id', Auth::id()))
            ->column('record_type', label: 'Type', sortable: true)
            ->column('description', label: 'Description')
            ->column('record_date', label: 'Date', sortable: true)
            ->column('actions', label: 'Actions')
            ->defaultSort('record_date', 'desc')
            ->paginate(10);

        return view('employee.medical-record.index', compact('user', 'categories', 'medicalRecords'));
    }

    /**
     * Show the form for creating a new medical record.
     */
    public function create()
    {
        return view('employee.medical-record.create');
    }

    /**
     * Store a newly created medical record in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'record_type' => 'required|string|max:255',
            'description' => 'required|string|max:255',
            'record_date' => 'required|date',
            'file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240',
        ]);

        $file = $request->file('file');
        $path = $file->store('medical-records', 'public');

        MedicalRecord::create([
            'patient_id' => Auth::id(),
            'record_type' => $request->record_type,
            'description' => $request->description,
            'record_date' => $request->record_date,
            'file_path' => $path,
        ]);

        Toast::success('Medical record uploaded successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Remove the specified medical record from storage.
     */
    public function destroy(MedicalRecord $medicalRecord)
    {
        // Ensure the user can only delete their own medical records
        if ($medicalRecord->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this medical record.');
            return redirect()->route('employee.medical-records.index');
        }

        // Delete the file from storage
        if ($medicalRecord->file_path) {
            Storage::disk('public')->delete($medicalRecord->file_path);
        }

        $medicalRecord->delete();

        Toast::success('Medical record deleted successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Store a new measurement for the authenticated user.
     */
    public function storeMeasurement(Request $request)
    {
        $validated = $request->validate([
            'height_ft' => 'required|numeric|min:0',
            'height_in' => 'required|numeric|min:0|max:11',
            'weight' => 'required|numeric|min:0',
        ]);

        // Convert height to inches (1 ft = 12 inches)
        $heightInches = ($validated['height_ft'] * 12) + $validated['height_in'];

        // Weight is already in pounds, no conversion needed
        $weightPounds = $validated['weight'];

        // Create the measurement
        $measurement = new UserMeasurement([
            'user_id' => Auth::id(),
            'height' => $heightInches,
            'weight' => $weightPounds,
            'measured_at' => now(),
        ]);

        $measurement->save();

        Toast::success('Measurement added successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Store a new medical condition for the authenticated user.
     */
    public function storeMedicalCondition(Request $request, \App\Actions\MedicalRecord\CreateMedicalCondition $action)
    {
        $request->validate([
            'condition_ids' => 'required|array',
            'condition_ids.*' => 'exists:conditions,id',
        ]);

        $result = $action->execute([
            'user' => $request->user(),
            'request' => $request
        ]);

        if ($result['success']) {
            Toast::success($result['message'])->autoDismiss(5);
        } else {
            Toast::danger($result['message'])->autoDismiss(5);
        }
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Update a medical condition.
     */
    public function updateMedicalCondition(Request $request, MedicalCondition $medical_condition)
    {
        // Ensure the user can only update their own medical conditions
        if ($medical_condition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this medical condition.');
            return redirect()->route('employee.medical-records.index');
        }

        $validated = $request->validate([
            'diagnosed_at' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive,resolved',
            'had_condition_before' => 'boolean',
            'is_chronic' => 'boolean',
        ]);

        $medical_condition->update($validated);

        Toast::success('Medical condition updated successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Delete a medical condition.
     */
    public function destroyMedicalCondition(MedicalCondition $medical_condition)
    {
        // Ensure the user can only delete their own medical conditions
        if ($medical_condition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this medical condition.');
            return redirect()->route('employee.medical-records.index');
        }

        $medical_condition->delete();

        Toast::success('Medical condition deleted successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Store a new allergy for the authenticated user.
     */
    public function storeAllergy(Request $request)
    {
        $validated = $request->validate([
            'allergen' => 'required|string|max:255',
            'reaction' => 'nullable|string|max:255',
            'severity' => 'required|in:mild,moderate,severe',
            'diagnosed_at' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['patient_id'] = Auth::id();

        Allergy::create($validated);

        Toast::success('Allergy added successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Update an allergy.
     */
    public function updateAllergy(Request $request, Allergy $allergy)
    {
        // Ensure the user can only update their own allergies
        if ($allergy->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this allergy.');
            return redirect()->route('employee.medical-records.index');
        }

        $validated = $request->validate([
            'allergen' => 'required|string|max:255',
            'reaction' => 'nullable|string|max:255',
            'severity' => 'required|in:mild,moderate,severe',
            'diagnosed_at' => 'nullable|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $allergy->update($validated);

        Toast::success('Allergy updated successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Delete an allergy.
     */
    public function destroyAllergy(Allergy $allergy)
    {
        // Ensure the user can only delete their own allergies
        if ($allergy->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this allergy.');
            return redirect()->route('employee.medical-records.index');
        }

        $allergy->delete();

        Toast::success('Allergy deleted successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Store a new surgical history for the authenticated user.
     */
    public function storeSurgicalHistory(Request $request)
    {
        $validated = $request->validate([
            'past_injuries' => 'boolean',
            'past_injuries_details' => 'nullable|string|max:1000',
            'surgery' => 'boolean',
            'surgery_details' => 'nullable|string|max:1000',
            'chronic_conditions_details' => 'nullable|string|max:1000',
        ]);

        $validated['patient_id'] = Auth::id();

        // Check if the user already has a surgical history
        $existingHistory = MedicalSurgicalHistory::where('patient_id', Auth::id())->first();

        if ($existingHistory) {
            $existingHistory->update($validated);
            $message = 'Surgical history updated successfully.';
        } else {
            MedicalSurgicalHistory::create($validated);
            $message = 'Surgical history added successfully.';
        }

        Toast::success($message);
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Update a surgical history.
     */
    public function updateSurgicalHistory(Request $request, MedicalSurgicalHistory $medicalSurgicalHistory)
    {
        // Ensure the user can only update their own surgical history
        if ($medicalSurgicalHistory->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this surgical history.');
            return redirect()->route('employee.medical-records.index');
        }

        $validated = $request->validate([
            'past_injuries' => 'boolean',
            'past_injuries_details' => 'nullable|string|max:1000',
            'surgery' => 'boolean',
            'surgery_details' => 'nullable|string|max:1000',
            'chronic_conditions_details' => 'nullable|string|max:1000',
        ]);

        $medicalSurgicalHistory->update($validated);

        Toast::success('Surgical history updated successfully.');
        return redirect()->route('employee.medical-records.index');
    }

    /**
     * Delete a surgical history.
     */
    public function destroySurgicalHistory(MedicalSurgicalHistory $medicalSurgicalHistory)
    {
        // Ensure the user can only delete their own surgical history
        if ($medicalSurgicalHistory->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this surgical history.');
            return redirect()->route('employee.medical-records.index');
        }

        $medicalSurgicalHistory->delete();

        Toast::success('Surgical history deleted successfully.');
        return redirect()->route('employee.medical-records.index');
    }
}
