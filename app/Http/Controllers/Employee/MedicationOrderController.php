<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Medication;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\MedicalQuestionnaire;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationOrderController extends Controller
{
    /**
     * Display a listing of the employee's medication orders.
     */
    public function index()
    {
        $orders = SpladeTable::for(MedicationOrder::where('patient_id', Auth::id()))
            ->column('id', label: 'Order #')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status')
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $order) => $order->doctor ? $order->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('employee.medication-orders.show', $order))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('employee.medication-orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Show the form for creating a new medication order.
     */
    public function create(Request $request)
    {
        $medications = Medication::orderBy('name')
            ->get();

        // Get the return URL if provided
        $returnUrl = $request->input('return_url');

        return view('employee.medication-orders.create', [
            'medications' => $medications,
            'returnUrl' => $returnUrl
        ]);
    }

    /**
     * Store a newly created medication order in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Create the medication order
            $order = MedicationOrder::create([
                'patient_id' => Auth::id(),
                'status' => 'pending',
                'patient_notes' => $request->patient_notes,
            ]);

            // Create the medication order items
            foreach ($request->medications as $medication) {
                MedicationOrderItem::create([
                    'medication_order_id' => $order->id,
                    'medication_id' => $medication['medication_id'],
                    'requested_dosage' => $medication['requested_dosage'] ?? null,
                    'requested_quantity' => $medication['requested_quantity'] ?? null,
                ]);
            }

            DB::commit();

            Toast::success('Medication order created successfully.');

            // Redirect to the return URL if provided
            if ($request->input('return_url')) {
                return redirect($request->input('return_url'));
            }

            // Check if the user has a medical questionnaire
            $userId = Auth::id();
            $latestQuestionnaire = MedicalQuestionnaire::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->first();

            // Redirect to edit or create medical questionnaire
            if ($latestQuestionnaire) {
                Toast::info('Please update your medical questionnaire to help your healthcare provider understand your health needs.')->autoDismiss(10);
                return redirect()->route('employee.medical-questionnaires.edit', $latestQuestionnaire);
            } else {
                Toast::info('Please complete your medical questionnaire to help your healthcare provider understand your health needs.')->autoDismiss(10);
                return redirect()->route('employee.medical-questionnaires.create');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create medication order: ' . $e->getMessage());
            Toast::danger('Failed to create medication order: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Display the specified medication order.
     */
    public function show(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only view their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to view this order.');
            return redirect()->route('employee.medication-orders.index');
        }

        $medicationOrder->load(['items.medication', 'doctor', 'prescription.items']);

        return view('employee.medication-orders.show', [
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for editing the specified medication order.
     */
    public function edit(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the user can only edit their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to edit this order.');
            return redirect()->route('employee.medication-orders.index');
        }

        // Only allow editing of pending orders
        if ($medicationOrder->status !== MedicationOrder::STATUS_PENDING) {
            Toast::warning('Only pending orders can be edited.');
            return redirect()->route('employee.medication-orders.show', $medicationOrder);
        }

        $medications = Medication::orderBy('name')->get();

        return view('employee.medication-orders.edit', [
            'order' => $medicationOrder,
            'medications' => $medications,
        ]);
    }

    /**
     * Update the specified medication order in storage.
     */
    public function update(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the user can only update their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this order.');
            return redirect()->route('employee.medication-orders.index');
        }

        // Only allow updating of pending orders
        if ($medicationOrder->status !== MedicationOrder::STATUS_PENDING) {
            Toast::warning('Only pending orders can be updated.');
            return redirect()->route('employee.medication-orders.show', $medicationOrder);
        }

        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Update the medication order
            $medicationOrder->update([
                'patient_notes' => $request->patient_notes,
            ]);

            // Delete existing items
            $medicationOrder->items()->delete();

            // Create new medication order items
            foreach ($request->medications as $medication) {
                MedicationOrderItem::create([
                    'medication_order_id' => $medicationOrder->id,
                    'medication_id' => $medication['medication_id'],
                    'requested_dosage' => $medication['requested_dosage'] ?? null,
                    'requested_quantity' => $medication['requested_quantity'] ?? null,
                ]);
            }

            DB::commit();

            Toast::success('Medication order updated successfully.');

            // Check if the user has a medical questionnaire
            $userId = Auth::id();
            $latestQuestionnaire = MedicalQuestionnaire::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->first();

            // Redirect to edit or create medical questionnaire
            if ($latestQuestionnaire) {
                Toast::info('Please update your medical questionnaire to help your healthcare provider understand your health needs.')->autoDismiss(10);
                return redirect()->route('employee.medical-questionnaires.edit', $latestQuestionnaire);
            } else {
                Toast::info('Please complete your medical questionnaire to help your healthcare provider understand your health needs.')->autoDismiss(10);
                return redirect()->route('employee.medical-questionnaires.create');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update medication order: ' . $e->getMessage());
            Toast::danger('Failed to update medication order: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Remove the specified medication order from storage.
     */
    public function destroy(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only delete their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this order.');
            return redirect()->route('employee.medication-orders.index');
        }

        // Only allow deletion of pending orders
        if ($medicationOrder->status !== MedicationOrder::STATUS_PENDING) {
            Toast::warning('Only pending medication orders can be deleted.');
            return redirect()->route('employee.medication-orders.show', $medicationOrder);
        }

        // Delete the order items first
        $medicationOrder->items()->delete();

        // Delete the order
        $medicationOrder->delete();

        Toast::success('Medication order deleted successfully.')->autoDismiss(10);
        return redirect()->route('employee.medication-orders.index');
    }
}
