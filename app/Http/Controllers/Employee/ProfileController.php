<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class ProfileController extends Controller
{
    /**
     * Show the employee profile edit form.
     */
    public function edit(Request $request)
    {
        $user = Auth::user();
        $employee = $user->businessEmployee;

        if (!$employee) {
            Toast::danger('Employee profile not found.')->autoDismiss(5);
            return redirect()->route('employee.dashboard');
        }

        return view('employee.profile.edit', compact('user', 'employee'));
    }

    /**
     * Update the employee profile.
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $employee = $user->businessEmployee;

        if (!$employee) {
            Toast::danger('Employee profile not found.')->autoDismiss(5);
            return redirect()->route('employee.dashboard');
        }

        $validated = $request->validate([
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'gender' => 'required|in:M,F,O',
            'dob' => 'required|date|before:today',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:2',
            'zip' => 'required|string|max:20',
            'phone' => 'required|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'image' => 'nullable|image|max:2048',
            'agree_to_terms' => 'required|accepted',
        ]);

        // Update name
        $validated['name'] = $validated['fname'] . ' ' . $validated['lname'];

        // Set terms agreement timestamp
        if ($validated['agree_to_terms']) {
            $validated['terms_agreed_at'] = now();
        }

        // Handle image upload
        if ($request->hasFile('image') && $request->file('image')->isValid()) {
            // Delete old image if it exists
            if ($user->image && Storage::disk('public')->exists($user->image)) {
                Storage::disk('public')->delete($user->image);
            }

            $imagePath = $request->file('image')->store('profile_images', 'public');
            $validated['image'] = $imagePath;
        } else {
            // Remove image from validated data if no file was uploaded
            unset($validated['image']);
        }

        // Remove agree_to_terms from validated data as it's not a database field
        unset($validated['agree_to_terms']);

        $user->update($validated);

        Toast::success('Profile updated successfully!')->autoDismiss(5);

        return redirect()->route('employee.profile.edit');
    }
}
