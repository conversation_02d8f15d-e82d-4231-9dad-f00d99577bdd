<?php

namespace App\Http\Controllers\Employee;

use App\Actions\Business\ProcessEmployeeSelfPayment;
use App\Http\Controllers\Controller;
use App\Http\Requests\Employee\SelfPaymentRequest;
use App\Models\BusinessEmployee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class SelfPaymentController extends Controller
{
    public function __construct(
        protected ProcessEmployeeSelfPayment $processEmployeeSelfPayment
    ) {}

    /**
     * Show the self-payment form.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Get the employee record for the authenticated user
        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->with(['business', 'business.plans'])
            ->first();

        if (!$employee) {
            Toast::danger('Employee record not found or inactive.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get eligibility information
        $eligibilityInfo = $this->processEmployeeSelfPayment->getEligibilityInfo($employee);

        // Get payment history
        $paymentHistory = $this->processEmployeeSelfPayment->getPaymentHistory($employee);

        return view('employee.self-payment.index', [
            'employee' => $employee,
            'eligibility' => $eligibilityInfo,
            'payment_history' => $paymentHistory['data'] ?? [],
        ]);
    }

    /**
     * Show the payment form.
     */
    public function create(Request $request)
    {
        $user = $request->user();

        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->with(['business'])
            ->first();

        if (!$employee) {
            Toast::danger('Employee record not found or inactive.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Check eligibility
        $eligibilityInfo = $this->processEmployeeSelfPayment->getEligibilityInfo($employee);

        if (!$eligibilityInfo['eligible']) {
            Toast::danger($eligibilityInfo['message'])->autoDismiss(10);
            return redirect()->route('employee.self-payment.index');
        }

        return view('employee.self-payment.create', [
            'employee' => $employee,
            'eligibility' => $eligibilityInfo,
        ]);
    }

    /**
     * Process the self-payment.
     */
    public function store(SelfPaymentRequest $request)
    {
        $user = $request->user();

        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        if (!$employee) {
            Toast::danger('Employee record not found or inactive.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        try {
            // Get payment data from the validated request
            $paymentData = $request->getPaymentData();

            // Process the self-payment
            $result = $this->processEmployeeSelfPayment->execute($employee, $paymentData);

            if ($result['success']) {
                Log::info('Employee self-payment successful', [
                    'employee_id' => $employee->id,
                    'user_id' => $user->id,
                    'self_payment_id' => $result['data']['self_payment_id'] ?? null,
                ]);

                Toast::success($result['message'])->autoDismiss(10);
                return redirect()->route('employee.self-payment.success', [
                    'payment_id' => $result['data']['self_payment_id']
                ]);
            } else {
                Log::warning('Employee self-payment failed', [
                    'employee_id' => $employee->id,
                    'user_id' => $user->id,
                    'error_message' => $result['message'],
                ]);

                Toast::danger($result['message'])->autoDismiss(10);
                return back()->withInput();
            }

        } catch (\Exception $e) {
            Log::error('Exception during employee self-payment', [
                'employee_id' => $employee->id,
                'user_id' => $user->id,
                'exception' => $e->getMessage(),
            ]);

            Toast::danger('An unexpected error occurred. Please try again.')->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Show the payment success page.
     */
    public function success(Request $request, $paymentId)
    {
        $user = $request->user();

        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        if (!$employee) {
            Toast::danger('Employee record not found or inactive.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get the self-payment record
        $selfPayment = $employee->selfPayments()
            ->with(['businessPlan', 'transaction'])
            ->find($paymentId);

        if (!$selfPayment) {
            Toast::danger('Payment record not found.')->autoDismiss(10);
            return redirect()->route('employee.self-payment.index');
        }

        return view('employee.self-payment.success', [
            'employee' => $employee,
            'self_payment' => $selfPayment,
        ]);
    }

    /**
     * Show payment history.
     */
    public function history(Request $request)
    {
        $user = $request->user();

        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->with(['business'])
            ->first();

        if (!$employee) {
            Toast::danger('Employee record not found or inactive.')->autoDismiss(10);
            return redirect()->route('dashboard');
        }

        // Get payment history
        $paymentHistory = $this->processEmployeeSelfPayment->getPaymentHistory($employee);

        return view('employee.self-payment.history', [
            'employee' => $employee,
            'payment_history' => $paymentHistory['data'] ?? [],
        ]);
    }

    /**
     * Get eligibility status via AJAX.
     */
    public function checkEligibility(Request $request)
    {
        $user = $request->user();

        $employee = BusinessEmployee::where('user_id', $user->id)
            ->where('status', 'active')
            ->first();

        if (!$employee) {
            return response()->json([
                'eligible' => false,
                'message' => 'Employee record not found or inactive.',
            ]);
        }

        $eligibilityInfo = $this->processEmployeeSelfPayment->getEligibilityInfo($employee);

        return response()->json($eligibilityInfo);
    }
}
