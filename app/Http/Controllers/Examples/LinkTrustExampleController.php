<?php

namespace App\Http\Controllers\Examples;

use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Services\LinkTrustService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class LinkTrustExampleController extends Controller
{
    use TracksLinkTrust;

    protected $linkTrustService;

    public function __construct(LinkTrustService $linkTrustService)
    {
        $this->linkTrustService = $linkTrustService;
    }

    /**
     * Example of using the trait directly
     */
    public function exampleUsingTrait(Request $request)
    {
        // Example of tracking a purchase using the trait
        $result = $this->trackLinkTrustPurchase(
            amount: 99.99,
            userId: auth()->id(),
            transactionId: 'TX-' . time()
        );

        return view('examples.linktrust', [
            'result' => $result,
            'clickId' => Cookie::get('LTClickID') ?? session('LTClickID'),
            'afid' => Cookie::get('AFID') ?? session('AFID'),
            'method' => 'Using TracksLinkTrust Trait'
        ]);
    }

    /**
     * Example of using the service
     */
    public function exampleUsingService(Request $request)
    {
        // Example of tracking a purchase using the service
        $result = $this->linkTrustService->trackPurchase(
            amount: 99.99,
            userId: auth()->id(),
            transactionId: 'TX-' . time()
        );

        return view('examples.linktrust', [
            'result' => $result,
            'clickId' => Cookie::get('LTClickID') ?? session('LTClickID'),
            'afid' => Cookie::get('AFID') ?? session('AFID'),
            'method' => 'Using LinkTrustService'
        ]);
    }
}
