<?php

namespace App\Http\Controllers;

use App\Models\Discount;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class ExtraHelpController extends Controller
{
    /**
     * Handle the ExtraHelp promo page
     */
    public function index(Request $request)
    {
        // Find the ExtraHelp discount
        $discount = Discount::where('promo_code', 'extrahelp')
            ->where('status', Discount::STATUS_ACTIVE)
            ->first();

        if (!$discount) {
            Log::warning('ExtraHelp discount not found or not active');
            Toast::info('No active discount found.')->autoDismiss(10);
            return redirect()->route('home');
        }

        // Store the discount ID in the session with a special flag to ADD to other discounts
        $request->session()->put('extrahelp_discount_id', $discount->id);
        $request->session()->put('stack_discount', true); // Changed from override_discount to stack_discount
        $request->session()->put('promo_code', 'extrahelp');

        // Store discount information for display
        $discountAmount = '$' . number_format($discount->value, 2);
        if ($discount->type === 'percentage') {
            $discountAmount = $discount->value . '%';
        }

        // Get subscription plans with the ExtraHelp discount applied
        $subscriptionPlans = SubscriptionPlan::where('is_active', true)
            ->orderBy('display_order')
            ->get()
            ->map(function ($plan) use ($discount) {
                $plan->original_price = $plan->price;
                $plan->discounted_price = $discount->applyDiscount($plan->price);
                $plan->discount_amount = $discount->getDiscountedAmount($plan->price);
                $plan->discount_percentage = $discount->getDiscountPercentage($plan->price);
                $plan->discount_type = $discount->type;
                $plan->discount_value = $discount->value;
                return $plan;
            });

        // Redirect to the health-plan page with the ExtraHelp discount applied and banner message
        return redirect()->route('health-plan')
            ->with('extrahelp_applied', true)
            ->with('discount_banner', [
                'message' => "Congratulations! An additional $discountAmount discount has been applied to all plans.",
                'type' => 'success',
                'discount_id' => $discount->id,
                'discount_value' => $discount->value,
                'discount_type' => $discount->type
            ]);
    }
}
