<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\Medication;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class InventoryController extends Controller
{
    public function __construct(Request $request, private $routePrefix = '')
    {
        if ($request->user()->hasRole('admin')) $this->routePrefix = 'admin.';
        elseif ($request->user()->hasRole('pharmacist')) $this->routePrefix = 'pharmacy.';
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('inventory.index', [
            'inventories' => SpladeTable::for(Inventory::class)
                ->column('id', sortable: true)
                ->column('medication.name', label: 'Medication')
                ->column('quantity', sortable: true)
                ->column('expiry_date', sortable: true, as: fn ($expiry_date) => $expiry_date->format('F j, Y'))
                ->column('batch_number', sortable: true, searchable: true)
                ->column('reorder_point', sortable: true, searchable: true)
                ->column('max_stock_level', sortable: true)
                ->column('actions')
                ->selectFilter('medication_id', Medication::pluck('name', 'id')->toArray(), label: 'Medication')
                ->paginate(15),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('inventory.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'medication_id' => 'required|exists:medications,id',
            'quantity' => 'required|integer|min:0',
            'expiry_date' => 'required|date',
            'batch_number' => 'required|string',
            'reorder_point' => 'required|integer|min:0',
            'max_stock_level' => 'required|integer|min:0',
        ]);

        Inventory::create($validated);

        Toast::success('Inventory item added successfully.');

        return redirect()->route($this->routePrefix . 'inventories.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Inventory $inventory)
    {
        $routePrefix = $this->routePrefix;
        return view('inventory.edit', compact('inventory', 'routePrefix'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Inventory $inventory)
    {
        $validated = $request->validate([
            'medication_id' => 'required|exists:medications,id',
            'quantity' => 'required|integer|min:0',
            'expiry_date' => 'required|date',
            'batch_number' => 'required|string',
            'reorder_point' => 'required|integer|min:0',
            'max_stock_level' => 'required|integer|min:0',
        ]);
    
        $inventory->update($validated);

        Toast::success('Inventory item updated successfully.');

        return redirect()->route($this->routePrefix . 'inventories.index');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function lowStock()
    {
        return view('inventory.low-stock', [
            'lowStockItems' => SpladeTable::for(Inventory::whereRaw('quantity <= reorder_point'))
                ->column('id', sortable: true)
                ->column('medication.name', label: 'Medication')
                ->column('quantity', sortable: true)
                ->column('expiry_date', sortable: true, as: fn ($expiry_date) => $expiry_date->format('F j, Y'))
                ->column('batch_number', sortable: true, searchable: true)
                ->column('reorder_point', sortable: true, searchable: true)
                ->column('max_stock_level', sortable: true)
                ->column('actions')
                ->selectFilter('medication_id', Medication::pluck('name', 'id')->toArray(), label: 'Medication')
                ->paginate(15),
        ]);
    }

    public function valueTrend()
    {
        $trend = Inventory::join('medications', 'inventories.medication_id', '=', 'medications.id')
            ->selectRaw('DATE(inventories.created_at) as date, SUM(inventories.quantity * medications.unit_price) as total_value')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('inventory.value-trend-chart', compact('trend'));
    }
}
