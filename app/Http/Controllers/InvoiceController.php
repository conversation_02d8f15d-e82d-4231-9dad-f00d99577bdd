<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\InvoiceGeneratorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class InvoiceController extends Controller
{
    protected $invoiceGeneratorService;

    public function __construct(InvoiceGeneratorService $invoiceGeneratorService)
    {
        $this->invoiceGeneratorService = $invoiceGeneratorService;
    }

    /**
     * Display a listing of invoices for the authenticated user.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get all invoice transactions for the user
        $invoices = Transaction::where('user_id', $user->id)
            ->where('payment_method', 'invoice')
            ->orderBy('created_at', 'desc')
            ->get();
        
        return view('invoices.index', [
            'invoices' => $invoices
        ]);
    }

    /**
     * Display a specific invoice.
     */
    public function show(Transaction $transaction)
    {
        // Check if the user has permission to view this invoice
        if (Auth::id() !== $transaction->user_id && !Auth::user()->hasRole(['admin', 'agent'])) {
            Toast::danger('You do not have permission to view this invoice.');
            return redirect()->back();
        }

        // Check if this is an invoice transaction
        if ($transaction->payment_method !== 'invoice') {
            Toast::danger('This transaction is not an invoice.');
            return redirect()->back();
        }

        // Get the invoice path
        $invoicePath = $this->invoiceGeneratorService->getInvoicePath($transaction);

        if (!$invoicePath || !Storage::exists('public/' . $invoicePath)) {
            // If the invoice doesn't exist, generate it
            $result = $this->invoiceGeneratorService->generateInvoice($transaction);
            
            if (!$result['success']) {
                Toast::danger('Failed to generate invoice: ' . $result['message']);
                return redirect()->back();
            }
            
            $invoicePath = $result['path'];
        }

        // Return the view with the invoice data
        return view('invoices.show', [
            'transaction' => $transaction,
            'invoicePath' => $invoicePath
        ]);
    }

    /**
     * Download an invoice PDF.
     */
    public function download(Transaction $transaction)
    {
        // Check if the user has permission to download this invoice
        if (Auth::id() !== $transaction->user_id && !Auth::user()->hasRole(['admin', 'agent'])) {
            Toast::danger('You do not have permission to download this invoice.');
            return redirect()->back();
        }

        // Check if this is an invoice transaction
        if ($transaction->payment_method !== 'invoice') {
            Toast::danger('This transaction is not an invoice.');
            return redirect()->back();
        }

        // Get the invoice path
        $invoicePath = $this->invoiceGeneratorService->getInvoicePath($transaction);

        if (!$invoicePath || !Storage::exists('public/' . $invoicePath)) {
            // If the invoice doesn't exist, generate it
            $result = $this->invoiceGeneratorService->generateInvoice($transaction);
            
            if (!$result['success']) {
                Toast::danger('Failed to generate invoice: ' . $result['message']);
                return redirect()->back();
            }
            
            $invoicePath = $result['path'];
        }

        // Return the file download
        return Storage::download('public/' . $invoicePath, 'invoice_' . $transaction->transaction_id . '.pdf');
    }

    /**
     * Regenerate an invoice PDF.
     */
    public function regenerate(Transaction $transaction)
    {
        // Check if the user has permission to regenerate this invoice
        if (!Auth::user()->hasRole(['admin', 'agent'])) {
            Toast::danger('You do not have permission to regenerate invoices.');
            return redirect()->back();
        }

        // Check if this is an invoice transaction
        if ($transaction->payment_method !== 'invoice') {
            Toast::danger('This transaction is not an invoice.');
            return redirect()->back();
        }

        // Regenerate the invoice
        $result = $this->invoiceGeneratorService->generateInvoice($transaction);
        
        if (!$result['success']) {
            Toast::danger('Failed to regenerate invoice: ' . $result['message']);
            return redirect()->back();
        }
        
        Toast::success('Invoice regenerated successfully.');
        return redirect()->route('invoices.show', $transaction);
    }
}
