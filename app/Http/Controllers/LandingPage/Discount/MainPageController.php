<?php

namespace App\Http\Controllers\LandingPage\Discount;

use App\Http\Controllers\Controller;
use App\Models\Medication;
use Illuminate\Http\Request;

class MainPageController extends Controller
{
    public function index($category = null)
    {
        // $categories = Category::with('medications')->get();
        $categories = [
            [
                'id' => 6,
                'name' => 'Pain Relief',
                'medications' => [
                    [
                        'id' => 12,
                        'name' => 'Celecoxib',
                        'brand_name' => 'Celebrex',
                        'description' => 'Used to treat pain and inflammation',
                        'uses' => 'Pain relief, inflammation',
                    ],
                    [
                        'id' => 18,
                        'name' => 'Meloxicam',
                        'brand_name' => 'Mobic',
                        'description' => 'Used to treat pain and inflammation',
                        'uses' => 'Pain relief, joint pain',
                    ],
                    [
                        'id' => 6,
                        'name' => 'Amitriptyline',
                        'brand_name' => 'Elavil',
                        'description' => 'Tricyclic antidepressant medication used to treat depression, anxiety, and certain types of chronic pain',
                        'uses' => 'Depression, anxiety, chronic pain',
                    ],
                    [
                        'id' => 1000,
                        'name' => 'Etodolac',
                        'brand_name' => 'Lodine',
                        'description' => 'NSAID for pain and inflammation.',
                        'uses' => 'Pain relief.',
                    ],
                    [
                        'id' => 19,
                        'name' => 'Methocarbamol',
                        'brand_name' => 'Robaxin',
                        'description' => 'Used to treat muscle pain and stiffness',
                        'uses' => 'Muscle pain, stiffness',
                    ],
                    [
                        'id' => 29,
                        'name' => 'Topiramate',
                        'brand_name' => 'Topamax',
                        'description' => 'Used to treat seizures and prevent migraines',
                        'uses' => 'Migraines, seizures',
                    ],
                    [
                        'id' => 8,
                        'name' => 'Baclofen',
                        'brand_name' => 'Lioresal',
                        'description' => 'Used to treat muscle spasms and pain',
                        'uses' => 'Muscle spasms, pain',
                    ],
                    [
                        'id' => 13,
                        'name' => 'Cyclobenzaprine',
                        'brand_name' => 'Flexeril',
                        'description' => 'Used to treat muscle spasms and pain',
                        'uses' => 'Muscle spasms, pain',
                    ],
                    [
                        'id' => 21,
                        'name' => 'Tizanidine',
                        'brand_name' => 'Zanaflex',
                        'description' => 'Used to treat muscle spasms',
                        'uses' => 'Muscle spasms',
                    ],
                    [
                        'id' => 1001,
                        'name' => 'Orphenadrine',
                        'brand_name' => 'Norflex',
                        'description' => 'Used for muscle pain.',
                        'uses' => 'Muscle injuries.',
                    ],
                    [
                        'id' => 1002,
                        'name' => 'Nabumetone',
                        'brand_name' => 'Relafen',
                        'description' => 'Long-term arthritis treatment.',
                        'uses' => 'Arthritis relief.',
                    ],
                ],
            ],
            [
                'id' => 4,
                'name' => 'Mental Health',
                'medications' => [
                    [
                        'id' => 11,
                        'name' => 'Buspirone',
                        'brand_name' => 'BuSpar',
                        'description' => 'Used to treat anxiety disorders',
                        'uses' => 'Anxiety',
                    ],
                    [
                        'id' => 14,
                        'name' => 'Duloxetine',
                        'brand_name' => 'Cymbalta',
                        'description' => 'Used to treat depression and anxiety',
                        'uses' => 'Depression, anxiety',
                    ],
                    [
                        'id' => 15,
                        'name' => 'Escitalopram',
                        'brand_name' => 'Lexapro',
                        'description' => 'Used to treat depression and anxiety',
                        'uses' => 'Depression, anxiety',
                    ],
                    [
                        'id' => 17,
                        'name' => 'Fluoxetine',
                        'brand_name' => 'Prozac',
                        'description' => 'Used to treat depression, OCD, and panic attacks',
                        'uses' => 'Depression, OCD, panic attacks',
                    ],
                    [
                        'id' => 1004,
                        'name' => 'Paroxetine',
                        'brand_name' => 'Paxil',
                        'description' => 'Treats anxiety disorders and depression.',
                        'uses' => 'Anxiety, depression.',
                    ],
                    [
                        'id' => 1005,
                        'name' => 'Venlafaxine',
                        'brand_name' => 'Effexor',
                        'description' => 'Treats anxiety, depression, and panic attacks.',
                        'uses' => 'Anxiety, panic.',
                    ],
                    [
                        'id' => 1006,
                        'name' => 'Nortriptyline',
                        'brand_name' => 'Pamelor',
                        'description' => 'Used for pain relief and depression.',
                        'uses' => 'Pain, depression.',
                    ],
                    [
                        'id' => 20,
                        'name' => 'Sertraline',
                        'brand_name' => 'Zoloft',
                        'description' => 'Used to treat depression, OCD, and panic attacks',
                        'uses' => 'Depression, OCD, panic attacks',
                    ],
                    [
                        'id' => 31,
                        'name' => 'Bupropion',
                        'brand_name' => 'Wellbutrin',
                        'description' => 'Used to treat depression and seasonal affective disorder',
                        'uses' => 'Depression, seasonal affective disorder',
                    ],
                ],
            ],
            [
                'id' => 1,
                'name' => 'Erectile Dysfunction',
                'medications' => [
                    [
                        'id' => 22,
                        'name' => 'Sildenafil',
                        'brand_name' => 'Viagra',
                        'description' => 'Used to treat erectile dysfunction',
                        'uses' => 'ED',
                    ],
                    [
                        'id' => 23,
                        'name' => 'Tadalafil',
                        'brand_name' => 'Cialis',
                        'description' => 'Used to treat erectile dysfunction and benign prostatic hyperplasia',
                        'uses' => 'ED, BPH',
                    ],
                ],
            ],
            [
                'id' => 2,
                'name' => 'Hair Loss',
                'medications' => [
                    [
                        'id' => 16,
                        'name' => 'Finasteride',
                        'brand_name' => 'Propecia',
                        'description' => 'Used to treat male pattern baldness and enlarged prostate',
                        'uses' => 'Hair loss, BPH',
                    ],
                ],
            ],
            [
                'id' => 3,
                'name' => 'Weight Management',
                'medications' => [
                    [
                        'id' => 30,
                        'name' => 'Metformin',
                        'brand_name' => 'Glucophage',
                        'description' => 'Used to treat type 2 diabetes',
                        'uses' => 'Diabetes, weight management',
                    ],
                    [
                        'id' => 29,
                        'name' => 'Topiramate',
                        'brand_name' => 'Topamax',
                        'description' => 'Used for weight loss and seizures',
                        'uses' => 'Weight loss, seizures.',
                    ],
                    [
                        'id' => 31,
                        'name' => 'Bupropion',
                        'brand_name' => 'Wellbutrin',
                        'description' => 'Used to treat depression and seasonal affective disorder',
                        'uses' => 'Depression, seasonal affective disorder',
                    ],
                ],
            ],
            [
                'id' => 5,
                'name' => 'Skin Care',
                'medications' => [
                    [
                        'id' => 1007,
                        'name' => 'Tretinoin',
                        'brand_name' => 'Retin-A',
                        'description' => 'Improves acne and skin renewal.',
                        'uses' => 'Acne, skin aging.',
                    ],
                    [
                        'id' => 1008,
                        'name' => 'Adapalene',
                        'brand_name' => 'Differin',
                        'description' => 'Used to treat acne.',
                        'uses' => 'Acne.',
                    ],
                    [
                        'id' => 1009,
                        'name' => 'Hydroquinone',
                        'brand_name' => 'Eldoquin',
                        'description' => 'Reduces skin discoloration.',
                        'uses' => 'Skin discoloration.',
                    ],
                    [
                        'id' => 1010,
                        'name' => 'Azelaic Acid',
                        'brand_name' => 'Finacea',
                        'description' => 'Treats acne and rosacea.',
                        'uses' => 'Acne, rosacea.',
                    ],
                ],
            ],
            [
                'id' => 7,
                'name' => 'Cold Sores & Herpes',
                'medications' => [
                    [
                        'id' => 5,
                        'name' => 'Acyclovir',
                        'brand_name' => 'Zovirax',
                        'description' => 'An antiviral medication used to treat herpes viruses',
                        'uses' => 'Cold sores, herpes',
                    ],
                ],
            ],
        ];
        $categories = collect($categories);

        $medications = Medication::where('status', 'approved')
            ->orderBy('is_usual_dosage', 'DESC')
            ->orderBy('order')
            ->orderBy('generic_name')
            ->get();

        return view('front.landing-pages.discount.index', compact('categories', 'medications'));
    }

    public function submit(Request $request)
    {
        $request->session()->put('preferred-medications', $request->all());
        return to_route('discount.email-phone-form', ['serviceId' => $request->categoryId]);
    }

    public function checkout()
    {
        dd('asdf');
    }
}
