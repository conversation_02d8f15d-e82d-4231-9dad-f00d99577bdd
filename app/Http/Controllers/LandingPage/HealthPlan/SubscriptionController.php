<?php

namespace App\Http\Controllers\LandingPage\HealthPlan;

use App\Actions\Medications\CreateSubscription;
use App\Actions\Medications\HandleMeasurements;
use App\Actions\Medications\HandleUserCreation;
use App\Actions\Medications\ProcessPayment;
use App\Actions\Medications\SelectPlan;
use App\Actions\Medications\TrackOrderProgress;
use App\Http\Controllers\Controller;
use App\Http\Requests\LandingPage\HealthPlanPaymentRequest;
use App\Mail\HealthPlanWelcomeMail;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Concerns\TracksLinkTrust;
use App\Notifications\HealthPlanWelcomeNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use ProtoneMedia\Splade\Facades\Toast;

class SubscriptionController extends Controller
{
    use TracksLinkTrust;

    public function __construct(
        private SelectPlan $selectPlan,
        private TrackOrderProgress $trackOrderProgress,
        private HandleUserCreation $handleUserCreation,
        private HandleMeasurements $handleMeasurements,
        private ProcessPayment $processPayment,
        private CreateSubscription $createSubscription
    ){}

    /**
     * Display the plan selection page
     */
    public function showPlans()
    {
        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'plan_selection'
        ]);

        // Get active subscription plans
        $plans = SubscriptionPlan::where('status', 'active')
            ->whereIn('type', ['individual', 'family', 'single'])
            ->where('is_active', true)
            ->with('discounts')
            ->get();

        return view('front.landing-pages.health-plan.plans', [
            'plans' => $plans,
            'progress' => $progressResult['progress'] ?? 33,
            'completed_steps' => $progressResult['completed_steps'] ?? []
        ]);
    }

    /**
     * Handle plan selection
     */
    public function selectPlan(Request $request)
    {
        $request->validate([
            'plan' => 'required|exists:subscription_plans,id',
            'hasInsurance' => 'nullable|array'
        ]);

        // Always set hasInsurance to 'no' since insurance options are hidden
        $hasInsurance = 'no';

        // Track progress
        $this->trackOrderProgress->execute([
            'current_step' => 'plan_selection'
        ]);

        // Use the action to select a plan
        $this->selectPlan->execute([
            'plan_id' => $request->plan,
            'has_insurance' => false
        ]);

        // Store in session that this is a health plan only subscription
        Session::put('health_plan_only', true);

        return to_route('health-plan.payment');
    }

    /**
     * Display the payment page
     */
    public function showPayment(Request $request)
    {
        // if already paid go to payment.success page
        if ($request->user() && $request->user()->transactions()->where('status', 'success')->exists()) {
            return to_route('health-plan.payment.success');
        }

        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'payment'
        ]);

        if (Session::has('plan')) {
            $planId = Session::get('plan')['id'];
        } else {
            // Default to a plan if none selected
            $planId = SubscriptionPlan::where('status', 'active')
                ->where('is_active', true)
                ->first()->id ?? 1;
        }

        $plan = SubscriptionPlan::where('id', $planId)
            ->with('discounts')
            ->first();

        return view('front.landing-pages.health-plan.payment', [
            'plan' => $plan,
            'progress' => $progressResult['progress'] ?? 66,
            'completed_steps' => $progressResult['completed_steps'] ?? []
        ]);
    }

    /**
     * Process payment
     */
    public function processPayment(HealthPlanPaymentRequest $request)
    {
        if (!Session::has('plan')) {
            return redirect()->route('health-plan.plans');
        }

        // Track progress
        $this->trackOrderProgress->execute([
            'current_step' => 'payment'
        ]);

        try {
            // Log the request data (excluding sensitive information)
            Log::info('Payment request received', [
                'request_data' => [
                    'payment' => [
                        'first_name' => $request->payment['first_name'] ?? null,
                        'last_name' => $request->payment['last_name'] ?? null,
                        'email' => $request->payment['email'] ?? null,
                        'phone' => $request->payment['phone'] ?? null,
                        'address1' => $request->payment['address1'] ?? null,
                        'address2' => $request->payment['address2'] ?? null,
                        'city' => $request->payment['city1'] ?? null,
                        'state' => $request->payment['state1'] ?? null,
                        'zip' => $request->payment['zip1'] ?? null,
                        'country' => $request->payment['country1'] ?? null,
                        // Exclude card details for security
                        'has_card_data' => isset($request->payment['card_number']),
                    ],
                    'has_measurement_data' => isset($request->measurement),
                    'session_data' => [
                        'has_plan' => Session::has('plan'),
                        'health_plan_only' => Session::get('health_plan_only', false),
                    ]
                ]
            ]);

            // Start a database transaction for the entire payment process
            DB::beginTransaction();
            Log::info('Database transaction started');

            $planDetails = $this->getPlanDetails();

            // Log detailed plan information
            $plan = $planDetails['plan'];
            Log::info('Plan details retrieved', [
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'plan_type' => $plan->type,
                'plan_price' => $plan->price,
                'plan_duration_months' => $plan->duration_months,
                'calculated_amount' => $planDetails['amount'],
                'hasInsurance' => $planDetails['hasInsurance'],
                'has_active_discount' => isset($plan->activeDiscount),
                'discount_details' => $plan->activeDiscount ? [
                    'discount_id' => $plan->activeDiscount->id,
                    'discount_type' => $plan->activeDiscount->type,
                    'discount_value' => $plan->activeDiscount->value,
                    'discount_name' => $plan->activeDiscount->name,
                ] : null
            ]);

            // Handle user creation
            Log::info('Handling user creation/retrieval');
            $userResult = $this->handleUserCreation->execute([
                'user_data' => $request->payment
            ]);
            $user = $userResult['user'];

            // Update user's address information if it's a new user or if the address fields are different
            if ($userResult['is_new'] ||
                $user->address1 !== $request->payment['address1'] ||
                $user->city !== $request->payment['city'] ||
                $user->state !== $request->payment['state'] ||
                $user->zip !== $request->payment['zip']) {

                $user->update([
                    'address1' => $request->payment['address1'],
                    'city' => $request->payment['city'],
                    'state' => $request->payment['state'],
                    'zip' => $request->payment['zip'],
                ]);

                Log::info('User address information updated', [
                    'user_id' => $user->id,
                    'address1' => $request->payment['address1'],
                    'city' => $request->payment['city'],
                    'state' => $request->payment['state'],
                    'zip' => $request->payment['zip'],
                ]);
            }

            Log::info('User created or retrieved', [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'user_name' => $user->name,
                'is_new_user' => $userResult['is_new'] ?? false,
                'has_roles' => $user->roles->isNotEmpty(),
                'roles' => $user->roles->pluck('name')->toArray(),
                'created_at' => $user->created_at->toDateTimeString(),
                'updated_at' => $user->updated_at->toDateTimeString()
            ]);

            // Handle measurements if provided
            if (isset($request->measurement)) {
                Log::info('Handling user measurements', [
                    'user_id' => $user->id,
                    'measurement_data_keys' => array_keys($request->validated('measurement'))
                ]);

                $measurementResult = $this->handleMeasurements->execute([
                    'user' => $user,
                    'measurement_data' => $request->validated('measurement')
                ]);

                Log::info('Measurements handled successfully', [
                    'user_id' => $user->id,
                    'result' => $measurementResult
                ]);
            } else {
                Log::info('No measurements provided for user', ['user_id' => $user->id]);
            }

            // Process payment
            $paymentResult = $this->processPayment->execute([
                'user' => $user,
                'card_data' => $request->validated('payment'),
                'amount' => $planDetails['amount'], // Charge the actual plan amount
                'is_discounted' => $planDetails['hasInsurance'] // Pass whether this is a discounted payment
            ]);

            // Log the payment result for debugging
            Log::info('Payment result', [
                'success' => $paymentResult['success'] ?? false,
                'transaction_id' => $paymentResult['transaction_id'] ?? null,
                'user_id' => $user->id,
                'amount' => $planDetails['amount']
            ]);

            if ($paymentResult['success']) {
                Log::info('Payment successful, handling subscription', [
                    'user_id' => $user->id,
                    'plan_id' => $planDetails['plan']->id,
                    'amount' => $planDetails['amount'],
                    'is_discounted' => $planDetails['hasInsurance']
                ]);

                // Use the CreateSubscription action to create or update the subscription
                Log::info('Creating or updating subscription using CreateSubscription action', [
                    'user_id' => $user->id,
                    'plan_id' => $planDetails['plan']->id,
                    'amount' => $planDetails['amount'],
                    'is_discounted' => $planDetails['hasInsurance']
                ]);

                $subscriptionResult = $this->createSubscription->execute([
                    'user' => $user,
                    'plan' => $planDetails['plan'],
                    'is_discounted' => $planDetails['hasInsurance'],
                    'discounted_price' => $planDetails['amount'],
                    'meta_data' => [
                        'type' => 'health_plan_only',
                        'payment_transaction_id' => $paymentResult['transaction_id'] ?? null
                    ]
                ]);

                $subscription = $subscriptionResult['subscription'];

                Log::info('Subscription created or updated', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_id' => $subscription->plan_id,
                    'is_new' => $subscriptionResult['is_new'],
                    'starts_at' => $subscription->starts_at ? $subscription->starts_at->toDateTimeString() : null,
                    'ends_at' => $subscription->ends_at ? $subscription->ends_at->toDateTimeString() : null,
                    'status' => $subscription->status,
                    'is_discounted' => $subscription->is_discounted,
                    'discounted_price' => $subscription->discounted_price
                ]);

                // Get transaction details
                $transaction = null;
                if (isset($paymentResult['transaction'])) {
                    $transaction = $paymentResult['transaction'];
                    Log::info('Transaction details', [
                        'transaction_id' => $transaction->id,
                        'transaction_external_id' => $transaction->transaction_id,
                        'amount' => $transaction->amount,
                        'status' => $transaction->status,
                        'payment_method' => $transaction->payment_method,
                        'created_at' => $transaction->created_at->toDateTimeString(),
                        'is_discounted' => $transaction->is_discounted ?? false
                    ]);
                }

                // Store transaction ID and amount in session
                $transactionId = $paymentResult['transaction_id'] ?? '';
                Session::put('transid', $transactionId);
                Session::put('amount', $planDetails['amount']);

                Log::info('Session data updated', [
                    'transaction_id' => $transactionId,
                    'amount' => $planDetails['amount'],
                    'health_plan_only' => Session::get('health_plan_only', false)
                ]);

                // Commit the database transaction
                DB::commit();
                Log::info('Database transaction committed successfully');

                // Send welcome email to the user
                try {
                    // You can use either the notification or the mailable approach
                    // Option 1: Using Notification
                    $user->notify(new HealthPlanWelcomeNotification($subscription, $planDetails['plan']));

                    // Option 2: Using Mailable (uncomment if you prefer this approach)
                    // Mail::to($user->email)->send(new HealthPlanWelcomeMail(
                    //     user: $user,
                    //     subscription: $subscription,
                    //     plan: $planDetails['plan']
                    // ));

                    Log::info('Health plan welcome email sent', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'subscription_id' => $subscription->id,
                        'plan_id' => $planDetails['plan']->id
                    ]);
                } catch (\Exception $e) {
                    // Log the error but don't stop the process
                    Log::error('Failed to send health plan welcome email', [
                        'user_id' => $user->id,
                        'email' => $user->email,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }

                Toast::info('Payment successful.')->autoDismiss();
                Log::info('Payment process completed successfully', [
                    'user_id' => $user->id,
                    'plan_id' => $planDetails['plan']->id,
                    'amount' => $planDetails['amount'],
                    'transaction_id' => $transactionId
                ]);

                return to_route('health-plan.payment.success');
            }

            // If payment failed but everything else was successful, still commit the database transaction
            // This ensures we keep the user data, measurements, medical data, etc.
            DB::commit();

            // Track the failed transaction in LinkTrust
            $customerInfo = [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone ?? ''
            ];

            $transactionId = 'FAILED-' . time();
            $this->trackLinkTrustFailedTransaction(
                userId: $user->id,
                transactionId: $transactionId,
                customerInfo: $customerInfo
            );

            Toast::danger('Payment processing failed. Please try again.')->autoDismiss();
            return to_route('health-plan.payment');

        } catch (\Exception $e) {
            // Roll back the database transaction if there was an error
            DB::rollBack();
            Log::error('Database transaction rolled back due to exception');

            // Track the exception in LinkTrust if we have a user
            if (isset($user) && $user) {
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? ''
                ];

                $transactionId = 'ERROR-' . time();

                Log::info('Tracking failed transaction in LinkTrust', [
                    'user_id' => $user->id,
                    'transaction_id' => $transactionId,
                    'customer_info' => $customerInfo
                ]);

                $trackingResult = $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );

                Log::info('LinkTrust tracking result for failed transaction', $trackingResult);
            }

            Log::error('Payment processing error', [
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'session_data' => [
                    'has_plan' => Session::has('plan'),
                    'plan_data' => Session::get('plan'),
                    'health_plan_only' => Session::get('health_plan_only', false)
                ],
                'user_data' => isset($user) ? [
                    'user_id' => $user->id,
                    'user_email' => $user->email
                ] : 'No user created yet',
                'linktrust_tracking' => isset($user) ? 'sent' : 'not sent (no user)'
            ]);

            Toast::danger('Payment processing error. Please try again.')->autoDismiss();
            return to_route('health-plan.payment');
        }
    }

    /**
     * Display payment success page
     */
    public function showSuccess(Request $request)
    {
        // Track the purchase using the TracksLinkTrust trait
        $amount = Session::has('amount') ? round(Session::get('amount'), 2) : 0;
        $userId = $request->user()->id;
        $transactionId = Session::get('transid', 'TX-' . time());

        // Track the purchase in LinkTrust
        $trackingResult = $this->trackLinkTrustPurchase(
            amount: $amount,
            userId: $userId,
            transactionId: $transactionId
        );

        info('LinkTrust tracking result:', $trackingResult);

        $amount = Session::get('amount', 0);
        return view('front.landing-pages.health-plan.success', [
            'amount' => $amount
        ]);
    }

    /**
     * Get plan details from session
     */
    private function getPlanDetails(): array
    {
        $planId = Session::get('plan')['id'];
        // Always set hasInsurance to false since insurance options are hidden
        $hasInsurance = false;

        $plan = SubscriptionPlan::where('id', $planId)
            ->with('discounts')
            ->first();

        if (!$plan) {
            throw new \Exception('Plan not found');
        }

        $amount = $plan->price;

        // Use the new discount stacking logic from the model
        if ($hasInsurance || Session::has('stack_discount')) {
            // Calculate total discount amount using the model's accessor
            $discountAmount = $plan->totalDiscountAmount;

            // Ensure discount doesn't make price negative
            $discountAmount = min($discountAmount, $plan->price);
            $amount = $plan->price - $discountAmount;

            // Log discount application
            if ($plan->activeDiscount || $plan->extraHelpDiscount) {
                Log::info('Discounts applied', [
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'original_price' => $plan->price,
                    'total_discount' => $discountAmount,
                    'final_price' => $amount,
                    'regular_discount' => $plan->activeDiscount ? [
                        'id' => $plan->activeDiscount->id,
                        'name' => $plan->activeDiscount->name,
                        'type' => $plan->activeDiscount->type,
                        'value' => $plan->activeDiscount->value
                    ] : null,
                    'extrahelp_discount' => $plan->extraHelpDiscount ? [
                        'id' => $plan->extraHelpDiscount->id,
                        'name' => $plan->extraHelpDiscount->name,
                        'type' => $plan->extraHelpDiscount->type,
                        'value' => $plan->extraHelpDiscount->value
                    ] : null
                ]);
            }
        }

        return [
            'plan' => $plan,
            'amount' => $amount,
            'hasInsurance' => $hasInsurance
        ];
    }
}
