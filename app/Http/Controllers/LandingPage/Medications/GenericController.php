<?php

namespace App\Http\Controllers\LandingPage\Medications;

use App\Http\Controllers\Controller;
use App\Models\MedicationBase;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\SEO;

class GenericController extends Controller
{
    public function show(MedicationBase $generic)
    {
        SEO::title("{$generic->generic_name} Online Prescription")
            ->description("Get {$generic->generic_name} prescribed online. Fast, easy and affordable telemedicine consultations available 24/7.");

        $plans = SubscriptionPlan::where('status', 'active')
            ->whereIn('type', ['individual', 'family', 'single'])
            ->with('discounts')
            ->get();

        $features = [
            "Unlimited Virtual Doctor Visits",
            "Unlimited medications",
            "Treatments for over 200 conditions",
            "Covers 2 adults and kids under 21",
            "Includes 1-year supply of medication",
            "Virtual Doctor consultation for a single treatment",
            "Includes 6-month supply of medication",
            "No recurring billing",
        ];

        return view('front.landing-pages.medications.show', [
            'generic' => $generic,
            'plans' => $plans,
            'features' => $features,
        ]);
    }
}
