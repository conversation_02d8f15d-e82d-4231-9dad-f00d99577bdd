<?php

namespace App\Http\Controllers\LandingPage\Medications;

use App\Actions\Medications\TrackOrderProgress;
use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Medication;
use App\Models\MedicationBase;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Surfsidemedia\Shoppingcart\Facades\Cart;

class MainPageController extends Controller
{
    use TracksLinkTrust;

    public function __construct(
        private TrackOrderProgress $trackOrderProgress
    ){}
    public function index(Request $request)
    {
        // The middleware will handle storing ClickID and AFID in cookies and session
        // This is kept for backward compatibility
        if (isset($request->ClickID)) {
            session()->put('LTClickID', $request->ClickID);
        }

        if (isset($request->AFID)) {
            session()->put('AFID', $request->AFID);
        }

        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'medication_selection'
        ]);

        // Get initial categories for the filter component
        $categories = \App\Models\Category::with('children.children')
            ->whereNull('parent_id')
            ->get();

        return view('front.landing-pages.medications.index', [
            'categories' => $categories,
            'progress' => $progressResult['progress'],
            'completed_steps' => $progressResult['completed_steps']
        ]);
    }

    public function byCategory(Request $request, $slug)
    {
        $category = Str::title(str_replace('-', ' ', $slug));
        $category = Category::where('name', $category)->firstOrFail();
        $category = $category ? $category->root_parent : null;

        return view('front.landing-pages.medications.index', compact('category'));
    }

    public function byMedication(Request $request, $slug)
    {
        $medication = Str::title(str_replace('-', ' ', $slug));
        $medication = MedicationBase::where('generic_name', $medication)
            ->orWhereHas('medicationVariants', function ($query) use ($medication) {
                $query->where('brand_name', $medication);
            })
            ->with([
                'medicationVariants',
                'primaryUses' => function($query) {
                    $query->with(['condition.categories']);
                },
                'offLabelUses' => function($query) {
                    $query->with(['condition.categories']);
                },
                'medicationClass',
                'dosageInformation'
            ])
            ->firstOrFail();

        return view('front.landing-pages.medications.index', compact('medication'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'medication_ids' => 'required|array',
            'medication_ids.*' => 'exists:medications,id'
        ]);

        // Process the selected medications here
        return response()->json(['message' => 'Medications selected successfully']);
    }

    public function genericNameList(Request $request)
    {
        $medications = MedicationBase::select('id', 'generic_name')
            ->with('medicationVariants')
            ->orderBy('generic_name')
            ->get()
            ->groupBy(function ($item) {
                return strtoupper(substr($item->generic_name, 0, 1));
            });

        $letters = $medications->map(function ($group, $letter) {
            return [
                'letter' => $letter,
                'count' => $group->count(),
                'link' => view('components.letter-link', [
                    'letter' => $letter,
                    'count' => $group->count()
                ])->render()
            ];
        });

        return view('front.landing-pages.medications.generic-name-list', [
            'groupedMedications' => $medications,
            'letters' => $letters,
            // 'cartItems' => Cart::instance('shopping')->content()
        ]);
    }
}
