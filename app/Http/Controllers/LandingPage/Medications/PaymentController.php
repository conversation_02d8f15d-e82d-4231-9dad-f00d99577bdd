<?php

namespace App\Http\Controllers\LandingPage\Medications;

use App\Actions\Medications\CreateMedicationOrderFromCart;
use App\Actions\Medications\CreateSubscription;
use App\Actions\Medications\HandleInsurance;
use App\Actions\Medications\HandleMeasurements;
use App\Actions\Medications\HandleMedicalData;
use App\Actions\Medications\HandleUserCreation;
use App\Actions\Medications\ProcessPayment;
use App\Actions\Medications\TrackOrderProgress;
use App\Concerns\TracksOC;
use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Http\Requests\LandingPage\RxPaymentRequest;
use App\Models\Cart;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentController extends Controller
{
    use TracksOC, TracksLinkTrust;

    protected $transactionUpdated = false;

    public function __construct(
        private ProcessPayment $processPayment,
        private HandleUserCreation $handleUserCreation,
        private HandleMedicalData $handleMedicalData,
        private HandleMeasurements $handleMeasurements,
        private HandleInsurance $handleInsurance,
        private TrackOrderProgress $trackOrderProgress,
        private CreateSubscription $createSubscription,
        private CreateMedicationOrderFromCart $createMedicationOrderFromCart
    ){}

    public function showForm(Request $request)
    {
        // if already paid go to payment.success page
        if ($request->user() && $request->user()->transactions()->where('status', 'success')->exists()) {
            return to_route('rx.payment.success');
        }

        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'payment'
        ]);

        // if (!Session::has('plan')) {
        //     return redirect()->route('rx.plan-options');
        // }

        if (Session::has('plan')) {
            $planId = Session::get('plan')['id'];
        } else {
            $planId = 7;
        }

        $plan = SubscriptionPlan::where('id', $planId)
            ->with('discounts')
            ->first();

        return view('front.landing-pages.medications.payment', [
            'plan' => $plan,
            'progress' => $progressResult['progress'],
            'completed_steps' => $progressResult['completed_steps']
        ]);
    }

    public function pay(RxPaymentRequest $request)
    {
        if (!Session::has('plan')) {
            return redirect()->route('rx.plan-options');
        }

        // Track progress
        $this->trackOrderProgress->execute([
            'current_step' => 'payment'
        ]);

        try {
            // Start a database transaction for the entire payment process
            DB::beginTransaction();

            $planDetails = $this->getPlanDetails();
            Log::info('Plan details retrieved', [
                'plan_id' => $planDetails['plan']->id,
                'amount' => $planDetails['amount'],
                'hasInsurance' => $planDetails['hasInsurance']
            ]);

            // Handle user creation
            $userResult = $this->handleUserCreation->execute([
                'user_data' => $request->payment
            ]);
            $user = $userResult['user'];
            Log::info('User created or retrieved', ['user_id' => $user->id]);

            // Handle measurements
            $this->handleMeasurements->execute([
                'user' => $user,
                'measurement_data' => $request->validated('measurement')
            ]);
            Log::info('Measurements handled', ['user_id' => $user->id]);

            // Handle medical data
            $this->handleMedicalData->execute([
                'user' => $user,
                'medical_questions' => Session::get('medical_questions'),
                'medications' => Session::get('medications'),
                'allergies' => Session::get('allergies')
            ]);
            Log::info('Medical data handled', ['user_id' => $user->id]);

            // Handle insurance if needed
            if ($planDetails['hasInsurance']) {
                $this->handleInsurance->execute([
                    'user' => $user,
                    'insurance_data' => $request->insurance
                ]);
                Log::info('Insurance handled', ['user_id' => $user->id]);
            }

            // Process payment
            $paymentResult = $this->processPayment->execute([
                'user' => $user,
                'card_data' => $request->validated('payment'),
                'amount' => $planDetails['amount'], // Charge the actual plan amount
                'is_discounted' => $planDetails['hasInsurance'] // Pass whether this is a discounted payment
            ]);

            // Log the payment result for debugging
            Log::info('Payment result', [
                'success' => $paymentResult['success'] ?? false,
                'transaction_id' => $paymentResult['transaction_id'] ?? null,
                'user_id' => $user->id,
                'amount' => $planDetails['amount']
            ]);

            if ($paymentResult['success']) {
                // Create subscription using the CreateSubscription action
                $subscriptionResult = $this->createSubscription->execute([
                    'user' => $user,
                    'plan' => $planDetails['plan'],
                    'is_discounted' => $planDetails['hasInsurance'],
                    'discounted_price' => $planDetails['hasInsurance'] ? $planDetails['amount'] : null,
                    'amount' => $planDetails['amount']
                ]);

                $subscription = $subscriptionResult['subscription'];

                Log::info('Subscription created using CreateSubscription action', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'plan_id' => $planDetails['plan']->id,
                    'amount' => $planDetails['amount'],
                    'is_new' => $subscriptionResult['is_new']
                ]);

                // Update transaction directly
                try {
                    if (!empty($paymentResult['transaction_id'])) {
                        // First check if a transaction with this ID already exists
                        $existingTransaction = Transaction::where('transaction_id', $paymentResult['transaction_id'])
                            ->first();

                        if ($existingTransaction) {
                            // Check for duplicate pending transactions for this user and amount
                            $pendingTransactions = Transaction::where('user_id', $user->id)
                                ->where('amount', $planDetails['amount'])
                                ->where('status', 'pending')
                                ->where('id', '!=', $existingTransaction->id)
                                ->get();

                            // Delete any duplicate pending transactions
                            foreach ($pendingTransactions as $pendingTransaction) {
                                Log::info('Deleting duplicate pending transaction in PaymentController', [
                                    'transaction_id' => $pendingTransaction->id,
                                    'user_id' => $user->id,
                                    'amount' => $planDetails['amount']
                                ]);

                                $pendingTransaction->delete();
                            }

                            // Update the existing transaction
                            $existingTransaction->subscription_id = $subscription->id;
                            $existingTransaction->is_discounted = $planDetails['hasInsurance']; // Only set to true if user has insurance
                            $existingTransaction->status = 'success'; // Ensure status is success
                            $existingTransaction->save();

                            Log::info('Updated existing transaction with transaction_id', [
                                'transaction_id' => $existingTransaction->transaction_id,
                                'transaction_db_id' => $existingTransaction->id,
                                'subscription_id' => $subscription->id,
                                'user_id' => $user->id
                            ]);
                        } else {
                            // Try to update by transaction_id
                            $updated = Transaction::where('transaction_id', $paymentResult['transaction_id'])
                                ->update([
                                    'subscription_id' => $subscription->id,
                                    'is_discounted' => $planDetails['hasInsurance'], // Only set to true if user has insurance
                                    'status' => 'success' // Ensure status is success
                                ]);

                            Log::info('Updated transaction directly in pay method', [
                                'transaction_id' => $paymentResult['transaction_id'],
                                'subscription_id' => $subscription->id,
                                'updated' => $updated,
                                'user_id' => $user->id
                            ]);

                            // If no rows were updated, try to find the transaction by user and amount
                            if ($updated == 0) {
                                $this->findAndUpdateTransaction($user, $subscription, $planDetails['amount'], $planDetails['hasInsurance']);
                            }
                        }
                    } else {
                        // Check for pending transactions for this user and amount
                        $pendingTransaction = Transaction::where('user_id', $user->id)
                            ->where('amount', $planDetails['amount'])
                            ->where('status', 'pending')
                            ->orderBy('created_at', 'desc')
                            ->first();

                        if ($pendingTransaction) {
                            // Update the pending transaction
                            $pendingTransaction->subscription_id = $subscription->id;
                            $pendingTransaction->is_discounted = $planDetails['hasInsurance']; // Only set to true if user has insurance
                            $pendingTransaction->status = 'success';
                            $pendingTransaction->transaction_id = 'manual-' . uniqid();
                            $pendingTransaction->save();

                            Log::info('Updated pending transaction with generated transaction_id', [
                                'transaction_id' => $pendingTransaction->transaction_id,
                                'transaction_db_id' => $pendingTransaction->id,
                                'subscription_id' => $subscription->id,
                                'user_id' => $user->id
                            ]);

                            // Update the paymentResult with the new transaction_id
                            $paymentResult['transaction_id'] = $pendingTransaction->transaction_id;
                        } else {
                            // Try to find the transaction by user and amount
                            $this->findAndUpdateTransaction($user, $subscription, $planDetails['amount'], $planDetails['hasInsurance']);

                            // If we couldn't find a transaction, create a new one
                            if (!$this->transactionUpdated) {
                                $newTransaction = Transaction::create([
                                    'user_id' => $user->id,
                                    'subscription_id' => $subscription->id,
                                    'amount' => $planDetails['amount'],
                                    'payment_method' => 'credit_card',
                                    'status' => 'success',
                                    'transaction_id' => 'manual-' . uniqid(),
                                    'is_discounted' => $planDetails['hasInsurance'] // Only set to true if user has insurance
                                ]);

                                Log::info('Created new transaction record in pay method', [
                                    'transaction_id' => $newTransaction->transaction_id,
                                    'transaction_db_id' => $newTransaction->id,
                                    'user_id' => $user->id,
                                    'subscription_id' => $subscription->id
                                ]);

                                // Update the paymentResult with the new transaction_id
                                $paymentResult['transaction_id'] = $newTransaction->transaction_id;
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Log the error but don't fail the entire payment process
                    Log::error('Error updating transaction, but continuing with payment process', [
                        'error' => $e->getMessage(),
                        'user_id' => $user->id,
                        'subscription_id' => $subscription->id,
                        'transaction_id' => $paymentResult['transaction_id'] ?? null
                    ]);
                }

                // Set session variables
                Session::put('payment-success', [
                    'form-name' => 'discount-payment',
                    'conversion-value' => 5,
                    'page-path' => 'https://gomdusa.com/rx/payment',
                    'page-name' => 'RXMeds',
                    'transaction-id' => $paymentResult['transaction_id'] ?? ''
                ]);

                Session::put('transid', $paymentResult['transaction_id'] ?? '');
                Session::put('amount', $planDetails['amount']);

                $this->recordPurchase($planDetails['amount'], $planDetails['plan']->id);

                // Collect ALL possible session IDs that might contain cart items
                $allSessionIds = [];

                // 1. Get the current session ID
                $currentSessionId = Session::getId();
                $allSessionIds[] = $currentSessionId;

                // 2. Get the original session ID from the request cookie if available
                $originalSessionId = $request->cookie('original_session_id') ?? Cookie::get('original_session_id');
                if ($originalSessionId) {
                    $allSessionIds[] = $originalSessionId;
                }

                // 3. Check if there's a cart session ID in the request
                if ($request->has('cart_session_id')) {
                    $cartSessionId = $request->input('cart_session_id');
                    $allSessionIds[] = $cartSessionId;
                }

                // 4. Get all session IDs from cookies
                $cookies = $request->cookies->all();
                foreach ($cookies as $name => $value) {
                    if (strpos($name, 'session') !== false && !in_array($value, $allSessionIds)) {
                        $allSessionIds[] = $value;
                    }
                }

                // Remove the problematic logic that was collecting random cart items
                // We only want session IDs that actually belong to this user

                // Log the session information for debugging
                Log::info('Session information for cart conversion', [
                    'current_session_id' => $currentSessionId,
                    'original_session_id' => $originalSessionId,
                    'all_session_ids' => $allSessionIds,
                    'user_id' => $user->id
                ]);

                // Create medication order from cart items
                $medicationOrderResult = $this->createMedicationOrderFromCart->execute([
                    'user' => $user,
                    'session_id' => $currentSessionId,
                    'original_session_id' => $originalSessionId,
                    'all_session_ids' => $allSessionIds,
                    'patient_notes' => 'Order created automatically after payment'
                ]);

                if ($medicationOrderResult['success']) {
                    Log::info('Medication order created from cart after payment', [
                        'user_id' => $user->id,
                        'order_id' => $medicationOrderResult['medication_order']->id
                    ]);

                    // Store the medication order ID in the session for reference
                    Session::put('medication_order_id', $medicationOrderResult['medication_order']->id);
                } else {
                    // Log the error but continue with the payment process
                    Log::warning('Failed to create medication order from cart', [
                        'user_id' => $user->id,
                        'error' => $medicationOrderResult['error'] ?? $medicationOrderResult['message'] ?? 'Unknown error'
                    ]);
                }

                // Commit the database transaction
                DB::commit();

                Toast::info('Payment successful.')->autoDismiss();
                Log::info('Payment completed successfully', ['user_id' => $user->id]);

                return to_route('rx.payment.success');
            }

            // If payment failed but everything else was successful, still commit the database transaction
            // This ensures we keep the user data, measurements, medical data, etc.
            DB::commit();

            // Track the failed transaction in LinkTrust
            $customerInfo = [
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone ?? ''
            ];

            $transactionId = 'FAILED-' . time();
            $this->trackLinkTrustFailedTransaction(
                userId: $user->id,
                transactionId: $transactionId,
                customerInfo: $customerInfo
            );

            Log::error('Payment failed', [
                'user_id' => $user->id,
                'error' => $paymentResult['error'],
                'linktrust_tracking' => 'sent'
            ]);

            Toast::danger('Payment failed: ' . $paymentResult['error'])->autoDismiss();
            return back();

        } catch (\Exception $e) {
            // Roll back the database transaction if there was an error
            DB::rollBack();

            // Track the exception in LinkTrust if we have a user
            if (isset($user) && $user) {
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? ''
                ];

                $transactionId = 'ERROR-' . time();
                $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );
            }

            Log::error('Payment processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'linktrust_tracking' => isset($user) ? 'sent' : 'not sent (no user)'
            ]);

            Toast::danger('Payment processing error. Please try again.')->autoDismiss();
            return to_route('rx.payment');
        }
    }

    private function getPlanDetails(): array
    {
        $planData = Session::get('plan');
        $plan = SubscriptionPlan::findOrFail($planData['id']);
        $hasInsurance = $planData['hasInsurance'] === 'yes';

        // Only apply discount if user has insurance
        $amount = $hasInsurance
            ? $plan->final_price
            : $plan->price;

        Log::info('Plan details calculated', [
            'plan_id' => $plan->id,
            'has_insurance' => $hasInsurance,
            'original_price' => $plan->price,
            'final_amount' => $amount
        ]);

        return [
            'plan' => $plan,
            'hasInsurance' => $hasInsurance,
            'amount' => $amount
        ];
    }

    /**
     * Find and update a transaction for a user and subscription
     * This method tries multiple strategies to find a transaction to update
     *
     * @param User $user
     * @param Subscription $subscription
     * @param float $amount
     * @param bool $hasInsurance Whether the user has insurance (for discount)
     * @return void
     */
    private function findAndUpdateTransaction(User $user, Subscription $subscription, float $amount, bool $hasInsurance = false): void
    {
        $this->transactionUpdated = false;

        Log::info('Finding and updating transaction', [
            'user_id' => $user->id,
            'subscription_id' => $subscription->id,
            'amount' => $amount,
            'has_insurance' => $hasInsurance
        ]);

        try {
            // First check if there's already a transaction linked to this subscription
            $existingLinked = Transaction::where('subscription_id', $subscription->id)
                ->first();

            if ($existingLinked) {
                Log::info('Transaction already linked to this subscription', [
                    'transaction_id' => $existingLinked->transaction_id,
                    'transaction_db_id' => $existingLinked->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id
                ]);

                $this->transactionUpdated = true;
                return;
            }

            // Try to find by user_id, amount, and status=success
            $transaction = Transaction::where('user_id', $user->id)
                ->where('amount', $amount)
                ->where('status', 'success')
                ->whereNull('subscription_id')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($transaction) {
                $transaction->subscription_id = $subscription->id;
                $transaction->is_discounted = $hasInsurance; // Only set to true if user has insurance
                $transaction->save();

                Log::info('Updated transaction by user_id and amount in findAndUpdateTransaction', [
                    'transaction_id' => $transaction->transaction_id,
                    'transaction_db_id' => $transaction->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id
                ]);

                $this->transactionUpdated = true;
                return;
            }

            // Try to find by user_id and status=success
            $transaction = Transaction::where('user_id', $user->id)
                ->where('status', 'success')
                ->whereNull('subscription_id')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($transaction) {
                $transaction->subscription_id = $subscription->id;
                $transaction->is_discounted = $hasInsurance; // Only set to true if user has insurance
                $transaction->save();

                Log::info('Updated transaction by user_id and success status in findAndUpdateTransaction', [
                    'transaction_id' => $transaction->transaction_id,
                    'transaction_db_id' => $transaction->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id
                ]);

                $this->transactionUpdated = true;
                return;
            }

            // Try to find by user_id and status=pending (might be a pending transaction that succeeded)
            $transaction = Transaction::where('user_id', $user->id)
                ->where('status', 'pending')
                ->whereNull('subscription_id')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($transaction) {
                $transaction->subscription_id = $subscription->id;
                $transaction->is_discounted = $hasInsurance; // Only set to true if user has insurance
                $transaction->status = 'success'; // Update status to success
                $transaction->save();

                Log::info('Updated pending transaction to success in findAndUpdateTransaction', [
                    'transaction_id' => $transaction->transaction_id,
                    'transaction_db_id' => $transaction->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id
                ]);

                $this->transactionUpdated = true;
                return;
            }

            // Try to find by user_id and any status
            $transaction = Transaction::where('user_id', $user->id)
                ->whereNull('subscription_id')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($transaction) {
                $transaction->subscription_id = $subscription->id;
                $transaction->is_discounted = $hasInsurance; // Only set to true if user has insurance
                $transaction->status = 'success'; // Update status to success
                $transaction->save();

                Log::info('Updated transaction by user_id (any status) in findAndUpdateTransaction', [
                    'transaction_id' => $transaction->transaction_id,
                    'transaction_db_id' => $transaction->id,
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id
                ]);

                $this->transactionUpdated = true;
                return;
            }

            Log::warning('Could not find any transaction to update in findAndUpdateTransaction', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'amount' => $amount
            ]);
        } catch (\Exception $e) {
            // Log the error but don't fail the entire process
            Log::error('Error in findAndUpdateTransaction', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'amount' => $amount
            ]);
        }
    }

    public function success(Request $request)
    {
        if (!$request->user()) {
            return to_route('rx.index');
        }

        // Check if the user has a subscription
        $hasSubscription = $request->user()->subscriptions()->where('status', 'active')->exists();

        // Check if the user has a successful transaction
        $hasTransaction = $request->user()->transactions()->where('status', 'success')->exists();

        // Allow access if the user has either a subscription or a successful transaction
        if (!$hasSubscription && !$hasTransaction) {
            return to_route('rx.index');
        }
        // Track the purchase using the TracksLinkTrust trait
        $amount = Session::has('amount') ? round(Session::get('amount'), 2) : 0;
        $userId = $request->user()->id;
        $transactionId = Session::get('transid', 'TX-' . time());

        // Track the purchase in LinkTrust
        $trackingResult = $this->trackLinkTrustPurchase(
            amount: $amount,
            userId: $userId,
            transactionId: $transactionId
        );

        info('LinkTrust tracking result:', $trackingResult);

        // info(print_r($response, true));

        $amount = Session::get('amount', 0);
        $medicationOrderId = Session::get('medication_order_id');

        // Get the medication order if it exists
        $medicationOrder = null;
        if ($medicationOrderId) {
            $medicationOrder = \App\Models\MedicationOrder::with('items.medication')
                ->find($medicationOrderId);
        }

        return view('front.landing-pages.medications.payment-success', [
            'amount' => $amount,
            'medicationOrder' => $medicationOrder
        ]);
    }
}
