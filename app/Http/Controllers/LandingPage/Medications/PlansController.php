<?php

namespace App\Http\Controllers\LandingPage\Medications;

use App\Actions\Medications\SelectPlan;
use App\Actions\Medications\TrackOrderProgress;
use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;

class PlansController extends Controller
{
    public function __construct(
        private SelectPlan $selectPlan,
        private TrackOrderProgress $trackOrderProgress
    ){}

    public function planOptions()
    {
        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'plan_selection'
        ]);

        $plans = SubscriptionPlan::where('status', 'active')
            ->whereIn('type', ['individual', 'family', 'single'])
            ->where('is_active', true)
            ->with('discounts')
            ->get();

        return view('front.landing-pages.medications.plan-options', [
            'plans' => $plans,
            'progress' => $progressResult['progress'],
            'completed_steps' => $progressResult['completed_steps']
        ]);
    }

    public function selectPlan(Request $request)
    {
        // Always set hasInsurance to 'no' since insurance options are hidden
        $hasInsurance = 'no';

        // Track progress
        $this->trackOrderProgress->execute([
            'current_step' => 'plan_selection'
        ]);

        // Use the action to select a plan
        $this->selectPlan->execute([
            'plan_id' => $request->plan,
            'has_insurance' => false
        ]);

        return to_route('rx.questions');
    }
}
