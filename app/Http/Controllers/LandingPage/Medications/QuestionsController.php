<?php

namespace App\Http\Controllers\LandingPage\Medications;

use App\Actions\Medications\DetermineRelevantQuestions;
use App\Actions\Medications\SaveMedicalQuestions;
use App\Actions\Medications\SecurelyStoreMedicalData;
use App\Actions\Medications\TrackOrderProgress;
use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use ProtoneMedia\Splade\Facades\Toast;

class QuestionsController extends Controller
{
    public function __construct(
        private SaveMedicalQuestions $saveMedicalQuestions,
        private DetermineRelevantQuestions $determineRelevantQuestions,
        private SecurelyStoreMedicalData $securelyStoreMedicalData,
        private TrackOrderProgress $trackOrderProgress
    ){}

    public function showQuestions()
    {
        if (!Session::has('plan')) {
            return redirect()->route('rx.plan-options');
        }

        // Track progress
        $progressResult = $this->trackOrderProgress->execute([
            'current_step' => 'medical_questions'
        ]);

        $medicalQuestions = ['medications' => [], 'allergies' => []];
        if (Session::has('medical_questions')) {
            $medicalQuestions = array_merge($medicalQuestions, Session::get('medical_questions'));
        }

        // Determine relevant questions based on medications in cart
        $relevantQuestionsResult = $this->determineRelevantQuestions->execute([]);
        $relevantSections = $relevantQuestionsResult['success'] ? $relevantQuestionsResult['relevant_sections'] : ['general'];

        $sessionId = Session::getId();
        $userId = Auth::id();

        // Get cart items with user ID support
        $cartItems = Cart::with('medicationVariant.medicationBase.primaryUses.condition.categories')
            ->where(function($q) use ($sessionId, $userId) {
                $q->where('session_id', $sessionId);
                if ($userId) {
                    $q->where(function($subQuery) use ($userId) {
                        $subQuery->whereNull('user_id')
                                 ->orWhere('user_id', $userId);
                    });
                }
            })
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'variant_id' => $item->medicationVariant->id,
                    'brand_name' => $item->medicationVariant->brand_name,
                    'generic_name' => $item->medicationVariant->medicationBase->generic_name,
                    'strength' => $item->medicationVariant->strength,
                    'dosage_form' => $item->medicationVariant->dosage_form,
                    'quantity' => $item->quantity,
                    // 'unit_price' => $item->medicationVariant->unit_price,
                    // 'total_price' => $item->medicationVariant->unit_price * $item->quantity,
                    'requires_prescription' => $item->medicationVariant->medicationBase->requires_prescription,
                    // 'primary_uses' => $item->medicationVariant->medicationBase->primaryUses->map(function ($primaryUse) {
                    //     return [
                    //         'id' => $primaryUse->id,
                    //         'name' => $primaryUse->name,
                    //         'condition' => [
                    //             'id' => $primaryUse->condition->id,
                    //             'name' => $primaryUse->condition->name,
                    //             'categories' => $primaryUse->condition->categories->pluck('name'),
                    //         ],
                    //     ];
                    // }),
                    'categories' => $item->medicationVariant->medicationBase->primaryUses->map(function ($primaryUse) {
                        return $primaryUse->condition->categories->pluck('name');
                    })->flatten(),
                ];
            });

        $categories = $cartItems->pluck('categories')->flatten()->unique();

        $plan = SubscriptionPlan::where('id', Session::get('plan')['id'])
            ->with('discounts')
            ->first();

        return view('front.landing-pages.medications.questions', [
            'categories' => $categories,
            'cartItems' => $cartItems,
            'plan' => $plan,
            'medicalQuestions' => $medicalQuestions,
            'relevantSections' => $relevantSections,
            'progress' => $progressResult['progress']
        ]);
    }

    public function submit(Request $request)
    {
        $validatedData = $request->validate([
            'allergies.*.allergen' => 'required',
            'medications.*.medication_name' => 'required',
            'cardiovascular_diagnosis' => 'sometimes|required',
            'cardiovascular_symptoms' => 'sometimes|required',
            'cardiovascular_medications' => 'sometimes|required',
            'cardiovascular_family' => 'sometimes|required',
            'cardiovascular_diet' => 'sometimes|required',
            'cardiovascular_lifestyle' => 'sometimes|required',
            'cardiovascular_monitoring' => 'sometimes|required',
            'neuro_diagnosis' => 'sometimes|required',
            'neuro_frequency' => 'sometimes|required',
            'neuro_triggers' => 'sometimes|required',
            'neuro_sleep' => 'sometimes|required',
            'neuro_daily_impact' => 'sometimes|required',
            'neuro_medications' => 'sometimes|required',
            'neuro_side_effects' => 'sometimes|required',
            'gi_symptoms' => 'sometimes|required',
            'gi_frequency' => 'sometimes|required',
            'gi_diet' => 'sometimes|required',
            'gi_medications' => 'sometimes|required',
            'gi_procedures' => 'sometimes|required',
            'gi_weight' => 'sometimes|required',
            'endocrine_diagnosis' => 'sometimes|required',
            'endocrine_symptoms' => 'sometimes|required',
            'endocrine_labs' => 'sometimes|required',
            'endocrine_medications' => 'sometimes|required',
            'endocrine_monitoring' => 'sometimes|required',
            'preventive_risk' => 'sometimes|required',
            'preventive_diet' => 'sometimes|required',
            'preventive_exercise' => 'sometimes|required',
            'preventive_screenings' => 'sometimes|required',
            'preventive_falls' => 'sometimes|required',
            'prophylaxis_history' => 'sometimes|required',
            'prophylaxis_risk' => 'sometimes|required',
            'prophylaxis_immunity' => 'sometimes|required',
            'prophylaxis_allergies' => 'sometimes|required',
            'prophylaxis_current' => 'sometimes|required',
            'skin_conditions' => 'sometimes|required',
            'skin_symptoms' => 'sometimes|required',
            'skin_triggers' => 'sometimes|required',
            'skin_treatments' => 'sometimes|required',
            'skin_impact' => 'sometimes|required',
            'immune_conditions' => 'sometimes|required',
            'immune_allergies' => 'sometimes|required',
            'immune_symptoms' => 'sometimes|required',
            'immune_treatments' => 'sometimes|required',
            'immune_triggers' => 'sometimes|required',
            'immune_emergency' => 'sometimes|required',
            'mh_symptoms_severity' => 'sometimes|required',
            'mh_sleep_patterns' => 'sometimes|required',
            'mh_concentration' => 'sometimes|required',
            'mh_support_system' => 'sometimes|required',
            'mh_coping_methods' => 'sometimes|required',
            'mh_suicidal_thoughts' => 'sometimes|required',
            'mh_treatment_history' => 'sometimes|required',
            'pain_location_type' => 'sometimes|required',
            'pain_frequency' => 'sometimes|required',
            'pain_severity' => 'sometimes|required',
            'pain_triggers' => 'sometimes|required',
            'pain_relief' => 'sometimes|required',
            'pain_impact' => 'sometimes|required',
            'pain_associated_symptoms' => 'sometimes|required',
            'respiratory_symptoms' => 'sometimes|required',
            'respiratory_triggers' => 'sometimes|required',
            'respiratory_sleep' => 'sometimes|required',
            'respiratory_exercise' => 'sometimes|required',
            'respiratory_treatments' => 'sometimes|required',
            'respiratory_smoking' => 'sometimes|required',
            'prevention_risk_factors' => 'sometimes|required',
            'prevention_history' => 'sometimes|required',
            'prevention_medications' => 'sometimes|required',
            'prevention_lifestyle' => 'sometimes|required',
            'prevention_monitoring' => 'sometimes|required',
            'prevention_family_history' => 'sometimes|required',
            'additional_symptoms' => 'sometimes|required',
            'quality_of_life' => 'sometimes|required',
            'treatment_goals' => 'sometimes|required',
            'concerns' => 'sometimes|required',
            'weight_history' => 'sometimes|required',
            'current_weight_goals' => 'sometimes|required',
            'lifestyle_factors' => 'sometimes|required',
            'underlying_conditions' => 'sometimes|required',
            'previous_attempts' => 'sometimes|required',
            'weight_medication_history' => 'sometimes|required',
            'barriers_to_weight_loss' => 'sometimes|required',
            'family_weight_history' => 'sometimes|required',
        ],
        [
            'medications.*.medication_name' => 'Medication name is required.',
        ]);

        // Track progress
        $this->trackOrderProgress->execute([
            'current_step' => 'medical_questions'
        ]);

        // Save medical questions to session for backward compatibility
        $this->saveMedicalQuestions->execute([
            'question_data' => $request->except('medications', 'allergies'),
            'medications' => $request->input('medications'),
            'allergies' => $request->input('allergies')
        ]);

        // If user is logged in, securely store medical data
        if (Auth::check()) {
            $this->securelyStoreMedicalData->execute([
                'user' => Auth::user(),
                'medical_data' => array_merge(
                    $request->except('medications', 'allergies'),
                    ['medications' => $request->input('medications')],
                    ['allergies' => $request->input('allergies')]
                ),
                'session_id' => Session::getId()
            ]);
        }

        Toast::success('Medical information saved successfully')->autoDismiss(3);
        return redirect()->route('rx.payment');
    }
}
