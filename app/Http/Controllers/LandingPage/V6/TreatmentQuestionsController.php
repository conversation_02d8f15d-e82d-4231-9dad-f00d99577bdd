<?php

namespace App\Http\Controllers\LandingPage\V6;

use App\Http\Controllers\Controller;
use App\Models\AdditionalInformation;
use App\Models\AdverseReaction;
use App\Models\Allergy;
use App\Models\DiagnosticTest;
use App\Models\FamilyMedicalHistory;
use App\Models\LifestyleHabit;
use App\Models\MedicalCondition;
use App\Models\MedicalSurgicalHistory;
use App\Models\MedicationScreening;
use App\Models\MentalHealthAssessment;
use App\Models\PainAssessment;
use App\Models\PhysicalExaminationIndicator;
use App\Models\PsychologicalSocialFactor;
use App\Models\Question;
use App\Models\Service;
use App\Models\UserReportedMedication;
use App\Models\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TreatmentQuestionsController extends Controller
{
    public function show(Request $request, $serviceId, $questionId)
    {
        $service = Service::findOrFail($serviceId);
        $question = Question::where('service_id', $serviceId)
            ->where('id', $questionId)
            ->with('options')
            ->firstOrFail();

        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);

        $previousQuestion = $this->getPreviousQuestion($serviceId, $question, $userAnswers);
        $nextQuestion = $this->getNextQuestion($serviceId, $question, $userAnswers);

        return view('front.landing-pages.v6.questions', compact('service', 'question', 'previousQuestion', 'nextQuestion', 'userAnswers'));
    }

    public function store(Request $request, $serviceId, $questionId)
    {
        $question = Question::findOrFail($questionId);

        $validatedData = $request->validate([
            'answer' => $question->required ? 'required' : 'nullable',
        ]);

        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);
        $userAnswers[$questionId] = $validatedData['answer'];
        $request->session()->put($sessionKey, $userAnswers);

        $nextQuestion = $this->getNextQuestion($serviceId, $question, $userAnswers);

        if ($nextQuestion) {
            return to_route('landing-page6.questions.show', [$serviceId, $nextQuestion->id]);
        } else {
            return to_route('landing-page6.questions.summary', $serviceId);
        }
    }

    public function showPainForm()
    {
        return view('front.landing-pages.v6.pain-questions');
    }

    public function storePainForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pain_type' => 'required|array|min:1',
            'pain_type_other' => 'required_if:pain_type,Other',
            'pain_location' => 'array',
            'pain_location_other' => 'required_if:pain_location,Other',
            'pain_intensity' => 'required|integer|min:1|max:10',
            'pain_duration' => 'nullable|string',
            'pain_start' => 'nullable|date',
            'pain_frequency' => 'nullable|string',
            'pain_triggers' => 'array',
            'pain_triggers_other' => 'required_if:pain_triggers,Other',
            'pain_relief' => 'array',
            'pain_relief_other' => 'required_if:pain_relief,Other',
            'allergies' => 'required|in:Yes,No',
            'allergy_details' => 'required_if:allergies,Yes',
            'current_medications' => 'nullable|string',
            'conditions' => 'required|array|min:1',
            'conditions_other' => 'required_if:conditions,Other',
            'adverse_reactions' => 'required|in:Yes,No',
            'adverse_reactions_details' => 'required_if:adverse_reactions,Yes',
            'current_side_effects' => 'required|in:Yes,No',
            'side_effects_details' => 'required_if:current_side_effects,Yes',
            'alcohol_use' => 'required|in:Yes,No',
            'alcohol_details' => 'required_if:alcohol_use,Yes',
            'tobacco_use' => 'required|in:Yes,No',
            'tobacco_details' => 'required_if:tobacco_use,Yes',
            'drug_use' => 'required|in:Yes,No',
            'drug_details' => 'required_if:drug_use,Yes',
            'exercise_frequency' => 'required|string',
            'mental_health_conditions' => 'required|in:Yes,No',
            'mental_health_details' => 'required_if:mental_health_conditions,Yes',
            'suicidal_thoughts' => 'required|in:Yes,No',
            'receiving_therapy' => 'required|in:Yes,No',
            'benefit_from_counseling' => 'required|in:Yes,No',
            'worried_about_counseling' => 'required|in:Yes,No',
            'family_chronic_pain' => 'required|in:Yes,No',
            'family_chronic_pain_details' => 'required_if:family_chronic_pain,Yes',
            'family_conditions' => 'array',
            'family_conditions_other' => 'required_if:family_conditions,Other',
            'additional_concerns' => 'nullable|string',
            'daily_activities_impact' => 'required|string',
            'sleep_impact' => 'required|string',
            'mobility_impact' => 'required|string',
            'emotional_impact' => 'required|string',
            'associated_symptoms' => 'array',
            'associated_symptoms_other' => 'required_if:associated_symptoms,Other',
            'systemic_symptoms' => 'required|in:Yes,No',
            'past_injuries' => 'required|in:Yes,No',
            'past_injuries_details' => 'required_if:past_injuries,Yes',
            'surgeries' => 'required|in:Yes,No',
            'surgery_details' => 'required_if:surgeries,Yes',
            'chronic_conditions_details' => 'nullable|string',
            'stress_levels' => 'required|integer|min:1|max:10',
            'support_system' => 'required|in:Yes,No',
            'work_environment' => 'required|in:Yes,No',
            'mental_health_changes' => 'required|string',
            'tenderness' => 'required|in:Yes,No',
            'difficulty_moving' => 'required|in:Yes,No',
            'reduced_activity' => 'required|in:Yes,No',
            'diagnostic_tests' => 'nullable|string',
            'physiotherapy_details' => 'nullable|string',
            'medication_screening' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $this->savePainTreatment($request);

        return to_route('landing-page6.register');
    }

    public function summary(Request $request, $serviceId)
    {
        if (!$user = $request->session()->get('customer')) {
            return to_route('landing-page6.index');
        }

        $service = Service::findOrFail($serviceId);
        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);
        $userService = UserService::where('user_id', $user->id)->first();

        info('Saving UserAnswer - start');
        $this->saveUserAnswers($userService, $userAnswers);
        info('Saving UserAnswer - end');

        $questions = Question::where('service_id', $serviceId)->orderBy('order')->get();

        return view('front.landing-pages.v6.summary', compact('service', 'questions', 'userAnswers'));
    }

    private function getPreviousQuestion($serviceId, $currentQuestion, $userAnswers)
    {
        $previousQuestions = Question::where('service_id', $serviceId)
            ->where('order', '<', $currentQuestion->order)
            ->orderBy('order', 'desc')
            ->get();

        foreach ($previousQuestions as $question) {
            if ($question->parent_question_id === null) {
                return $question;
            }

            $parentAnswer = $userAnswers[$question->parent_question_id] ?? null;
            if ($parentAnswer === $question->parent_answer_value) {
                return $question;
            }
        }

        return null;
    }

    private function getNextQuestion($serviceId, $currentQuestion, $userAnswers)
    {
        $nextQuestions = Question::where('service_id', $serviceId)
            ->where('order', '>', $currentQuestion->order)
            ->orderBy('order')
            ->get();

        foreach ($nextQuestions as $question) {
            if ($question->parent_question_id === null) {
                return $question;
            }

            $parentAnswer = $userAnswers[$question->parent_question_id] ?? null;
            if ($parentAnswer === $question->parent_answer_value) {
                return $question;
            }
        }

        return null;
    }

    private function saveUserAnswers($userService, $answers)
    {
        foreach ($answers as $questionId => $answer) {
            $userService->answers()->updateOrCreate(
                ['question_id' => $questionId],
                ['answer' => $answer]
            );
        }
    }

    protected function savePainTreatment(Request $request)
    {
        if (!$user = $request->session()->get('customer')) {
            return to_route('landing-page6.index');
        }

        // Save PainAssessment
        info('Saving PainAssessment - start');
        $painAssessment = new PainAssessment([
            'pain_type' => implode(', ', $request['pain_type']),
            'pain_type_other' => $request['pain_type_other'] ?? null,
            'pain_location' => implode(', ', $request['pain_location']),
            'pain_location_other' => $request['pain_location_other'] ?? null,
            'pain_intensity' => $request['pain_intensity'],
            'pain_duration' => $request['pain_duration'],
            'pain_start' => $request['pain_start'],
            'pain_frequency' => $request['pain_frequency'],
            'pain_triggers' => implode(', ', $request['pain_triggers']),
            'pain_triggers_other' => $request['pain_triggers_other'] ?? null,
            'pain_relief' => implode(', ', $request['pain_relief']),
            'pain_relief_other' => $request['pain_relief_other'] ?? null,
        ]);
        $user->painAssessment()->save($painAssessment);
        info('Saving PainAssessment - end');

        // Save MedicalCondition
        info('Saving MedicalCondition - start');
        foreach ($request['conditions'] as $condition) {
            MedicalCondition::create([
                'patient_id' => $user->id,
                'condition_name' => $condition,
            ]);
        }
        if ($request['conditions_other']) {
            MedicalCondition::create([
                'patient_id' => $user->id,
                'condition_name' => $request['conditions_other'],
            ]);
        }
        info('Saving MedicalCondition - end');

        if ($request['allergies'] == 'Yes') {
            info('Saving Allergy - start');
            Allergy::create([
                'user_id' =>  $user->id,
                'allergen' => $request['allergy_details'],
            ]);
            info('Saving Allergy - end');
        }

        info('Saving UserReportedMedication - start');
        $userReportedMedication = new UserReportedMedication([
            'medication_name' => $request['current_medications'] ?? "",
            'side_effects' => $request['current_side_effects'] == 'Yes' ? $request['side_effects_details'] : null,
        ]);
        $urm = $user->userReportedMedication()->save($userReportedMedication);
        info('Saving UserReportedMedication - end');

        if ($request['adverse_reactions'] === 'Yes') {
            info('Saving AdverseReaction - start');
            $adverseReaction = new AdverseReaction([
                'patient_id' => $user->id,
                'reaction_details' => $request['adverse_reactions_details'],
            ]);
            $urm->adverseReaction()->save($adverseReaction);
            info('Saving AdverseReaction - end');
        }

        // Save LifestyleHabit
        info('Saving LifestyleHabit - start');
        $lifestyleHabit = new LifestyleHabit([
            'alcohol_use' => $request['alcohol_use'] === 'Yes',
            'alcohol_details' => $request['alcohol_details'] ?? null,
            'tobacco_use' => $request['tobacco_use'] === 'Yes',
            'tobacco_details' => $request['tobacco_details'] ?? null,
            'drug_use' => $request['drug_use'] === 'Yes',
            'drug_details' => $request['drug_details'] ?? null,
            'exercise_frequency' => $request['exercise_frequency'],
        ]);
        $user->lifestyleHabit()->save($lifestyleHabit);
        info('Saving LifestyleHabit - end');

        // Save MentalHealthAssessment
        info('Saving MentalHealthAssessment - start');
        $mentalHealthAssessment = new MentalHealthAssessment([
            'mental_health_conditions' => $request['mental_health_conditions'] === 'Yes',
            'mental_health_details' => $request['mental_health_details'] ?? null,
            'suicidal_thoughts' => $request['suicidal_thoughts'] === 'Yes',
            'receiving_therapy' => $request['receiving_therapy'] === 'Yes',
            'benefit_from_counseling' => $request['benefit_from_counseling'] === 'Yes',
            'worried_about_counseling' => $request['worried_about_counseling'] === 'Yes',
        ]);
        $user->mentalHealthAssessment()->save($mentalHealthAssessment);
        info('Saving MentalHealthAssessment - end');

        // Save FamilyMedicalHistory
        info('Saving FamilyMedicalHistory - start');
        $familyMedicalHistory = new FamilyMedicalHistory([
            'chronic_pain' => $request['family_chronic_pain'] === 'Yes',
            'chronic_pain_details' => $request['family_chronic_pain_details'] ?? null,
        ]);
        $user->familyMedicalHistory()->save($familyMedicalHistory);
        info('Saving FamilyMedicalHistory - end');

        info('Saving FamilyMedicalCondition - start');
        foreach ($request['family_conditions'] as $condition) {
            if ($condition == 'Other') {
                $condition = $request['family_conditions_other'];
            }

            $user->familyMedicalHistory->familyMedicalConditions()->create([
                'name' => $condition,
            ]);
            info('Condition: ' . $condition);
        }
        info('Saving FamilyMedicalCondition - end');

        // Save AdditionalInformation
        if (in_array('Other', $request['associated_symptoms'])) {
            // Find the index of 'Other' in the array
            $otherIndex = array_search('Other', $request['associated_symptoms']);

            // Replace 'Other' with the combined string
            $request['associated_symptoms'][$otherIndex] = 'Other - ' . $request['associated_symptoms_other'];
        }

        info('Saving AdditionalInformation - start');
        $additionalInformation = new AdditionalInformation([
            'additional_concerns' => $request['additional_concerns'] ?? null,
            'daily_activities_impact' => $request['daily_activities_impact'],
            'sleep_impact' => $request['sleep_impact'],
            'mobility_impact' => $request['mobility_impact'],
            'emotional_impact' => $request['emotional_impact'],
            'associated_symptoms' => implode(', ', $request['associated_symptoms']),
            'systemic_symptoms' => $request['systemic_symptoms'] === 'Yes',
        ]);
        $user->additionalInformation()->save($additionalInformation);
        info('Saving AdditionalInformation - end');

        // Save MedicalSurgicalHistory
        info('Saving MedicalSurgicalHistory - start');
        $medicalSurgicalHistory = new MedicalSurgicalHistory([
            'past_injuries' => $request['past_injuries'] === 'Yes',
            'past_injuries_details' => $request['past_injuries_details'] ?? null,
            'surgery' => $request['surgeries'] === 'Yes',
            'surgery_details' => $request['surgery_details'] ?? null,
            'chronic_conditions_details' => $request['chronic_conditions_details'] ?? null,
        ]);
        $user->medicalSurgicalHistory()->save($medicalSurgicalHistory);
        info('Saving MedicalSurgicalHistory - end');

        // Save PsychologicalSocialFactor
        info('Saving PsychologicalSocialFactor - start');
        $psychologicalSocialFactor = new PsychologicalSocialFactor([
            'stress_levels' => $request['stress_levels'],
            'support_system' => $request['support_system'] === 'Yes',
            'work_environment' => $request['work_environment'] === 'Yes',
            'mental_health_changes' => $request['mental_health_changes'],
        ]);
        $user->psychologicalSocialFactors()->save($psychologicalSocialFactor);
        info('Saving PsychologicalSocialFactor - end');

        // Save PhysicalExaminationIndicator
        info('Saving PhysicalExaminationIndicator - start');
        $physicalExaminationIndicator = new PhysicalExaminationIndicator([
            'tenderness' => $request['tenderness'] === 'Yes',
            'difficulty_moving' => $request['difficulty_moving'] === 'Yes',
            'reduced_activity' => $request['reduced_activity'] === 'Yes',
        ]);
        $user->physicalExaminationIndicators()->save($physicalExaminationIndicator);
        info('Saving PhysicalExaminationIndicator - end');

        // Save DiagnosticTest
        if (!empty($request['diagnostic_tests'])) {
            info('Saving DiagnosticTest - start');
            $diagnosticTest = new DiagnosticTest([
                'details' => $request['diagnostic_tests'],
                'physiotherapy_details' => $request['physiotherapy_details'],
            ]);
            $user->diagnosticTests()->save($diagnosticTest);
            info('Saving DiagnosticTest - end');
        }

        // Save MedicationScreening
        info('Saving MedicationScreening - start');
        $medicationScreening = new MedicationScreening();
        foreach ($request['medication_screening'] as $key => $ms) {
            $medicationScreening->{$key} = $ms === 'Yes';
        }
        $user->medicationScreening()->save($medicationScreening);
        info('Saving MedicationScreening - end');

        // Clear the session data
        $request->session()->forget('pain_treatment_form');
    }
}
