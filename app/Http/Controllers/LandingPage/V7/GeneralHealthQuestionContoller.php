<?php

namespace App\Http\Controllers\LandingPage\V7;

use App\Http\Controllers\Controller;
use App\Models\Allergy;
use App\Models\HealthQuestion;
use App\Models\UserReportedMedication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use ProtoneMedia\Splade\Facades\Toast;

class GeneralHealthQuestionContoller extends Controller
{
    public function showForm()
    {
        return view('front.landing-pages.v7.general-health');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'has_chronic_conditions' => 'required|boolean',
            'chronic_conditions' => 'required_if:has_chronic_conditions,true|string|nullable',
            'uses_tobacco_alcohol_drugs' => 'required|boolean',
            'substance_use_frequency' => 'required_if:uses_tobacco_alcohol_drugs,true|string|nullable',
            'is_pregnant' => 'required|boolean',
            'had_recent_surgeries' => 'required|boolean',
            'recent_surgeries_details' => 'required_if:had_recent_surgeries,true|string|nullable',
            'has_health_concerns' => 'required|boolean',
            'health_concerns_details' => 'required_if:has_health_concerns,true|string|nullable',
            'has_current_medications' => 'required|boolean',
            'current_medications' => 'required_if:has_current_medications,true|array',
            'current_medications.*.name' => 'required_if:has_current_medications,true|string',
            'current_medications.*.dosage' => 'nullable|string',
            'current_medications.*.frequency' => 'nullable|string',
            'has_allergies' => 'required|boolean',
            'allergies' => 'required_if:has_allergies,true|string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            DB::transaction(function () use ($request) {
                if (!$user = $request->session()->get('customer')) {
                    return to_route('landing-page7.index');
                }

                // Handle user-reported medications
                if ($request->has_current_medications) {
                    foreach ($request->current_medications as $medication) {
                        UserReportedMedication::create([
                            'user_id' => $user->id,
                            'medication_name' => $medication['name'],
                            'dosage' => $medication['dosage'],
                            'frequency' => $medication['frequency']
                        ]);
                    }
                }

                $user->createPreferredMedications($request);

                // Handle allergies
                if ($request->has_allergies) {
                    $allergies = explode(',', $request->allergies);
                    foreach ($allergies as $allergen) {
                        Allergy::create([
                            'user_id' => $user->id,
                            'allergen' => trim($allergen),
                            'reaction' => 'Not specified'
                        ]);
                    }
                }

                // Handle other health information
                HealthQuestion::create([
                    'user_id' => $user->id,
                    'has_chronic_conditions' => $request->has_chronic_conditions,
                    'chronic_conditions' => $request->chronic_conditions,
                    'uses_tobacco_alcohol_drugs' => $request->uses_tobacco_alcohol_drugs,
                    'substance_use_frequency' => $request->substance_use_frequency,
                    'is_pregnant' => $request->is_pregnant,
                    'had_recent_surgeries' => $request->had_recent_surgeries,
                    'recent_surgeries_details' => $request->recent_surgeries_details,
                    'has_health_concerns' => $request->has_health_concerns,
                    'health_concerns_details' => $request->health_concerns_details,
                ]);
            });
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while saving the questionnaire data',
                'error' => $e->getMessage()
            ], 500);
        }

        if (in_array($request->service_id, [1, 2, 3, 4, 5])) {
            switch ($request->service_id) {
                case '1':
                    $questionId = 1;
                    break;
                case '2':
                    $questionId = 14;
                    break;
                case '3':
                    $questionId = 27;
                    break;
                case '4':
                    $questionId = 46;
                    break;
                case '5':
                    $questionId = 128;
                    break;
            }

            return to_route('landing-page7.questions.show', [$request->service_id, $questionId]);
        }

        if ($request->service_id == '6') { // PainTreatment
            return to_route('landing-page7.pain-treatment.create');
        }

        Toast::success('Medical questionnaire data saved successfully. Please complete registration to finalize.')->autoDismiss();

        return to_route('landing-page7.register', $request->service_id);
    }
}
