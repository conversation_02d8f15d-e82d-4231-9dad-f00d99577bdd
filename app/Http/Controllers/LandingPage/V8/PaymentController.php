<?php

namespace App\Http\Controllers\LandingPage\V8;

use App\Concerns\TracksOC;
use App\Http\Controllers\Controller;
use App\Http\Requests\LandingPage\PaymentRequest;
use App\Models\OfflineConversion;
use App\Models\Transaction;
use App\Models\UserService;
use App\Services\AuthorizeNetService;
use App\Services\CreditCardService;
use App\Services\MockAuthorizeNetService;
use App\Services\TrialPlanService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentController extends Controller
{
    use TracksOC;

    public function __construct(
        private AuthorizeNetService $authorizeNetService,
        private CreditCardService $creditCardService,
        private TrialPlanService $trialPlanService
    ) {}

    public function showPaymentForm()
    {
        return view('front.landing-pages.v8.payment');
    }

    public function pay(PaymentRequest $request)
    {
        Log::info('Payment process started');
        if (!$user = $request->session()->get('customer')) {
            return to_route('landing-page8.index');
        }

        // @TODO
        // fix this later
        // hard code the amount for now
        $metaData = ['plan' => $request->plan];
        if ($request->plan == 'monthly') {
            $amount = 29.99;
            $planId = 1;
            $durationMonths = 1;
            $metaData['first_month'] = 29.99;
            $metaData['amount'] = 59.99;
        } elseif ($request->plan == 'annual-one-time') {
            $amount = 299.99;
            $planId = 4;
            $durationMonths = 12;
            $metaData['amount'] = 299.99;
        } elseif ($request->plan == 'annual-three-payments') {
            $amount = 99.99;
            $planId = 4;
            $durationMonths = 12;
            $metaData['amount'] = 299.99;
            $metaData['first_pament'] = 99.99;
            $metaData['payments_made'] = 1;
            $metaData['next_payment'] = now()->addMonths(1);
        } else {
            Toast::danger('Invalid Plan')->autoDismiss();
            return to_route('landing-page8.payment');
        }
        Log::info('Amount to charge: ' . $amount);

        try {
            // Add credit card
            $creditCard = $this->creditCardService->addCreditCard(
                $user,
                $request->card_number,
                $request->expiration_month,
                $request->expiration_year,
                $request->cvv
            );

            if ($creditCard->wasRecentlyCreated) {
                Log::info('New credit card added for user: ' . $user->id);
                Toast::success('Credit card added successfully')->autoDismiss();
            } else {
                Log::info('Existing credit card used for user: ' . $user->id);
                Toast::info('This credit card is already on file')->autoDismiss();
            }

            // Proceed with charging the card
            Log::info('Attempting to charge credit card for user: ' . $user->id);
            $paymentResult = $this->authorizeNetService->chargeCreditCard($amount, $user);
            Log::info('Charge result: ' . json_encode($paymentResult));

            if ($paymentResult['success']) {
                Log::info('Payment successful, creating subscription');
                $subscription = $user->subscriptions()->create([
                    'plan_id' => $planId,
                    'starts_at' => now(),
                    'ends_at' => now()->addMonths($durationMonths),
                    'status' => 'active',
                    'is_discounted' => true,
                    'discounted_price' => $amount,
                    'meta_data' => $metaData,
                ]);
                $paymentSuccess = [
                    'form-name' => 'lp8-payment',
                    'conversion-value' => 5,
                    'page-path' => 'https://gomdusa.com/landing-page8/payment',
                    'page-name' => 'LP8',
                    'transaction-id' => $paymentResult['transaction_id'],
                ];
                $request->session()->put('payment-success', $paymentSuccess);

                // Set UserService status to paid
                $userService = UserService::where('user_id', $user->id)->first();
                $userService->status = 'paid';
                $userService->save();

                // Update the transaction record with the subscription ID
                Transaction::where('transaction_id', $paymentResult['transaction_id'])
                    ->update([
                        'subscription_id' => $subscription->id,
                        'is_discounted' => true,
                    ]);

                // $conversionData = [
                //     'gclid' => $request->cookie('gclid') ?? null,
                //     'conversion_name' => 'lp8-purchase',
                //     'conversion_value' => $amount,
                //     'plan_id' => $planId,
                //     'transaction_id' => $paymentResult['transaction_id'],
                //     'user_id' => $user->id
                // ];
                // OfflineConversion::create($conversionData);
                $this->recordPurchase(
                    value: $amount,
                    planId: $planId
                );

                Toast::info('Payment successful.')->autoDismiss();
                Log::info('Payment process completed successfully for user: ' . $user->id);
                return to_route('landing-page8.record-video.show');
            } else {
                Log::error('Payment failed for user: ' . $user->id . '. Error: ' . $paymentResult['error']);
                Toast::danger('Payment failed: ' . $paymentResult['error'])->autoDismiss();
                return back()->with('error', 'Payment failed: ' . $paymentResult['error']);
            }
        } catch (\Exception $e) {
            Log::error('Exception in payment process: ' . $e->getMessage());
            Toast::danger('An error occurred during payment processing. Please try again or contact support.')->autoDismiss();
            return back()->with('error', 'An error occurred during payment processing. Please try again or contact support.');
        }
    }
}
