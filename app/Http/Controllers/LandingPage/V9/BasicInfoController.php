<?php

namespace App\Http\Controllers\LandingPage\V9;

use App\Concerns\TracksOC;
use App\Http\Controllers\Controller;
use App\Http\Requests\LandingPage\BasicInfoRequest;
use App\Http\Requests\LandingPage\GetStartedRequest;
use App\Models\Service;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules;
use ProtoneMedia\Splade\Facades\Toast;

class BasicInfoController extends Controller
{
    use TracksOC;

    public function showEmailPhoneForm(Request $request, $serviceId)
    {
        $request->session()->put('service', $serviceId);
        $services = Service::select('id', 'name')
            ->orderByRaw("CASE name 
                WHEN 'Pain Treatment' THEN 1
                WHEN 'Anxiety' THEN 2
                WHEN 'Weight Loss' THEN 3
                WHEN 'Erectile Dysfunction' THEN 4
                WHEN 'Skin Care' THEN 5
                WHEN 'Hair Loss' THEN 6
                ELSE 7 END")
            ->get();
        return view('front.landing-pages.v9.email-phone-form', compact('services', 'serviceId'));
    }

    public function storeEmailPhone(GetStartedRequest $request)
    {
        $this->recordLeadGeneration(formData: $request->except(['_token']));

        $user = User::create([
            'name' => '',
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => bcrypt($request->password)
        ]);
        $user->userServices()->create(['service_id' => $request->service_id]);
        $request->session()->put('customer', $user);
        return to_route('landing-page9.questions.general.form');
    }

    public function showForm(Request $request, $serviceId)
    {
        $request->session()->put('service', $serviceId);
        $customer = $request->session()->has('customer') ? $request->session()->get('customer') : null;
        return view('front.landing-pages.v9.basic-info', compact('customer'));
    }

    public function store(BasicInfoRequest $request)
    {
        $user = User::create([
            'name' => $request->fname . ' ' . $request->lname,
            'email' => $request->email,
            'password' => bcrypt($request->phone),
            'fname' => $request->fname,
            'lname' => $request->lname,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip,
            'phone' => $request->phone,
            'mobile_phone' => $request->mobile_phone,
        ]);
        $request->session()->put('customer', $user);

        return to_route('landing-page9.questions.general.form');
    }

    public function update(BasicInfoRequest $request, $user)
    {
        $user = User::create([
            'name' => $request->fname . ' ' . $request->lname,
            'email' => $request->email,
            'password' => bcrypt($request->phone),
            'fname' => $request->fname,
            'lname' => $request->lname,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip,
            'phone' => $request->phone,
            'mobile_phone' => $request->mobile_phone,
        ]);
        $request->session()->put('customer', $user);

        return to_route('landing-page9.questions.general.form');
    }

    public function showRegistrationForm(Request $request)
    {
        if (!$user = $request->session()->get('customer')) {
            return to_route('landing-page9.index');
        }
        return view('front.landing-pages.v9.register', compact('user'));
    }
    public function register(Request $request)
    {
        $request->validate([
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'gender' => ['required', 'string', 'in:M,F,O'],
            'dob' => ['required', 'date'],
            'address1' => ['required', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'bio' => ['nullable', 'string'],
            'image' => ['nullable', 'image', 'max:2048'], // 2MB Max
            'phone' => ['required', 'string', 'max:20'],
            'mobile_phone' => ['nullable', 'string', 'max:20'],
        ]);
        if (!$user = User::find($request->id)) {
            return to_route('landing-page9.index');
        }

        DB::beginTransaction();
        try {
            $user->update([
                'name' => $request->fname . ' ' . $request->lname,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'fname' => $request->fname,
                'lname' => $request->lname,
                'gender' => $request->gender,
                'dob' => $request->dob,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'bio' => $request->bio,
                'phone' => $request->phone,
                'mobile_phone' => $request->mobile_phone,
            ]);

            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('profile_images', 'public');
                $user->image = $imagePath;
                $user->save();
            }

            $user->assignRole('patient');

            DB::commit();

            event(new Registered($user));

            Auth::login($user);

            Log::info('Registration process completed successfully for user: ' . $user->id);
            return to_route('landing-page9.payment');
        } catch (\Exception $e) {
            report($e);
            DB::rollback();
            Toast::info('Registration failed. Please try again.')->autoDismiss(10);
            return back();
        }
    }
}
