<?php

namespace App\Http\Controllers;

use App\Http\Requests\CaptureVisitorRequest;
use App\Models\AdditionalInformation;
use App\Models\AdverseReaction;
use App\Models\Allergy;
use App\Models\DiagnosticTest;
use App\Models\Discount;
use App\Models\FamilyMedicalHistory;
use App\Models\HealthQuestion;
use App\Models\LifestyleHabit;
use App\Models\MedicalCondition;
use App\Models\MedicalSurgicalHistory;
use App\Models\Medication;
use App\Models\MedicationHistory;
use App\Models\MedicationScreening;
use App\Models\MentalHealthAssessment;
use App\Models\PainAssessment;
use App\Models\PhysicalExaminationIndicator;
use App\Models\PreferredMedication;
use App\Models\PsychologicalSocialFactor;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Models\UserReportedMedication;
use App\Models\UserService;
use App\Models\Visitor;
use Faker\Factory as Faker;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules;
use ProtoneMedia\Splade\Facades\Toast;

class LandingPageController extends Controller
{
    public function index(Request $request)
    {
        $request->session()->put('source', 'landing-page');
        $request->session()->put('landing-page', true);
        return view('front.landing-page');
    }

    public function landingPage2(Request $request)
    {
        $request->session()->put('source', 'landing-page2');
        $request->session()->put('landing-page', true);
        return view('front.landing-page2');
    }

    public function getBasicInfo($serviceId)
    {
        return view('front.basic-info', [
            'serviceId' => $serviceId
        ]);
    }

    public function storeBasicInfo(CaptureVisitorRequest $request, $serviceId)
    {
        if (!is_null($request->favorite_color)) {
            info('bot-detected', $request->all());
            info('bot-ip' . $request->ip());
        }
        $visitor = Visitor::updateOrCreate(
            ['email' => $request->input('email')],
            $request->validated()
        );
        $visitor->update(['ip' => $request->ip()]);
        $request->session()->put('visitor', $visitor);

        Toast::success('Thank you.')->autoDismiss(3);

        return to_route('landing-page.create', ['serviceId' => $serviceId]);
    }

    public function create($serviceId)
    {
        return view('front.medical-questions', [
            'serviceId' => $serviceId
        ]);
    }

    public function store(Request $request, $serviceId)
    {
        $validator = Validator::make($request->all(), [
            'has_chronic_conditions' => 'required|boolean',
            'chronic_conditions' => 'required_if:has_chronic_conditions,true|string|nullable',
            'uses_tobacco_alcohol_drugs' => 'required|boolean',
            'substance_use_frequency' => 'required_if:uses_tobacco_alcohol_drugs,true|string|nullable',
            'is_pregnant' => 'required|boolean',
            'had_recent_surgeries' => 'required|boolean',
            'recent_surgeries_details' => 'required_if:had_recent_surgeries,true|string|nullable',
            'has_health_concerns' => 'required|boolean',
            'health_concerns_details' => 'required_if:has_health_concerns,true|string|nullable',
            'has_current_medications' => 'required|boolean',
            'current_medications' => 'required_if:has_current_medications,true|array',
            'current_medications.*.name' => 'required_if:has_current_medications,true|string',
            'current_medications.*.dosage' => 'nullable|string',
            'current_medications.*.frequency' => 'nullable|string',
            'has_allergies' => 'required|boolean',
            'allergies' => 'required_if:has_allergies,true|string|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Store validated data in session
        $request->session()->put('medical_questionnaire', $request->all());

        Toast::success('Medical questionnaire data saved successfully. Please complete registration to finalize.')->autoDismiss();

        if (in_array($serviceId, [1, 2, 3, 4, 5])) {
            switch ($serviceId) {
                case '1':
                    $questionId = 1;
                    break;
                case '2':
                    $questionId = 14;
                    break;
                case '3':
                    $questionId = 27;
                    break;
                case '4':
                    $questionId = 46;
                    break;
                case '5':
                    $questionId = 128;
                    break;
            }

            return to_route('questions.show', [$serviceId, $questionId]);
        }

        if ($serviceId == '6') { // PainTreatment
            return to_route('services.pain-treatment.create');
        }

        return to_route('landing-page.register.form', $serviceId);
        // return response()->json([
        //     'message' => 'Medical questionnaire data saved successfully. Please complete registration to finalize.',
        // ], 200);
    }

    public function saveQuestionnaireData()
    {
        $user = auth()->user();
        $questionnaireData = session('medical_questionnaire');
        Log::info('Questionnaire Data: ' . auth()->id(), $questionnaireData);

        if (!$user || !$questionnaireData) {
            return response()->json([
                'message' => 'User not logged in or questionnaire data not found',
            ], 400);
        }

        try {
            DB::transaction(function () use ($user, $questionnaireData) {
                // Handle user-reported medications
                if ($questionnaireData['has_current_medications']) {
                    foreach ($questionnaireData['current_medications'] as $medication) {
                        UserReportedMedication::create([
                            'user_id' => $user->id,
                            'medication_name' => $medication['name'],
                            'dosage' => $medication['dosage'],
                            'frequency' => $medication['frequency']
                        ]);
                    }
                }

                $user->createPreferredMedications($questionnaireData);

                // Handle allergies
                if ($questionnaireData['has_allergies']) {
                    $allergies = explode(',', $questionnaireData['allergies']);
                    foreach ($allergies as $allergen) {
                        Allergy::create([
                            'user_id' => $user->id,
                            'allergen' => trim($allergen),
                            'reaction' => 'Not specified'
                        ]);
                    }
                }

                // Handle other health information
                HealthQuestion::create([
                    'user_id' => $user->id,
                    'has_chronic_conditions' => $questionnaireData['has_chronic_conditions'],
                    'chronic_conditions' => $questionnaireData['chronic_conditions'],
                    'uses_tobacco_alcohol_drugs' => $questionnaireData['uses_tobacco_alcohol_drugs'],
                    'substance_use_frequency' => $questionnaireData['substance_use_frequency'],
                    'is_pregnant' => $questionnaireData['is_pregnant'],
                    'had_recent_surgeries' => $questionnaireData['had_recent_surgeries'],
                    'recent_surgeries_details' => $questionnaireData['recent_surgeries_details'],
                    'has_health_concerns' => $questionnaireData['has_health_concerns'],
                    'health_concerns_details' => $questionnaireData['health_concerns_details'],
                ]);
            });

            // Clear the session data after successful save
            session()->forget('medical_questionnaire');

            return response()->json([
                'message' => 'Medical questionnaire data saved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while saving the questionnaire data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getRegistrationForm($serviceId)
    {
        $subscriptionPlans = SubscriptionPlan::where('status', 'active')->get();

        // Update statuses of all discounts
        Discount::all()->each->updateStatus();

        // Fetch active discounts
        $activeDiscounts = Discount::active()->get();

        // Apply discounts to subscription plans
        $subscriptionPlans = $subscriptionPlans->map(function ($plan) use ($activeDiscounts) {
            // Check if there's an override discount in the session (ExtraHelp)
            if (session()->has('override_discount') && session()->has('applied_discount_id')) {
                $discountId = session()->get('applied_discount_id');
                $overrideDiscount = Discount::find($discountId);

                if ($overrideDiscount &&
                    $overrideDiscount->status === 'active' &&
                    $overrideDiscount->start_date <= now() &&
                    $overrideDiscount->end_date >= now() &&
                    $overrideDiscount->appliesToPlan($plan->id)) {
                    $appliedDiscount = $overrideDiscount;
                }
            } else {
                // Otherwise, find the highest priority discount that applies to this plan
                $appliedDiscount = $activeDiscounts
                    ->where(function ($discount) use ($plan) {
                        return $discount->appliesToPlan($plan->id);
                    })
                    ->sortByDesc('priority')
                    ->first();
            }

            if ($appliedDiscount) {
                $plan->original_price = $plan->price;
                $plan->discounted_price = $appliedDiscount->applyDiscount($plan->price);
                $plan->discount_amount = $appliedDiscount->getDiscountedAmount($plan->price);
                $plan->discount_percentage = $appliedDiscount->getDiscountPercentage($plan->price);
                $plan->discount_type = $appliedDiscount->type;
                $plan->discount_value = $appliedDiscount->value;
            }

            return $plan;
        });

        $fakeData = ['plan' => '1'];
        if (app()->environment('local', 'testing')) {
            // Generate fake data
            $faker = Faker::create();
            // Generate fake data for the form fields
            $fakeData = [
                'fname' => $faker->firstName,
                'lname' => $faker->lastName,
                'email' => $faker->safeEmail,
                'password' => 'password',
                'password_confirmation' => 'password',
                'gender' => $faker->randomElement(['M', 'F', 'O']),
                'dob' => $faker->date('Y-m-d', '-18 years'),
                'address1' => $faker->streetAddress,
                'address2' => $faker->secondaryAddress,
                'city' => $faker->city,
                'state' => $faker->stateAbbr,
                'zip' => $faker->postcode,
                'bio' => $faker->paragraph,
                'phone' => $faker->phoneNumber,
                'mobile_phone' => $faker->phoneNumber,
                'plan' => '1',
            ];
        }

        if (request()->session()->has('visitor')) {
            $visitor = request()->session()->get('visitor');
            $fakeData['fname'] = $visitor->fname;
            $fakeData['lname'] = $visitor->lname;
            $fakeData['email'] = $visitor->email;
            $fakeData['address1'] = $visitor->address;
            $fakeData['city'] = $visitor->city;
            $fakeData['state'] = $visitor->state;
            $fakeData['zip'] = $visitor->zip;
            $fakeData['phone'] = $visitor->phone;
        }

        return view('front.register', compact('serviceId', 'subscriptionPlans', 'fakeData'));
    }

    public function submitRegistration(Request $request, $serviceId)
    {
        $request->validate([
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'gender' => ['required', 'string', 'in:M,F,O'],
            'dob' => ['required', 'date'],
            'address1' => ['required', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'bio' => ['nullable', 'string'],
            'image' => ['nullable', 'image', 'max:2048'], // 2MB Max
            'phone' => ['required', 'string', 'max:20'],
            'mobile_phone' => ['nullable', 'string', 'max:20'],
            'plan' => ['required', 'exists:subscription_plans,id'],
        ]);

        DB::beginTransaction();

        try {
            $user = User::create([
                'name' => $request->fname . ' ' . $request->lname,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'fname' => $request->fname,
                'lname' => $request->lname,
                'gender' => $request->gender,
                'dob' => $request->dob,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'bio' => $request->bio,
                'phone' => $request->phone,
                'mobile_phone' => $request->mobile_phone,
            ]);

            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('profile_images', 'public');
                $user->image = $imagePath;
                $user->save();
            }

            $user->assignRole('patient');

            $sessionKey = "service_{$serviceId}_answers";
            $userAnswers = $request->session()->get($sessionKey, []);

            $userService = UserService::firstOrCreate(
                ['user_id' => $user->id, 'service_id' => $serviceId],
                ['status' => 'in_progress']
            );

            $planId = $request->plan;
            $plan = SubscriptionPlan::findOrFail($planId);

            // Fetch active discounts and find the applicable one
            $activeDiscounts = Discount::active()->get();

            // Check if there's an override discount in the session (ExtraHelp)
            if ($request->session()->has('override_discount') && $request->session()->has('applied_discount_id')) {
                $discountId = $request->session()->get('applied_discount_id');
                $overrideDiscount = Discount::find($discountId);

                if ($overrideDiscount &&
                    $overrideDiscount->status === 'active' &&
                    $overrideDiscount->start_date <= now() &&
                    $overrideDiscount->end_date >= now() &&
                    $overrideDiscount->appliesToPlan($plan->id)) {
                    $appliedDiscount = $overrideDiscount;
                }
            } else {
                // Otherwise, find the highest priority discount that applies to this plan
                $appliedDiscount = $activeDiscounts
                    ->where(function ($discount) use ($plan) {
                        return $discount->appliesToPlan($plan->id);
                    })
                    ->sortByDesc('priority')
                    ->first();
            }

            // Calculate the discounted price if a discount is applied
            $originalPrice = $plan->price;
            $discountedPrice = $originalPrice;
            $discountAmount = 0;

            if ($appliedDiscount) {
                $discountedPrice = $appliedDiscount->applyDiscount($originalPrice);
                $discountAmount = $appliedDiscount->getDiscountedAmount($originalPrice);
            }

            // Store pricing information in the session
            $request->session()->put('selected_plan_id', $planId);
            $request->session()->put('original_price', $originalPrice);
            $request->session()->put('discounted_price', $discountedPrice);
            $request->session()->put('discount_amount', $discountAmount);
            if ($appliedDiscount) {
                $request->session()->put('discount_id', $appliedDiscount->id);
            }

            $request->session()->put('selected_plan_id', $request->plan);

            DB::commit();

            event(new Registered($user));

            Auth::login($user);

            // Save the answers to the database
            if ($userAnswers) {
                info('Saving UserAnswer - start');
                $this->saveUserAnswers($userService, $userAnswers);
                info('Saving UserAnswer - end');
            }

            // Save medical questions
            if ($request->session()->has('medical_questionnaire')) {
                $this->saveQuestionnaireData();
            }

            if ($request->session()->has('pain_treatment_form')) {
                $this->savePainTreatment($request);
            }

            if ($request->session()->has('visitor')) {
                $visitor = $request->session()->get('visitor');
                $visitor->delete();
                $request->session()->forget('visitor');
            }

            return redirect()->route('patient.payment');
        } catch (\Exception $e) {
            report($e);
            DB::rollback();
            Toast::info('Registration failed. Please try again.')->autoDismiss(10);
            return back();
        }
    }

    private function saveUserAnswers($userService, $answers)
    {
        foreach ($answers as $questionId => $answer) {
            $userService->answers()->updateOrCreate(
                ['question_id' => $questionId],
                ['answer' => $answer]
            );
        }
    }

    protected function savePainTreatment(Request $request)
    {
        $painTreatmentData = $request->session()->get('pain_treatment_form');

        if (!$painTreatmentData) {
            return;
        }

        $user = $request->user();

        // Save PainAssessment
        $painAssessment = new PainAssessment([
            'pain_type' => implode(', ', $painTreatmentData['pain_type']),
            'pain_type_other' => $painTreatmentData['pain_type_other'] ?? null,
            'pain_location' => implode(', ', $painTreatmentData['pain_location']),
            'pain_location_other' => $painTreatmentData['pain_location_other'] ?? null,
            'pain_intensity' => $painTreatmentData['pain_intensity'],
            'pain_duration' => $painTreatmentData['pain_duration'],
            'pain_start' => $painTreatmentData['pain_start'],
            'pain_frequency' => $painTreatmentData['pain_frequency'],
            'pain_triggers' => implode(', ', $painTreatmentData['pain_triggers']),
            'pain_triggers_other' => $painTreatmentData['pain_triggers_other'] ?? null,
            'pain_relief' => implode(', ', $painTreatmentData['pain_relief']),
            'pain_relief_other' => $painTreatmentData['pain_relief_other'] ?? null,
        ]);
        $user->painAssessment()->save($painAssessment);

        // Save MedicalCondition
        foreach ($painTreatmentData['conditions'] as $condition) {
            MedicalCondition::create([
                'patient_id' => auth()->id(),
                'condition_name' => $condition,
            ]);
        }
        if ($painTreatmentData['conditions_other']) {
            MedicalCondition::create([
                'patient_id' => auth()->id(),
                'condition_name' => $painTreatmentData['conditions_other'],
            ]);
        }
        if ($painTreatmentData['allergies'] == 'Yes') {
            Allergy::create([
                'user_id' =>  auth()->id(),
                'allergen' => $painTreatmentData['allergy_details'],
            ]);
        }

        $userReportedMedication = new UserReportedMedication([
            'medication_name' => $painTreatmentData['current_medications'] ?? "",
            'side_effects' => $painTreatmentData['current_side_effects'] == 'Yes' ? $painTreatmentData['side_effects_details'] : null,
        ]);

        $urm = $user->userReportedMedication()->save($userReportedMedication);

        if ($painTreatmentData['adverse_reactions'] === 'Yes') {
            $adverseReaction = new AdverseReaction([
                'patient_id' => auth()->id(),
                'reaction_details' => $painTreatmentData['adverse_reactions_details'],
            ]);
            $urm->adverseReaction()->save($adverseReaction);
        }

        // Save LifestyleHabit
        $lifestyleHabit = new LifestyleHabit([
            'alcohol_use' => $painTreatmentData['alcohol_use'] === 'Yes',
            'alcohol_details' => $painTreatmentData['alcohol_details'] ?? null,
            'tobacco_use' => $painTreatmentData['tobacco_use'] === 'Yes',
            'tobacco_details' => $painTreatmentData['tobacco_details'] ?? null,
            'drug_use' => $painTreatmentData['drug_use'] === 'Yes',
            'drug_details' => $painTreatmentData['drug_details'] ?? null,
            'exercise_frequency' => $painTreatmentData['exercise_frequency'],
        ]);
        $user->lifestyleHabit()->save($lifestyleHabit);

        // Save MentalHealthAssessment
        $mentalHealthAssessment = new MentalHealthAssessment([
            'mental_health_conditions' => $painTreatmentData['mental_health_conditions'] === 'Yes',
            'mental_health_details' => $painTreatmentData['mental_health_details'] ?? null,
            'suicidal_thoughts' => $painTreatmentData['suicidal_thoughts'] === 'Yes',
            'receiving_therapy' => $painTreatmentData['receiving_therapy'] === 'Yes',
            'benefit_from_counseling' => $painTreatmentData['benefit_from_counseling'] === 'Yes',
            'worried_about_counseling' => $painTreatmentData['worried_about_counseling'] === 'Yes',
        ]);
        $user->mentalHealthAssessment()->save($mentalHealthAssessment);

        // Save FamilyMedicalHistory
        $familyMedicalHistory = new FamilyMedicalHistory([
            'chronic_pain' => $painTreatmentData['family_chronic_pain'] === 'Yes',
            'chronic_pain_details' => $painTreatmentData['family_chronic_pain_details'] ?? null,
        ]);
        $user->familyMedicalHistory()->save($familyMedicalHistory);

        foreach ($painTreatmentData['family_conditions'] as $condition) {
            if ($condition == 'Other') {
                $condition = $painTreatmentData['family_conditions_other'];
            }

            $user->familyMedicalHistory->familyMedicalConditions()->create([
                'name' => $condition,
            ]);
        }

        // Save AdditionalInformation
        if (in_array('Other', $painTreatmentData['associated_symptoms'])) {
            // Find the index of 'Other' in the array
            $otherIndex = array_search('Other', $painTreatmentData['associated_symptoms']);

            // Replace 'Other' with the combined string
            $painTreatmentData['associated_symptoms'][$otherIndex] = 'Other - ' . $painTreatmentData['associated_symptoms_other'];
        }

        $additionalInformation = new AdditionalInformation([
            'additional_concerns' => $painTreatmentData['additional_concerns'] ?? null,
            'daily_activities_impact' => $painTreatmentData['daily_activities_impact'],
            'sleep_impact' => $painTreatmentData['sleep_impact'],
            'mobility_impact' => $painTreatmentData['mobility_impact'],
            'emotional_impact' => $painTreatmentData['emotional_impact'],
            'associated_symptoms' => implode(', ', $painTreatmentData['associated_symptoms']),
            'systemic_symptoms' => $painTreatmentData['systemic_symptoms'] === 'Yes',
        ]);
        $user->additionalInformation()->save($additionalInformation);

        // Save MedicalSurgicalHistory
        $medicalSurgicalHistory = new MedicalSurgicalHistory([
            'past_injuries' => $painTreatmentData['past_injuries'] === 'Yes',
            'past_injuries_details' => $painTreatmentData['past_injuries_details'] ?? null,
            'surgery' => $painTreatmentData['surgeries'] === 'Yes',
            'surgery_details' => $painTreatmentData['surgery_details'] ?? null,
            'chronic_conditions_details' => $painTreatmentData['chronic_conditions_details'] ?? null,
        ]);
        $user->medicalSurgicalHistory()->save($medicalSurgicalHistory);

        // Save PsychologicalSocialFactor
        $psychologicalSocialFactor = new PsychologicalSocialFactor([
            'stress_levels' => $painTreatmentData['stress_levels'],
            'support_system' => $painTreatmentData['support_system'] === 'Yes',
            'work_environment' => $painTreatmentData['work_environment'] === 'Yes',
            'mental_health_changes' => $painTreatmentData['mental_health_changes'],
        ]);
        $user->psychologicalSocialFactors()->save($psychologicalSocialFactor);

        // Save PhysicalExaminationIndicator
        $physicalExaminationIndicator = new PhysicalExaminationIndicator([
            'tenderness' => $painTreatmentData['tenderness'] === 'Yes',
            'difficulty_moving' => $painTreatmentData['difficulty_moving'] === 'Yes',
            'reduced_activity' => $painTreatmentData['reduced_activity'] === 'Yes',
        ]);
        $user->physicalExaminationIndicators()->save($physicalExaminationIndicator);

        // Save DiagnosticTest
        if (!empty($painTreatmentData['diagnostic_tests'])) {
            $diagnosticTest = new DiagnosticTest([
                'details' => $painTreatmentData['diagnostic_tests'],
                'physiotherapy_details' => $painTreatmentData['physiotherapy_details'],
            ]);
            $user->diagnosticTests()->save($diagnosticTest);
        }

        // Save MedicationScreening
        $medicationScreening = new MedicationScreening();
        foreach ($painTreatmentData['medication_screening'] as $key => $ms) {
            $medicationScreening->{$key} = $ms === 'Yes';
        }
        $user->medicationScreening()->save($medicationScreening);

        // Clear the session data
        $request->session()->forget('pain_treatment_form');
    }
}
