<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class MedicalQuestionnaireCompletionController extends Controller
{
    /**
     * Temporarily dismiss the medical questionnaire completion prompt.
     */
    public function dismiss(Request $request)
    {
        $user = $request->user();

        if ($user) {
            $user->temporarilyDismissMedicalQuestionnaire();
            Toast::info('Medical questionnaire reminder dismissed for 24 hours.')->autoDismiss(3);
        }

        // Redirect back to the dashboard based on user role
        if ($user->hasRole('patient')) {
            return redirect()->route('patient.dashboard');
        } elseif ($user->hasRole('employee')) {
            return redirect()->route('employee.dashboard');
        }

        // Fallback redirect
        return redirect()->back();
    }

    /**
     * Get the medical questionnaire completion status for the current user.
     */
    public function status(Request $request)
    {
        $user = $request->user();

        if (!$user || !$user->hasAnyRole(['patient', 'employee'])) {
            return response()->json([
                'has_questionnaire' => true,
                'temporarily_dismissed' => false
            ]);
        }

        return response()->json([
            'has_questionnaire' => $user->hasMedicalQuestionnaire(),
            'temporarily_dismissed' => $user->hasTemporarilyDismissedMedicalQuestionnaire()
        ]);
    }
}
