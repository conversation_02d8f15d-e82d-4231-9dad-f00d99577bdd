<?php

namespace App\Http\Controllers;

use App\Models\MedicalRecord;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Gate;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicalRecordController extends Controller
{
    public function index(User $patient)
    {
        if (Gate::denies('viewAny', [MedicalRecord::class, $patient])) {
            abort(403, 'You are not authorized to view medical records for this patient.');
        }

        return view('medical-records.index', [
            'patient' => $patient,
            'medicalRecords' => SpladeTable::for($patient->medicalRecords())
                ->column('record_type')
                ->column('record_date', sortable: true)
                ->column('doctor', label: 'Created By')
                ->column('actions')
                ->paginate(10),
        ]);
    }

    public function create(User $patient)
    {
        if (Gate::denies('create', [MedicalRecord::class, $patient])) {
            abort(403, 'You are not authorized to create medical records for this patient.');
        }

        return view('medical-records.create', compact('patient'));
    }

    public function store(Request $request, User $patient)
    {
        if (Gate::denies('create', [MedicalRecord::class, $patient])) {
            abort(403, 'You are not authorized to create medical records for this patient.');
        }

        $validated = $request->validate([
            'record_type' => 'required|string|max:255',
            'description' => 'required|string',
            'record_date' => 'required|date',
            'file' => 'nullable|file|max:10240', // 10MB max
        ]);

        $medicalRecord = new MedicalRecord($validated);
        $medicalRecord->patient_id = $patient->id;
        $medicalRecord->doctor_id = auth()->id();

        if ($request->hasFile('file')) {
            $path = $request->file('file')->store('medical_records');
            $medicalRecord->file_path = $path;
        }

        $medicalRecord->save();

        Toast::success('Medical record created successfully.');

        return redirect()->route('medical-records.index', $patient);
    }

    public function show(User $patient, MedicalRecord $medicalRecord)
    {
        if (Gate::denies('view', [$medicalRecord, $patient])) {
            abort(403, 'You are not authorized to view this medical record.');
        }

        return view('medical-records.show', compact('patient', 'medicalRecord'));
    }

    public function edit(User $patient, MedicalRecord $medicalRecord)
    {
        if (Gate::denies('update', [$medicalRecord, $patient])) {
            abort(403, 'You are not authorized to edit this medical record.');
        }

        return view('medical-records.edit', compact('patient', 'medicalRecord'));
    }

    public function update(Request $request, User $patient, MedicalRecord $medicalRecord)
    {
        if (Gate::denies('update', [$medicalRecord, $patient])) {
            abort(403, 'You are not authorized to update this medical record.');
        }

        $validated = $request->validate([
            'record_type' => 'required|string|max:255',
            'description' => 'required|string',
            'record_date' => 'required|date',
            'file' => 'nullable|file|max:10240', // 10MB max
        ]);

        $medicalRecord->update($validated);

        if ($request->hasFile('file')) {
            if ($medicalRecord->file_path) {
                Storage::delete($medicalRecord->file_path);
            }
            $path = $request->file('file')->store('medical_records');
            $medicalRecord->file_path = $path;
            $medicalRecord->save();
        }

        Toast::success('Medical record updated successfully.');

        return redirect()->route('medical-records.show', [$patient, $medicalRecord]);
    }

    public function destroy(User $patient, MedicalRecord $medicalRecord)
    {
        if (Gate::denies('delete', [$medicalRecord, $patient])) {
            abort(403, 'You are not authorized to delete this medical record.');
        }

        if ($medicalRecord->file_path) {
            Storage::delete($medicalRecord->file_path);
        }

        $medicalRecord->delete();

        Toast::success('Medical record deleted successfully.');

        return redirect()->route('medical-records.index', $patient);
    }

    public function downloadFile(User $patient, MedicalRecord $medicalRecord)
    {
        if (Gate::denies('downloadFile', [$medicalRecord, $patient])) {
            abort(403, 'You are not authorized to download this file.');
        }

        if (!$medicalRecord->file_path || !Storage::exists($medicalRecord->file_path)) {
            abort(404, 'File not found.');
        }

        return Storage::download($medicalRecord->file_path);
    }
}