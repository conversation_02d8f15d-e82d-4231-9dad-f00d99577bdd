<?php

namespace App\Http\Controllers;

use App\Models\Medication;
use App\Models\MedicationBase;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class MedicationsController extends Controller
{
    public function index()
    {
        return view('medications.index', [
            'medications' => SpladeTable::for(Medication::class)
                ->column('name', sortable: true, searchable: true)
                ->column('generic_name', sortable: true, searchable: true)
                ->column('description')
                ->column('dosage_form')
                ->column('strength')
                ->column('storage_conditions')
                ->column('actions')
                ->paginate(10),
        ]);
    }

    public function create()
    {
        return view('medications.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'generic_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'dosage_form' => 'required|string|max:255',
            'strength' => 'required|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'ndc_number' => 'nullable|string|max:255',
            'unit_price' => 'required|numeric|min:0',
            'requires_prescription' => 'boolean',
            'controlled_substance' => 'boolean',
            'storage_conditions' => 'nullable|string|max:255',
        ]);

        Medication::create($validated);

        Toast::success('Medication created successfully.');

        return redirect()->route('medications.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Medication $medication)
    {
        return view('medications.edit', compact('medication'));
    }

    public function update(Request $request, Medication $medication)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'generic_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'dosage_form' => 'required|string|max:255',
            'strength' => 'required|string|max:255',
            'manufacturer' => 'nullable|string|max:255',
            'ndc_number' => 'nullable|string|max:255',
            'unit_price' => 'required|numeric|min:0',
            'requires_prescription' => 'boolean',
            'controlled_substance' => 'boolean',
            'storage_conditions' => 'nullable|string|max:255',
        ]);

        $medication->update($validated);

        Toast::success('Medication updated successfully.');

        return redirect()->route('medications.index');
    }

    public function destroy(Medication $medication)
    {
        $medication->delete();

        Toast::success('Medication deleted successfully.');

        return redirect()->route('medications.index');
    }

    public function genericsAZ()
    {
        $medications = MedicationBase::select('id', 'generic_name')
            ->with('medicationVariants')
            ->orderBy('generic_name')
            ->get()
            ->groupBy(function ($item) {
                return strtoupper(substr($item->generic_name, 0, 1));
            });

        $letters = $medications->map(function ($group, $letter) {
            return [
                'letter' => $letter,
                'count' => $group->count(),
                'link' => view('components.letter-link', [
                    'letter' => $letter,
                    'count' => $group->count()
                ])->render()
            ];
        });

        return response()->json([
            'medications' => $medications,
            'letters' => $letters
        ]);
    }
}
