<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\Allergy;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class AllergyController extends Controller
{
    public function showForm(Allergy $allergy = null)
    {
        return view('patient.allergies.create-or-update', [
            'allergy' => $allergy,
            'severityOptions' => Allergy::getSeverityOptions()
        ]);
    }

    public function createOrUpdate(Request $request)
    {
        $validated = $request->validate([
            'id' => 'nullable|exists:allergies,id', // Allow existing ID for updates
            'allergen' => 'required|string|max:255',
            'reaction' => 'nullable|string|max:255',
            'severity' => 'nullable|string|in:mild,moderate,severe,life_threatening',
            'notes' => 'nullable|string|max:1000',
        ]);

        $validated['user_id'] = auth()->user()->id;

        // Find or create based on ID
        $allergy = Allergy::updateOrCreate(
            ['id' => $validated['id']], // Find by ID if provided
            $validated // Data to create or update
        );

        Toast::success('Allergy information saved successfully')->autoDismiss(3);

        return to_route('patient.dashboard');
    }

    public function destroy(Allergy $allergy)
    {
        // Ensure the user can only delete their own allergies
        if ($allergy->user_id !== auth()->id()) {
            Toast::warning('You do not have permission to delete this allergy.');
            return redirect()->route('patient.dashboard');
        }

        $allergy->delete();
        Toast::success('Allergy removed successfully')->autoDismiss(3);

        return to_route('patient.dashboard');
    }
}
