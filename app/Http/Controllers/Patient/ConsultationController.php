<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\Consultation;
use App\Models\MedicalCondition;
use App\Models\MedicationOrder;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class ConsultationController extends Controller
{
    public function create()
    {
        return view('patient.consultations.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'scheduled_at' => 'required|date|after:now',
            'type' => 'required|in:video,audio,chat',
            'notes' => 'nullable|string',
        ]);

        $consultation = $request->user()->patientConsultations()->create([
            'scheduled_at' => $validated['scheduled_at'],
            'type' => $validated['type'],
            'notes' => $validated['notes'] ?? null,
            'status' => 'scheduled',
        ]);

        Toast::success('Consultation scheduled successfully.');

        // If this is part of the guided flow, redirect to the next step
        if (session('in_consultation_flow')) {
            return redirect()->route('patient.consultations.flow', ['step' => 2]);
        }

        return redirect()->route('patient.dashboard');
    }

    public function view(Request $request, Consultation $consultation)
    {
        // Ensure the logged-in user is the patient for this consultation
        if ($request->user()->id !== $consultation->patient_id) {
            abort(403, 'Unauthorized action.');
        }

        // Load related data
        $consultation->load('doctor');

        return view('patient.consultations.view', compact('consultation'));
    }

    /**
     * Show the guided consultation flow
     */
    public function flow(Request $request, $step = 1)
    {
        $user = $request->user();
        $step = (int) $step;
        $consultationId = $request->query('consultation_id');
        $activeConsultation = null;

        // Set a session variable to indicate we're in the flow
        session(['in_consultation_flow' => true]);

        // If a consultation ID is provided, store it in the session
        if ($consultationId) {
            $activeConsultation = $user->patientConsultations()
                ->where('id', $consultationId)
                ->where('status', 'scheduled')
                ->first();

            if ($activeConsultation) {
                session(['active_consultation_id' => $consultationId]);
            }
        } elseif (session('active_consultation_id')) {
            // If no consultation ID is provided but one exists in the session, retrieve it
            $activeConsultation = $user->patientConsultations()
                ->where('id', session('active_consultation_id'))
                ->where('status', 'scheduled')
                ->first();

            // If the consultation no longer exists or is no longer scheduled, remove it from the session
            if (!$activeConsultation) {
                session()->forget('active_consultation_id');
            }
        }

        // Validate step is between 1 and 3
        if ($step < 1 || $step > 3) {
            $step = 1;
        }

        // Get pending consultations (scheduled and not expired)
        $pendingConsultations = $user->patientConsultations()
            ->where('status', 'scheduled')
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at')
            ->get();

        // If on step 2, check if user has medical conditions or medication orders
        if ($step == 2) {
            $hasMedicalConditions = MedicalCondition::where('patient_id', $user->id)->exists();
            $hasMedicationOrders = MedicationOrder::where('patient_id', $user->id)->exists();

            // If user has both, automatically move to step 3
            if ($hasMedicalConditions && $hasMedicationOrders) {
                return redirect()->route('patient.consultations.flow', ['step' => 3]);
            }
        }

        return view('patient.consultations.start-flow', [
            'step' => $step,
            'pendingConsultations' => $pendingConsultations,
            'activeConsultation' => $activeConsultation
        ]);
    }
}
