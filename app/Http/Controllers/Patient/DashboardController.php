<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use App\Models\PrescriptionItem;
use App\Models\UserReportedMedication;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $patient = $request->user();

        // Check if the consultation process was just completed
        if (session('consultation_process_complete')) {
            // Clear the flag
            session()->forget('consultation_process_complete');

            // Show a success message
            Toast::success('Your consultation process is complete! A healthcare provider will review your information.')->autoDismiss(5);
        }

        // Get profile completion data from middleware
        $showProfileCompletionModal = $request->get('show_profile_completion_modal', false);
        $missingProfileFields = $request->get('missing_profile_fields', []);

        // Get medical questionnaire completion data from middleware
        $showMedicalQuestionnaireModal = $request->get('show_medical_questionnaire_modal', false);

        $upcomingConsultation = $patient->patientConsultations()
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at')
            ->first();

        $allergies = $patient->allergies;

        // Fetch current medications from the most recent prescriptions
        $medications = PrescriptionItem::whereHas('prescription', function ($query) use ($patient) {
            $query->where('user_id', $patient->id)
                  ->where('status', 'active');
        })
        ->with('medication')
        ->latest()
        ->take(10)  // Adjust this number as needed
        ->get()
        ->unique('medication_id')
        ->map(function ($item) {
            return $item->medication;
        });

        // Get user-reported medications
        $userMedications = UserReportedMedication::where('user_id', $patient->id)
            ->latest()
            ->get();

        // Get recent medication orders
        $recentMedicationOrders = MedicationOrder::where('patient_id', $patient->id)
            ->with(['doctor', 'items.medication'])
            ->latest()
            ->take(3)
            ->get();

        $recentMedicalRecords = $patient->medicalRecords()
            ->orderBy('record_date', 'desc')
            ->take(5)
            ->get();

        $consultations = SpladeTable::for($patient->patientConsultations()->with('doctor')->latest('scheduled_at'))
            ->column('scheduled_at', label: 'Date', sortable: true)
            ->column('doctorname', label: 'Doctor')
            ->column('type', sortable: true)
            ->column('actions')
            ->paginate(10);

        // $healthReminders = [
        //     [
        //         'title' => 'Annual Check-up',
        //         'description' => 'Schedule your annual physical examination',
        //         'due_date' => Carbon::now()->addMonths(3),
        //     ],
        //     [
        //         'title' => 'Flu Shot',
        //         'description' => 'Get your annual flu vaccination',
        //         'due_date' => Carbon::now()->addMonths(2),
        //     ],
        //     [
        //         'title' => 'Medication Refill',
        //         'description' => 'Refill your prescription',
        //         'due_date' => Carbon::now()->addDays(7),
        //     ],
        // ];

        return view('patient.dashboard', compact(
            'patient',
            'consultations',
            'medications',
            'userMedications',
            'allergies',
            'recentMedicalRecords',
            'upcomingConsultation',
            'recentMedicationOrders',
            'showProfileCompletionModal',
            'missingProfileFields',
            'showMedicalQuestionnaireModal'
            // 'healthReminders'
        ));
    }
}
