<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\FamilyMember\StoreFamilyMemberRequest;
use App\Models\FamilyMember;
use App\Models\Subscription;
use App\Models\User;
use App\Services\FamilySubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class FamilyMemberController extends Controller
{
    public function __construct(protected FamilySubscriptionService $familySubscriptionService)
    {}

    /**
     * Display a listing of the family members.
     */
    public function index()
    {
        $user = Auth::user();

        // Get the user's active subscription
        $subscription = $user->activeSubscription();

        if (!$subscription || !$subscription->plan->isFamilyPlan()) {
            Toast::warning('You do not have an active family plan subscription.')->autoDismiss(10);
            return redirect()->route('patient.dashboard');
        }

        // If user is a dependent, redirect to a view-only page
        if ($subscription->isDependentAccount()) {
            $primarySubscription = $subscription->primarySubscription;
            $primaryUser = $primarySubscription->user;

            $familyMembers = FamilyMember::where('subscription_id', $primarySubscription->id)
                ->with(['dependentUser', 'primaryUser'])
                ->get();

            return view('patient.family-members.view', [
                'subscription' => $primarySubscription,
                'primaryUser' => $primaryUser,
                'familyMembers' => $familyMembers,
                'isDependent' => true,
            ]);
        }

        // For primary account holders, show management view
        // Create a query for family members that belong to this subscription and user
        $query = FamilyMember::where('subscription_id', $subscription->id)
            ->where('primary_user_id', Auth::id())
            ->with(['dependentUser', 'primaryUser']);

        $familyMembers = SpladeTable::for($query)
            ->column('id', sortable: true)
            ->column('dependentUser.name', label: 'Name', sortable: true)
            ->column('dependentUser.email', label: 'Email', sortable: true)
            ->column('relationship_type', label: 'Relationship', sortable: true)
            ->column('date_of_birth', label: 'Date of Birth', sortable: true)
            ->column('actions', label: 'Actions')
            ->withGlobalSearch(columns: ['dependentUser.name', 'dependentUser.email'])
            ->paginate(10);

        return view('patient.family-members.index', [
            'familyMembers' => $familyMembers,
            'subscription' => $subscription,
            'canAddMore' => !$subscription->hasReachedMaxDependents(),
            'canAddOlderDependent' => !$subscription->hasReachedMaxOlderDependents(),
            'canAddYoungerDependent' => !$subscription->hasReachedMaxYoungerDependents(),
            'dependentCount' => $subscription->getDependentCount(),
            'youngerDependentCount' => $subscription->getYoungerDependentCount(),
            'adultDependentCount' => $subscription->getAdultDependentCount(),
            'olderDependentCount' => $subscription->getOlderDependentCount(),
            'maxDependents' => 5,
            'maxYoungerDependents' => 4,
            'maxOlderDependents' => 1,
        ]);
    }

    /**
     * Show the form for creating a new family member.
     */
    public function create()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if (!$subscription || !$subscription->plan->isFamilyPlan()) {
            Toast::warning('You do not have an active family plan subscription.')->autoDismiss(10);
            return redirect()->route('patient.dashboard');
        }

        if ($subscription->isDependentAccount()) {
            Toast::warning('Only primary account holders can add family members.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        if ($subscription->hasReachedMaxDependents()) {
            Toast::warning('You have reached the maximum number of dependents for your plan.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        $canAddOlderDependent = !$subscription->hasReachedMaxOlderDependents();
        $canAddYoungerDependent = !$subscription->hasReachedMaxYoungerDependents();

        return view('patient.family-members.create', [
            'subscription' => $subscription,
            'canAddOlderDependent' => $canAddOlderDependent,
            'canAddYoungerDependent' => $canAddYoungerDependent,
            'youngerDependentCount' => $subscription->getYoungerDependentCount(),
            'adultDependentCount' => $subscription->getAdultDependentCount(),
            'olderDependentCount' => $subscription->getOlderDependentCount(),
            'maxYoungerDependents' => 4,
            'maxOlderDependents' => 1,
            'relationshipTypes' => [
                'spouse' => 'Spouse/Partner',
                'child' => 'Child',
                'other' => 'Other',
            ],
        ]);
    }

    /**
     * Store a newly created family member in storage.
     */
    public function store(StoreFamilyMemberRequest $request)
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription();

        if (!$subscription || !$subscription->plan->isFamilyPlan()) {
            Toast::warning('You do not have an active family plan subscription.')->autoDismiss(10);
            return redirect()->route('patient.dashboard');
        }

        if ($subscription->isDependentAccount()) {
            Toast::warning('Only primary account holders can add family members.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        if ($subscription->hasReachedMaxDependents()) {
            Toast::warning('You have reached the maximum number of dependents for your plan.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        try {
            $result = $this->familySubscriptionService->addDependentToFamily(
                $subscription,
                $request->validated()
            );

            // If this is an adult dependent with login credentials
            if (isset($result['password']) && $result['password']) {
                // Store the password in the session for the confirmation page
                session(['temp_dependent_password' => $result['password']]);
                session(['temp_dependent_email' => $result['user']->email]);

                Toast::success('Family member added successfully. Login credentials have been generated.')->autoDismiss(10);
                return redirect()->route('patient.family-members.confirmation', $result['family_member']->id);
            }

            Toast::success('Family member added successfully.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');

        } catch (\Exception $e) {
            Toast::danger('Error adding family member: ' . $e->getMessage())->autoDismiss(10);
            return back()->withInput();
        }
    }

    /**
     * Display the specified family member.
     */
    public function show(FamilyMember $familyMember)
    {
        $user = Auth::user();

        // Check if the user is authorized to view this family member
        if ($familyMember->primary_user_id !== $user->id && $familyMember->dependent_user_id !== $user->id) {
            Toast::warning('You are not authorized to view this family member.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        return view('patient.family-members.show', [
            'familyMember' => $familyMember->load(['dependentUser', 'primaryUser']),
            'isDependent' => $familyMember->dependent_user_id === $user->id,
        ]);
    }

    /**
     * Show the form for editing the specified family member.
     */
    public function edit(FamilyMember $familyMember)
    {
        $user = Auth::user();

        // Check if the user is authorized to edit this family member
        if ($familyMember->primary_user_id !== $user->id) {
            Toast::warning('Only the primary account holder can edit family members.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        return view('patient.family-members.edit', [
            'familyMember' => $familyMember->load('dependentUser'),
            'relationshipTypes' => [
                'spouse' => 'Spouse/Partner',
                'child' => 'Child',
                'other' => 'Other',
            ],
        ]);
    }

    /**
     * Update the specified family member in storage.
     */
    public function update(StoreFamilyMemberRequest $request, FamilyMember $familyMember)
    {
        $user = Auth::user();

        // Check if the user is authorized to update this family member
        if ($familyMember->primary_user_id !== $user->id) {
            Toast::warning('Only the primary account holder can update family members.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        $dependentUser = $familyMember->dependentUser;
        $validatedData = $request->validated();

        // Update the dependent user
        $dependentUser->fname = $validatedData['fname'];
        $dependentUser->lname = $validatedData['lname'];
        $dependentUser->name = $validatedData['fname'] . ' ' . $validatedData['lname'];
        $dependentUser->email = $validatedData['email'] ?? $dependentUser->email;
        $dependentUser->dob = $validatedData['dob'] ?? $dependentUser->dob;
        $dependentUser->gender = $validatedData['gender'] ?? $dependentUser->gender;
        $dependentUser->address1 = $validatedData['address1'] ?? $dependentUser->address1;
        $dependentUser->address2 = $validatedData['address2'] ?? $dependentUser->address2;
        $dependentUser->city = $validatedData['city'] ?? $dependentUser->city;
        $dependentUser->state = $validatedData['state'] ?? $dependentUser->state;
        $dependentUser->zip = $validatedData['zip'] ?? $dependentUser->zip;
        $dependentUser->phone = $validatedData['phone'] ?? $dependentUser->phone;
        $dependentUser->mobile_phone = $validatedData['mobile_phone'] ?? $dependentUser->mobile_phone;
        $dependentUser->save();

        // Update the family member
        $familyMember->relationship_type = $validatedData['relationship_type'] ?? $familyMember->relationship_type;
        $familyMember->date_of_birth = $validatedData['dob'] ?? $familyMember->date_of_birth;
        $familyMember->save();

        Toast::success('Family member updated successfully.')->autoDismiss(10);
        return redirect()->route('patient.family-members.index');
    }

    /**
     * Remove the specified family member from storage.
     */
    public function destroy(FamilyMember $familyMember)
    {
        $user = Auth::user();

        // Check if the user is authorized to delete this family member
        if ($familyMember->primary_user_id !== $user->id) {
            Toast::warning('Only the primary account holder can remove family members.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        $subscription = $user->activeSubscription();

        try {
            // Store dependent name for the success message
            $dependentName = $familyMember->dependentUser->name;

            $this->familySubscriptionService->removeDependentFromFamily(
                $subscription,
                $familyMember->dependentUser
            );

            Toast::success("Family member {$dependentName} removed successfully. Their account has been marked as deleted and they can no longer log in.")->autoDismiss(10);
        } catch (\Exception $e) {
            Toast::danger('Error removing family member: ' . $e->getMessage())->autoDismiss(10);
        }

        return redirect()->route('patient.family-members.index');
    }

    /**
     * Show the confirmation page for adult dependent credentials.
     */
    public function confirmation(FamilyMember $familyMember)
    {
        $user = Auth::user();

        // Check if the user is authorized to view this confirmation
        if ($familyMember->primary_user_id !== $user->id) {
            Toast::warning('You are not authorized to view this page.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        // Check if we have temporary credentials in the session
        $password = session('temp_dependent_password');
        $email = session('temp_dependent_email');

        if (!$password || !$email) {
            Toast::warning('No temporary credentials found. The dependent may have already been created.')->autoDismiss(10);
            return redirect()->route('patient.family-members.index');
        }

        // Clear the session data after retrieving it
        session()->forget(['temp_dependent_password', 'temp_dependent_email']);

        $subscription = $user->activeSubscription();

        return view('patient.family-members.confirmation', [
            'familyMember' => $familyMember->load('dependentUser'),
            'password' => $password,
            'email' => $email,
            'subscription' => $subscription,
            'dependentCount' => $subscription->getDependentCount(),
            'youngerDependentCount' => $subscription->getYoungerDependentCount(),
            'adultDependentCount' => $subscription->getAdultDependentCount(),
            'olderDependentCount' => $subscription->getOlderDependentCount(),
            'canAddMore' => !$subscription->hasReachedMaxDependents(),
            'canAddYoungerDependent' => !$subscription->hasReachedMaxYoungerDependents(),
            'canAddOlderDependent' => !$subscription->hasReachedMaxOlderDependents(),
            'maxYoungerDependents' => 4,
            'maxOlderDependents' => 1,
        ]);
    }
}
