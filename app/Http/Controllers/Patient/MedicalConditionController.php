<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicalCondition;
use App\Models\Condition;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class MedicalConditionController extends Controller
{
    /**
     * Display a listing of the patient's medical conditions.
     */
    public function index()
    {
        $conditions = MedicalCondition::where('patient_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('patient.medical-conditions.index', [
            'conditions' => $conditions
        ]);
    }

    /**
     * Show the form for creating a new medical condition.
     */
    public function create(Request $request)
    {
        // Get the return URL if provided
        $returnUrl = $request->input('return_url');

        // Get predefined conditions from the database
        $predefinedConditions = Condition::orderBy('name')->get();

        return view('patient.medical-conditions.create', [
            'predefinedConditions' => $predefinedConditions,
            'returnUrl' => $returnUrl
        ]);
    }

    /**
     * Store a newly created medical condition in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'condition_name' => 'required|string|max:255',
            'diagnosed_at' => 'nullable|date',
            'notes' => 'nullable|string',
            'condition_id' => 'nullable|exists:conditions,id',
            'had_condition_before' => 'nullable|boolean',
            'is_chronic' => 'nullable|boolean',
        ]);

        // Create the medical condition
        $condition = new MedicalCondition();
        $condition->patient_id = Auth::id();
        $condition->condition_name = $request->condition_name;
        // Store the date as is without timezone conversion
        $condition->diagnosed_at = $request->diagnosed_at;
        $condition->notes = $request->notes;
        $condition->condition_id = $request->condition_id;
        $condition->is_custom = $request->condition_id ? false : true;
        $condition->had_condition_before = $request->had_condition_before ?? false;
        $condition->is_chronic = $request->is_chronic ?? false;
        $condition->save();

        Toast::success('Medical condition added successfully.');

        // Redirect back to the return_url if provided, otherwise to the conditions index
        if ($request->filled('return_url')) {
            return redirect($request->return_url);
        }

        return redirect()->route('patient.medical-conditions.index');
    }

    /**
     * Display the specified medical condition.
     */
    public function show(MedicalCondition $medicalCondition)
    {
        // Ensure the user can only view their own conditions
        if ($medicalCondition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to view this condition.');
            return redirect()->route('patient.medical-conditions.index');
        }

        return view('patient.medical-conditions.show', [
            'condition' => $medicalCondition
        ]);
    }

    /**
     * Show the form for editing the specified medical condition.
     */
    public function edit(Request $request, MedicalCondition $medicalCondition)
    {
        // Ensure the user can only edit their own conditions
        if ($medicalCondition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to edit this condition.');
            return redirect()->route('patient.medical-conditions.index');
        }

        // Get predefined conditions from the database
        $predefinedConditions = Condition::orderBy('name')->get();

        // Get the return URL if provided
        $returnUrl = $request->input('return_url');

        // If diagnosed_at is a Carbon instance, convert it to a string in Y-m-d format
        if ($medicalCondition->diagnosed_at instanceof \Carbon\Carbon) {
            $medicalCondition->diagnosed_at = $medicalCondition->diagnosed_at->format('Y-m-d');
        }

        return view('patient.medical-conditions.edit', [
            'condition' => $medicalCondition,
            'returnUrl' => $returnUrl,
            'predefinedConditions' => $predefinedConditions
        ]);
    }

    /**
     * Update the specified medical condition in storage.
     */
    public function update(Request $request, MedicalCondition $medicalCondition)
    {
        // Ensure the user can only update their own conditions
        if ($medicalCondition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this condition.');
            return redirect()->route('patient.medical-conditions.index');
        }

        $request->validate([
            'condition_name' => 'required|string|max:255',
            'diagnosed_at' => 'nullable|date',
            'notes' => 'nullable|string',
            'condition_id' => 'nullable|exists:conditions,id',
            'had_condition_before' => 'nullable|boolean',
            'is_chronic' => 'nullable|boolean',
        ]);

        // Update the medical condition
        $medicalCondition->condition_name = $request->condition_name;
        // Store the date as is without timezone conversion
        $medicalCondition->diagnosed_at = $request->diagnosed_at;
        $medicalCondition->notes = $request->notes;
        $medicalCondition->condition_id = $request->condition_id;
        $medicalCondition->is_custom = $request->condition_id ? false : true;
        $medicalCondition->had_condition_before = $request->had_condition_before ?? false;
        $medicalCondition->is_chronic = $request->is_chronic ?? false;
        $medicalCondition->save();

        Toast::success('Medical condition updated successfully.');

        // Redirect back to the return_url if provided, otherwise to the conditions index
        if ($request->filled('return_url')) {
            return redirect($request->return_url);
        }

        return redirect()->route('patient.medical-conditions.index');
    }

    /**
     * Remove the specified medical condition from storage.
     */
    public function destroy(MedicalCondition $medicalCondition)
    {
        // Ensure the user can only delete their own conditions
        if ($medicalCondition->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this condition.');
            return redirect()->route('patient.medical-conditions.index');
        }

        $medicalCondition->delete();

        Toast::success('Medical condition deleted successfully.');
        return redirect()->route('patient.medical-conditions.index');
    }
}
