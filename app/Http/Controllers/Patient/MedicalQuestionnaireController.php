<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicalCondition;
use App\Models\MedicationOrder;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use App\Traits\HandlesMedicalQuestionnaires;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class MedicalQuestionnaireController extends Controller
{
    use HandlesMedicalQuestionnaires;
    /**
     * Display a listing of the user's medical questionnaires.
     */
    public function index()
    {
        $questionnaires = MedicalQuestionnaire::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->get();

        return view('patient.medical-questionnaires.index', [
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Show the form for creating a new medical questionnaire.
     */
    public function create()
    {
        $user = Auth::user();

        // Check if the user has any medical conditions
        $hasMedicalConditions = MedicalCondition::where('patient_id', $user->id)->exists();

        // Check if the user has any medication orders
        $hasMedicationOrders = MedicationOrder::where('patient_id', $user->id)->exists();

        // If the user doesn't have any medical conditions or medication orders,
        // show a page asking them to create one first
        if (!$hasMedicalConditions && !$hasMedicationOrders) {
            return view('patient.medical-questionnaires.choose-path', [
                'returnUrl' => route('patient.medical-questionnaires.create')
            ]);
        }

        // Get user's medications, conditions, and allergies
        $formData = $this->getFormData($user);

        // Get user's medication orders with items and medications
        $medicationOrders = $user->medicationOrders()
            ->with(['items.medication'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get all categories from the medication orders
        $categories = collect();

        // If there are medication orders, extract categories from them
        if ($medicationOrders->isNotEmpty()) {
            foreach ($medicationOrders as $order) {
                foreach ($order->items as $item) {
                    if ($item->medication) {
                        // Add medication categories if available
                        if (isset($item->medication->drug_class)) {
                            $categories->push($item->medication->drug_class);
                        }
                    }
                }
            }
        }

        return view('patient.medical-questionnaires.create', [
            'formData' => $formData,
            'hasMedicalConditions' => $hasMedicalConditions,
            'hasMedicationOrders' => $hasMedicationOrders,
            'medicationOrders' => $medicationOrders,
            'categories' => $categories
        ]);
    }

    /**
     * Store a newly created medical questionnaire.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // Store the questionnaire
        $questionnaire = $this->storeQuestionnaire($request, $user);

        // If there's an active consultation in the session, associate the questionnaire with it
        if (session('active_consultation_id')) {
            $consultation = $user->patientConsultations()
                ->where('id', session('active_consultation_id'))
                ->where('status', 'scheduled')
                ->first();

            if ($consultation && $questionnaire) {
                // Update the consultation with the questionnaire information
                // This assumes you have a way to link consultations and questionnaires
                // You might need to add a consultation_id field to the medical_questionnaires table
                // or create a separate pivot table for this relationship

                // For now, we'll just add a note to the consultation
                $notes = $consultation->notes ?? '';
                $consultation->update([
                    'notes' => $notes . "\n\nPatient completed medical questionnaire #" . $questionnaire->id . " on " . now()->format('Y-m-d H:i:s')
                ]);

                // Clear the active consultation from the session
                session()->forget('active_consultation_id');
                session()->forget('in_consultation_flow');

                // Set a session flag to indicate the consultation process is complete
                session(['consultation_process_complete' => true]);
            }
        }

        Toast::success('Medical questionnaire created successfully.')->autoDismiss(3);

        // If this is part of the consultation flow, redirect to the dashboard with a success message
        if ($request->filled('from_consultation_flow')) {
            Toast::success('Your consultation process is complete! A healthcare provider will review your information.')->autoDismiss(5);
            return redirect()->route('patient.dashboard');
        }

        return redirect()->route('patient.medical-questionnaires.index');
    }

    /**
     * Display a listing of the patient's medical questionnaires for an agent.
     */
    public function agentIndex(User $patient)
    {
        // Check if the agent has permission to view this patient's questionnaires
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to view this patient\'s medical questionnaires.')->autoDismiss(3);
            return redirect()->back();
        }

        $questionnaires = MedicalQuestionnaire::where('user_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('agent.patients.medical-questionnaires.index', [
            'patient' => $patient,
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Show the form for creating a new medical questionnaire for a patient by an agent.
     */
    public function agentCreate(User $patient)
    {
        // Check if the agent has permission to create a questionnaire for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medical questionnaire for this patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Get patient's medications and allergies
        $userMedications = $patient->userReportedMedication()->get()->toArray();
        $userAllergies = $patient->allergies()->get()->toArray();

        // Prepare empty form data
        $formData = [
            'medications' => $userMedications,
            'allergies' => $userAllergies,
        ];

        return view('agent.patients.medical-questionnaires.create', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Store a newly created medical questionnaire for a patient by an agent.
     */
    public function agentStore(Request $request, User $patient)
    {
        // Check if the agent has permission to create a questionnaire for this patient
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to create a medical questionnaire for this patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Use the trait method to store the questionnaire
        $questionnaire = $this->storeQuestionnaire($request, $patient);

        Toast::success('Medical questionnaire created successfully.')->autoDismiss(3);
        return redirect()->route('agent.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Show the form for editing a medical questionnaire for a patient by an agent.
     */
    public function agentEdit(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the agent has permission to edit this patient's questionnaire
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to edit this patient\'s medical questionnaire.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Get patient's medications and allergies
        $userMedications = $patient->userReportedMedication()->get()->toArray();
        $userAllergies = $patient->allergies()->get()->toArray();

        // Prepare form data
        $formData = [
            'questionnaire' => $questionnaire,
            'medications' => $userMedications,
            'allergies' => $userAllergies,
        ];

        return view('agent.patients.medical-questionnaires.edit', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Update a medical questionnaire for a patient by an agent.
     */
    public function agentUpdate(Request $request, User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the agent has permission to update this patient's questionnaire
        $agent = Auth::user()->agent;
        if (!$agent || !$this->isPatientEnrolledByAgent($patient, $agent)) {
            Toast::danger('You do not have permission to update this patient\'s medical questionnaire.')->autoDismiss(3);
            return redirect()->back();
        }

        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Use the trait method to update the questionnaire
        $this->updateQuestionnaire($request, $patient, $questionnaire);

        Toast::success('Medical questionnaire updated successfully.')->autoDismiss(3);
        return redirect()->route('agent.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Display a listing of the patient's medical questionnaires for an admin.
     */
    public function adminIndex(User $patient)
    {
        $questionnaires = MedicalQuestionnaire::where('user_id', $patient->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.patients.medical-questionnaires.index', [
            'patient' => $patient,
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Show the form for creating a new medical questionnaire for a patient by an admin.
     */
    public function adminCreate(User $patient)
    {
        // Get patient's medications and allergies
        $userMedications = $patient->userReportedMedication()->get()->toArray();
        $userAllergies = $patient->allergies()->get()->toArray();

        // Prepare empty form data
        $formData = [
            'medications' => $userMedications,
            'allergies' => $userAllergies,
        ];

        return view('admin.patients.medical-questionnaires.create', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Store a newly created medical questionnaire for a patient by an admin.
     */
    public function adminStore(Request $request, User $patient)
    {
        // Use the trait method to store the questionnaire
        $questionnaire = $this->storeQuestionnaire($request, $patient);

        Toast::success('Medical questionnaire created successfully.')->autoDismiss(3);
        return redirect()->route('admin.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Show the form for editing a medical questionnaire for a patient by an admin.
     */
    public function adminEdit(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Get patient's medications and allergies
        $userMedications = $patient->userReportedMedication()->get()->toArray();
        $userAllergies = $patient->allergies()->get()->toArray();

        // Prepare form data
        $formData = [
            'questionnaire' => $questionnaire,
            'medications' => $userMedications,
            'allergies' => $userAllergies,
        ];

        return view('admin.patients.medical-questionnaires.edit', [
            'patient' => $patient,
            'formData' => $formData
        ]);
    }

    /**
     * Update a medical questionnaire for a patient by an admin.
     */
    public function adminUpdate(Request $request, User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Use the trait method to update the questionnaire
        $this->updateQuestionnaire($request, $patient, $questionnaire);

        Toast::success('Medical questionnaire updated successfully.')->autoDismiss(3);
        return redirect()->route('admin.patients.medical-questionnaires.index', $patient);
    }

    /**
     * Check if a patient was enrolled by an agent
     *
     * @param User $patient
     * @param \App\Models\Agent $agent
     * @return bool
     */
    private function isPatientEnrolledByAgent(User $patient, $agent)
    {
        // Check if the patient has any subscriptions with the agent's ID in the agent_id field
        return $patient->subscriptions()
            ->where('agent_id', $agent->id)
            ->exists();
    }

    /**
     * Show the form for editing a medical questionnaire.
     */
    public function edit(MedicalQuestionnaire $questionnaire)
    {
        $user = Auth::user();

        // Ensure the user can only edit their own questionnaires
        if ($questionnaire->user_id !== $user->id) {
            Toast::danger('You do not have permission to edit this questionnaire.')->autoDismiss(3);
            return redirect()->route('patient.medical-questionnaires.index');
        }

        // Check if the user has any medical conditions
        $hasMedicalConditions = MedicalCondition::where('patient_id', $user->id)->exists();

        // Check if the user has any medication orders
        $hasMedicationOrders = MedicationOrder::where('patient_id', $user->id)->exists();

        // If the user doesn't have any medical conditions or medication orders,
        // show a page asking them to create one first
        if (!$hasMedicalConditions && !$hasMedicationOrders) {
            return view('patient.medical-questionnaires.choose-path', [
                'returnUrl' => route('patient.medical-questionnaires.edit', $questionnaire)
            ]);
        }

        // Get user's medication orders with items and medications
        $medicationOrders = $user->medicationOrders()
            ->with(['items.medication'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get all categories from the medication orders
        $categories = collect();

        // If there are medication orders, extract categories from them
        if ($medicationOrders->isNotEmpty()) {
            foreach ($medicationOrders as $order) {
                foreach ($order->items as $item) {
                    if ($item->medication) {
                        // Add medication categories if available
                        if (isset($item->medication->drug_class)) {
                            $categories->push($item->medication->drug_class);
                        }
                    }
                }
            }
        }

        // Get user's medications and allergies
        $userMedications = $user->userReportedMedication()->get()->toArray();
        $userAllergies = $user->allergies()->get()->toArray();

        // Prepare the form data
        $formData = $questionnaire->toArray();
        $formData['medications'] = $userMedications;
        $formData['allergies'] = $userAllergies;

        return view('patient.medical-questionnaires.edit', [
            'questionnaire' => $questionnaire,
            'categories' => $categories,
            'formData' => $formData,
            'hasMedicalConditions' => $hasMedicalConditions,
            'hasMedicationOrders' => $hasMedicationOrders,
            'medicationOrders' => $medicationOrders
        ]);
    }

    /**
     * Update the specified medical questionnaire.
     */
    public function update(Request $request, MedicalQuestionnaire $questionnaire)
    {
        // Ensure the user can only update their own questionnaires
        if ($questionnaire->user_id !== Auth::id()) {
            Toast::danger('You do not have permission to update this questionnaire.')->autoDismiss(3);
            return redirect()->route('patient.medical-questionnaires.index');
        }

        $user = Auth::user();

        // Use the trait method to update the questionnaire
        $this->updateQuestionnaire($request, $user, $questionnaire);

        Toast::success('Medical questionnaire updated successfully.')->autoDismiss(3);
        return redirect()->route('patient.medical-questionnaires.index');
    }
}
