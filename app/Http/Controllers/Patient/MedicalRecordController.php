<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicalRecord;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Splade;
use ProtoneMedia\Splade\FormBuilder\Date;
use ProtoneMedia\Splade\FormBuilder\Input;
use ProtoneMedia\Splade\FormBuilder\Select;
use ProtoneMedia\Splade\FormBuilder\Submit;
use ProtoneMedia\Splade\FormBuilder\Textarea;
use ProtoneMedia\Splade\SpladeForm;

class MedicalRecordController extends Controller
{
    public function create()
    {
        return view('patient.medical-records.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'record_type' => 'required|in:general,lab_result,imaging,prescription,surgery',
            'record_date' => 'required|date',
            'description' => 'required|string',
            'file_path' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
        ]);

        $validated['patient_id'] = auth()->user()->id;

        $medicalRecord = MedicalRecord::create($validated);

        if ($request->hasFile('file_path')) {
            info('File present in request');
            try {
                $filePath = $request->file('file_path')->store('medical_records', 'public');
                info('File stored at: ' . $filePath);
                $medicalRecord->file_path = $filePath;
                $medicalRecord->save();
                info('Medical record updated with file path');
            } catch (\Exception $e) {
                info('Error storing file: ' . $e->getMessage());
            }
        } else {
            info('No file present in request');
        }

        Splade::toast('Medical record created successfully')->autoDismiss(3);

        return to_route('patient.dashboard');
    }
}
