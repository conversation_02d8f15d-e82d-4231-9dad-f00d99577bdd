<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use App\Models\MedicationOrderItem;
use App\Models\Medication;
use App\Models\MedicalQuestionnaire;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationOrderController extends Controller
{
    /**
     * Display a listing of the patient's medication orders.
     */
    public function index()
    {
        $orders = SpladeTable::for(MedicationOrder::where('patient_id', Auth::id()))
            ->column('id', label: 'Order #')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status')
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $order) => $order->doctor ? $order->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('patient.medication-orders.show', $order))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('patient.medication-orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Show the form for creating a new medication order.
     */
    public function create(Request $request)
    {
        $medications = Medication::orderBy('name')
            ->get();

        // Get the return URL if provided
        $returnUrl = $request->input('return_url');

        return view('patient.medication-orders.create', [
            'medications' => $medications,
            'returnUrl' => $returnUrl
        ]);
    }

    /**
     * Store a newly created medication order in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'required_if:medications.*.custom_medication_name,null',
            'medications.*.custom_medication_name' => 'required_if:medications.*.medication_id,null|string|max:255',
            'medications.*.custom_medication_details' => 'nullable|string|max:1000',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        // Validate that each medication has either medication_id or custom_medication_name
        foreach ($request->medications as $index => $medication) {
            if (empty($medication['medication_id']) && empty($medication['custom_medication_name'])) {
                return back()->withErrors([
                    "medications.{$index}.medication_id" => 'Either a medication must be selected or a custom medication name must be provided.'
                ])->withInput();
            }
        }

        // Create the medication order
        $order = MedicationOrder::create([
            'patient_id' => Auth::id(),
            'status' => 'pending',
            'patient_notes' => $request->patient_notes,
        ]);

        // Create the medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $order->id,
                'medication_id' => !empty($medication['medication_id']) ? $medication['medication_id'] : null,
                'custom_medication_name' => $medication['custom_medication_name'] ?? null,
                'custom_medication_details' => $medication['custom_details'] ?? null,
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order created successfully.');

        // Redirect back to the return_url if provided
        if ($request->filled('return_url')) {
            return redirect($request->return_url);
        }

        // Check if the user has any existing questionnaires
        $latestQuestionnaire = MedicalQuestionnaire::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestQuestionnaire) {
            // Redirect to edit the latest questionnaire
            return redirect()->route('patient.medical-questionnaires.edit', $latestQuestionnaire);
        } else {
            // Redirect to create a new questionnaire
            return redirect()->route('patient.medical-questionnaires.create');
        }
    }

    /**
     * Display the specified medication order.
     */
    public function show(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only view their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to view this order.');
            return redirect()->route('patient.medication-orders.index');
        }

        $medicationOrder->load(['items.medication', 'doctor', 'prescription.items']);

        return view('patient.medication-orders.show', [
            'order' => $medicationOrder,
        ]);
    }

    /**
     * Show the form for editing the specified medication order.
     */
    public function edit(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the user can only edit their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to edit this order.');
            return redirect()->route('patient.medication-orders.index');
        }

        // Only allow editing of pending orders
        if ($medicationOrder->status !== 'pending') {
            Toast::warning('Only pending orders can be edited.');
            return redirect()->route('patient.medication-orders.show', $medicationOrder);
        }

        $medications = Medication::orderBy('name')->get();

        // Get the return URL if provided
        $returnUrl = $request->input('return_url');

        return view('patient.medication-orders.edit', [
            'order' => $medicationOrder,
            'medications' => $medications,
            'returnUrl' => $returnUrl,
        ]);
    }

    /**
     * Update the specified medication order in storage.
     */
    public function update(Request $request, MedicationOrder $medicationOrder)
    {
        // Ensure the user can only update their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to update this order.');
            return redirect()->route('patient.medication-orders.index');
        }

        // Only allow updating of pending orders
        if ($medicationOrder->status !== 'pending') {
            Toast::warning('Only pending orders can be updated.');
            return redirect()->route('patient.medication-orders.show', $medicationOrder);
        }

        $request->validate([
            'patient_notes' => 'nullable|string|max:1000',
            'medications' => 'required|array|min:1',
            'medications.*.medication_id' => 'nullable|exists:medications,id',
            'medications.*.custom_medication_name' => 'nullable|string|max:255',
            'medications.*.custom_details' => 'nullable|string|max:1000',
            'medications.*.requested_dosage' => 'nullable|string|max:255',
            'medications.*.requested_quantity' => 'nullable|string|max:255',
        ]);

        // Validate that each medication has either medication_id or custom_medication_name
        foreach ($request->medications as $index => $medication) {
            if (empty($medication['medication_id']) && empty($medication['custom_medication_name'])) {
                return back()->withErrors([
                    "medications.{$index}.medication_id" => 'Either a medication must be selected or a custom medication name must be provided.'
                ])->withInput();
            }
        }

        // Update the medication order
        $medicationOrder->update([
            'patient_notes' => $request->patient_notes,
        ]);

        // Delete existing items
        $medicationOrder->items()->delete();

        // Create new medication order items
        foreach ($request->medications as $medication) {
            MedicationOrderItem::create([
                'medication_order_id' => $medicationOrder->id,
                'medication_id' => !empty($medication['medication_id']) ? $medication['medication_id'] : null,
                'custom_medication_name' => $medication['custom_medication_name'] ?? null,
                'custom_medication_details' => $medication['custom_details'] ?? null,
                'requested_dosage' => $medication['requested_dosage'] ?? null,
                'requested_quantity' => $medication['requested_quantity'] ?? null,
                'status' => 'pending',
            ]);
        }

        Toast::success('Medication order updated successfully.');

        // Redirect back to the return_url if provided
        if ($request->filled('return_url')) {
            return redirect($request->return_url);
        }

        // Check if the user has any existing questionnaires
        $latestQuestionnaire = MedicalQuestionnaire::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestQuestionnaire) {
            // Redirect to edit the latest questionnaire
            return redirect()->route('patient.medical-questionnaires.edit', $latestQuestionnaire);
        } else {
            // Redirect to create a new questionnaire
            return redirect()->route('patient.medical-questionnaires.create');
        }
    }

    /**
     * Create a new order based on an existing one.
     */
    public function reorder(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only reorder their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to reorder this medication.');
            return redirect()->route('patient.medication-orders.index');
        }

        // Create a new medication order
        $newOrder = new MedicationOrder();
        $newOrder->patient_id = Auth::id();
        $newOrder->status = 'pending';
        $newOrder->patient_notes = $medicationOrder->patient_notes;
        $newOrder->save();

        // Copy the medication order items
        foreach ($medicationOrder->items as $item) {
            $newItem = new MedicationOrderItem();
            $newItem->medication_order_id = $newOrder->id;
            $newItem->medication_id = $item->medication_id;
            $newItem->custom_medication_name = $item->custom_medication_name;
            $newItem->custom_medication_details = $item->custom_medication_details;
            $newItem->requested_dosage = $item->requested_dosage;
            $newItem->requested_quantity = $item->requested_quantity;
            $newItem->status = 'pending';
            $newItem->save();
        }

        Toast::success('Medication order has been reordered successfully.');

        // Check if the user has any existing questionnaires
        $latestQuestionnaire = MedicalQuestionnaire::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestQuestionnaire) {
            // Redirect to edit the latest questionnaire
            return redirect()->route('patient.medical-questionnaires.edit', $latestQuestionnaire);
        } else {
            // Redirect to create a new questionnaire
            return redirect()->route('patient.medical-questionnaires.create');
        }
    }

    /**
     * Remove the specified medication order from storage.
     */
    public function destroy(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only delete their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            Toast::warning('You do not have permission to delete this order.');
            return redirect()->route('patient.medication-orders.index');
        }

        // Only allow deletion of pending orders
        if ($medicationOrder->status !== MedicationOrder::STATUS_PENDING) {
            Toast::warning('Only pending orders can be deleted.');
            return redirect()->route('patient.medication-orders.show', $medicationOrder);
        }

        // Delete the medication order items first
        $medicationOrder->items()->delete();

        // Delete the medication order
        $medicationOrder->delete();

        Toast::success('Medication order deleted successfully.');
        return redirect()->route('patient.medication-orders.index');
    }
}
