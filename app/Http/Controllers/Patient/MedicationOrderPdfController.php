<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class MedicationOrderPdfController extends Controller
{
    /**
     * Generate a PDF for the medication order.
     */
    public function export(MedicationOrder $medicationOrder)
    {
        // Ensure the user can only export their own orders
        if ($medicationOrder->patient_id !== Auth::id()) {
            return redirect()->route('patient.medication-orders.index')
                ->with('error', 'You do not have permission to export this order.');
        }

        $medicationOrder->load(['items.medication', 'doctor', 'prescription.items']);

        $pdf = Pdf::loadView('patient.medication-orders.pdf', ['order' => $medicationOrder]);
        return $pdf->stream("medication-order-{$medicationOrder->id}.pdf");
    }
}
