<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use ProtoneMedia\Splade\Facades\Toast;

class PainTreatmentController extends Controller
{
    public function create()
    {
        return view('front.services.form.pain-treatment');
    }

    public function submit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pain_type' => 'required|array|min:1',
            'pain_type_other' => 'required_if:pain_type,Other',
            'pain_location' => 'array',
            'pain_location_other' => 'required_if:pain_location,Other',
            'pain_intensity' => 'required|integer|min:1|max:10',
            'pain_duration' => 'nullable|string',
            'pain_start' => 'nullable|date',
            'pain_frequency' => 'nullable|string',
            'pain_triggers' => 'array',
            'pain_triggers_other' => 'required_if:pain_triggers,Other',
            'pain_relief' => 'array',
            'pain_relief_other' => 'required_if:pain_relief,Other',
            'allergies' => 'required|in:Yes,No',
            'allergy_details' => 'required_if:allergies,Yes',
            'current_medications' => 'nullable|string',
            'conditions' => 'required|array|min:1',
            'conditions_other' => 'required_if:conditions,Other',
            'adverse_reactions' => 'required|in:Yes,No',
            'adverse_reactions_details' => 'required_if:adverse_reactions,Yes',
            'current_side_effects' => 'required|in:Yes,No',
            'side_effects_details' => 'required_if:current_side_effects,Yes',
            'alcohol_use' => 'required|in:Yes,No',
            'alcohol_details' => 'required_if:alcohol_use,Yes',
            'tobacco_use' => 'required|in:Yes,No',
            'tobacco_details' => 'required_if:tobacco_use,Yes',
            'drug_use' => 'required|in:Yes,No',
            'drug_details' => 'required_if:drug_use,Yes',
            'exercise_frequency' => 'required|string',
            'mental_health_conditions' => 'required|in:Yes,No',
            'mental_health_details' => 'required_if:mental_health_conditions,Yes',
            'suicidal_thoughts' => 'required|in:Yes,No',
            'receiving_therapy' => 'required|in:Yes,No',
            'benefit_from_counseling' => 'required|in:Yes,No',
            'worried_about_counseling' => 'required|in:Yes,No',
            'family_chronic_pain' => 'required|in:Yes,No',
            'family_chronic_pain_details' => 'required_if:family_chronic_pain,Yes',
            'family_conditions' => 'array',
            'family_conditions_other' => 'required_if:family_conditions,Other',
            'additional_concerns' => 'nullable|string',
            'daily_activities_impact' => 'required|string',
            'sleep_impact' => 'required|string',
            'mobility_impact' => 'required|string',
            'emotional_impact' => 'required|string',
            'associated_symptoms' => 'array',
            'associated_symptoms_other' => 'required_if:associated_symptoms,Other',
            'systemic_symptoms' => 'required|in:Yes,No',
            'past_injuries' => 'required|in:Yes,No',
            'past_injuries_details' => 'required_if:past_injuries,Yes',
            'surgeries' => 'required|in:Yes,No',
            'surgery_details' => 'required_if:surgeries,Yes',
            'chronic_conditions_details' => 'nullable|string',
            'stress_levels' => 'required|integer|min:1|max:10',
            'support_system' => 'required|in:Yes,No',
            'work_environment' => 'required|in:Yes,No',
            'mental_health_changes' => 'required|string',
            'tenderness' => 'required|in:Yes,No',
            'difficulty_moving' => 'required|in:Yes,No',
            'reduced_activity' => 'required|in:Yes,No',
            'diagnostic_tests' => 'nullable|string',
            'physiotherapy_details' => 'nullable|string',
            'medication_screening' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Store the validated data in the session
        $request->session()->put('pain_treatment_form', $validator->validated());

        if ($request->session()->has('landing-page') && $request->session()->get('landing-page') === true) {
            $request->session()->forget('landing-page');

            return to_route('landing-page.register.form', 6); // 6 = Pain Treatment
        }
        // return response()->json(['message' => 'Form submitted successfully']);
        return to_route('patient.register', ['serviceId' => 6]);
    }
}
