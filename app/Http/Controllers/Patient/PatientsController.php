<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\DoctorPatientAssignment;
use App\Models\MedicationOrder;
use App\Models\User;
use App\Models\UserMeasurement;
use App\Services\HeightConverterService;
use App\Services\MeasurementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class PatientsController extends Controller
{
    protected $heightConverter;
    protected $measurementService;

    public function __construct(HeightConverterService $heightConverter, MeasurementService $measurementService)
    {
        $this->heightConverter = $heightConverter;
        $this->measurementService = $measurementService;
    }


    public function assignForm()
    {
        if (Gate::denies('assign', User::class)) {
            abort(403, 'You are not authorized to assign patients to doctors.');
        }

        $patients = User::role('patient')->get();
        $doctors = User::role('doctor')->get();

        return view('admin.assign-patient', compact('patients', 'doctors'));
    }

    public function assign(Request $request)
    {
        if (Gate::denies('assign', User::class)) {
            abort(403, 'You are not authorized to assign patients to doctors.');
        }

        $request->validate([
            'patient_id' => 'required|exists:users,id',
            'doctor_id' => 'required|exists:users,id',
        ]);

        $patient = User::findOrFail($request->patient_id);
        $doctor = User::findOrFail($request->doctor_id);

        $patient->assignedDoctor()->associate($doctor);
        $patient->save();

        Toast::success('Patient assigned to doctor successfully.');

        return redirect()->route('admin.dashboard');
    }

    public function index()
    {
        return view('patient.index', [
            'patients' => SpladeTable::for(User::role('patient'))
                ->column('name')
                ->column('email')
                ->column('actions')
                ->paginate(15)
        ]);
    }

    public function create()
    {
        return view('admin.patients.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $patient = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
        ]);

        $patient->assignRole('patient');

        Toast::success('Patient created successfully.')->autoDismiss(10);

        return redirect()->route('admin.patients.index');
    }

    public function show(User $patient)
    {
        $medicalRecords = $patient->medicalRecords()->latest()->get();
        $consultations = $patient->patientConsultations()->where('doctor_id', auth()->id())->latest()->get();
        $prescriptions = $patient->prescriptionsAsPatient()->where('doctor_id', auth()->id())->latest()->get();
        $medicalQuestionnaires = $patient->medicalQuestionnaires()->latest()->get();
        $patient->load(['allergies', 'healthQuestion', 'painAssessment', 'preferredMedications']);

        // Get patient's medication orders
        $medicationOrders = MedicationOrder::where('patient_id', $patient->id)
            ->with(['items.medication', 'prescription.items'])
            ->latest()
            ->get();

        // Get assignment information
        $assignment = DoctorPatientAssignment::where('doctor_id', auth()->id())
            ->where('patient_id', $patient->id)
            ->with('assignedBy')
            ->first();

        return view('doctor.patients.show', compact(
            'patient',
            'medicalRecords',
            'consultations',
            'prescriptions',
            'medicalQuestionnaires',
            'medicationOrders',
            'assignment'
        ));
    }

    public function edit(User $patient)
    {
        $statuses = User::STATUSES;

        // Get the latest measurement or create a default one
        $measurement = $patient->measurements()->latest('measured_at')->first();

        // Convert height from inches to feet and inches for display
        $height_feet = 0;
        $height_inches = 0;
        $bmi = 0;
        $bmiCategory = null;
        $bmiColor = '';

        if ($measurement && $measurement->height) {
            $patient->height_feet = floor($measurement->height / 12);;
            $patient->height_inches = $measurement->height % 12;

            // Calculate BMI if both height and weight are available
            if ($measurement->weight) {
                $bmi = $this->measurementService->calculateBMI($measurement->height, $measurement->weight);
                $bmiData = $this->measurementService->getBMICategory($bmi);
                $bmiCategory = $bmiData['category'];
                $bmiColor = $bmiData['color'];
                $patient->weight = $measurement->weight;
            }
        }

        return view('patient.edit', compact(
            'patient',
            'statuses',
            'measurement',
            'bmi',
            'bmiCategory',
            'bmiColor'
        ));
    }

    public function update(Request $request, User $patient)
    {
        $validated = $request->validate([
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $patient->id,
            'gender' => 'required|string|in:M,F,O',
            'dob' => 'required|date',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip' => 'required|string|max:20',
            'phone' => 'required|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string',
            'status' => 'nullable|string',
            // Measurement fields
            'height_feet' => 'nullable|numeric|min:0|max:8',
            'height_inches' => 'nullable|numeric|min:0|max:11',
            'weight' => 'nullable|numeric|min:0|max:999',
        ]);

        // Update the name field (combination of first and last name)
        $validated['name'] = $validated['fname'] . ' ' . $validated['lname'];

        $patient->update($validated);

        // Handle measurements if provided
        if ($request->filled(['height_feet', 'height_inches', 'weight'])) {
            // Use the measurement service to update measurements
            $this->measurementService->updateUserMeasurements($patient, [
                'height_ft' => $request->input('height_feet'),
                'height_in' => $request->input('height_inches'),
                'weight' => $request->input('weight')
            ]);
        }

        Toast::success('Patient updated successfully.');

        // Redirect based on user role
        if (auth()->user()->hasRole('agent')) {
            Toast::success('Patient information updated successfully.');
            return redirect()->route('agent.enrollments');
        }

        return redirect()->route('admin.patients.show', $patient);
    }

    public function destroy(User $patient)
    {
        $patient->delete();

        Toast::success('Patient deleted successfully.');

        return redirect()->route('admin.patients.index');
    }
}
