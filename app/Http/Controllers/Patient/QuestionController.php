<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\Question;
use App\Models\Service;
use Illuminate\Http\Request;

class QuestionController extends Controller
{
    public function show(Request $request, $serviceId, $questionId)
    {
        $service = Service::findOrFail($serviceId);
        $question = Question::where('service_id', $serviceId)
            ->where('id', $questionId)
            ->with('options')
            ->firstOrFail();

        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);

        $previousQuestion = $this->getPreviousQuestion($serviceId, $question, $userAnswers);
        $nextQuestion = $this->getNextQuestion($serviceId, $question, $userAnswers);

        return view('patient.questions', compact('service', 'question', 'previousQuestion', 'nextQuestion', 'userAnswers'));
    }

    public function store(Request $request, $serviceId, $questionId)
    {
        $question = Question::findOrFail($questionId);

        $validatedData = $request->validate([
            'answer' => $question->required ? 'required' : 'nullable',
        ]);

        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);
        $userAnswers[$questionId] = $validatedData['answer'];
        $request->session()->put($sessionKey, $userAnswers);

        $nextQuestion = $this->getNextQuestion($serviceId, $question, $userAnswers);

        if ($nextQuestion) {
            return redirect()->route('questions.show', [$serviceId, $nextQuestion->id]);
        } else {
            return redirect()->route('questions.summary', $serviceId);
        }
    }

    public function summary(Request $request, $serviceId)
    {
        $service = Service::findOrFail($serviceId);
        $sessionKey = "service_{$serviceId}_answers";
        $userAnswers = $request->session()->get($sessionKey, []);

        $questions = Question::where('service_id', $serviceId)->orderBy('order')->get();

        return view('patient.summary', compact('service', 'questions', 'userAnswers'));
    }

    private function getPreviousQuestion($serviceId, $currentQuestion, $userAnswers)
    {
        $previousQuestions = Question::where('service_id', $serviceId)
            ->where('order', '<', $currentQuestion->order)
            ->orderBy('order', 'desc')
            ->get();

        foreach ($previousQuestions as $question) {
            if ($question->parent_question_id === null) {
                return $question;
            }

            $parentAnswer = $userAnswers[$question->parent_question_id] ?? null;
            if ($parentAnswer === $question->parent_answer_value) {
                return $question;
            }
        }

        return null;
    }

    private function getNextQuestion($serviceId, $currentQuestion, $userAnswers)
    {
        $nextQuestions = Question::where('service_id', $serviceId)
            ->where('order', '>', $currentQuestion->order)
            ->orderBy('order')
            ->get();

        foreach ($nextQuestions as $question) {
            if ($question->parent_question_id === null) {
                return $question;
            }

            $parentAnswer = $userAnswers[$question->parent_question_id] ?? null;
            if ($parentAnswer === $question->parent_answer_value) {
                return $question;
            }
        }

        return null;
    }

    public function general($serviceId)
    {
        return view('front.general-questions', [
            'serviceId' => $serviceId
        ]);
    }
}