<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\AdditionalInformation;
use App\Models\AdverseReaction;
use App\Models\Allergy;
use App\Models\DiagnosticTest;
use App\Models\FamilyMedicalHistory;
use App\Models\HealthQuestion;
use App\Models\LifestyleHabit;
use App\Models\MedicalCondition;
use App\Models\MedicalSurgicalHistory;
use App\Models\MedicationScreening;
use App\Models\MentalHealthAssessment;
use App\Models\OfflineConversion;
use App\Models\PainAssessment;
use App\Models\PhysicalExaminationIndicator;
use App\Models\PsychologicalSocialFactor;
use App\Models\SubscriptionPlan;
use App\Models\User;
use App\Models\UserReportedMedication;
use App\Models\UserService;
use Faker\Factory as Faker;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;
use ProtoneMedia\Splade\Facades\Toast;

class RegisterController extends Controller
{
    public function create(Request $request, $serviceId)
    {
        $fakeData = ['plan' => '1'];
        if (app()->environment('local', 'testing')) {
            // Generate fake data
            $faker = Faker::create();
            // Generate fake data for the form fields
            $fakeData = [
                'fname' => $faker->firstName,
                'lname' => $faker->lastName,
                'email' => $faker->safeEmail,
                'password' => 'password',
                'password_confirmation' => 'password',
                'gender' => $faker->randomElement(['M', 'F', 'O']),
                'dob' => $faker->date('Y-m-d', '-18 years'),
                'address1' => $faker->streetAddress,
                'address2' => $faker->secondaryAddress,
                'city' => $faker->city,
                'state' => $faker->stateAbbr,
                'zip' => $faker->postcode,
                'bio' => $faker->paragraph,
                'phone' => $faker->phoneNumber,
                'mobile_phone' => $faker->phoneNumber,
                'plan' => '1',
            ];
        }

        if ($request->session()->has('visitor')) {
            $visitor = $request->session()->get('visitor');
            $fakeData['fname'] = $visitor->fname;
            $fakeData['lname'] = $visitor->lname;
            $fakeData['email'] = $visitor->email;
            $fakeData['address1'] = $visitor->address;
            $fakeData['city'] = $visitor->city;
            $fakeData['state'] = $visitor->state;
            $fakeData['zip'] = $visitor->zip;
            $fakeData['phone'] = $visitor->phone;
        }

        if ($request->session()->has('landing-page3') && $request->session()->get('landing-page3')) {
            $fakeData['plan'] = '6';
        }

        $subscriptionPlans = SubscriptionPlan::query()
            ->where('status', 'active')
            ->when($request->session()->has('landing-page3') && $request->session()->get('landing-page3'), function ($query) {
                return $query->orWhere('id', 6);
            })
            ->get();
        return view('patient.register', compact('fakeData', 'serviceId', 'subscriptionPlans'));
    }

    public function store(Request $request, $serviceId)
    {
        $request->validate([
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'gender' => ['required', 'string', 'in:M,F,O'],
            'dob' => ['required', 'date'],
            'address1' => ['required', 'string', 'max:255'],
            'address2' => ['nullable', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state' => ['required', 'string', 'max:255'],
            'zip' => ['required', 'string', 'max:20'],
            'bio' => ['nullable', 'string'],
            'image' => ['nullable', 'image', 'max:2048'], // 2MB Max
            'phone' => ['required', 'string', 'max:20'],
            'mobile_phone' => ['nullable', 'string', 'max:20'],
            'plan' => ['required', 'exists:subscription_plans,id'],
        ]);

        DB::beginTransaction();

        try {
            // Get referring agent ID from cookie or session
            $referringAgentId = $request->cookie('referring_agent_id') ??
                                $request->session()->get('referring_agent_id');

            $user = User::create([
                'name' => $request->fname . ' ' . $request->lname,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'fname' => $request->fname,
                'lname' => $request->lname,
                'gender' => $request->gender,
                'dob' => $request->dob,
                'address1' => $request->address1,
                'address2' => $request->address2,
                'city' => $request->city,
                'state' => $request->state,
                'zip' => $request->zip,
                'bio' => $request->bio,
                'phone' => $request->phone,
                'mobile_phone' => $request->mobile_phone,
                'referring_agent_id' => $referringAgentId,
            ]);

            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('profile_images', 'public');
                $user->image = $imagePath;
                $user->save();
            }

            $user->assignRole('patient');

            $sessionKey = "service_{$serviceId}_answers";
            $userAnswers = $request->session()->get($sessionKey, []);

            $request->session()->put('selected_plan_id', $request->plan);

            DB::commit();

            event(new Registered($user));

            Auth::login($user);

            info('Saving UserService - start');
            $userService = $this->saveUserService($user, $serviceId);
            info('Saving UserService - end');

            // Save the answers to the database
            if ($userAnswers) {
                info('Saving UserAnswer - start');
                $this->saveUserAnswers($userService, $userAnswers);
                info('Saving UserAnswer - end');
            }

            // Save medical questions
            if ($request->session()->has('medical_questionnaire')) {
                $this->saveQuestionnaireData();
            }

            if ($request->session()->has('pain_treatment_form')) {
                info('Saving PainTreatment - start');
                $this->savePainTreatment($request);
                info('Saving PainTreatment - end');
            }

            // $userService = UserService::firstOrCreate(
            //     ['user_id' => auth()->id(), 'service_id' => $serviceId],
            //     ['status' => 'in_progress']
            // );

            // foreach ($userAnswers as $questionId => $answer) {
            //     $userService->answers()->updateOrCreate(
            //         ['question_id' => $questionId],
            //         ['answer' => $answer]
            //     );
            // }

            // Clear the session
            $request->session()->forget($sessionKey);

            if ($request->session()->has('visitor')) {
                $visitor = $request->session()->get('visitor');
                $visitor->delete();
                $request->session()->forget('visitor');
            }

            if ($request->plan == "6") {
                // Prepare conversion data
                $conversionData = [
                    'gclid' => $request->cookie('gclid') ?? null,
                    'conversion_name' => 'purchase',
                    'conversion_value' => 0,
                    'plan_id' => $request->plan,
                    'transaction_id' => 'freetrial',
                    'user_id' => $user->id
                ];

                OfflineConversion::create($conversionData);

                return to_route('patient.free-promo');
            }

            return redirect()->route('patient.payment');
        } catch (\Exception $e) {
            report($e);
            DB::rollback();
            Toast::info('Registration failed. Please try again.')->autoDismiss(10);
            return back();
        }
    }

    private function saveUserService(User $user, $serviceId)
    {
        $userService = UserService::firstOrCreate(
            ['user_id' => $user->id, 'service_id' => $serviceId],
            ['status' => 'in_progress']
        );

        return $userService;
    }

    private function saveUserAnswers($userService, $answers)
    {
        foreach ($answers as $questionId => $answer) {
            $userService->answers()->updateOrCreate(
                ['question_id' => $questionId],
                ['answer' => $answer]
            );
        }
    }

    public function saveQuestionnaireData()
    {
        $user = auth()->user();
        $questionnaireData = session('medical_questionnaire');
        Log::info('Questionnaire Data: ' . auth()->id(), $questionnaireData);

        if (!$user || !$questionnaireData) {
            return response()->json([
                'message' => 'User not logged in or questionnaire data not found',
            ], 400);
        }

        try {
            DB::transaction(function () use ($user, $questionnaireData) {
                // Handle user-reported medications
                if ($questionnaireData['has_current_medications']) {
                    foreach ($questionnaireData['current_medications'] as $medication) {
                        UserReportedMedication::create([
                            'user_id' => $user->id,
                            'medication_name' => $medication['name'],
                            'dosage' => $medication['dosage'],
                            'frequency' => $medication['frequency']
                        ]);
                    }
                }

                $user->createPreferredMedications($questionnaireData);

                // Handle allergies
                if ($questionnaireData['has_allergies']) {
                    $allergies = explode(',', $questionnaireData['allergies']);
                    foreach ($allergies as $allergen) {
                        Allergy::create([
                            'user_id' => $user->id,
                            'allergen' => trim($allergen),
                            'reaction' => 'Not specified'
                        ]);
                    }
                }

                // Handle other health information
                HealthQuestion::create([
                    'user_id' => $user->id,
                    'has_chronic_conditions' => $questionnaireData['has_chronic_conditions'],
                    'chronic_conditions' => $questionnaireData['chronic_conditions'],
                    'uses_tobacco_alcohol_drugs' => $questionnaireData['uses_tobacco_alcohol_drugs'],
                    'substance_use_frequency' => $questionnaireData['substance_use_frequency'],
                    'is_pregnant' => $questionnaireData['is_pregnant'],
                    'had_recent_surgeries' => $questionnaireData['had_recent_surgeries'],
                    'recent_surgeries_details' => $questionnaireData['recent_surgeries_details'],
                    'has_health_concerns' => $questionnaireData['has_health_concerns'],
                    'health_concerns_details' => $questionnaireData['health_concerns_details'],
                ]);
            });

            // Clear the session data after successful save
            session()->forget('medical_questionnaire');

            return response()->json([
                'message' => 'Medical questionnaire data saved successfully',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while saving the questionnaire data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    protected function savePainTreatment(Request $request)
    {
        info('Retrieving PainTreatmentData from session');
        $painTreatmentData = $request->session()->get('pain_treatment_form');

        if (!$painTreatmentData) {
            return;
        }

        $user = $request->user();

        // Save PainAssessment
        info('Saving PainAssessment - start');
        $painAssessment = new PainAssessment([
            'pain_type' => implode(', ', $painTreatmentData['pain_type']),
            'pain_type_other' => $painTreatmentData['pain_type_other'] ?? null,
            'pain_location' => implode(', ', $painTreatmentData['pain_location']),
            'pain_location_other' => $painTreatmentData['pain_location_other'] ?? null,
            'pain_intensity' => $painTreatmentData['pain_intensity'],
            'pain_duration' => $painTreatmentData['pain_duration'],
            'pain_start' => $painTreatmentData['pain_start'],
            'pain_frequency' => $painTreatmentData['pain_frequency'],
            'pain_triggers' => implode(', ', $painTreatmentData['pain_triggers']),
            'pain_triggers_other' => $painTreatmentData['pain_triggers_other'] ?? null,
            'pain_relief' => implode(', ', $painTreatmentData['pain_relief']),
            'pain_relief_other' => $painTreatmentData['pain_relief_other'] ?? null,
        ]);
        $user->painAssessment()->save($painAssessment);
        info('Saving PainAssessment - end');

        // Save MedicalCondition
        info('Saving MedicalCondition - start');
        foreach ($painTreatmentData['conditions'] as $condition) {
            MedicalCondition::create([
                'patient_id' => auth()->id(),
                'condition_name' => $condition,
            ]);
        }
        if ($painTreatmentData['conditions_other']) {
            MedicalCondition::create([
                'patient_id' => auth()->id(),
                'condition_name' => $painTreatmentData['conditions_other'],
            ]);
        }
        info('Saving MedicalCondition - end');

        if ($painTreatmentData['allergies'] == 'Yes') {
            info('Saving Allergy - start');
            Allergy::create([
                'user_id' =>  auth()->id(),
                'allergen' => $painTreatmentData['allergy_details'],
            ]);
            info('Saving Allergy - end');
        }

        info('Saving UserReportedMedication - start');
        $userReportedMedication = new UserReportedMedication([
            'medication_name' => $painTreatmentData['current_medications'] ?? "",
            'side_effects' => $painTreatmentData['current_side_effects'] == 'Yes' ? $painTreatmentData['side_effects_details'] : null,
        ]);
        $urm = $user->userReportedMedication()->save($userReportedMedication);
        info('Saving UserReportedMedication - end');

        if ($painTreatmentData['adverse_reactions'] === 'Yes') {
            info('Saving AdverseReaction - start');
            $adverseReaction = new AdverseReaction([
                'patient_id' => auth()->id(),
                'reaction_details' => $painTreatmentData['adverse_reactions_details'],
            ]);
            $urm->adverseReaction()->save($adverseReaction);
            info('Saving AdverseReaction - end');
        }

        // Save LifestyleHabit
        info('Saving LifestyleHabit - start');
        $lifestyleHabit = new LifestyleHabit([
            'alcohol_use' => $painTreatmentData['alcohol_use'] === 'Yes',
            'alcohol_details' => $painTreatmentData['alcohol_details'] ?? null,
            'tobacco_use' => $painTreatmentData['tobacco_use'] === 'Yes',
            'tobacco_details' => $painTreatmentData['tobacco_details'] ?? null,
            'drug_use' => $painTreatmentData['drug_use'] === 'Yes',
            'drug_details' => $painTreatmentData['drug_details'] ?? null,
            'exercise_frequency' => $painTreatmentData['exercise_frequency'],
        ]);
        $user->lifestyleHabit()->save($lifestyleHabit);
        info('Saving LifestyleHabit - end');

        // Save MentalHealthAssessment
        info('Saving MentalHealthAssessment - start');
        $mentalHealthAssessment = new MentalHealthAssessment([
            'mental_health_conditions' => $painTreatmentData['mental_health_conditions'] === 'Yes',
            'mental_health_details' => $painTreatmentData['mental_health_details'] ?? null,
            'suicidal_thoughts' => $painTreatmentData['suicidal_thoughts'] === 'Yes',
            'receiving_therapy' => $painTreatmentData['receiving_therapy'] === 'Yes',
            'benefit_from_counseling' => $painTreatmentData['benefit_from_counseling'] === 'Yes',
            'worried_about_counseling' => $painTreatmentData['worried_about_counseling'] === 'Yes',
        ]);
        $user->mentalHealthAssessment()->save($mentalHealthAssessment);
        info('Saving MentalHealthAssessment - end');

        // Save FamilyMedicalHistory
        info('Saving FamilyMedicalHistory - start');
        $familyMedicalHistory = new FamilyMedicalHistory([
            'chronic_pain' => $painTreatmentData['family_chronic_pain'] === 'Yes',
            'chronic_pain_details' => $painTreatmentData['family_chronic_pain_details'] ?? null,
        ]);
        $user->familyMedicalHistory()->save($familyMedicalHistory);
        info('Saving FamilyMedicalHistory - end');

        info('Saving FamilyMedicalCondition - start');
        foreach ($painTreatmentData['family_conditions'] as $condition) {
            if ($condition == 'Other') {
                $condition = $painTreatmentData['family_conditions_other'];
            }

            $user->familyMedicalHistory->familyMedicalConditions()->create([
                'name' => $condition,
            ]);
            info('Condition: ' . $condition);
        }
        info('Saving FamilyMedicalCondition - end');

        // Save AdditionalInformation
        if (in_array('Other', $painTreatmentData['associated_symptoms'])) {
            // Find the index of 'Other' in the array
            $otherIndex = array_search('Other', $painTreatmentData['associated_symptoms']);

            // Replace 'Other' with the combined string
            $painTreatmentData['associated_symptoms'][$otherIndex] = 'Other - ' . $painTreatmentData['associated_symptoms_other'];
        }

        info('Saving AdditionalInformation - start');
        $additionalInformation = new AdditionalInformation([
            'additional_concerns' => $painTreatmentData['additional_concerns'] ?? null,
            'daily_activities_impact' => $painTreatmentData['daily_activities_impact'],
            'sleep_impact' => $painTreatmentData['sleep_impact'],
            'mobility_impact' => $painTreatmentData['mobility_impact'],
            'emotional_impact' => $painTreatmentData['emotional_impact'],
            'associated_symptoms' => implode(', ', $painTreatmentData['associated_symptoms']),
            'systemic_symptoms' => $painTreatmentData['systemic_symptoms'] === 'Yes',
        ]);
        $user->additionalInformation()->save($additionalInformation);
        info('Saving AdditionalInformation - end');

        // Save MedicalSurgicalHistory
        info('Saving MedicalSurgicalHistory - start');
        $medicalSurgicalHistory = new MedicalSurgicalHistory([
            'past_injuries' => $painTreatmentData['past_injuries'] === 'Yes',
            'past_injuries_details' => $painTreatmentData['past_injuries_details'] ?? null,
            'surgery' => $painTreatmentData['surgeries'] === 'Yes',
            'surgery_details' => $painTreatmentData['surgery_details'] ?? null,
            'chronic_conditions_details' => $painTreatmentData['chronic_conditions_details'] ?? null,
        ]);
        $user->medicalSurgicalHistory()->save($medicalSurgicalHistory);
        info('Saving MedicalSurgicalHistory - end');

        // Save PsychologicalSocialFactor
        info('Saving PsychologicalSocialFactor - start');
        $psychologicalSocialFactor = new PsychologicalSocialFactor([
            'stress_levels' => $painTreatmentData['stress_levels'],
            'support_system' => $painTreatmentData['support_system'] === 'Yes',
            'work_environment' => $painTreatmentData['work_environment'] === 'Yes',
            'mental_health_changes' => $painTreatmentData['mental_health_changes'],
        ]);
        $user->psychologicalSocialFactors()->save($psychologicalSocialFactor);
        info('Saving PsychologicalSocialFactor - end');

        // Save PhysicalExaminationIndicator
        info('Saving PhysicalExaminationIndicator - start');
        $physicalExaminationIndicator = new PhysicalExaminationIndicator([
            'tenderness' => $painTreatmentData['tenderness'] === 'Yes',
            'difficulty_moving' => $painTreatmentData['difficulty_moving'] === 'Yes',
            'reduced_activity' => $painTreatmentData['reduced_activity'] === 'Yes',
        ]);
        $user->physicalExaminationIndicators()->save($physicalExaminationIndicator);
        info('Saving PhysicalExaminationIndicator - end');

        // Save DiagnosticTest
        if (!empty($painTreatmentData['diagnostic_tests'])) {
            info('Saving DiagnosticTest - start');
            $diagnosticTest = new DiagnosticTest([
                'details' => $painTreatmentData['diagnostic_tests'],
                'physiotherapy_details' => $painTreatmentData['physiotherapy_details'],
            ]);
            $user->diagnosticTests()->save($diagnosticTest);
            info('Saving DiagnosticTest - end');
        }

        // Save MedicationScreening
        info('Saving MedicationScreening - start');
        $medicationScreening = new MedicationScreening();
        foreach ($painTreatmentData['medication_screening'] as $key => $ms) {
            $medicationScreening->{$key} = $ms === 'Yes';
        }
        $user->medicationScreening()->save($medicationScreening);
        info('Saving MedicationScreening - end');

        // Clear the session data
        $request->session()->forget('pain_treatment_form');
    }
}
