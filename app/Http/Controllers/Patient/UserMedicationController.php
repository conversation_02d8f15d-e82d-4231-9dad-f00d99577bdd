<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\UserReportedMedication;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class UserMedicationController extends Controller
{
    public function showForm(UserReportedMedication $medication = null)
    {
        return view('patient.medications.create-or-update', [
            'medication' => $medication
        ]);
    }

    public function createOrUpdate(Request $request)
    {
        $validated = $request->validate([
            'id' => 'nullable|exists:user_reported_medications,id', // Allow existing ID for updates
            'medication_name' => 'required|string|max:255',
            'dosage' => 'nullable|string|max:255',
            'frequency' => 'nullable|string|max:255',
            'reaction' => 'nullable|string|max:255',
            'side_effects' => 'nullable|string|max:1000',
        ]);

        $validated['user_id'] = auth()->user()->id;

        // Find or create based on ID
        $medication = UserReportedMedication::updateOrCreate(
            ['id' => $validated['id']], // Find by ID if provided
            $validated // Data to create or update
        );

        Toast::success('Medication information saved successfully')->autoDismiss(3);

        return to_route('patient.dashboard');
    }

    public function destroy(UserReportedMedication $medication)
    {
        // Ensure the user can only delete their own medications
        if ($medication->user_id !== auth()->id()) {
            Toast::warning('You do not have permission to delete this medication.');
            return redirect()->route('patient.dashboard');
        }

        $medication->delete();
        Toast::success('Medication removed successfully')->autoDismiss(3);

        return to_route('patient.dashboard');
    }
}
