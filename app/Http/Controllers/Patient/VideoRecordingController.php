<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Models\UserService;
use App\Models\VideoRecording;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class VideoRecordingController extends Controller
{
    public function show(Request $request)
    {
        // Get the latest user service that has been paid
        $userService = UserService::where('user_id', $request->user()->id)
                                  ->where('status', 'paid')
                                  ->latest()
                                  ->first();

        return view('patient.video-recording', compact('userService'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'video' => [
                'required',
                'file',
                'mimes:mp4,mov,webm,mkv',  // Common iPhone video formats
                'max:100000'
            ]
        ]);

        // $userService = UserService::where('user_id', $request->user()->id)
        //                           ->where('status', 'paid')
        //                           ->latest()
        //                           ->firstOrFail();

        if ($request->hasFile('video')) {
            $path = $request->file('video')->store('patient-videos', 'public');

            // Create video recording entry
            VideoRecording::create([
                'user_id' => $request->user()->id,
                'filename' => $request->file('video')->getClientOriginalName(),
                'path' => $path,
                'format' => $request->file('video')->getClientMimeType(),
                'duration' => null // You'll need ffmpeg or similar to get duration
            ]);
    
            // $userService->update(['status' => 'under_review']);
    
            Toast::success('Video uploaded successfully. Your case is now under review.')->autoDismiss();
            return redirect()->route('patient.dashboard');
        }
    
        Toast::danger('Failed to upload the video. Please try again.')->autoDismiss();
        return to_route('dashboard');
    }
}
