<?php

namespace App\Http\Controllers;

use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\PaymentMethodService;
use App\Services\PaymentMethodTransitionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentMethodController extends Controller
{
    public function __construct(
        private PaymentMethodService $paymentMethodService,
        private PaymentMethodTransitionService $transitionService
    ) {}

    /**
     * Display a listing of the payment methods.
     *
     * @param Request $request
     * @param User|null $user
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, User $user = null)
    {
        $user ??= $request->user();
        $role = $request->user()->roles->first()->name ?? 'patient';

        return view('payment-methods.index', compact('user', 'role'));
    }

    /**
     * Get all payment methods for a user
     *
     * @param Request $request
     * @param User|null $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentMethods(Request $request, User $user = null)
    {
        $user ??= $request->user();

        try {
            // Use the transition service to get payment methods from both tables
            $paymentMethods = $this->transitionService->getPaymentMethodsForUser($user);

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting payment methods: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment methods: ' . $e->getMessage(),
                'payment_methods' => []
            ], 500);
        }
    }

    /**
     * Store a new credit card payment method
     *
     * @param Request $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeCreditCard(Request $request, User $user)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to add payment methods for this user')->autoDismiss(3);
            return back();
        }

        $request->validate([
            'card_number' => 'required|string|min:13|max:19',
            'expiration_month' => 'required|string|size:2',
            'expiration_year' => 'required|string|size:2',
            'cvv' => 'required|string|min:3|max:4',
        ]);

        // Add credit card payment method
        $result = $this->paymentMethodService->addCreditCardPaymentMethod(
            $user,
            $request->card_number,
            $request->expiration_month,
            $request->expiration_year,
            $request->cvv
        );

        if (!$result['success']) {
            Toast::danger($result['message'])->autoDismiss(3);
            return back();
        }

        Toast::success($result['message'])->autoDismiss(3);

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'payment_method' => [
                'id' => $result['payment_method']->id,
                'type' => $result['payment_method']->type,
                'display_name' => $result['payment_method']->getDisplayName(),
                'is_default' => (bool) $result['payment_method']->is_default,
                'details' => $this->getPaymentMethodDetails($result['payment_method']),
            ]
        ]);
    }

    /**
     * Store a new ACH payment method
     *
     * @param Request $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeAch(Request $request, User $user)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to add payment methods for this user')->autoDismiss(3);
            return back();
        }

        $request->validate([
            'account_name' => 'required|string|max:255',
            'account_type' => 'required|string|in:checking,savings',
            'routing_number' => 'required|string|size:9',
            'account_number' => 'required|string|min:4|max:17',
            'bank_name' => 'required|string|max:255',
        ]);

        // Add ACH payment method
        $result = $this->paymentMethodService->addAchPaymentMethod(
            $user,
            $request->account_name,
            $request->account_type,
            $request->routing_number,
            $request->account_number,
            $request->bank_name
        );

        if (!$result['success']) {
            Toast::danger($result['message'])->autoDismiss(3);
            return back();
        }

        Toast::success($result['message'])->autoDismiss(3);

        return back();
    }

    /**
     * Store a new invoice payment method
     *
     * @param Request $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeInvoice(Request $request, User $user)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to add payment methods for this user')->autoDismiss(3);
            return back();
        }

        $request->validate([
            'email' => 'required|email|max:255',
            'company_name' => 'required|string|max:255',
            'contact_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'billing_address' => 'required|string|max:1000',
            'payment_terms' => 'nullable|string|in:net15', // ,net30,net45,net60,net90',
        ]);

        // Add invoice payment method
        $result = $this->paymentMethodService->addInvoicePaymentMethod(
            $user,
            $request->all()
        );

        if (!$result['success']) {
            Toast::danger($result['message'])->autoDismiss(3);
            return back();
        }

        Toast::success($result['message'])->autoDismiss(3);

        return back();
    }

    /**
     * Set a payment method as default
     *
     * @param Request $request
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault(Request $request, User $user, PaymentMethod $paymentMethod)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to modify payment methods for this user')->autoDismiss(3);
            return back();
        }

        // Check if this is for a specific service
        $service = $request->input('service');
        if ($service) {
            // Set as preferred for this service
            $paymentMethod->setPreferredForService($service);

            // Remove preferred status from other payment methods for this service
            $user->paymentMethods()
                ->where('id', '!=', $paymentMethod->id)
                ->get()
                ->each(function ($method) use ($service) {
                    if ($method->isPreferredForService($service)) {
                        $method->setPreferredForService($service, false);
                    }
                });

            Toast::success('Payment method set as preferred for ' . $service)->autoDismiss(3);
            return back();
        }

        // Set payment method as default
        $result = $this->paymentMethodService->setDefaultPaymentMethod($user, $paymentMethod);

        Toast::success($result['message'])->autoDismiss(3);
        return back();
    }

    /**
     * Get preferred payment method for a service
     *
     * @param Request $request
     * @param User $user
     * @param string $service
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPreferredForService(Request $request, User $user, string $service)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to view payment methods for this user')->autoDismiss(3);
            return back();
        }

        // Find preferred payment method for this service
        $preferredMethod = $user->paymentMethods()
            ->get()
            ->first(function ($method) use ($service) {
                return $method->isPreferredForService($service);
            });

        // If no preferred method is found, use the default payment method
        if (!$preferredMethod) {
            $preferredMethod = $user->defaultPaymentMethod();
        }

        Toast::success('Preferred payment method found')->autoDismiss(3);

        return back();
    }

    /**
     * Remove a payment method
     *
     * @param Request $request
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, User $user, PaymentMethod $paymentMethod)
    {
        // Check permissions
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser)) &&
            !($authUser->hasRole('business_hr') && $this->isBusinessHrForUser($user, $authUser))) {
            Toast::danger('You do not have permission to delete payment methods for this user')->autoDismiss(3);
            return back();
        }

        // Remove payment method
        $result = $this->paymentMethodService->removePaymentMethod($user, $paymentMethod);

        if ($result['success']) {
            Toast::success($result['message'])->autoDismiss(3);
        } else {
            Toast::danger($result['message'])->autoDismiss(3);
        }

        $paymentMethods = $this->transitionService->getPaymentMethodsForUser($user);
        return response()->json(['payment_methods' => $paymentMethods]);
    }

    /**
     * Get payment method details based on type
     *
     * @param PaymentMethod $paymentMethod
     * @return array
     */
    private function getPaymentMethodDetails(PaymentMethod $paymentMethod): array
    {
        if ($paymentMethod->isCreditCard()) {
            return [
                'brand' => $paymentMethod->cc_brand,
                'last_four' => $paymentMethod->cc_last_four,
                'expiration_month' => $paymentMethod->cc_expiration_month,
                'expiration_year' => $paymentMethod->cc_expiration_year,
            ];
        } elseif ($paymentMethod->isAch()) {
            return [
                'account_name' => $paymentMethod->ach_account_name,
                'account_type' => $paymentMethod->ach_account_type,
                'routing_number_last_four' => $paymentMethod->ach_routing_number_last_four,
                'account_number_last_four' => $paymentMethod->ach_account_number_last_four,
            ];
        } elseif ($paymentMethod->isInvoice()) {
            return [
                'email' => $paymentMethod->invoice_email,
                'company_name' => $paymentMethod->invoice_company_name,
                'contact_name' => $paymentMethod->invoice_contact_name,
                'payment_terms' => $paymentMethod->invoice_payment_terms,
            ];
        }

        return [];
    }

    /**
     * Check if the authenticated user is an agent who enrolled the target user
     *
     * @param User $targetUser
     * @param User $agent
     * @return bool
     */
    private function isPatientEnrolledByAgent(User $targetUser, User $agent): bool
    {
        // Check direct referral (for patients)
        $directReferral = $targetUser->referring_agent_id === $agent->agent->id;

        // Check business referral (for business admins)
        $businessReferral = false;
        if ($targetUser->business_id) {
            $business = \App\Models\Business::find($targetUser->business_id);
            if ($business && $business->referring_agent_id === $agent->agent->id) {
                $businessReferral = true;
            }
        }

        // Check subscription referral
        $subscriptionReferral = $targetUser->subscriptions()
            ->where('agent_id', $agent->agent->id)
            ->exists();

        return $agent->hasRole('agent') && ($directReferral || $businessReferral || $subscriptionReferral);
    }

    /**
     * Check if the authenticated user is a business HR for the target user
     *
     * @param User $targetUser
     * @param User $businessHr
     * @return bool
     */
    private function isBusinessHrForUser(User $targetUser, User $businessHr): bool
    {
        return $businessHr->hasRole('business_hr') &&
               $targetUser->hasRole('business_admin') &&
               $businessHr->business_id === $targetUser->business_id;
    }
}
