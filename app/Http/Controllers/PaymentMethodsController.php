<?php

namespace App\Http\Controllers;

use App\Actions\PaymentMethods\AddPaymentMethod;
use App\Actions\PaymentMethods\GetPaymentMethods;
use App\Actions\PaymentMethods\RemovePaymentMethod;
use App\Actions\PaymentMethods\SetDefaultPaymentMethod;
use App\Http\Requests\CreditCardRequest;
use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentMethodsController extends Controller
{
    public function __construct(
        private AddPaymentMethod $addPaymentMethod,
        private GetPaymentMethods $getPaymentMethods,
        private RemovePaymentMethod $removePaymentMethod,
        private SetDefaultPaymentMethod $setDefaultPaymentMethod
    ) {}

    /**
     * Show payment methods for a user
     *
     * @param User $user The user whose payment methods to show
     * @return \Illuminate\View\View
     */
    public function index(User $user)
    {
        // Check permissions - only allow viewing if:
        // 1. The authenticated user is viewing their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            Toast::danger('You do not have permission to view this user\'s payment methods')->autoDismiss(10);
            return redirect()->back();
        }

        // This will be used as prefix
        $role = $authUser->getRoleNames()->first();

        // Get credit cards for the user using the action
        // $result = $this->getPaymentMethods->execute([
        //     'user' => $user
        // ]);

        // if (!$result['success']) {
        //     Toast::danger($result['message'])->autoDismiss(10);
        //     return redirect()->back();
        // }

        return view('payment-methods.index', [
            'user' => $user,
            'role' => $role,
        ]);
    }

    /**
     * Store a new credit card
     *
     * @param CreditCardRequest $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreditCardRequest $request, User $user)
    {
        // Check permissions - only allow adding if:
        // 1. The authenticated user is adding their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to add payment methods for this user'
            ], 403);
        }

        $request->validate([
            'card_number' => 'required|string|min:13|max:19',
            'expiration_month' => 'required|string|size:2',
            'expiration_year' => 'required|string|size:2',
            'cvv' => 'required|string|min:3|max:4',
        ]);

        // Use the AddPaymentMethod action
        $result = $this->addPaymentMethod->execute([
            'user' => $user,
            'card_number' => $request->card_number,
            'expiration_month' => $request->expiration_month,
            'expiration_year' => $request->expiration_year,
            'cvv' => $request->cvv
        ]);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        $card = $result['card'];

        // Handle both PaymentMethod and CreditCard models for backward compatibility
        if ($card instanceof PaymentMethod) {
            $responseData = [
                'success' => true,
                'message' => $result['message'],
                'card' => [
                    'id' => $card->id,
                    'brand' => $card->cc_brand,
                    'cc' => substr($card->cc_last_four, -4),
                    'expiration_month' => $card->cc_expiration_month,
                    'expiration_year' => $card->cc_expiration_year,
                    'is_default' => (bool) $card->is_default,
                ]
            ];
        } else {
            // Legacy CreditCard model
            $responseData = [
                'success' => true,
                'message' => $result['message'],
                'card' => [
                    'id' => $card->id,
                    'brand' => $card->brand,
                    'cc' => substr($card->last_four, -4),
                    'expiration_month' => $card->expiration_month,
                    'expiration_year' => $card->expiration_year,
                    'is_default' => (bool) $card->is_default,
                ]
            ];
        }

        return response()->json($responseData);
    }

    /**
     * Set a payment method as default (supports both PaymentMethod and CreditCard)
     *
     * @param User $user
     * @param PaymentMethod|CreditCard $paymentMethod
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault(User $user, $paymentMethod)
    {
        // Handle both PaymentMethod and CreditCard models
        if ($paymentMethod instanceof PaymentMethod) {
            return $this->setDefaultPaymentMethod($user, $paymentMethod);
        } elseif ($paymentMethod instanceof CreditCard) {
            return $this->setDefaultCc($user, $paymentMethod);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Invalid payment method type'
            ], 400);
        }
    }

    /**
     * Set a payment method as default
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefaultPaymentMethod(User $user, PaymentMethod $paymentMethod)
    {
        // Check permissions - only allow setting default if:
        // 1. The authenticated user is modifying their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to modify this user\'s payment methods'
            ], 403);
        }

        $result = $this->setDefaultPaymentMethod->execute([
            'user' => $user,
            'payment_method' => $paymentMethod
        ]);

        if ($result['success']) {
            Toast::success($result['message']);
        } else {
            Toast::error($result['message']);
        }

        return response()->json($result);
    }

    /**
     * Set a credit card as default
     *
     * @param User $user
     * @param CreditCard $creditCard
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefaultCc(User $user, CreditCard $creditCard)
    {
        // Check permissions - only allow setting default if:
        // 1. The authenticated user is modifying their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to modify payment methods for this user'
            ], 403);
        }

        // Use the SetDefaultPaymentMethod action
        $result = $this->setDefaultPaymentMethod->execute([
            'user' => $user,
            'credit_card' => $creditCard
        ]);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message']
        ], $result['success'] ? 200 : 500);
    }

    /**
     * Delete a payment method (supports both PaymentMethod and CreditCard)
     *
     * @param User $user
     * @param PaymentMethod|CreditCard $paymentMethod
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(User $user, $paymentMethod)
    {
        // Check permissions - only allow deleting if:
        // 1. The authenticated user is deleting their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete payment methods for this user'
            ], 403);
        }

        // Use the RemovePaymentMethod action
        $result = $this->removePaymentMethod->execute([
            'user' => $user,
            'payment_method' => $paymentMethod
        ]);

        if ($result['success']) {
            Toast::success($result['message'])->autoDismiss(3);
        } else {
            Toast::danger($result['message'])->autoDismiss(3);
        }

        return back();
    }

    /**
     * Delete a credit card (legacy method for backward compatibility)
     *
     * @param User $user
     * @param CreditCard $creditCard
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyCreditCard(User $user, CreditCard $creditCard)
    {
        return $this->destroy($user, $creditCard);
    }

    /**
     * Check if a user (patient or business admin) was enrolled by an agent
     *
     * @param User $user The user (patient or business admin)
     * @param User $agentUser The agent user
     * @return bool
     */
    private function isPatientEnrolledByAgent(User $user, User $agentUser)
    {
        // Get the agent model from the user
        $agent = $agentUser->agent;

        if (!$agent) {
            return false;
        }

        // Check if the user has any subscriptions with the agent's ID in the agent_id field
        // This works for both patients and business admins
        return $user->subscriptions()
            ->where('agent_id', $agent->id)
            ->exists();
    }

    public function getCreditCards(Request $request, User $user)
    {
        $user ??= $request->user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $this->getPaymentMethods->execute([
            'user' => $user
        ]);
    }

    /**
     * Store a new credit card for the current user (patient routes)
     */
    public function storeForCurrentUser(CreditCardRequest $request)
    {
        return $this->store($request, $request->user());
    }

    /**
     * Set a credit card as default for the current user (patient routes)
     */
    public function setDefaultForCurrentUser(Request $request, CreditCard $creditCard)
    {
        return $this->setDefault($request->user(), $creditCard);
    }

    /**
     * Delete a credit card for the current user (patient routes)
     */
    public function destroyForCurrentUser(Request $request, CreditCard $creditCard)
    {
        return $this->destroy($request->user(), $creditCard);
    }
}
