<?php

namespace App\Http\Controllers;

use App\Actions\PaymentMethods\AddPaymentMethod;
use App\Actions\PaymentMethods\GetPaymentMethods;
use App\Actions\PaymentMethods\RemovePaymentMethod;
use App\Actions\PaymentMethods\SetDefaultPaymentMethod;
use App\Http\Requests\CreditCardRequest;
use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentMethodsController extends Controller
{
    public function __construct(
        private AddPaymentMethod $addPaymentMethod,
        private GetPaymentMethods $getPaymentMethods,
        private RemovePaymentMethod $removePaymentMethod,
        private SetDefaultPaymentMethod $setDefaultPaymentMethod
    ) {}

    /**
     * Show payment methods for a user
     *
     * @param User $user The user whose payment methods to show
     * @return \Illuminate\View\View
     */
    public function index(User $user)
    {
        // Check permissions - only allow viewing if:
        // 1. The authenticated user is viewing their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            Toast::danger('You do not have permission to view this user\'s payment methods')->autoDismiss(10);
            return redirect()->back();
        }

        // This will be used as prefix
        $role = $authUser->getRoleNames()->first();

        // Get credit cards for the user using the action
        // $result = $this->getPaymentMethods->execute([
        //     'user' => $user
        // ]);

        // if (!$result['success']) {
        //     Toast::danger($result['message'])->autoDismiss(10);
        //     return redirect()->back();
        // }

        return view('payment-methods.index', [
            'user' => $user,
            'role' => $role,
        ]);
    }

    /**
     * Store a new credit card
     *
     * @param CreditCardRequest $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreditCardRequest $request, User $user)
    {
        // Check permissions - only allow adding if:
        // 1. The authenticated user is adding their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to add payment methods for this user'
            ], 403);
        }

        $request->validate([
            'card_number' => 'required|string|min:13|max:19',
            'expiration_month' => 'required|string|size:2',
            'expiration_year' => 'required|string|size:2',
            'cvv' => 'required|string|min:3|max:4',
        ]);

        // Use the AddPaymentMethod action
        $result = $this->addPaymentMethod->execute([
            'user' => $user,
            'card_number' => $request->card_number,
            'expiration_month' => $request->expiration_month,
            'expiration_year' => $request->expiration_year,
            'cvv' => $request->cvv
        ]);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        $card = $result['card'];
        $responseData = [
            'success' => true,
            'message' => $result['message'],
            'card' => [
                'id' => $card->id,
                'brand' => $card->brand,
                'cc' => substr($card->last_four, -4),
                'expiration_month' => $card->expiration_month,
                'expiration_year' => $card->expiration_year,
                'is_default' => (bool) $card->is_default,
            ]
        ];

        return response()->json($responseData);
    }

    /**
     * Set a credit card as default
     *
     * @param User $user
     * @param CreditCard $creditCard
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefaultCc(User $user, CreditCard $creditCard)
    {
        // Check permissions - only allow setting default if:
        // 1. The authenticated user is modifying their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to modify payment methods for this user'
            ], 403);
        }

        // Use the SetDefaultPaymentMethod action
        $result = $this->setDefaultPaymentMethod->execute([
            'user' => $user,
            'credit_card' => $creditCard
        ]);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message']
        ], $result['success'] ? 200 : 500);
    }

    /**
     * Delete a credit card
     *
     * @param User $user
     * @param CreditCard $creditCard
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(User $user, CreditCard $creditCard)
    {
        // Check permissions - only allow deleting if:
        // 1. The authenticated user is deleting their own payment methods
        // 2. The authenticated user is an admin
        // 3. The authenticated user is an agent and the target user is a patient or business admin they enrolled
        $authUser = Auth::user();

        if ($authUser->id !== $user->id &&
            !$authUser->hasRole('admin') &&
            !($authUser->hasRole('agent') && $this->isPatientEnrolledByAgent($user, $authUser))) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to delete payment methods for this user'
            ], 403);
        }

        // Use the RemovePaymentMethod action
        $result = $this->removePaymentMethod->execute([
            'user' => $user,
            'credit_card' => $creditCard
        ]);

        if ($result['success']) {
            Toast::success($result['message'])->autoDismiss(3);
        } else {
            Toast::danger($result['message'])->autoDismiss(3);
        }

        return back();
    }

    /**
     * Check if a user (patient or business admin) was enrolled by an agent
     *
     * @param User $user The user (patient or business admin)
     * @param User $agentUser The agent user
     * @return bool
     */
    private function isPatientEnrolledByAgent(User $user, User $agentUser)
    {
        // Get the agent model from the user
        $agent = $agentUser->agent;

        if (!$agent) {
            return false;
        }

        // Check if the user has any subscriptions with the agent's ID in the agent_id field
        // This works for both patients and business admins
        return $user->subscriptions()
            ->where('agent_id', $agent->id)
            ->exists();
    }

    public function getCreditCards(Request $request, User $user)
    {
        $user ??= $request->user();

        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $this->getPaymentMethods->execute([
            'user' => $user
        ]);
    }
}
