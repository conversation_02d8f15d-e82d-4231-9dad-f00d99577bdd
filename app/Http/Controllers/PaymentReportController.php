<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\PaymentReportService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentReportController extends Controller
{
    protected $paymentReportService;

    public function __construct(PaymentReportService $paymentReportService)
    {
        $this->paymentReportService = $paymentReportService;
    }

    /**
     * Display the payment reports dashboard.
     */
    public function index(Request $request)
    {
        // Check if user has permission to view reports
        if (!Auth::user()->hasRole(['admin', 'agent', 'business_admin'])) {
            Toast::danger('You do not have permission to view payment reports.');
            return redirect()->back();
        }

        // Default to current month if no dates provided
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        $paymentMethod = $request->input('payment_method');
        $status = $request->input('status');

        // Get payment method summary
        $methodSummary = $this->paymentReportService->getPaymentMethodSummary($startDate, $endDate);
        
        // Get payment status summary
        $statusSummary = $this->paymentReportService->getPaymentStatusSummary($startDate, $endDate);

        // Get transactions based on filters
        $filters = array_filter([
            'start_date' => $startDate,
            'end_date' => $endDate,
            'payment_method' => $paymentMethod,
            'status' => $status,
        ]);

        // For non-admin users, limit to their own transactions or related users
        if (!Auth::user()->hasRole('admin')) {
            if (Auth::user()->hasRole('business_admin')) {
                // Business admins can see transactions for their business
                $businessUsers = Auth::user()->business->users()->pluck('id')->toArray();
                $filters['user_ids'] = $businessUsers;
            } else {
                // Regular users only see their own transactions
                $filters['user_id'] = Auth::id();
            }
        }

        $transactions = $this->paymentReportService->getFilteredTransactions($filters);

        return view('payment-reports.index', [
            'methodSummary' => $methodSummary,
            'statusSummary' => $statusSummary,
            'transactions' => $transactions,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'paymentMethod' => $paymentMethod,
            'status' => $status,
        ]);
    }

    /**
     * Generate and download a PDF report.
     */
    public function downloadPdf(Request $request)
    {
        // Check if user has permission to download reports
        if (!Auth::user()->hasRole(['admin', 'agent', 'business_admin'])) {
            Toast::danger('You do not have permission to download payment reports.');
            return redirect()->back();
        }

        // Get filters from request
        $filters = array_filter([
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'payment_method' => $request->input('payment_method'),
            'status' => $request->input('status'),
        ]);

        // For non-admin users, limit to their own transactions or related users
        if (!Auth::user()->hasRole('admin')) {
            if (Auth::user()->hasRole('business_admin')) {
                // Business admins can see transactions for their business
                $businessUsers = Auth::user()->business->users()->pluck('id')->toArray();
                $filters['user_ids'] = $businessUsers;
            } else {
                // Regular users only see their own transactions
                $filters['user_id'] = Auth::id();
            }
        }

        // Generate the PDF report
        $pdfPath = $this->paymentReportService->generatePaymentReportPdf($filters);

        // Return the file download
        return Storage::download('public/' . $pdfPath, 'payment_report.pdf');
    }

    /**
     * Display user-specific payment history.
     */
    public function userHistory(Request $request)
    {
        $user = Auth::user();
        
        // Default to last 3 months if no dates provided
        $startDate = $request->input('start_date', Carbon::now()->subMonths(3)->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        $paymentMethod = $request->input('payment_method');
        $status = $request->input('status');

        // Get user transactions
        $transactions = $this->paymentReportService->getUserTransactions(
            $user,
            $startDate,
            $endDate,
            $paymentMethod,
            $status
        );

        return view('payment-reports.user-history', [
            'transactions' => $transactions,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'paymentMethod' => $paymentMethod,
            'status' => $status,
        ]);
    }
}
