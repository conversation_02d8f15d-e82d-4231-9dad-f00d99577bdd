<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use App\Models\Inventory;
use App\Models\Medication;
use App\Models\Prescription;
use App\Tables\PendingPrescriptions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class DashboardController extends Controller
{
    public function index()
    {
        $lowStockInventory = Inventory::with('medication')
            ->select('medication_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('MIN(reorder_point) as reorder_point'),
                DB::raw('MIN(max_stock_level) as max_stock_level'))
            ->groupBy('medication_id')
            ->havingRaw('SUM(quantity) < MIN(reorder_point)')
            ->get();

        $expiringInventory = Inventory::with('medication')
            ->where('expiry_date', '<=', now()->addMonths(3))
            ->orderBy('expiry_date')
            ->take(10)
            ->get();

        $totalItems = Inventory::sum('quantity');
        $lowStockItems = Inventory::whereRaw('quantity <= reorder_point')->count();
        $expiringItems = Inventory::where('expiry_date', '<=', now()->addDays(30))->count();
        $totalValue = Inventory::join('medications', 'inventories.medication_id', '=', 'medications.id')
                            ->selectRaw('SUM(inventories.quantity * medications.unit_price) as total_value')
                            ->first()->total_value;

        $medications = Medication::all();

        // Get recent medication orders
        $recentMedicationOrders = \App\Models\MedicationOrder::with(['patient', 'doctor'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent patient allergies
        $recentAllergies = \App\Models\Allergy::with(['user'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent medical conditions
        $recentMedicalConditions = \App\Models\MedicalCondition::with('patient')
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent consultations
        $recentConsultations = \App\Models\Consultation::with(['patient', 'doctor'])
            ->orderBy('scheduled_at', 'desc')
            ->take(5)
            ->get();

        return view('pharmacy.dashboard', [
            'lowStockInventory' => SpladeTable::for($lowStockInventory)
                ->column('medication.name', 'Medication')
                ->column('total_quantity', 'Total Quantity')
                ->column('reorder_point', 'Reorder Point')
                ->column('max_stock_level', 'Max Stock Level'),
            'expiringInventory' => SpladeTable::for($expiringInventory)
                ->column('medication.name', 'Medication')
                ->column('quantity', 'Quantity')
                ->column('expiry_date', 'Expiry Date')
                ->column('batch_number', 'Batch Number'),
            'medications' => $medications,
            'pendingPrescriptions' => PendingPrescriptions::class,
            'totalItems' => $totalItems,
            'lowStockItems' => $lowStockItems,
            'expiringItems' => $expiringItems,
            'totalValue' => $totalValue,
            'recentMedicationOrders' => $recentMedicationOrders,
            'recentAllergies' => $recentAllergies,
            'recentMedicalConditions' => $recentMedicalConditions,
            'recentConsultations' => $recentConsultations,
        ]);
    }

    public function medicationLookup(Request $request)
    {
        $medication = Medication::findOrFail($request->medication_id);
        $inventories = $medication->inventories()->orderBy('expiry_date')->get();

        return view('pharmacy.medication-lookup', [
            'medication' => $medication,
            'inventories' => SpladeTable::for($inventories)
                ->column('quantity', 'Quantity')
                ->column('expiry_date', 'Expiry Date')
                ->column('batch_number', 'Batch Number')
                ->column('reorder_point', 'Reorder Point')
                ->column('max_stock_level', 'Max Stock Level'),
        ]);
    }

    public function prescriptionDetails(Prescription $prescription)
    {
        return view('pharmacy.prescription-details', [
            'prescription' => $prescription->load(['user', 'doctor', 'items.medication'])
        ]);
    }

    public function verifyPrescription(Request $request, Prescription $prescription)
    {
        $request->validate([
            'status' => 'required',
            'notes' => 'nullable|string',
        ]);

        $prescription->update([
            'status' => $request->status,
            'notes' => $request->notes,
            'pharmacist_id' => auth()->id(),
        ]);

        Toast::success('Updated')->autoDismiss(10);

        return redirect()->route('pharmacy.dashboard')->with('success', 'Prescription verified successfully.');
    }

    public function inventory()
    {
        $totalItems = Inventory::sum('quantity');
        $lowStockItems = Inventory::whereRaw('quantity <= reorder_point')->count();
        $expiringItems = Inventory::where('expiry_date', '<=', now()->addDays(30))->count();
        $totalValue = Inventory::join('medications', 'inventories.medication_id', '=', 'medications.id')
                            ->selectRaw('SUM(inventories.quantity * medications.unit_price) as total_value')
                            ->first()->total_value;

        return view('dashboard.inventory', compact('totalItems', 'lowStockItems', 'expiringItems', 'totalValue'));
    }
}
