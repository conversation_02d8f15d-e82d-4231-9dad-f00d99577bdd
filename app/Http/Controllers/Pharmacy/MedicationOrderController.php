<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use App\Models\MedicationOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class MedicationOrderController extends Controller
{
    /**
     * Display a listing of all medication orders.
     */
    public function index()
    {
        $orders = SpladeTable::for(MedicationOrder::query())
            ->column('id', label: 'Order #')
            ->column('patient_id', label: 'Patient', as: fn ($patient_id, $order) => $order->patient ? $order->patient->name : 'Unknown')
            ->column('created_at', label: 'Order Date')
            ->column('status', label: 'Status')
            ->column('doctor_id', label: 'Doctor', as: fn ($doctor_id, $order) => $order->doctor ? $order->doctor->name : 'Not Assigned')
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc')
            ->rowLink(fn (MedicationOrder $order) => route('pharmacy.medication-orders.show', $order))
            ->selectFilter('status', MedicationOrder::STATUSES);

        return view('pharmacy.medication-orders.index', [
            'orders' => $orders,
        ]);
    }

    /**
     * Display the specified medication order.
     */
    public function show(MedicationOrder $medicationOrder)
    {
        $medicationOrder->load([
            'items.medication',
            'patient.allergies',
            'patient.healthQuestion',
            'patient.preferredMedications',
            'prescription.items',
            'doctor'
        ]);

        return view('pharmacy.medication-orders.show', [
            'order' => $medicationOrder,
        ]);
    }
}
