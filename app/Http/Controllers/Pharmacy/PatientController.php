<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\SpladeTable;

class PatientController extends Controller
{
    /**
     * Display a listing of all patients.
     */
    public function index()
    {
        $patients = SpladeTable::for(User::role('patient'))
            ->column('name', sortable: true)
            ->column('email', sortable: true)
            ->column('created_at', label: 'Registered', sortable: true)
            ->column('actions')
            ->paginate(10)
            ->withGlobalSearch()
            ->defaultSort('created_at', 'desc');

        return view('pharmacy.patients.index', [
            'patients' => $patients,
        ]);
    }

    /**
     * Display the specified patient.
     */
    public function show(User $patient)
    {
        // Load related data
        $patient->load([
            'allergies',
            'medicalConditions',
            'prescriptionsAsPatient',
            'medicationOrders',
            'medicalQuestionnaires',
            'userReportedMedication',
            'healthQuestion',
            'painAssessment',
            'preferredMedications'
        ]);

        return view('pharmacy.patients.show', [
            'patient' => $patient,
        ]);
    }
}
