<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use App\Traits\HandlesMedicalQuestionnaires;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

class PatientMedicalQuestionnaireController extends Controller
{
    use HandlesMedicalQuestionnaires;

    /**
     * Display a listing of all medical questionnaires.
     */
    public function index()
    {
        $questionnaires = MedicalQuestionnaire::with(['user', 'treatment'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('pharmacy.medical-questionnaires.index', [
            'questionnaires' => $questionnaires
        ]);
    }

    /**
     * Display the specified medical questionnaire.
     */
    public function show(User $patient, MedicalQuestionnaire $questionnaire)
    {
        // Check if the questionnaire belongs to the patient
        if ($questionnaire->user_id !== $patient->id) {
            Toast::danger('This questionnaire does not belong to the specified patient.')->autoDismiss(3);
            return redirect()->back();
        }

        // Load related data
        $questionnaire->load(['user', 'treatment']);

        return view('pharmacy.medical-questionnaires.show', [
            'patient' => $patient,
            'questionnaire' => $questionnaire
        ]);
    }
}
