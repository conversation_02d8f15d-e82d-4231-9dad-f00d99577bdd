<?php

namespace App\Http\Controllers\Pharmacy;

use App\Http\Controllers\Controller;
use App\Models\DispenseRecord;
use App\Models\Inventory;
use App\Models\Label;
use App\Models\Prescription;
use App\Models\PrescriptionItem;
use App\Models\Subscription;
use App\Tables\Prescriptions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class PrescriptionsController extends Controller
{
    public function approve(Prescription $prescription)
    {
        $user = $prescription->user;
        $userAllergies = $user->allergies->pluck('allergen')->toArray();

        foreach ($prescription->items as $item) {
            if (in_array($item->medicine->name, $userAllergies) || in_array($item->medicine->generic_name, $userAllergies)) {
                return redirect()->back()->with('error', "Patient is allergic to {$item->medicine->name}. Please review the prescription.");
            }
        }

        $prescription->update([
            'status' => 'approved',
            'pharmacist_id' => auth()->id(),
        ]);

        // Generate labels for each prescription item
        foreach ($prescription->items as $item) {
            Label::create([
                'prescription_item_id' => $item->id,
                'instructions' => $this->generateInstructions($item),
            ]);
        }

        return redirect()->route('prescriptions.index')->with('success', 'Prescription approved and labels generated.');
    }

    private function generateInstructions(PrescriptionItem $item)
    {
        // Logic to generate instructions based on dosage, frequency, and duration
        return "Take {$item->dosage} {$item->frequency} for {$item->duration} days.";
    }

    public function refill(PrescriptionItem $item)
    {
        if ($item->refills > 0) {
            $item->decrement('refills');
            
            // Create a new prescription item for the refill
            $newItem = $item->replicate();
            $newItem->refills = 0;
            $newItem->save();

            // Generate a new label for the refill
            Label::create([
                'prescription_item_id' => $newItem->id,
                'instructions' => $this->generateInstructions($newItem),
            ]);

            return redirect()->back()->with('success', 'Medication refilled successfully.');
        }

        return redirect()->back()->with('error', 'No refills remaining for this medication.');
    }

    public function index()
    {
        return view('pharmacy.prescriptions.index', [
            'prescriptions' => Prescriptions::class,
        ]);
    }

    public function create()
    {
        return view('pharmacy.prescriptions.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:users,id',
            'doctor_id' => 'required|exists:users,id',
            'medications' => 'required|array',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.dosage' => 'required|string',
            'medications.*.frequency' => 'required|string',
            'medications.*.duration' => 'required|integer',
            'notes' => 'nullable|string',
        ]);

        $prescription = Prescription::create($validated);

        foreach ($validated['medications'] as $medication) {
            $prescription->medications()->attach($medication['medication_id'], [
                'dosage' => $medication['dosage'],
                'frequency' => $medication['frequency'],
                'duration' => $medication['duration'],
            ]);
        }

        Toast::success('Prescription created successfully.');

        return redirect()->route('pharmacy.prescriptions.index');
    }

    public function show(Prescription $prescription)
    {
        return view('pharmacy.prescriptions.show', compact('prescription'));
    }

    public function edit(Prescription $prescription)
    {
        return view('pharmacy.prescriptions.edit', compact('prescription'));
    }

    public function update(Request $request, Prescription $prescription)
    {
        $validated = $request->validate([
            'patient_id' => 'required|exists:users,id',
            'doctor_id' => 'required|exists:users,id',
            'medications' => 'required|array',
            'medications.*.medication_id' => 'required|exists:medications,id',
            'medications.*.dosage' => 'required|string',
            'medications.*.frequency' => 'required|string',
            'medications.*.duration' => 'required|integer',
            'notes' => 'nullable|string',
        ]);

        $prescription->update($validated);

        $prescription->medications()->detach();

        foreach ($validated['medications'] as $medication) {
            $prescription->medications()->attach($medication['medication_id'], [
                'dosage' => $medication['dosage'],
                'frequency' => $medication['frequency'],
                'duration' => $medication['duration'],
            ]);
        }

        Toast::success('Prescription updated successfully.');

        return redirect()->route('pharmacy.prescriptions.index');
    }

    public function destroy(Prescription $prescription)
    {
        $prescription->delete();

        Toast::success('Prescription deleted successfully.');

        return redirect()->route('pharmacy.prescriptions.index');
    }

    public function fillPrescription(Prescription $prescription)
    {
        Log::info("Attempting to fill prescription", ['prescription_id' => $prescription->id]);

        if ($prescription->status !== 'approved') {
            Log::warning("Attempt to fill non-approved prescription", ['prescription_id' => $prescription->id, 'status' => $prescription->status]);
            Toast::danger('Only approved prescriptions can be dispensed.')->autoDismiss();
            return back();
        }

        if ($prescription->status === 'dispensed') {
            Log::warning("Attempt to fill already dispensed prescription", ['prescription_id' => $prescription->id]);
            Toast::danger('This prescription has already been dispensed.')->autoDismiss();
            return back();
        }

        $subscription = $prescription->user->activeSubscription();

        if (!$subscription) {
            Log::warning("User without active subscription attempted to fill prescription", ['prescription_id' => $prescription->id, 'user_id' => $prescription->user_id]);
            Toast::danger('User does not have an active subscription.')->autoDismiss();
            return back();
        }

        // Check if the prescription can be filled based on subscription limits
        if (!$this->canFillPrescription($prescription, $subscription)) {
            Log::warning("Prescription cannot be filled due to subscription limits", ['prescription_id' => $prescription->id, 'subscription_id' => $subscription->id]);
            Toast::danger('Prescription cannot be filled due to subscription limits.')->autoDismiss();
            return back();
        }

        DB::beginTransaction();
        
        try {
            $dispensedItems = [];
            $insufficientStockItems = [];
            $totalCost = 0;

            foreach ($prescription->items as $item) {
                // Check if the item can be dispensed based on its status
                if (!in_array($item->status, ['active', 'as-needed', 'temporary', 'tapering', 'scheduled', 'long-term'])) {
                    Log::info("Skipping prescription item due to status", ['prescription_id' => $prescription->id, 'item_id' => $item->id, 'status' => $item->status]);
                    continue; // Skip items that shouldn't be dispensed
                }

                $inventories = Inventory::where('medication_id', $item->medication_id)
                                        ->where('quantity', '>', 0)
                                        ->orderBy('expiry_date')
                                        ->get();

                $quantityNeeded = $item->quantity;
                $quantityDispensed = 0;

                foreach ($inventories as $inventory) {
                    if ($quantityNeeded <= 0) break;

                    $quantityToDeduct = min($quantityNeeded, $inventory->quantity);
                    $inventory->quantity -= $quantityToDeduct;
                    $inventory->save();

                    $quantityNeeded -= $quantityToDeduct;
                    $quantityDispensed += $quantityToDeduct;

                    Log::info("Deducted inventory", [
                        'prescription_id' => $prescription->id,
                        'item_id' => $item->id,
                        'inventory_id' => $inventory->id,
                        'quantity_deducted' => $quantityToDeduct
                    ]);
                }

                if ($quantityDispensed > 0) {
                    $dispensedItems[] = [
                        'item' => $item,
                        'quantity_dispensed' => $quantityDispensed,
                        'fully_dispensed' => $quantityNeeded == 0
                    ];
                    $totalCost += $quantityDispensed * $item->medication->unit_price;

                    Log::info("Item dispensed", [
                        'prescription_id' => $prescription->id,
                        'item_id' => $item->id,
                        'quantity_dispensed' => $quantityDispensed,
                        'fully_dispensed' => $quantityNeeded == 0
                    ]);
                }

                if ($quantityNeeded > 0) {
                    $insufficientStockItems[] = [
                        'item' => $item,
                        'quantity_short' => $quantityNeeded
                    ];

                    Log::warning("Insufficient stock for item", [
                        'prescription_id' => $prescription->id,
                        'item_id' => $item->id,
                        'quantity_short' => $quantityNeeded
                    ]);
                }
            }

            if (empty($dispensedItems)) {
                Log::error("No items could be dispensed due to insufficient stock", ['prescription_id' => $prescription->id]);
                throw new \Exception('No items could be dispensed from this prescription due to insufficient stock.');
            }

            // Update subscription usage
            // $subscription->used_prescription_count++;
            // $subscription->used_medication_coverage += $totalCost;
            // $subscription->save();
            $subscription->incrementUsage(1, $totalCost);
            Log::info("Subscription usage incremented", [
                'subscription_id' => $subscription->id,
                'prescription_count' => 1,
                'total_cost' => $totalCost
            ]);

            // Create dispense record
            $dispenseRecord = new DispenseRecord([
                'prescription_id' => $prescription->id,
                'dispensed_by' => auth()->id(),
                'dispensed_at' => now(),
            ]);
            $dispenseRecord->save();
            Log::info("Dispense record created", ['dispense_record_id' => $dispenseRecord->id, 'prescription_id' => $prescription->id]);

            // Record dispensed items
            foreach ($dispensedItems as $dispensedItem) {
                $dispenseRecord->items()->create([
                    'prescription_item_id' => $dispensedItem['item']->id,
                    'quantity_dispensed' => $dispensedItem['quantity_dispensed'],
                    'fully_dispensed' => $dispensedItem['fully_dispensed'],
                ]);
            }

            // Update prescription status
            $allFullyDispensed = empty($insufficientStockItems);
            $prescription->status = $allFullyDispensed ? 'dispensed' : 'partially_dispensed';
            $prescription->dispensed_at = now();

            // Prepare feedback message and notes
            $message = "Dispensed on " . now()->format('Y-m-d H:i:s') . "\n"; // . " by " . auth()->user()->name . ":\n";
            $message .= "Items dispensed:\n";
            foreach ($dispensedItems as $item) {
                $message .= "- {$item['item']->medication->name}: {$item['quantity_dispensed']} units";
                if (!$item['fully_dispensed']) {
                    $message .= " (partially dispensed)";
                }
                $message .= "\n";
            }
            if (!empty($insufficientStockItems)) {
                $message .= "\nItems with insufficient stock:\n";
                foreach ($insufficientStockItems as $item) {
                    $message .= "- {$item['item']->medication->name}: Short by {$item['quantity_short']} units\n";
                }
            }

            // Append the message to prescription notes
            $prescription->notes = $prescription->notes . "\n\n" . $message;
            $prescription->save();

            Log::info("Prescription updated", [
                'prescription_id' => $prescription->id,
                'new_status' => $prescription->status,
                'notes_updated' => true
            ]);

            DB::commit();

            $toastMessage = 'Prescription items dispensed successfully.';
            if (!empty($insufficientStockItems)) {
                $toastMessage .= ' Some items had insufficient stock. Check prescription notes for details.';
            }

            Log::info("Prescription filled successfully", ['prescription_id' => $prescription->id, 'fully_dispensed' => $allFullyDispensed]);

            Toast::success($toastMessage)->autoDismiss();
            return redirect()->route('pharmacy.prescriptions.index');
        } catch (\Exception $e) {
            DB::rollBack();
            Toast::danger('Error: ' . $e->getMessage())->autoDismiss();
            return back();
        }
    }

    private function canFillPrescription(Prescription $prescription, Subscription $subscription): bool
    {
        if ($subscription->getRemainingPrescriptions() < 1) {
            return false;
        }

        $prescriptionCost = $this->calculatePrescriptionCost($prescription);
        
        if ($subscription->getRemainingMedicationCoverage() < $prescriptionCost) {
            return false;
        }

        return true;
    }

    private function calculatePrescriptionCost(Prescription $prescription): float
    {
        $totalCost = 0;
        foreach ($prescription->items as $item) {
            $totalCost += $item->quantity * $item->medication->unit_price;
        }
        return $totalCost;
    }
}