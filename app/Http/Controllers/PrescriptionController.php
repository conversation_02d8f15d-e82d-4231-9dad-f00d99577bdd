<?php

namespace App\Http\Controllers;

use App\Models\Label;
use App\Models\Prescription;
use App\Models\PrescriptionItem;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class PrescriptionController extends Controller
{
    public function approve(Prescription $prescription)
    {
        $user = $prescription->user;
        $userAllergies = $user->allergies->pluck('allergen')->toArray();

        foreach ($prescription->items as $item) {
            if (in_array($item->medicine->name, $userAllergies) || in_array($item->medicine->generic_name, $userAllergies)) {
                return redirect()->back()->with('error', "Patient is allergic to {$item->medicine->name}. Please review the prescription.");
            }
        }

        $prescription->update([
            'status' => 'approved',
            'pharmacist_id' => auth()->id(),
        ]);

        // Generate labels for each prescription item
        foreach ($prescription->items as $item) {
            Label::create([
                'prescription_item_id' => $item->id,
                'instructions' => $this->generateInstructions($item),
            ]);
        }

        return redirect()->route('prescriptions.index')->with('success', 'Prescription approved and labels generated.');
    }

    public function refill(PrescriptionItem $item)
    {
        if ($item->refills > 0) {
            $item->decrement('refills');
            
            // Create a new prescription item for the refill
            $newItem = $item->replicate();
            $newItem->refills = 0;
            $newItem->save();

            // Generate a new label for the refill
            Label::create([
                'prescription_item_id' => $newItem->id,
                'instructions' => $this->generateInstructions($newItem),
            ]);

            return redirect()->back()->with('success', 'Medication refilled successfully.');
        }

        return redirect()->back()->with('error', 'No refills remaining for this medication.');
    }

    private function generateInstructions(PrescriptionItem $item)
    {
        // Logic to generate instructions based on dosage, frequency, and duration
        return "Take {$item->dosage} {$item->frequency} for {$item->duration} days.";
    }

    public function downloadPDF(Prescription $prescription)
    {
        $pdf = Pdf::loadView('doctor.prescriptions.pdf', ['prescription' => $prescription->load('doctor', 'pharmacist', 'items.medication')]);
        return $pdf->stream("prescription-{$prescription->id}.pdf");
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
