<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class ProfileCompletionController extends Controller
{
    /**
     * Get the profile completion status for the current user.
     */
    public function status(Request $request)
    {
        $user = $request->user();

        if (!$user || !$user->hasAnyRole(['patient', 'employee', 'agent'])) {
            return response()->json([
                'is_complete' => true,
                'missing_fields' => []
            ]);
        }

        return response()->json([
            'is_complete' => $user->isProfileComplete(),
            'missing_fields' => $user->getMissingProfileFields()
        ]);
    }
}
