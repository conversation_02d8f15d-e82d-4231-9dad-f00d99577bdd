<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ShareASaleController extends Controller
{
    public function markFired(Request $request, $id)
    {
        if ($request->session()->has($id)) {
            $data = $request->session()->get($id);
            $data['fired'] = true;
            $request->session()->put($id, $data);
            return response()->json(['success' => true]);
        }
        return response()->json(['success' => false], 404);
    }
}
