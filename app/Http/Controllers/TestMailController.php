<?php

namespace App\Http\Controllers;

use App\Mail\TestMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use ProtoneMedia\Splade\Facades\Toast;

class TestMailController extends Controller
{
    /**
     * Show the form to send a test email.
     */
    public function showForm()
    {
        return view('test-mail');
    }

    /**
     * Send a test email.
     */
    public function sendTestMail(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'name' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        try {
            Mail::to($validated['email'])->send(new TestMail(
                name: $validated['name'],
                messageContent: $validated['message']
            ));

            Toast::success('Test email sent successfully! Please check your inbox (and spam folder).');

            return back();
        } catch (\Exception $e) {
            Toast::danger('Failed to send test email: ' . $e->getMessage());

            return back()->withInput();
        }
    }
}
