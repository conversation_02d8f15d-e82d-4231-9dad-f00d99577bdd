<?php

namespace App\Http\Controllers\UrgentCare;

use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Http\Requests\UrgentCareQuestionsRequest;
use App\Models\AdditionalInformation;
use App\Models\Allergy;
use App\Models\Condition;
use App\Models\MedicalCondition;
use App\Models\MedicalSurgicalHistory;
use App\Models\PatientSymptom;
use App\Models\PreferredMedication;
use App\Models\Symptom;
use App\Models\User;
use App\Models\UserMeasurement;
use App\Models\UserReportedMedication;
use App\Services\HeightConverterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use ProtoneMedia\Splade\Facades\Toast;

class ConditionsController extends Controller
{
    use TracksLinkTrust;

    public function __construct(private HeightConverterService $heightConverterService){}

    public function index()
    {
        $conditions = Condition::select('id', 'name', 'therapeutic_use', 'slug')
            ->whereHas('primaryUses')
            ->orWhereHas('offLabelUses')
            ->with([
                'primaryUses' => function($query) {
                    $query->select('id', 'medication_base_id', 'condition_id')
                        ->with(['medicationBase' => function($query) {
                            $query->select('id', 'generic_name');
                        }]);
                },
                'offLabelUses' => function($query) {
                    $query->select('id', 'medication_base_id', 'condition_id')
                        ->with('medicationBase');
                },
                'symptoms' => function($query) {
                    $query->select('id', 'name', 'condition_id');
                },
            ])
            ->get()
            ->map(function ($condition) {
                return [
                    'name' => $condition->name,
                    'therapeutic_use' => $condition->therapeutic_use,
                    'slug' => $condition->slug,
                    'primary_uses' => $condition->primaryUses->map(function ($use) {
                        return [
                            'medication' => $use->medicationBase,
                        ];
                    }),
                    'off_label_uses' => $condition->offLabelUses->map(function ($use) {
                        return [
                            'medication' => $use->medicationBase,
                        ];
                    }),
                    'symptoms' => $condition->symptoms->map(function ($symptom) {
                        return [
                            'name' => $symptom->name,
                        ];
                    }),
                    'random' => Str::replaceMatches('/[^a-zA-Z]/', '', Str::random(10))
                ];
            });

        return view('front.urgent-care.index', compact('conditions'));
    }

    public function show($condition)
    {
        $condition = Condition::where('name', $condition)->firstOrFail();
        return view('front.urgent-care.show', compact('condition'));
    }

    public function questionsForm(Request $request, Condition $condition = null, $step = 1)
    {
        $step = (int) $step;

        // Define the steps
        $steps = [
            'Personal Information',
            'Health Concerns',
            'Medications & Allergies',
            'Symptoms & Additional Information'
        ];

        // Validate step range
        if ($step < 1 || $step > count($steps)) {
            $step = 1;
        }

        $groupDefinitions = $this->getConditionGroups();

        $conditions = Condition::whereHas('symptoms')
            ->with(['symptoms'])
            ->get();

        $groupedConditions = collect($groupDefinitions)->map(function($conditionNames, $groupName) use ($conditions) {
            if (empty($conditionNames)) {
                // For "Other Conditions", get conditions not in any other group
                $allGroupConditions = collect($this->getConditionGroups())
                    ->flatten()
                    ->filter();

                return $conditions->filter(function($condition) use ($allGroupConditions) {
                    return !$allGroupConditions->contains($condition->name);
                });
            }

            return $conditions->filter(function($condition) use ($conditionNames) {
                return in_array($condition->name, $conditionNames);
            });
        })->filter(function($group) {
            return $group->isNotEmpty();
        });

        // Get saved form data from session if available
        $sessionData = $request->session()->get('urgent_care_form_data', []);

        $default = array_merge([
            'allergies' => [],
            'medications' => [],
            'dob' => now()->subYears(30)->format('Y-m-d'),
            'main_health_concern' => $condition?->name,
            'step' => $step,
            'consent_to_treatment' => false,
            'privacy_policy_accepted' => false
        ], $sessionData);

        return view('front.urgent-care.questions-form', compact('default', 'groupedConditions', 'condition', 'steps'));
    }

    private function getConditionGroups()
    {
        return [
            'Respiratory Symptoms' => [
                'Asthma', 'COPD', 'Bacterial Pneumonia', 'Rhinitis', 'Allergic Rhinitis'
            ],
            'Skin & Soft Tissue Symptoms' => [
                'Acne', 'Eczema', 'Psoriasis', 'Rosacea', 'Hives', 'Bacterial Infections'
            ],
            'Gastrointestinal & Urinary Symptoms' => [
                'Acid Reflux', 'IBS', 'Gastroparesis', 'H. pylori', 'UTIs',
                'Catheter-Associated Urinary Tract Infections'
            ],
            'Pain & Headaches' => [
                'Chronic Pain', 'Fibromyalgia', 'Migraine', 'Neuropathic Pain',
                'Arthritis', 'Psoriatic Arthritis', 'Rheumatoid Arthritis'
            ],
            'Mental Health & Sleep Disorders' => [
                'ADHD', 'Anxiety', 'Bipolar', 'Depression', 'GAD', 'OCD',
                'Panic Disorder', 'PTSD', 'Sleep Disorders'
            ],
            'Sexual Health & Infections' => [
                'HIV Prophylaxis (PrEP)', 'Erectile Dysfunction', 'PCOS'
            ],
            'Chronic Health Conditions' => [
                'Diabetes', 'Hypertension', 'Thyroid Conditions', 'Weight Management'
            ],
            'Other Conditions' => []
        ];
    }

    public function store(UrgentCareQuestionsRequest $request)
    {
        if ($request->input('step') < 4) {
            $formData = $request->except('_token', 'step');
            // $formData['step'] = (int)$request->input('step') + 1; // Increment the step
            $step = $request->input('step') + 1;
            $request->session()->put('urgent_care_form_data', $formData);
            return redirect()->route('urgent-care.questions-form', ['step' => $step]);
        }

        try {
            // Validate consent checkboxes
            if (!$request->boolean('consent_to_treatment') || !$request->boolean('privacy_policy_accepted')) {
                Toast::danger('You must consent to treatment and accept the privacy policy to continue.')->autoDismiss();
                return redirect()->back()->withInput();
            }

            DB::beginTransaction();

            // Generate a secure temporary password
            $temporaryPassword = Str::random(10);

            // Create the user
            $user = User::create([
                'name' => $request->input('first_name') . ' ' . $request->input('last_name'),
                'fname' => $request->input('first_name'),
                'lname' => $request->input('last_name'),
                'email' => $request->input('email'),
                'password' => bcrypt($temporaryPassword),
                'phone' => $request->input('phone'),
                'dob' => $request->date('dob'),
                'status' => 'active',
            ]);
            $user->assignRole('patient');

            // Store the user and temporary password in session for later use
            Session::put('user', $user);
            Session::put('temporary_password', $temporaryPassword);

            // 1. Store User Measurements
            UserMeasurement::create([
                'user_id' => $user->id,
                'height' => $this->heightConverterService->feetInchesToInches($request->input('height_ft'), $request->input('height_in')),
                'weight' => $request->input('weight'),
                'measured_at' => now(),
            ]);

            // 2. Store Medical Conditions
            // if (!empty($request->input('conditions'))) {
            //     foreach ($request->input('conditions') as $condition) {
            //         MedicalCondition::create([
            //             'patient_id' => $user->id,
            //             'condition_name' => $condition['condition_name'],
            //             'has_condition_before' => $condition['had_condition_before'] ?? false,
            //             'is_chronic' => $condition['is_chronic'] ?? false,
            //         ]);
            //     }
            // }

            // 3. Store Surgical History
            if ($request->boolean('surgery')) {
                MedicalSurgicalHistory::create([
                    'patient_id' => $user->id,
                    'surgery' => true,
                    'surgery_details' => $request->input('surgery_details'),
                ]);
            }

            // 4. Store Allergies
            if (!empty($request->input('allergies'))) {
                foreach ($request->input('allergies') as $allergy) {
                    Allergy::create([
                        'user_id' => $user->id,
                        'allergen' => $allergy['allergen'],
                        'reaction' => $allergy['reaction'],
                    ]);
                }
            }

            // 5. Store Current Medications
            if (!empty($request->input('medications'))) {
                foreach ($request->input('medications') as $medication) {
                    $userMed = UserReportedMedication::create([
                        'user_id' => $user->id,
                        'medication_name' => $medication['medication_name'],
                        'side_effects' => $medication['side_effects'],
                    ]);

                    // Store adverse reactions if any
                    if (!empty($medication['side_effects'])) {
                        $userMed->adverseReaction()->create([
                            'reaction_details' => $medication['side_effects'], // Use side_effects instead of reaction
                            'reaction_date' => now(),
                            'severity' => 'moderate', // Default value, adjust as needed
                        ]);
                    }
                }
            }

            // 6. Store Main Health Concern as Medical Condition
            MedicalCondition::create([
                'patient_id' => $user->id,
                'condition_name' => $request->input('main_health_concern'),
                'had_condition_before' => $request->boolean('had_condition_before'),
                'symptom_start_date' => $request->date('symptoms_start'),
            ]);

            // 7. Store Symptoms
            if (!empty($request->input('symptoms'))) {
                foreach ($request->input('symptoms') as $symptom) {
                    PatientSymptom::create([
                        'patient_id' => $user->id,
                        'symptom' => $symptom,
                    ]);
                }
            }

            // 8. Store Additional Information
            AdditionalInformation::create([
                'patient_id' => $user->id,
                'additional_concerns' => $request->input('additional_concerns') ?? null,
            ]);

            // 9. Store Preferred Medication if specified
            if ($request->input('specific_medication') && !empty($request->input('medication_name'))) {
                PreferredMedication::create([
                    'user_id' => $user->id,
                    'medication_name' => $request->input('medication_name'),
                    'taken_before' => false, // Default value
                    'effectiveness' => '',
                ]);
            }

            // Clear the form data from session after successful submission
            $request->session()->forget('urgent_care_form_data');

            DB::commit();

            Toast::success('Medical intake form submitted successfully.')->autoDismiss();
            return redirect()
                ->route('urgent-care.payment')
                ->with('success', 'Medical intake form submitted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Toast::danger('There was an error processing your medical intake form. Please try again.')->autoDismiss();

            // Log the error
            Log::error('Medical Intake Form Error: ' . $e->getMessage(), [
                'user_id' => $user->id ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'There was an error processing your medical intake form. Please try again.');
        }
    }
}
