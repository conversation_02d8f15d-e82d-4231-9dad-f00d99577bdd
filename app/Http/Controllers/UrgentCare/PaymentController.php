<?php

namespace App\Http\Controllers\UrgentCare;

use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Services\Api\AuthorizeNetApi;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentController extends Controller
{
    use TracksLinkTrust;

    protected $authorizeNetApi;

    public function __construct(AuthorizeNetApi $authorizeNetApi)
    {
        $this->authorizeNetApi = $authorizeNetApi;
    }

    public function showForm()
    {
        return view('front.urgent-care.payment');
    }

    public function processPayment(Request $request)
    {
        // Validate the payment form
        $validated = $request->validate([
            'card_holder_name' => 'required|string|max:255',
            'card_number' => 'required|string|min:13|max:19',
            'expiration_month' => 'required|string|size:2',
            'expiration_year' => 'required|string|size:4',
            'cvv' => 'required|string|min:3|max:4',
            'billing_address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:50',
            'zip' => 'required|string|max:20',
            'agree_terms' => 'required|accepted',
        ]);

        try {
            $user = Session::get('user');
            $amount = config('services.pricing.urgent_care', '39.00');

            // Prepare payment data for Authorize.net
            $paymentData = [
                'refId' => 'UC' . Str::random(8),
                'transactionRequest' => [
                    'transactionType' => 'authCaptureTransaction',
                    'amount' => $amount,
                    'payment' => [
                        'creditCard' => [
                            'cardNumber' => $validated['card_number'],
                            'expirationDate' => $validated['expiration_year'] . '-' . $validated['expiration_month'],
                            'cardCode' => $validated['cvv']
                        ]
                    ],
                    'customer' => [
                        'id' => $user->id,
                        'email' => $user->email
                    ],
                    'billTo' => [
                        'firstName' => $user->first_name,
                        'lastName' => $user->last_name,
                        'address' => $validated['billing_address'],
                        'city' => $validated['city'],
                        'state' => $validated['state'],
                        'zip' => $validated['zip'],
                        'country' => 'US',
                        'phoneNumber' => $user->mobile_phone
                    ],
                    'customerIP' => $request->ip()
                ]
            ];

            // Process payment through Authorize.net
            $response = $this->authorizeNetApi->sendRequest('createTransactionRequest', $paymentData);

            // Check if transaction was successful
            if (isset($response['transactionResponse']) &&
                isset($response['transactionResponse']['responseCode']) &&
                $response['transactionResponse']['responseCode'] == '1') {

                Auth::login($user);
                $user = Auth::user();

                // Get ClickID and AFID for affiliate tracking
                $clickId = \Illuminate\Support\Facades\Cookie::get('LTClickID') ??
                          Session::get('LTClickID') ??
                          $request->query('ClickID');

                $afid = \Illuminate\Support\Facades\Cookie::get('AFID') ??
                       Session::get('AFID') ??
                       $request->query('AFID');

                // Save transaction details to database
                $transaction = $user->transactions()->create([
                    'transaction_id' => $response['transactionResponse']['transId'],
                    'amount' => $amount,
                    'status' => 'success',
                    'payment_method' => 'credit_card',
                    'meta_data' => [
                        'auth_code' => $response['transactionResponse']['authCode'],
                        'avs_result_code' => $response['transactionResponse']['avsResultCode'] ?? null,
                        'cvv_result_code' => $response['transactionResponse']['cvvResultCode'] ?? null,
                        'click_id' => $clickId,
                        'afid' => $afid
                    ]
                ]);

                Log::info('User:', ['email' => $user->email]);
                Log::info(['metadata' => json_encode([
                    'auth_code' => $response['transactionResponse']['authCode'],
                    'avs_result_code' => $response['transactionResponse']['avsResultCode'] ?? null,
                    'cvv_result_code' => $response['transactionResponse']['cvvResultCode'] ?? null,
                ])]);

                // Update user status to indicate payment completed
                $user->update(['status' => 'active']);

                Session::forget('user');
                // Redirect to success page
                // Track the purchase using the TracksLinkTrust trait
                $trackingResult = $this->trackLinkTrustPurchase(
                    amount: $amount,
                    userId: $user->id,
                    transactionId: $response['transactionResponse']['transId']
                );

                // Log the tracking result
                Log::info('LinkTrust tracking result for UrgentCare payment', [
                    'result' => $trackingResult,
                    'user_id' => $user->id,
                    'transaction_id' => $response['transactionResponse']['transId'],
                    'amount' => $amount
                ]);

                Toast::success('Payment successful! Your urgent care consultation is being processed.');
                return redirect()->route('urgent-care.success');
            } else {
                // Handle payment failure
                $errorMessage = $response['transactionResponse']['errors'][0]['errorText'] ?? 'Payment processing failed. Please try again.';

                // Track the failed transaction in LinkTrust
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->mobile_phone ?? ''
                ];

                $transactionId = 'FAILED-UC-PAYMENT-' . time();
                $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );

                Log::error('Payment failed: ' . json_encode($response) . ' (LinkTrust tracking sent)');
                Toast::danger($errorMessage)->autoDismiss(10);
                return back()->withInput();
            }
        } catch (\Exception $e) {
            // Track the exception in LinkTrust if we have a user
            if (isset($user) && $user) {
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->mobile_phone ?? ''
                ];

                $transactionId = 'ERROR-UC-PAYMENT-' . time();
                $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );
            }

            Log::error('Payment exception: ' . $e->getMessage() . (isset($user) ? ' (LinkTrust tracking sent)' : ' (no user for tracking)'));
            Toast::danger('An error occurred while processing your payment. Please try again.')->autoDismiss(10);
            return back()->withInput();
        }
    }
}
