<?php

namespace App\Http\Controllers\UrgentCare;

use App\Actions\UrgentCare\HandleUrgentCareMeasurements;
use App\Actions\UrgentCare\HandleUrgentCareUserCreation;
use App\Actions\UrgentCare\ProcessUrgentCarePayment;
use App\Concerns\TracksLinkTrust;
use App\Http\Controllers\Controller;
use App\Models\Condition;
use App\Models\Consultation;
use App\Models\MedicalQuestionnaire;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Models\Treatment;
use App\Models\TreatmentQuestion;
use App\Models\User;
use App\Models\UserReportedMedication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class UrgentCareController extends Controller
{
    use TracksLinkTrust;

    public function __construct(
        private HandleUrgentCareUserCreation $handleUserCreation,
        private HandleUrgentCareMeasurements $handleMeasurements,
        private ProcessUrgentCarePayment $processPayment
    ) {}
    /**
     * Display the urgent care landing page with all conditions
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $searchTerm = $request->input('search');
        $treatmentType = $request->input('treatment_type');

        // Start with base query
        $conditionsQuery = Condition::with(['treatments' => function($query) {
                $query->orderBy('primary_treatment', 'desc');
            }]);

        // Apply search filter if provided
        if ($searchTerm) {
            $conditionsQuery->where('name', 'like', "%{$searchTerm}%");
        }

        // Get all conditions with their treatments
        $conditions = $conditionsQuery->orderBy('name')
            ->get()
            ->map(function ($condition) {
                // Get the primary treatment type if available
                $primaryTreatment = $condition->treatments->where('pivot.primary_treatment', true)->first();

                // If no primary treatment, get the first available treatment
                $treatment = $primaryTreatment ?? $condition->treatments->first();

                // Determine primary treatment type based on the treatment slug
                $treatmentType = null;
                $treatmentName = null;

                if ($treatment) {
                    $treatmentName = $treatment->name;

                    if ($treatment->slug === 'general-consultation') {
                        $treatmentType = 'consultation';
                    } elseif ($treatment->slug === 'urgent-care-visit') {
                        $treatmentType = 'urgent';
                    } elseif ($treatment->slug === 'specialized-consultation') {
                        $treatmentType = 'specialized';
                    }
                }

                // Add primary treatment information to the condition
                $condition->treatment_type = $treatmentType;
                $condition->treatment_name = $treatmentName;

                // Add all available treatment types for this condition
                $condition->available_treatments = $condition->treatments->map(function ($treatment) {
                    $type = null;
                    if ($treatment->slug === 'general-consultation') {
                        $type = 'consultation';
                    } elseif ($treatment->slug === 'urgent-care-visit') {
                        $type = 'urgent';
                    } elseif ($treatment->slug === 'specialized-consultation') {
                        $type = 'specialized';
                    }

                    return [
                        'id' => $treatment->id,
                        'name' => $treatment->name,
                        'type' => $type,
                        'is_primary' => $treatment->pivot->primary_treatment
                    ];
                });

                return $condition;
            });

        // Apply treatment type filter if provided (after mapping since treatment_type is added dynamically)
        if ($treatmentType) {
            $conditions = $conditions->filter(function ($condition) use ($treatmentType) {
                return $condition->treatment_type === $treatmentType;
            })->values(); // Reset array keys
        }

        // Get all available treatment types for the filter dropdown
        $treatmentTypes = [
            'consultation' => 'Consultation',
            'urgent' => 'Urgent Care',
            'specialized' => 'Specialized'
        ];

        return view('front.urgent-care.index', [
            'conditions' => $conditions,
            'treatmentTypes' => $treatmentTypes,
            'currentSearch' => $searchTerm,
            'currentTreatmentType' => $treatmentType,
        ]);
    }

    /**
     * Display details for a specific condition
     */
    public function showCondition($slug)
    {
        $condition = Condition::where('slug', $slug)
            ->with([
                'treatments' => function($query) {
                    $query->orderBy('condition_treatment.primary_treatment', 'desc');
                },
                'primaryUses' => function($query) {
                    $query->with('medicationBase');
                }
            ])
            ->firstOrFail();

        // Add treatment types to each treatment
        $condition->treatments->each(function ($treatment) {
            if ($treatment->slug === 'general-consultation') {
                $treatment->type = 'consultation';
            } elseif ($treatment->slug === 'urgent-care-visit') {
                $treatment->type = 'urgent';
            } elseif ($treatment->slug === 'specialized-consultation') {
                $treatment->type = 'specialized';
            }
        });

        return view('front.urgent-care.condition-details', [
            'condition' => $condition,
        ]);
    }

    /**
     * Display details for a specific treatment
     */
    public function showTreatment($slug)
    {
        $treatment = Treatment::where('slug', $slug)
            ->with(['conditions', 'questions'])
            ->firstOrFail();

        return view('front.urgent-care.treatment-details', [
            'treatment' => $treatment,
        ]);
    }

    /**
     * Select a treatment and proceed to subscription plans
     */
    public function selectTreatment(Request $request)
    {
        $request->validate([
            'treatment_id' => 'required|exists:treatments,id',
        ]);

        $treatment = Treatment::findOrFail($request->treatment_id);

        // Store the selected treatment in the session
        Session::put('uc_treatment', [
            'id' => $treatment->id,
            'name' => $treatment->name,
            'slug' => $treatment->slug,
        ]);

        return redirect()->route('urgent-care.plans');
    }

    /**
     * Display subscription plans
     */
    public function showPlans()
    {
        // Check if a treatment has been selected
        if (!Session::has('uc_treatment')) {
            return redirect()->route('urgent-care.index');
        }

        // Get active subscription plans
        $plans = SubscriptionPlan::where('status', 'active')
            ->whereIn('type', ['individual', 'family', 'single'])
            ->where('is_active', 1)
            ->with('discounts')
            ->get();

        return view('front.urgent-care.plans', [
            'plans' => $plans,
            'treatment' => Session::get('uc_treatment'),
            'progress' => 33, // Progress indicator (33%)
        ]);
    }

    /**
     * Select a subscription plan and proceed to medical questionnaire
     */
    public function selectPlan(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'has_insurance' => 'nullable|in:yes,no',
        ]);

        $plan = SubscriptionPlan::findOrFail($request->plan_id);

        // Store the selected plan in the session
        // Always set hasInsurance to 'no' since insurance options are hidden
        Session::put('uc_plan', [
            'id' => $plan->id,
            'name' => $plan->name,
            'price' => $plan->price,
            'hasInsurance' => 'no',
        ]);

        return redirect()->route('urgent-care.questionnaire');
    }

    /**
     * Display the medical questionnaire
     */
    public function showQuestionnaire()
    {
        // Check if a treatment and plan have been selected
        if (!Session::has('uc_treatment') || !Session::has('uc_plan')) {
            return redirect()->route('urgent-care.index');
        }

        $treatment = Treatment::with(['questions', 'conditions'])
            ->findOrFail(Session::get('uc_treatment.id'));

        // Get any previously saved questionnaire data
        $medicalQuestions = Session::get('uc_medical_questions', []);

        // Get condition-specific questions
        $conditionIds = $treatment->conditions->pluck('id')->toArray();
        $conditionQuestions = [];

        if (!empty($conditionIds)) {
            $conditionQuestions = TreatmentQuestion::whereIn('condition_id', $conditionIds)
                ->orderBy('display_order')
                ->get();
        }

        // Determine which sections of questions are relevant for this treatment
        $relevantSections = ['general', $treatment->slug];

        return view('front.urgent-care.questionnaire', [
            'treatment' => $treatment,
            'medicalQuestions' => $medicalQuestions,
            'relevantSections' => $relevantSections,
            'conditionQuestions' => $conditionQuestions,
            'progress' => 66, // Progress indicator (66%)
        ]);
    }

    /**
     * Process the medical questionnaire submission
     */
    public function submitQuestionnaire(Request $request)
    {
        // Check if a treatment and plan have been selected
        if (!Session::has('uc_treatment') || !Session::has('uc_plan')) {
            return redirect()->route('urgent-care.index');
        }

        // Validate the form data
        $request->validate([
            'medications' => 'nullable|array',
            'allergies' => 'nullable|array',
            'concerns' => 'nullable|string|max:1000',
            'symptom_duration' => 'required|string',
            'previous_diagnosis' => 'required|string',
            'additional_symptoms' => 'required|string',
            'mh_symptoms_severity' => 'nullable|string',
            'taking_medications' => 'required|string',
            'prevention_medications' => 'nullable|string',
            'past_medications' => 'required|string',
            'past_medication_details' => 'nullable|string',
            'switching_medications' => 'required|string',
            'switching_medication_reason' => 'nullable|string',
            'tests_imaging' => 'nullable|array',
            'prevention_lifestyle' => 'nullable|string',
            'affects_daily_life' => 'required|string',
            'quality_of_life' => 'nullable|string',
            'mental_health_history' => 'required|string',
            'mh_treatment_history' => 'nullable|string',
            'preferred_pharmacy' => 'nullable|string',
            'treatment_goals' => 'nullable|string',
        ]);

        // Store all form data in the session
        $medicalData = $request->except('_token');
        Session::put('uc_medical_questions', $medicalData);

        // If user is logged in, store the data in the medical_questionnaires table
        if (Auth::check()) {
            $user = Auth::user();
            $treatment = Treatment::findOrFail(Session::get('uc_treatment.id'));
            $plan = SubscriptionPlan::findOrFail(Session::get('uc_plan.id'));

            // Create or update medical questionnaire
            $questionnaire = MedicalQuestionnaire::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'treatment_id' => $treatment->id,
                    'subscription_plan_id' => $plan->id,
                    'status' => 'pending'
                ],
                [
                    'data' => json_encode($medicalData),
                    // Use the direct field mappings from the form
                    'additional_symptoms' => $request->additional_symptoms,
                    'quality_of_life' => $request->quality_of_life,
                    'treatment_goals' => $request->treatment_goals,
                    'concerns' => $request->concerns,
                    'prevention_medications' => $request->prevention_medications,
                    'prevention_lifestyle' => $request->prevention_lifestyle,
                    'mh_symptoms_severity' => $request->mh_symptoms_severity,
                    'mh_treatment_history' => $request->mh_treatment_history,
                ]
            );

            // Store medications in user_reported_medications table
            if (isset($medicalData['medications']) && is_array($medicalData['medications'])) {
                foreach ($medicalData['medications'] as $medication) {
                    if (!empty($medication['medication_name'])) {
                        UserReportedMedication::create([
                            'user_id' => $user->id,
                            'medication_name' => $medication['medication_name'],
                            'dosage' => $medication['dosage'] ?? null,
                            'frequency' => $medication['frequency'] ?? null,
                        ]);
                    }
                }
            }

            // Log the questionnaire creation
            Log::info('UrgentCare medical questionnaire created/updated', [
                'user_id' => $user->id,
                'treatment_id' => $treatment->id,
                'plan_id' => $plan->id,
                'questionnaire_id' => $questionnaire->id
            ]);
        }

        return redirect()->route('urgent-care.payment');
    }

    /**
     * Display the payment page
     */
    public function showPayment()
    {
        // Check if all required data is in the session
        if (!Session::has('uc_treatment') || !Session::has('uc_plan') || !Session::has('uc_medical_questions')) {
            return redirect()->route('urgent-care.index');
        }

        $treatment = Treatment::findOrFail(Session::get('uc_treatment.id'));
        $plan = SubscriptionPlan::findOrFail(Session::get('uc_plan.id'));

        return view('front.urgent-care.payment', [
            'treatment' => $treatment,
            'plan' => $plan,
            'progress' => 100, // Progress indicator (100%)
        ]);
    }

    /**
     * Process the payment
     */
    public function submitPayment(Request $request)
    {
        // Check if all required data is in the session
        if (!Session::has('uc_treatment') || !Session::has('uc_plan') || !Session::has('uc_medical_questions')) {
            return redirect()->route('urgent-care.index');
        }

        // Validate the payment form
        $request->validate([
            'payment.first_name' => 'required|string|max:255',
            'payment.last_name' => 'required|string|max:255',
            'payment.email' => 'required|email|max:255',
            'payment.phone' => 'required|string|max:20',
            'payment.card_holder_name' => 'required|string|max:255',
            'payment.card_number' => 'required|string|max:19',
            'payment.expiration_month' => 'required|string|size:2',
            'payment.expiration_year' => 'required|string|size:4',
            'payment.cvv' => 'required|string|max:4',
            'payment.billing_address' => 'required|string|max:255',
            'payment.city' => 'required|string|max:255',
            'payment.state' => 'required|string|max:255',
            'payment.zip' => 'required|string|max:10',
            'agree_terms' => 'required|accepted',
            'measurement.height_feet' => 'required|integer|min:1|max:8',
            'measurement.height_inches' => 'required|integer|min:0|max:11',
            'measurement.weight' => 'required|integer|min:1|max:999',
        ]);

        // Get the treatment and plan from the session
        $treatment = Treatment::findOrFail(Session::get('uc_treatment.id'));
        $plan = SubscriptionPlan::findOrFail(Session::get('uc_plan.id'));

        // Calculate the final amount
        $amount = $plan->price;
        $isDiscounted = false;

        // Use the new discount stacking logic from the model
        $totalDiscount = 0;
        $isDiscounted = false;

        // Insurance options are hidden, so no insurance discount is applied
        // The following code is kept for reference but is now disabled
        /*
        if (Session::get('uc_plan.hasInsurance') === 'yes' && $plan->discounts->isNotEmpty()) {
            $discount = $plan->discounts->first();
            if ($discount->type === 'percentage') {
                $totalDiscount += $amount * ($discount->value / 100);
            } else {
                $totalDiscount += $discount->value;
            }
            $isDiscounted = true;
        }
        */

        // Apply ExtraHelp discount if available
        if (Session::has('stack_discount') && Session::has('extrahelp_discount_id')) {
            $discountId = Session::get('extrahelp_discount_id');
            $extraHelpDiscount = \App\Models\Discount::find($discountId);

            if ($extraHelpDiscount &&
                $extraHelpDiscount->status === 'active' &&
                $extraHelpDiscount->start_date <= now() &&
                $extraHelpDiscount->end_date >= now()) {

                // Calculate remaining amount after previous discounts
                $remainingAmount = $amount - $totalDiscount;

                if ($extraHelpDiscount->type === 'percentage') {
                    $totalDiscount += $remainingAmount * ($extraHelpDiscount->value / 100);
                } else {
                    $totalDiscount += $extraHelpDiscount->value;
                }
                $isDiscounted = true;

                Log::info('ExtraHelp discount applied', [
                    'discount_id' => $extraHelpDiscount->id,
                    'discount_name' => $extraHelpDiscount->name,
                    'discount_type' => $extraHelpDiscount->type,
                    'discount_value' => $extraHelpDiscount->value,
                    'original_amount' => $plan->price,
                    'total_discount' => $totalDiscount
                ]);
            }
        }

        // Apply total discount
        if ($isDiscounted) {
            $amount = $amount - $totalDiscount;
            $amount = max(0, $amount);
        }

        try {
            // Start a database transaction
            DB::beginTransaction();

            // Handle user creation
            Log::info('UrgentCare user creation started', [
                'email' => $request->input('payment.email'),
                'treatment_id' => $treatment->id,
                'plan_id' => $plan->id
            ]);

            $userResult = $this->handleUserCreation->execute([
                'payment_data' => $request->input('payment')
            ]);

            if (!$userResult['success']) {
                Log::warning('UrgentCare user creation failed', [
                    'message' => $userResult['message'] ?? 'Unknown error',
                    'email' => $request->input('payment.email')
                ]);
                return redirect()->back()->withErrors(['payment' => $userResult['message'] ?? 'Failed to create user']);
            }

            Log::info('UrgentCare user creation successful', [
                'user_id' => $userResult['user']->id,
                'email' => $userResult['user']->email
            ]);

            $user = $userResult['user'];

            // Handle measurements
            Log::info('UrgentCare measurements processing started', [
                'user_id' => $user->id,
                'height_feet' => $request->input('measurement.height_feet'),
                'height_inches' => $request->input('measurement.height_inches'),
                'weight' => $request->input('measurement.weight')
            ]);

            $measurementResult = $this->handleMeasurements->execute([
                'user' => $user,
                'measurement_data' => $request->input('measurement')
            ]);

            if (!$measurementResult['success']) {
                Log::warning('UrgentCare measurements processing failed', [
                    'user_id' => $user->id,
                    'message' => $measurementResult['message'] ?? 'Unknown error'
                ]);
                return redirect()->back()->withErrors(['measurement' => $measurementResult['message'] ?? 'Failed to save measurements']);
            }

            Log::info('UrgentCare measurements processing successful', [
                'user_id' => $user->id
            ]);

            // Process payment
            Log::info('UrgentCare payment processing started', [
                'user_id' => $user->id,
                'treatment_id' => $treatment->id,
                'plan_id' => $plan->id,
                'amount' => $amount,
                'is_discounted' => $isDiscounted
            ]);

            $paymentResult = $this->processPayment->execute([
                'user' => $user,
                'treatment' => $treatment,
                'plan' => $plan,
                'payment_data' => $request->input('payment'),
                'is_discounted' => $isDiscounted,
                'amount' => $amount
            ]);

            if (!$paymentResult['success']) {
                // Track the failed transaction in LinkTrust
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->mobile_phone ?? ''
                ];

                $transactionId = 'FAILED-UC-' . time();
                $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );

                Log::warning('UrgentCare payment processing failed', [
                    'user_id' => $user->id,
                    'treatment_id' => $treatment->id,
                    'plan_id' => $plan->id,
                    'message' => $paymentResult['message'] ?? 'Unknown error',
                    'amount' => $amount,
                    'linktrust_tracking' => 'sent'
                ]);
                DB::rollBack();
                return redirect()->back()->withErrors(['payment' => $paymentResult['message']]);
            }

            Log::info('UrgentCare payment processing successful', [
                'user_id' => $user->id,
                'treatment_id' => $treatment->id,
                'plan_id' => $plan->id,
                'amount' => $amount,
                'transaction_id' => $paymentResult['transaction_id'] ?? null
            ]);

            // Commit the transaction
            DB::commit();

            Log::info('UrgentCare transaction committed successfully', [
                'user_id' => $user->id,
                'treatment_id' => $treatment->id,
                'plan_id' => $plan->id,
                'amount' => $amount,
                'transaction_id' => $paymentResult['transaction_id'] ?? null
            ]);

            // Clear the session data except for the transaction ID
            Session::forget(['uc_treatment', 'uc_plan', 'uc_medical_questions']);

            Log::info('UrgentCare session data cleared', [
                'user_id' => $user->id
            ]);

            return redirect()->route('urgent-care.payment.success', ['amount' => $amount]);

        } catch (\Exception $e) {
            // Rollback the transaction
            DB::rollBack();

            // Track the exception in LinkTrust if we have a user
            if (isset($user) && $user) {
                $customerInfo = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->mobile_phone ?? ''
                ];

                $transactionId = 'ERROR-UC-' . time();
                $this->trackLinkTrustFailedTransaction(
                    userId: $user->id,
                    transactionId: $transactionId,
                    customerInfo: $customerInfo
                );
            }

            // Log the error with detailed context
            Log::error('UrgentCare payment processing error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id ?? null,
                'treatment_id' => $treatment->id ?? null,
                'plan_id' => $plan->id ?? null,
                'amount' => $amount ?? 0,
                'is_discounted' => $isDiscounted ?? false,
                'linktrust_tracking' => isset($user) ? 'sent' : 'not sent (no user)'
            ]);

            // Redirect back with an error message
            return redirect()->back()->withErrors(['payment' => 'An error occurred while processing your payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the payment success page
     */
    public function showPaymentSuccess(Request $request)
    {
        $amount = $request->query('amount', 0);

        if (!Session::has('transid')) {
            return redirect()->route('urgent-care.index');
        }

        $transactionId = Session::get('transid');

        // Get the transaction details if the user is logged in
        $transaction = null;
        $consultation = null;
        if (Auth::check()) {
            $transaction = Transaction::where('transaction_id', $transactionId)
                ->where('user_id', Auth::id())
                ->first();

            $consultation = Consultation::where('patient_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->first();
        }

        // Track the purchase using the TracksLinkTrust trait
        $trackingResult = $this->trackLinkTrustPurchase(
            amount: $amount,
            userId: $request->user()->id,
            transactionId: $transactionId
        );

        // Log the tracking result
        Log::info('LinkTrust tracking result for UrgentCare purchase', [
            'result' => $trackingResult,
            'user_id' => $request->user()->id,
            'transaction_id' => $transactionId,
            'amount' => $amount
        ]);

        return view('front.urgent-care.payment-success', [
            'amount' => $amount,
            'transaction' => $transaction,
            'consultation' => $consultation,
        ]);
    }
}
