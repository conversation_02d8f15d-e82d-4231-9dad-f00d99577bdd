<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\SubscriptionPlan;
use App\Services\AuthorizeNetService;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;

class UserServiceController extends Controller
{
    public function __construct(private AuthorizeNetService $authorizeNetService)
    {}

    public function addService(Request $request, Service $service)
    {
        $user = $request->user();
        $plan = $user->subscriptionPlan;

        if (!$plan) {
            Toast::danger('You need to subscribe to a plan first.');
            return redirect()->route('subscriptions.index')->with('error', 'You need to subscribe to a plan first.');
        }

        $activeServicesCount = $user->activeServices()->count();

        if ($plan->isUnlimited()) {
            // For unlimited plan, just add the service
            $user->services()->attach($service->id, ['expires_at' => $user->subscription_ends_at]);
            return redirect()->route('dashboard')->with('success', 'Service added successfully.');
        }

        if ($activeServicesCount >= $plan->service_limit) {
            // If the user has reached their service limit, offer to upgrade or charge for an additional service
            return view('services.upgrade-or-add', compact('service', 'plan'));
        }

        // Add the service within the current plan limits
        $user->services()->attach($service->id, ['expires_at' => $user->subscription_ends_at]);
        return redirect()->route('dashboard')->with('success', 'Service added successfully.');
    }

    public function upgradeOrAddService(Request $request, Service $service)
    {
        $user = $request->user();
        $action = $request->input('action');

        if ($action === 'upgrade') {
            return redirect()->route('subscriptions.change', SubscriptionPlan::where('name', '1 Year Unlimited'));
        } elseif ($action === 'add') {
            $chargeResult = $this->authorizeNetService->chargeCreditCard(99.99, $user);

            if (!$chargeResult['success']) {
                return back()->with('error', 'Failed to charge for additional service: ' . $chargeResult['error']);
            }

            $user->services()->attach($service->id, ['expires_at' => now()->addMonth()]);
            return redirect()->route('dashboard')->with('success', 'Additional service added successfully.');
        }

        return back()->with('error', 'Invalid action.');
    }
}