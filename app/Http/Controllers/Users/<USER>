<?php

namespace App\Http\Controllers\Users;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use Spatie\Permission\Models\Role;

class UpdateUserController extends Controller
{
    public function edit(User $user)
    {
        $statuses = User::STATUSES;
        $allRoles = Role::all();

        return view('users.edit', compact('allRoles', 'statuses', 'user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'roles' => 'required|array',
            'roles.*' => 'integer|exists:roles,id',
            'status' => 'required|string|in:active,inactive,pending_approval,deleted,awaiting_verification,payment_due,on_hold',
            'gender' => 'required|in:M,F,O',
            'dob' => 'required|date|before:today',
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:2',
            'zip' => 'required|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'image' => 'nullable|image|max:2048',
            'phone' => 'required|string|max:20',
            'mobile_phone' => 'nullable|string|max:20',
        ]);

        $user->update([
            'name' => $validated['fname'] . ' ' . $validated['lname'],
            'fname' => $validated['fname'],
            'lname' => $validated['lname'],
            'email' => $validated['email'],
            'status' => $validated['status'],
            'gender' => $validated['gender'],
            'dob' => $validated['dob'],
            'address1' => $validated['address1'],
            'address2' => $validated['address2'],
            'city' => $validated['city'],
            'state' => $validated['state'],
            'zip' => $validated['zip'],
            'bio' => $validated['bio'],
            'phone' => $validated['phone'],
            'mobile_phone' => $validated['mobile_phone'],
        ]);

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('profile_images', 'public');
            $user->image = $imagePath;
            $user->save();
        }

        $roleNames = Role::whereIn('id', $validated['roles'])->pluck('name')->toArray();

        $user->syncRoles($roleNames);

        Toast::success('User updated successfully.')->autoDismiss(10);

        return redirect()->route('admin.users.list');
    }
}
