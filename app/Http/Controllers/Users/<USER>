<?php

namespace App\Http\Controllers\Users;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CreateUserController extends Controller
{
    public function create()
    {
        return view('users.create', [
            'form' => [
                'role' => 'doctor'
            ],
            'roles' => [
                'doctor' => 'Doctor',
                'patient' => 'Patient',
                'pharmacist' => 'Pharmacist',
                'staff' => 'Staff'
            ],
            
        ]);
    }

    public function store(Request $request)
    {
        dd($request);
    }
}
