<?php

namespace App\Http\Controllers\Users;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\Facades\Toast;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class DoctorsSignupController extends Controller
{
    public function create()
    {
        return view('auth.doctors-signup');
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                // Existing User Fields
                'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
                'password' => ['required', 'string', 'min:8', 'confirmed'],
                'fname' => ['required', 'string', 'max:255'],
                'lname' => ['required', 'string', 'max:255'],
                'gender' => ['required', Rule::in(['M', 'F', 'O'])],
                'dob' => ['required', 'date', 'before:today'],
                'address1' => ['required', 'string', 'max:255'],
                'address2' => ['nullable', 'string', 'max:255'],
                'city' => ['required', 'string', 'max:255'],
                'state' => ['required'],
                'zip' => ['required', 'string', 'max:20'],
                'bio' => ['nullable', 'string', 'max:1000'],
                'image' => ['nullable', 'image', 'max:2048'],

                // Phone Fields
                'phone' => ['required', 'string', 'max:20'],
                'mobile_phone' => ['nullable', 'string', 'max:20'],

                // Doctor-Specific Fields
                'specialties' => ['required', 'array'],
                'specialties.*' => ['string', 'max:255'], 
                'other_specialty' => [Rule::requiredIf(in_array('Other', $request->input('specialties') ?? [])), 'string', 'max:255'],
                'primary_license_state' => ['required'],
                'primary_license_number' => ['required', 'string', 'max:255'],
                'dea_number' => ['required', 'string', 'max:255'],
                'licensed_states' => ['required', 'array'],
                'availability' => ['required', Rule::in(['Full-time', 'Part-time', 'Weekends'])],
                'hourly_rate' => ['required', 'numeric', 'min:60'],
                'lowest_consult_rate' => ['required', 'numeric', 'min:15'],
                'consultation_types' => ['required', 'array'],
                'consultation_types.*' => [
                    Rule::in(['Telemedicine', 'In-person', 'Emergency', 'Specialty']),
                ],
                'hours_of_availability' => ['required', 'array'],
                'hours_of_availability.*' => ['array'],
                'hours_of_availability.*.*' => ['string', 'max:255'], // Adjust if you have specific hour formats
                'interstate_license_registered' => ['required', 'boolean'],
            ]);

            $user = User::create([
                'name' => $validated['fname'] . ' ' . $validated['lname'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'fname' => $validated['fname'],
                'lname' => $validated['lname'],
                'gender' => $validated['gender'],
                'dob' => $validated['dob'],
                'address1' => $validated['address1'],
                'address2' => $validated['address2'] ?? null,
                'city' => $validated['city'],
                'state' => $validated['state'],
                'zip' => $validated['zip'],
                'bio' => $validated['bio'] ?? null,
                'phone' => $validated['phone'],
                'mobile_phone' => $validated['mobile_phone'],
                'status' => 'pending_approval',
            ]);

            $user->assignRole('doctor');

            if ($request->hasFile('image')) {
                $imagePath = $request->file('image')->store('profile_images', 'public');
                $user->image = $imagePath;
                $user->save();
            }

            $specialties = $validated['specialties'];
            if (in_array('Other', $specialties) && !empty($validated['other_specialty'])) {
                $specialties = array_diff($specialties, ['Other']);
                $specialties[] = $validated['other_specialty'];
            }

            $user->doctorProfile()->create([
                'specialties' => $specialties,
                'primary_license_state' => $validated['primary_license_state'],
                'primary_license_number' => $validated['primary_license_number'],
                'dea_number' => $validated['dea_number'],
                'licensed_states' => $validated['licensed_states'],
                'availability' => $validated['availability'],
                'hourly_rate' => $validated['hourly_rate'],
                'lowest_consult_rate' => $validated['lowest_consult_rate'],
                'consultation_types' => json_encode($validated['consultation_types']),
                'hours_of_availability' => json_encode($validated['hours_of_availability']),
                'interstate_license_registered' => $validated['interstate_license_registered'],
            ]);

            Toast::success('Doctor account created successfully. Awaiting approval.');
            return redirect()->route('home');
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = $e->validator->errors()->all();
            foreach ($errors as $error) {
                Toast::danger($error)->autoDismiss(10);
            }
            throw $e;
        }
    }
}
