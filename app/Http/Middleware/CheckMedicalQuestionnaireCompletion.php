<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckMedicalQuestionnaireCompletion
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        // Only check for authenticated users with patient or employee roles
        if (!$user || !$user->hasAnyRole(['patient', 'employee'])) {
            return $next($request);
        }

        // Only check on dashboard routes
        $routeName = $request->route()->getName();
        $dashboardRoutes = [
            'patient.dashboard',
            'employee.dashboard'
        ];

        if (!in_array($routeName, $dashboardRoutes)) {
            return $next($request);
        }

        // Check if medical questionnaire is incomplete and not temporarily dismissed
        if (!$user->hasMedicalQuestionnaire() && !$user->hasTemporarilyDismissedMedicalQuestionnaire()) {
            // Add medical questionnaire completion data to the request
            $request->merge([
                'show_medical_questionnaire_modal' => true
            ]);
        }

        return $next($request);
    }
}
