<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class DetectBots
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $score = 0;
        
        // Check for common bot indicators
        if (empty($request->header('Accept-Language'))) $score += 2;
        if (empty($request->header('User-Agent'))) $score += 2;
        if (!$request->header('Accept')) $score += 1;
        if ($request->header('Accept') == '*/*') $score += 1;
        
        // Check request speed (needs session)
        if (session()->has('last_request')) {
            $timeDiff = time() - session('last_request');
            if ($timeDiff < 1) $score += 2; // Too fast for human
        }
        session(['last_request' => time()]);
        
        // Log suspicious activity
        if ($score >= 3) {
            Log::channel('bot-detection')->info('Suspicious Request', [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'score' => $score,
                'path' => $request->path()
            ]);
            
            // Optional: Block very suspicious requests
            if ($score >= 5) {
                return response('Access Denied', 403);
            }
        }

        return $next($request);
    }
}
