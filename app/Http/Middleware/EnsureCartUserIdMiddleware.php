<?php

namespace App\Http\Middleware;

use App\Models\Cart;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class EnsureCartUserIdMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only process cart-related requests
        $isCartRequest = strpos($request->path(), 'cart') !== false ||
                         strpos($request->path(), 'medications') !== false;

        if ($isCartRequest) {
            // Get the current session ID
            $sessionId = Session::getId();

            // Check if user is authenticated
            if (Auth::check()) {
                $userId = Auth::id();

                // Log for debugging
                Log::info('EnsureCartUserIdMiddleware', [
                    'session_id' => $sessionId,
                    'user_id' => $userId,
                    'is_authenticated' => true,
                    'path' => $request->path()
                ]);

                // Only update cart items for new transactions
                // We're not updating existing cart items as per the requirement

                // Merge any existing cart items that might have the same medication variant
                // but only for future transactions
                if ($request->isMethod('POST') || $request->isMethod('PATCH')) {
                    $this->mergeCartItems($sessionId, $userId);
                }
            } else {
                // Log for debugging
                Log::info('EnsureCartUserIdMiddleware - User not authenticated', [
                    'session_id' => $sessionId,
                    'path' => $request->path()
                ]);
            }
        }

        return $next($request);
    }

    /**
     * Merge cart items with the same medication variant
     *
     * @param string $sessionId
     * @param int $userId
     * @return void
     */
    private function mergeCartItems(string $sessionId, int $userId): void
    {
        // Get all cart items for this user
        $cartItems = Cart::where('user_id', $userId)->get();

        // Group by medication variant ID
        $groupedItems = $cartItems->groupBy('medication_variant_id');

        foreach ($groupedItems as $variantId => $items) {
            // If there's more than one item with the same variant, merge them
            if ($items->count() > 1) {
                // Keep the first item and update its quantity
                $firstItem = $items->first();
                $totalQuantity = $items->sum('quantity');

                // Update the first item with the total quantity
                $firstItem->quantity = $totalQuantity;
                $firstItem->save();

                // Delete all other items except the first one
                Cart::where('user_id', $userId)
                    ->where('medication_variant_id', $variantId)
                    ->where('id', '!=', $firstItem->id)
                    ->delete();

                Log::info('Merged cart items', [
                    'variant_id' => $variantId,
                    'total_quantity' => $totalQuantity,
                    'merged_items' => $items->count() - 1
                ]);
            }
        }
    }
}
