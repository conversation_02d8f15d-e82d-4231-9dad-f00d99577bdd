<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Discount;
use Illuminate\Support\Facades\Log;

class PromoCodeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // This middleware now just ensures the discount is properly applied across requests
        // The ExtraHelpController already sets the session values we need

        // We can still support query parameter promos for flexibility
        if ($request->has('promo')) {
            $promoCode = $request->input('promo');

            // Find the discount by promo code
            $discount = Discount::where('promo_code', $promoCode)
                ->where('status', Discount::STATUS_ACTIVE)
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->first();

            if ($discount) {
                // Store the discount in the session for later use
                $request->session()->put('applied_discount_id', $discount->id);
                $request->session()->put('stack_discount', true); // Changed from override_discount to stack_discount
                $request->session()->put('promo_code', $promoCode);

                Log::info('Promo code applied via query parameter', [
                    'promo_code' => $promoCode,
                    'discount_id' => $discount->id,
                    'discount_name' => $discount->name,
                    'discount_value' => $discount->value,
                    'discount_type' => $discount->type
                ]);
            } else {
                Log::info('Invalid promo code', ['promo_code' => $promoCode]);
            }
        }

        // If we have extrahelp_applied in the flash data, log it for debugging
        if ($request->session()->has('extrahelp_applied')) {
            Log::info('ExtraHelp discount applied via route', [
                'discount_id' => $request->session()->get('extrahelp_discount_id'),
                'stack_discount' => $request->session()->get('stack_discount'),
                'promo_code' => $request->session()->get('promo_code')
            ]);
        }

        return $next($request);
    }
}
