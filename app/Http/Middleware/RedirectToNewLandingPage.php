<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectToNewLandingPage
{
    /**
     * List of query parameters to keep in redirect
     */
    protected $allowedParams = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'ref'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('landing-page*') || $request->is('LANDING-PAGE*')) {
            $queryParams = collect($request->query())
                // ->only($this->allowedParams)
                // ->filter()
                ->all();

            if (empty($queryParams))
                return redirect('/discount');
            else
                return redirect()->to('/discount?' . http_build_query($queryParams));
        }

        return $next($request);
    }
}
