<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class StoreAgentReferralInCookie
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the request has a referral code
        if ($request->has('agent_ref')) {
            $referralCode = $request->input('agent_ref');

            // Store the referral code in a cookie that lasts 30 days
            $cookie = cookie('agent_referral', $referralCode, 43200); // 30 days in minutes

            // Also store in session as a backup
            $request->session()->put('agent_referral', $referralCode);

            // Find the agent by referral code
            $agent = \App\Models\Agent::where('referral_code', $referralCode)
                ->where('status', 'approved')
                ->first();

            if ($agent) {
                // Store the agent ID in a cookie for attribution
                $agentIdCookie = cookie('referring_agent_id', $agent->id, 43200); // 30 days in minutes
                $request->session()->put('referring_agent_id', $agent->id);

                // Log the referral for tracking
                \Illuminate\Support\Facades\Log::info('Agent referral tracked', [
                    'code' => $referralCode,
                    'agent_id' => $agent->id,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'path' => $request->path(),
                    'url' => $request->url(),
                    'session_id' => $request->session()->getId()
                ]);

                // Continue with the request and attach the cookies to the response
                return $next($request)->withCookie($cookie)->withCookie($agentIdCookie);
            } else {
                // Log invalid referral code
                \Illuminate\Support\Facades\Log::warning('Invalid agent referral code', [
                    'code' => $referralCode,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]);
            }

            // Continue with the request and attach the cookie to the response
            return $next($request)->withCookie($cookie);
        }

        return $next($request);
    }
}
