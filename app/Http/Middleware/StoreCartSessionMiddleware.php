<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class StoreCartSessionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get the current session ID
        $sessionId = Session::getId();

        // Store the session ID in a cookie that will be used for cart tracking
        // This ensures we can find cart items even if the session changes during login
        if (!$request->cookie('original_session_id')) {
            Cookie::queue('original_session_id', $sessionId, 60 * 24 * 7); // 1 week

            Log::info('Stored original session ID in cookie', [
                'session_id' => $sessionId,
                'path' => $request->path()
            ]);
        }

        // Store a list of all session IDs we've seen
        $allSessionIds = json_decode($request->cookie('all_session_ids') ?? '[]', true);

        // Add the current session ID if it's not already in the list
        if (!in_array($sessionId, $allSessionIds)) {
            $allSessionIds[] = $sessionId;

            // Store the updated list in a cookie
            Cookie::queue('all_session_ids', json_encode($allSessionIds), 60 * 24 * 7); // 1 week

            Log::info('Updated all session IDs cookie', [
                'session_id' => $sessionId,
                'all_session_ids' => $allSessionIds,
                'path' => $request->path()
            ]);
        }

        // If this is a cart-related request, log more details
        if (strpos($request->path(), 'cart') !== false || strpos($request->path(), 'medication') !== false) {
            Log::info('Cart-related request', [
                'session_id' => $sessionId,
                'path' => $request->path(),
                'method' => $request->method(),
                'all_session_ids' => $allSessionIds
            ]);
        }

        return $next($request);
    }
}
