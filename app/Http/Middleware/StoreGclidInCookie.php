<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class StoreGclidInCookie
{
    public function handle(Request $request, Closure $next)
    {
        // Check if gclid exists in the URL
        if ($gclid = $request->query('gclid')) {
            // Store in cookie that lasts 30 days
            Cookie::queue('gclid', $gclid, 43200); // 43200 minutes = 30 days
        }

        return $next($request);
    }
}
