<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class StoreLinkTrustClickIdInCookie
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if ClickID exists in the URL
        if ($clickId = $request->query('ClickID')) {
            // Store in cookie that lasts 30 days
            Cookie::queue('LTClickID', $clickId, 43200); // 43200 minutes = 30 days
            
            // Also store in session for backward compatibility
            session()->put('LTClickID', $clickId);
        }

        // Check if AFID exists in the URL (for recurring billing)
        if ($afid = $request->query('AFID')) {
            // Store in cookie that lasts 30 days
            Cookie::queue('AFID', $afid, 43200); // 43200 minutes = 30 days
            
            // Also store in session
            session()->put('AFID', $afid);
        }

        return $next($request);
    }
}
