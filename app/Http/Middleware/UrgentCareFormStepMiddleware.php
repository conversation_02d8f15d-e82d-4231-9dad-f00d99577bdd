<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class UrgentCareFormStepMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $step = (int) $request->route('step', 1);
        $sessionData = $request->session()->get('urgent_care_form_data', []);

        // If trying to access a step beyond what's allowed
        $maxAllowedStep = isset($sessionData['step']) ? min((int)$sessionData['step'] + 1, 4) : 1;

        if ($step > $maxAllowedStep) {
            return redirect()->route('urgent-care.questions-form', ['step' => $maxAllowedStep]);
        }

        return $next($request);
    }
}
