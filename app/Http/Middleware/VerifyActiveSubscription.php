<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use ProtoneMedia\Splade\Facades\Toast;

class VerifyActiveSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user || !$user->hasRole('patient')) {
            abort(403, 'Unauthorized action.');
        }

        $activeSubscription = $user->activeSubscription();

        if (!$activeSubscription) {
            Toast::warning('You need an active subscription to access this feature. Please select a subscription plan below.');
            return redirect()->route('patient.subscriptions.index');
        }

        return $next($request);
    }
}
