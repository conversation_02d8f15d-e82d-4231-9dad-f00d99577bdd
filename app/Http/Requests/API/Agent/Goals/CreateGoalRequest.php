<?php

namespace App\Http\Requests\API\Agent\Goals;

use Illuminate\Foundation\Http\FormRequest;

class CreateGoalRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasR<PERSON>('agent');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'required|string|in:commissions,referrals',
            'target_value' => 'required|numeric|min:1',
            'target_date' => 'required|date|after:today',
            'description' => 'required|string|max:255'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Goal type is required',
            'type.in' => 'Goal type must be either commissions or referrals',
            'target_value.required' => 'Target value is required',
            'target_value.numeric' => 'Target value must be a number',
            'target_value.min' => 'Target value must be at least 1',
            'target_date.required' => 'Target date is required',
            'target_date.date' => 'Target date must be a valid date',
            'target_date.after' => 'Target date must be in the future',
            'description.required' => 'Goal description is required',
            'description.max' => 'Goal description cannot exceed 255 characters'
        ];
    }
}
