<?php

namespace App\Http\Requests\API\Agent\Reports;

use Illuminate\Foundation\Http\FormRequest;

class CreateReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasR<PERSON>('agent');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:commission,referral,performance',
            'columns' => 'required|array',
            'columns.*' => 'string',
            'filters' => 'required|array',
            'sorting' => 'required|array',
            'sorting.*.column' => 'required|string',
            'sorting.*.direction' => 'required|string|in:asc,desc',
            'is_scheduled' => 'boolean',
            'schedule_frequency' => 'required_if:is_scheduled,true|string|in:daily,weekly,monthly,quarterly',
            'schedule_time' => 'required_if:is_scheduled,true|string'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Report name is required',
            'name.max' => 'Report name cannot exceed 255 characters',
            'type.required' => 'Report type is required',
            'type.in' => 'Report type must be commission, referral, or performance',
            'columns.required' => 'Report columns are required',
            'columns.array' => 'Report columns must be an array',
            'filters.required' => 'Report filters are required',
            'filters.array' => 'Report filters must be an array',
            'sorting.required' => 'Report sorting is required',
            'sorting.array' => 'Report sorting must be an array',
            'sorting.*.column.required' => 'Each sorting entry must have a column',
            'sorting.*.direction.required' => 'Each sorting entry must have a direction',
            'sorting.*.direction.in' => 'Sorting direction must be asc or desc',
            'schedule_frequency.required_if' => 'Schedule frequency is required for scheduled reports',
            'schedule_frequency.in' => 'Schedule frequency must be daily, weekly, monthly, or quarterly',
            'schedule_time.required_if' => 'Schedule time is required for scheduled reports'
        ];
    }
}
