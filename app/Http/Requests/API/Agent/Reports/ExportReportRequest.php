<?php

namespace App\Http\Requests\API\Agent\Reports;

use Illuminate\Foundation\Http\FormRequest;

class ExportReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasR<PERSON>('agent');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'format' => 'required|string|in:csv,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'format.required' => 'Export format is required',
            'format.in' => 'Export format must be csv or pdf',
            'date_from.date' => 'Start date must be a valid date',
            'date_to.date' => 'End date must be a valid date',
            'date_to.after_or_equal' => 'End date must be after or equal to start date'
        ];
    }
}
