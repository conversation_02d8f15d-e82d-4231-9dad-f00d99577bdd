<?php

namespace App\Http\Requests\Admin\FamilyMember;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AdminAddDependentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole(['admin', 'staff']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $subscription = $this->route('subscription');
        $canAddOlderDependent = !$subscription->hasReachedMaxOlderDependents();
        $canAddYoungerDependent = !$subscription->hasReachedMaxYoungerDependents();
        $canAddMoreDependents = !$subscription->hasReachedMaxDependents();

        // If admin is overriding age restrictions, we don't need to validate age limits
        if ($this->override_age_restrictions) {
            return [
                'fname' => 'required|string|max:255',
                'lname' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:users,email',
                'dob' => 'required|date|before_or_equal:today',
                'gender' => 'required|in:m,f,o',
                'relationship_type' => ['required', Rule::in(['spouse', 'child', 'other'])],
                'address1' => 'required|string|max:255',
                'address2' => 'nullable|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'zip' => 'required|string|max:20',
                'phone' => 'nullable|string|max:20',
                'mobile_phone' => 'required|string|max:20',
                'override_age_restrictions' => 'sometimes|boolean',
                'override_reason' => 'required_if:override_age_restrictions,1|string|max:500',
            ];
        }

        // Standard validation rules when not overriding
        $rules = [
            'fname' => 'required|string|max:255',
            'lname' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'dob' => 'required|date|before_or_equal:today',
            'gender' => 'required|in:m,f,o',
            'relationship_type' => ['required', Rule::in(['spouse', 'child', 'other'])],
            'address1' => 'required|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'zip' => 'required|string|max:20',
            'phone' => 'nullable|string|max:20',
            'mobile_phone' => 'required|string|max:20',
            'override_age_restrictions' => 'sometimes|boolean',
        ];

        // Add age validation based on available slots
        if (!$canAddMoreDependents) {
            $rules['dob'] = array_merge(
                (array)$rules['dob'],
                ['prohibited']
            );
        } elseif (!$canAddOlderDependent && !$canAddYoungerDependent) {
            $rules['dob'] = array_merge(
                (array)$rules['dob'],
                ['prohibited']
            );
        } elseif (!$canAddOlderDependent) {
            // Can only add younger dependents (23 or younger)
            $rules['dob'] = array_merge(
                (array)$rules['dob'],
                ['after:' . now()->subYears(24)->format('Y-m-d')]
            );
        } elseif (!$canAddYoungerDependent) {
            // Can only add older dependents (24+)
            $rules['dob'] = array_merge(
                (array)$rules['dob'],
                ['before:' . now()->subYears(24)->format('Y-m-d')]
            );
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        $subscription = $this->route('subscription');
        $canAddOlderDependent = !$subscription->hasReachedMaxOlderDependents();
        $canAddYoungerDependent = !$subscription->hasReachedMaxYoungerDependents();
        $canAddMoreDependents = !$subscription->hasReachedMaxDependents();

        $messages = [
            'dob.prohibited' => 'Cannot add more dependents. The maximum limit has been reached.',
            'override_reason.required_if' => 'Please provide a reason for overriding age restrictions.',
        ];

        if (!$canAddOlderDependent && $canAddYoungerDependent) {
            $messages['dob.after'] = 'This family plan has reached its limit of older dependents (24+). You can only add dependents who are 23 years old or younger.';
        }

        if (!$canAddYoungerDependent && $canAddOlderDependent) {
            $messages['dob.before'] = 'This family plan has reached its limit of younger dependents (≤23). You can only add dependents who are 24 years old or older.';
        }

        return $messages;
    }
}
