<?php

namespace App\Http\Requests\Admin\FamilyMember;

use Illuminate\Foundation\Http\FormRequest;

class AdminOverrideAgeRestrictionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole(['admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'max_older_dependents' => 'required|integer|min:0|max:10',
            'max_younger_dependents' => 'required|integer|min:0|max:10',
            'max_total_dependents' => 'required|integer|min:1|max:15|gte:sum_of_dependents',
            'reason' => 'required|string|max:500',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->addRule('sum_of_dependents', function ($attribute, $value, $parameters, $validator) {
            return $this->input('max_older_dependents') + $this->input('max_younger_dependents');
        });

        $validator->after(function ($validator) {
            $maxTotal = $this->input('max_total_dependents');
            $maxOlder = $this->input('max_older_dependents');
            $maxYounger = $this->input('max_younger_dependents');
            
            if ($maxOlder + $maxYounger > $maxTotal) {
                $validator->errors()->add(
                    'max_total_dependents', 
                    'The maximum total dependents must be at least equal to the sum of maximum older and younger dependents.'
                );
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'max_total_dependents.gte' => 'The maximum total dependents must be at least equal to the sum of maximum older and younger dependents.',
        ];
    }
}
