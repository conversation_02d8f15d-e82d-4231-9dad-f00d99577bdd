<?php

namespace App\Http\Requests\Business;

use Illuminate\Foundation\Http\FormRequest;

class AddBusinessPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'plan_quantity' => 'required|integer|min:5',
            'price_per_plan' => 'required|integer|min:1',
            'duration_months' => 'required|integer|in:1,3,6,12',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'plan_quantity.required' => 'Please specify the number of plans to add',
            'plan_quantity.integer' => 'The plan quantity must be a whole number',
            'plan_quantity.min' => 'Please add at least 5 plans (1 block)',

            'price_per_plan.required' => 'Please specify the price per plan',
            'price_per_plan.integer' => 'The price per plan must be a whole number',
            'price_per_plan.min' => 'The price per plan must be at least 1 cent',

            'duration_months.required' => 'Please select a plan duration',
            'duration_months.integer' => 'The plan duration must be a whole number',
            'duration_months.in' => 'The plan duration must be 1, 3, 6, or 12 months',
        ];
    }
}
