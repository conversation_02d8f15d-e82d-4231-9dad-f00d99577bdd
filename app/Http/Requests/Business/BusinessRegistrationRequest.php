<?php

namespace App\Http\Requests\Business;

use Illuminate\Foundation\Http\FormRequest;

class BusinessRegistrationRequest extends FormRequest
{
    public function rules()
    {
        return [
            // Step 0 - Business Information
            'business_name' => 'required_if:step,1|string|max:255',
            'business_name' => 'required_if:step,1|string|max:255',
            'business_email' => 'required_if:step,1|email|unique:businesses,email',
            'phone' => 'required_if:step,1|min:10|max:20',
            'address1' => 'required_if:step,1|string|max:255',
            'address2' => 'nullable|string|max:255',
            'city' => 'required_if:step,1|string|max:255',
            'state' => 'required_if:step,1|string|size:2',
            'zip' => 'required_if:step,1|digits:5',
            'trial_enabled' => 'boolean',

            // Step 1 - Contact Information
            'owner_fname' => 'required_if:step,2|string|max:255',
            'owner_lname' => 'required_if:step,2|string|max:255',
            'billing_contact_email' => 'required_if:step,2|email',
            'billing_contact_phone' => 'nullable|min:10|max:20',
            'hr_contact_email' => 'nullable|email',
            'hr_contact_phone' => 'nullable|min:10|max:20',

            // Step 2 - Plan Selection
            'plan_quantity' => 'required_if:step,3|integer|min:5',

            // Step 3 - Terms
            'terms' => 'accepted_if:step,4',
        ];
    }

    public function messages()
    {
        return [
            'business_name.required' => 'Please enter your business name',
            'business_email.required' => 'Please enter your business email',
            'business_email.email' => 'Please enter a valid email address',
            'business_email.unique' => 'This email is already registered',
            'phone.required' => 'Please enter your business phone number',
            'phone.min' => 'Please enter a valid phone number',
            'phone.max' => 'Please enter a valid phone number',
            'address1.required' => 'Please enter your business address',
            'city.required' => 'Please enter your business address',
            'state.required' => 'Please enter your business address',
            'zip.required' => 'Please enter your business address',
            'owner_name.required_if' => 'Please enter the business owner name',
            'billing_contact_email.required_if' => 'Please enter a billing contact email',
            'billing_contact_email.email' => 'Please enter a valid billing email address',
            'billing_contact_phone.min' => 'Please enter a valid phone number',
            'billing_contact_phone.max' => 'Please enter a valid phone number',
            'hr_contact_email.email' => 'Please enter a valid HR email address',
            'hr_contact_phone.min' => 'Please enter a valid phone number',
            'hr_contact_phone.max' => 'Please enter a valid phone number',
            'plan_quantity.required_if' => 'Please select the number of plans',
            'plan_quantity.min' => 'Please select at least 5 plans (1 block)',
            'terms.accepted_if' => 'Please accept the terms and conditions',
        ];
    }
}
