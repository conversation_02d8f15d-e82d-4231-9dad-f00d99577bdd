<?php

namespace App\Http\Requests\Business;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class CreditCardRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'card_number' => 'required|string|min_digits:14|max_digits:19',
            'expiration_month' => 'required|string|size:2|between:01,12',
            'expiration_year' => 'required|string|size:2',
            'cvv' => 'required|string|min_digits:3|max_digits:4',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'card_number.required' => 'Card number is required',
            'card_number.min_digits' => 'Card number cannot be less than 14 digits',
            'card_number.max_digits' => 'Card number cannot exceed 19 digits',
            'expiration_month.required' => 'Expiration month is required',
            'expiration_month.size' => 'Expiration month must be 2 digits',
            'expiration_month.between' => 'Expiration month must be between 01 and 12',
            'expiration_year.required' => 'Expiration year is required',
            'expiration_year.size' => 'Expiration year must be 2 digits',
            'cvv.required' => 'CVV is required',
            'cvv.min_digits' => 'CVV must be 3 or 4 digits',
            'cvv.max_digits' => 'CVV must be 3 or 4 digits',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param \Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate expiration date isn't in the past
            $expMonth = $this->expiration_month;
            $expYear = '20' . $this->expiration_year;

            $expirationDate = Carbon::createFromDate($expYear, $expMonth, 1)->endOfMonth();
            $today = Carbon::today();

            if ($expirationDate->lt($today)) {
                $validator->errors()->add('expiration_date', 'Card has expired');
            }

            // Validate card number using Luhn algorithm if needed
            if (!$this->validateLuhn($this->card_number)) {
                $validator->errors()->add('card_number', 'Invalid card number');
            }
        });
    }

    /**
     * Validate a credit card number with Luhn algorithm
     *
     * @param string $number
     * @return bool
     */
    private function validateLuhn($number)
    {
        // Remove any non-digits
        $number = preg_replace('/\D/', '', $number);

        // Luhn algorithm implementation
        $sum = 0;
        $alt = false;

        for ($i = strlen($number) - 1; $i >= 0; $i--) {
            $digit = $number[$i];

            if ($alt) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }

            $sum += $digit;
            $alt = !$alt;
        }

        return $sum % 10 === 0;
    }
}
