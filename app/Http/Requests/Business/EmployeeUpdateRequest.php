<?php

namespace App\Http\Requests\Business;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class EmployeeUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Ensure the employee belongs to the current business
        $employee = $this->route('employee');
        return $employee && $employee->business_id === Auth::user()->business_id;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255',
            'phone' => 'sometimes|string|max:20|nullable',
            'address1' => 'sometimes|string|max:255|nullable',
            'address2' => 'sometimes|string|max:255|nullable',
            'city' => 'sometimes|string|max:100|nullable',
            'state' => 'sometimes|string|max:100|nullable',
            'zip' => 'sometimes|string|max:20|nullable',
        ];
    }
}
