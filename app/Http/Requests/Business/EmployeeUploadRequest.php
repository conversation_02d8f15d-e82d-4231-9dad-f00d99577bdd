<?php

namespace App\Http\Requests\Business;

use Illuminate\Foundation\Http\FormRequest;

class EmployeeUploadRequest extends FormRequest
{
    public function rules()
    {
        return [
            'csv_file' => 'required|file|mimes:csv,txt|max:10240',
        ];
    }

    public function messages()
    {
        return [
            'csv_file.required' => 'Please select a CSV file to upload',
            'csv_file.mimes' => 'File must be a CSV file',
            'csv_file.max' => 'File size cannot exceed 10MB',
        ];
    }
}
