<?php

namespace App\Http\Requests\Employee;

use Illuminate\Foundation\Http\FormRequest;

class SelfPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'card_number' => [
                'required',
                'string',
                'regex:/^\d{13,19}$/',
            ],
            'expiration_month' => [
                'required',
                'string',
                'regex:/^(0[1-9]|1[0-2])$/',
            ],
            'expiration_year' => [
                'required',
                'string',
                'regex:/^\d{4}$/',
                'after_or_equal:' . date('Y'),
            ],
            'cvv' => [
                'required',
                'string',
                'regex:/^\d{3,4}$/',
            ],
            'cardholder_name' => [
                'required',
                'string',
                'max:255',
            ],
            'billing_address1' => [
                'sometimes',
                'nullable',
                'string',
                'max:255',
            ],
            'billing_city' => [
                'sometimes',
                'nullable',
                'string',
                'max:100',
            ],
            'billing_state' => [
                'sometimes',
                'nullable',
                'string',
                'max:50',
            ],
            'billing_zip' => [
                'sometimes',
                'nullable',
                'string',
                'max:20',
            ],
            'terms_accepted' => [
                'required',
                'accepted',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'card_number.required' => 'Credit card number is required.',
            'card_number.regex' => 'Please enter a valid credit card number (13-19 digits).',
            'expiration_month.required' => 'Expiration month is required.',
            'expiration_month.regex' => 'Please select a valid expiration month (01-12).',
            'expiration_year.required' => 'Expiration year is required.',
            'expiration_year.regex' => 'Please enter a valid 4-digit year.',
            'expiration_year.after_or_equal' => 'The expiration year must be current year or later.',
            'cvv.required' => 'CVV is required.',
            'cvv.regex' => 'Please enter a valid CVV (3-4 digits).',
            'cardholder_name.required' => 'Cardholder name is required.',
            'cardholder_name.max' => 'Cardholder name must not exceed 255 characters.',
            'terms_accepted.required' => 'You must accept the terms and conditions.',
            'terms_accepted.accepted' => 'You must accept the terms and conditions to proceed.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'card_number' => 'credit card number',
            'expiration_month' => 'expiration month',
            'expiration_year' => 'expiration year',
            'cvv' => 'CVV',
            'cardholder_name' => 'cardholder name',
            'billing_address1' => 'billing address',
            'billing_city' => 'billing city',
            'billing_state' => 'billing state',
            'billing_zip' => 'billing ZIP code',
            'terms_accepted' => 'terms and conditions',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Remove any spaces or dashes from card number
        if ($this->has('card_number')) {
            $this->merge([
                'card_number' => preg_replace('/[\s\-]/', '', $this->card_number),
            ]);
        }

        // Ensure expiration month is zero-padded
        if ($this->has('expiration_month') && strlen($this->expiration_month) === 1) {
            $this->merge([
                'expiration_month' => '0' . $this->expiration_month,
            ]);
        }
    }

    /**
     * Get the sanitized payment data.
     */
    public function getPaymentData(): array
    {
        return [
            'card_number' => $this->card_number,
            'expiration_month' => $this->expiration_month,
            'expiration_year' => $this->expiration_year,
            'cvv' => $this->cvv,
            'cardholder_name' => $this->cardholder_name,
            'billing_address' => [
                'address1' => $this->billing_address1,
                'city' => $this->billing_city,
                'state' => $this->billing_state,
                'zip' => $this->billing_zip,
            ],
        ];
    }
}
