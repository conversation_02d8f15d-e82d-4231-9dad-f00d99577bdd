<?php

namespace App\Http\Requests\FamilyMember;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreFamilyMemberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Check if the user has an active family plan subscription
        $user = $this->user();
        $subscription = $user->activeSubscription();

        return $subscription &&
               $subscription->plan->isFamilyPlan() &&
               $subscription->isPrimaryAccount();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->user();
        $subscription = $user->activeSubscription();
        $canAddOlderDependent = !$subscription->hasReachedMaxOlderDependents();
        $canAddYoungerDependent = !$subscription->hasReachedMaxYoungerDependents();
        $canAddMoreDependents = !$subscription->hasReachedMaxDependents();

        $rules = [
            'fname' => ['required', 'string', 'max:255'],
            'lname' => ['required', 'string', 'max:255'],
            'relationship_type' => ['required', Rule::in(['spouse', 'child', 'other'])],
            'dob' => [
                'required',
                'date',
                'before_or_equal:today',
                function ($attribute, $value, $fail) use ($canAddOlderDependent, $canAddYoungerDependent, $canAddMoreDependents) {
                    if (!$canAddMoreDependents) {
                        $fail('You have reached the maximum number of dependents (5) for your family plan.');
                        return;
                    }

                    $age = Carbon::parse($value)->age;

                    // Handle older dependents (24+)
                    if ($age >= 24) {
                        if (!$canAddOlderDependent) {
                            $fail('You have reached the maximum number of older dependents (24+ years old) for your plan. Only one older dependent is allowed.');
                        }
                        return; // Age validation passes for older dependents if we can add one
                    }

                    // Handle younger dependents (23 or younger)
                    if (!$canAddYoungerDependent) {
                        $fail('You have reached the maximum number of younger dependents (23 years old or younger) for your plan. You can add up to 4 younger dependents.');
                        return;
                    }

                    // No specific limit on adult dependents (18-23) as long as the total younger dependents limit is not exceeded
                },
            ],
            'gender' => ['sometimes', 'nullable', 'string', Rule::in(['m', 'f', 'o'])],
            'address1' => ['sometimes', 'nullable', 'string', 'max:255'],
            'address2' => ['sometimes', 'nullable', 'string', 'max:255'],
            'city' => ['sometimes', 'nullable', 'string', 'max:255'],
            'state' => ['sometimes', 'nullable', 'string', 'max:255'],
            'zip' => ['sometimes', 'nullable', 'string', 'max:20'],
            'phone' => ['sometimes', 'nullable', 'string', 'max:20'],
            'mobile_phone' => ['sometimes', 'nullable', 'string', 'max:20'],
        ];

        // Email is required for adult dependents (18-23) and older dependents (24+) who need their own login
        if ($this->has('dob')) {
            $age = Carbon::parse($this->dob)->age;

            if ($age >= 18) {
                $rules['email'] = [
                    'required',
                    'string',
                    'email',
                    'max:255',
                    Rule::unique('users', 'email'),
                ];
            } else {
                $rules['email'] = [
                    'sometimes',
                    'nullable',
                    'string',
                    'email',
                    'max:255',
                    Rule::unique('users', 'email'),
                ];
            }
        } else {
            $rules['email'] = [
                'sometimes',
                'nullable',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email'),
            ];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'fname.required' => 'The first name is required.',
            'lname.required' => 'The last name is required.',
            'relationship_type.required' => 'Please specify the relationship to the dependent.',
            'dob.required' => 'The date of birth is required.',
            'dob.before_or_equal' => 'The date of birth must be today or in the past.',
            'email.required' => 'Email is required for dependents 18 years or older.',
            'email.unique' => 'This email is already in use. Please use a different email address.',
        ];
    }
}
