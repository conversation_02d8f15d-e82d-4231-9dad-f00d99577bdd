<?php

namespace App\Http\Requests\LandingPage;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;

class HealthPlanPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            // Personal Information
            'payment.first_name' => ['required', 'string', 'max:255'],
            'payment.last_name' => ['required', 'string', 'max:255'],
            'payment.email' => ['required', 'email', 'max:255'],
            'payment.phone' => ['required', 'string', 'max:20'],

            // Address Information
            'payment.address1' => ['required', 'string', 'max:255'],
            'payment.address2' => ['nullable', 'string', 'max:255'],
            'payment.city' => ['required', 'string', 'max:255'],
            'payment.state' => ['required', 'string', 'max:2'],
            'payment.zip' => ['required', 'string', 'max:10'],

            // Payment Details
            'payment.card_number' => ['required', 'string', 'min:13', 'max:19'],
            'payment.exp_month' => ['required', 'string', 'size:2'],
            'payment.exp_year' => ['required', 'string', 'size:4'],
            'payment.cvv' => ['required', 'string', 'min:3', 'max:4'],

            // Terms and Conditions
            'terms' => ['required', 'accepted'],
        ];

        // If the user is not logged in, require a unique email and password
        if (!auth()->check()) {
            $rules['payment.email'] = ['required', 'email', 'max:255', Rule::unique(User::class, 'email')];
            // $rules['payment.password'] = ['required', 'string', 'min:8', 'max:255'];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'payment.first_name.required' => 'First name is required.',
            'payment.last_name.required' => 'Last name is required.',
            'payment.email.required' => 'Email address is required.',
            'payment.email.email' => 'Please enter a valid email address.',
            'payment.email.unique' => 'This email is already registered. Please log in instead.',
            'payment.phone.required' => 'Phone number is required.',

            'payment.address1.required' => 'Street address is required.',
            'payment.city.required' => 'City is required.',
            'payment.state.required' => 'State is required.',
            'payment.zip.required' => 'ZIP code is required.',

            'payment.card_number.required' => 'Card number is required.',
            'payment.card_number.min' => 'Card number must be at least 13 digits.',
            'payment.card_number.max' => 'Card number cannot exceed 19 digits.',
            'payment.exp_month.required' => 'Expiration month is required.',
            'payment.exp_year.required' => 'Expiration year is required.',
            'payment.cvv.required' => 'CVV is required.',
            'payment.cvv.min' => 'CVV must be at least 3 digits.',
            'payment.cvv.max' => 'CVV cannot exceed 4 digits.',

            'terms.required' => 'You must agree to the terms and conditions.',
            'terms.accepted' => 'You must agree to the terms and conditions.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert expiry date format if needed
        if ($this->has('payment.exp_month') && $this->has('payment.exp_year')) {
            $this->merge([
                'payment' => array_merge($this->payment, [
                    'expiry_date' => $this->payment['exp_month'] . '/' . substr($this->payment['exp_year'], -2),
                ]),
            ]);
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // If we're validating the entire request and payment data is present
        if ($key === null && isset($validated['payment'])) {
            // Make sure expiry_date is set for the ProcessPayment action
            if (isset($validated['payment']['exp_month']) && isset($validated['payment']['exp_year'])) {
                $validated['payment']['expiry_date'] = $validated['payment']['exp_year'] . '-' . $validated['payment']['exp_month'];
            }
        }

        return $validated;
    }
}
