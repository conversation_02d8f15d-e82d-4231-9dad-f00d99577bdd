<?php

namespace App\Http\Requests\LandingPage;

use Illuminate\Foundation\Http\FormRequest;

class PaymentRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'plan' => ['required'],
            'card_number' => ['required'],
            'expiration_month' => ['required'],
            'expiration_year' => ['required'],
            'cvv' => ['required']
        ];
    }

    public function messages(): array
    {
        return [
            'required' => 'Required'
        ];
    }
}
