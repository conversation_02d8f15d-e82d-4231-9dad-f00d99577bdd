<?php

namespace App\Http\Requests\LandingPage;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;

class RxPaymentRequest extends FormRequest
{
    public function rules(): array
    {
        $rules = [
            'payment.first_name' => ['sometimes', 'required', 'string', 'max:255'],
            'payment.last_name' => ['sometimes', 'required', 'string', 'max:255'],
            'payment.email' => ['sometimes', 'required', 'email', 'max:255', Rule::unique(User::class, 'email')],
            'payment.password' => ['sometimes', 'required', 'max:255'],
            'payment.address1' => ['required', 'string', 'max:255'],
            'payment.city' => ['sometimes', 'required', 'string', 'max:255'],
            'payment.state' => ['sometimes', 'required', 'string', 'max:2'],
            'payment.zip' => ['sometimes', 'required', 'string', 'max:10'],
            'payment.dob' => ['sometimes', 'nullable', 'date', 'before:'.now()->subYears(18)->toDateString()],
            'payment.card_number' => ['required', 'string', 'max:255'],
            'payment.cvv' => ['required', 'string', 'max:4'],
            'payment.expiration_month' => ['required', 'string', 'size:2'],
            'payment.expiration_year' => ['required', 'string', 'size:4'],
            'measurement.height_ft' => ['required', 'numeric', 'lt:9'],
            'measurement.height_in' => ['required', 'numeric', 'lt:12'],
            'measurement.weight' => ['required', 'numeric', 'lt:1000']
        ];

        if (Session::has('plan') && Session::get('plan')['hasInsurance'] === 'yes' && !$this->boolean('insurance.add_later')) {
            $rules = array_merge($rules, [
                'insurance.status' => ['required', 'string', 'in:active,inactive'],
                'insurance.provider_name' => ['required', 'string', 'max:255'],
                'insurance.policy_number' => ['required', 'string', 'max:255'],
                'insurance.group_number' => ['required', 'string', 'max:255'],
                'insurance.plan_type' => ['required', 'string', 'max:255']
            ]);
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'payment.email.unique' => 'Email already exists.',
            'payment.dob.before' => 'You must be at least 18 years old.',
            'payment.card_number.required' => 'Card number is required.',
            'payment.cvv.required' => 'CVV is required.',
            'payment.expiration_month.required' => 'Expiration month is required.',
            'payment.expiration_month.size' => 'Expiration month must be 2 digits.',
            'payment.expiration_year.required' => 'Expiration year is required.',
            'payment.expiration_year.size' => 'Expiration year must be 4 digits.',
            'measurement.height_ft.required' => 'Required.',
            'measurement.height_ft.numeric' => 'Number only.',
            'measurement.height_ft.lt' => 'Must be less than 9',
            'measurement.height_in.required' => 'Required.',
            'measurement.height_in.numeric' => 'Number only.',
            'measurement.height_in.lt' => 'Must be less than 12',
            'measurement.weight.required' => 'Required.',
            'measurement.weight.numeric' => 'Number only.',
            'measurement.weight.lt' => 'Must be less than 1000',
            'insurance.status.required' => 'Insurance status is required.',
            'insurance.provider_name.required' => 'Insurance provider name is required.',
            'insurance.policy_number.required' => 'Insurance policy number is required.',
            'insurance.group_number.required' => 'Insurance group number is required.',
            'insurance.plan_type.required' => 'Insurance plan type is required.'
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate expiration date isn't in the past
            if ($this->has('payment.expiration_month') && $this->has('payment.expiration_year')) {
                $expMonth = $this->input('payment.expiration_month');
                $expYear = $this->input('payment.expiration_year');

                if ($expMonth && $expYear) {
                    $expirationDate = Carbon::createFromDate($expYear, $expMonth, 1)->endOfMonth();
                    $today = Carbon::today();

                    if ($expirationDate->lt($today)) {
                        $validator->errors()->add('payment.expiration_month', 'Card has expired');
                        $validator->errors()->add('payment.expiration_year', 'Card has expired');
                    }
                }
            }
        });
    }
}
