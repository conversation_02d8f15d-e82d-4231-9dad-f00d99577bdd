<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MedicationVariantRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'brand_name' => ['required', 'string', 'max:255'],
            'manufacturer' => ['required', 'string', 'max:255'],
            'medication_base_id' => ['required', 'exists:medication_bases,id'],
            'strength' => ['required', 'string', 'max:50'],
            'dosage_form' => ['required', 'string', 'max:50'],
            'route_of_administration' => ['required', 'string', 'max:50'],
            'ndc_number' => ['required', 'string', 'max:50', 'unique:medication_variants,ndc_number,' . ($this->medication_variant->id ?? '')],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'storage_conditions' => ['nullable', 'string'],
            'is_usual_dosage' => ['boolean'],
            'order_index' => ['nullable', 'integer'],
        ];
    }

    public function messages()
    {
        return [
            'ndc_number.unique' => 'This NDC number is already registered in the system.',
            'medication_base_id.exists' => 'The selected medication base does not exist.',
            'unit_price.min' => 'The unit price must be greater than or equal to 0.',
        ];
    }
}
