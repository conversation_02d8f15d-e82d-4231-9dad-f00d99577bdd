<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'fname' => ['sometimes', 'string', 'max:255'],
            'lname' => ['sometimes', 'string', 'max:255'],
            'email' => ['email', 'max:255', Rule::unique(User::class)->ignore($this->user()->id)],
            'gender' => ['sometimes', 'string', 'in:M,F,O'],
            'dob' => ['sometimes', 'date', 'before:today'],
            'address1' => ['sometimes', 'string', 'max:255'],
            'address2' => ['sometimes', 'string', 'max:255', 'nullable'],
            'city' => ['sometimes', 'string', 'max:255'],
            'state' => ['sometimes', 'string', 'max:2'],
            'zip' => ['sometimes', 'string', 'max:20'],
            'phone' => ['sometimes', 'string', 'max:20'],
            'mobile_phone' => ['sometimes', 'string', 'max:20', 'nullable'],
        ];
    }
}
