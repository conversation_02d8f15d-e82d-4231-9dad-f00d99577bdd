<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreQuoteRequest extends FormRequest
{
    public function rules()
    {
        return [
            'company_name' => 'required|string|max:255',
            'contact_name' => 'required|string|max:255',
            'job_title' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'suite_number' => 'nullable|string|max:50',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:2',
            'zip_code' => 'required|string|max:10',
            'num_employees' => 'required|integer|min:1',
            'industry' => 'required|string|max:255',
            'other_industry' => 'required_if:industry,Other|nullable|string|max:255',
            'current_insurance' => 'required|in:Yes,No',
            'happy_with_coverage' => 'required_if:current_insurance,Yes|nullable|in:Yes,No',
            'coverage_issues' => 'required_if:happy_with_coverage,No|nullable|string',
            'contact_method' => 'required|in:Email,Phone,Text,No Preference',
            'budget' => 'nullable|string',
            'current_provider' => 'nullable|string|max:255',
            'start_date' => 'nullable|string',
            'referral_source' => 'required|string',
            'other_referral' => 'required_if:referral_source,Other|nullable|string|max:255',
            'additional_info' => 'nullable|string|max:500'
        ];
    }
}
