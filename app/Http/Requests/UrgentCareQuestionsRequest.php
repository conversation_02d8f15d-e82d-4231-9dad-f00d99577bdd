<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UrgentCareQuestionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        // Get the current step from the request
        $step = $this->input('step', 1);

        // Base rules that apply to all steps
        $rules = [
            'step' => 'required|integer|min:1|max:4',
        ];

        // Add rules based on the current step
        if ($step == 1) {
            // Personal Information
            $rules = array_merge($rules, [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|size:10',
                'dob' => 'required|date|before:today',
                'height_ft' => 'required|integer|min:0|max:8',
                'height_in' => 'required|integer|min:0|max:11',
                'weight' => 'required|numeric|min:1|max:1000',
            ]);
        } elseif ($step == 2) {
            // Health Concerns
            $rules = array_merge($rules, [
                'main_health_concern' => 'required|string',
                'symptoms_start' => 'required|date|before_or_equal:today',
                'had_condition_before' => 'boolean',
                'surgery' => 'boolean',
                'surgery_details' => 'required_if:surgery,true|string',
            ]);
        } elseif ($step == 3) {
            // Medications & Allergies
            $rules = array_merge($rules, [
                'allergies' => 'array',
                'allergies.*.allergen' => 'nullable|string|max:255',
                'allergies.*.reaction' => 'nullable|string',

                'medications' => 'array',
                'medications.*.medication_name' => 'nullable|string|max:255',
                'medications.*.dosage' => 'nullable|string|max:255',
                'medications.*.frequency' => 'nullable|string|max:255',
                'medications.*.side_effects' => 'nullable|string',
            ]);
        } elseif ($step == 4) {
            // Symptoms & Additional Information
            $rules = array_merge($rules, [
                'symptoms' => 'array',
                'symptoms.*' => 'exists:symptoms,id',

                'needs_prescription_today' => 'boolean',
                'specific_medication' => 'boolean',
                'medication_name' => 'required_if:specific_medication,true|string|max:255',
                'medication_dosage' => 'nullable|string|max:255',

                'additional_concerns' => 'nullable|string',

                'consent_to_treatment' => 'required|accepted',
                'privacy_policy_accepted' => 'required|accepted',
            ]);

            // For the final step, we need to validate all previous steps' data as well
            // This ensures that if someone tries to skip steps, we still validate everything
            $rules = array_merge($rules, [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|size:10',
                'dob' => 'required|date|before:today',
                'height_ft' => 'required|integer|min:0|max:8',
                'height_in' => 'required|integer|min:0|max:11',
                'weight' => 'required|numeric|min:1|max:1000',
                'main_health_concern' => 'required|string',
                'symptoms_start' => 'required|date|before_or_equal:today',
            ]);
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'first_name.required' => 'Your first name is required.',
            'last_name.required' => 'Your last name is required.',
            'email.required' => 'Your email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'phone.required' => 'Your phone number is required.',
            'phone.size' => 'Please enter a 10-digit phone number.',
            'dob.required' => 'Your date of birth is required.',
            'dob.before' => 'Date of birth must be in the past.',

            'height_ft.required' => 'Height (feet) is required.',
            'height_in.required' => 'Height (inches) is required.',
            'weight.required' => 'Weight is required.',
            'weight.min' => 'Weight must be greater than 0.',

            'main_health_concern.required' => 'Please describe your main health concern.',
            'symptoms_start.required' => 'Please indicate when your symptoms started.',

            'surgery_details.required_if' => 'Please provide details about your surgery or hospitalization.',

            'medication_name.required_if' => 'Please specify the medication you are requesting.',

            'consent_to_treatment.accepted' => 'You must consent to treatment to continue.',
            'privacy_policy_accepted.accepted' => 'You must accept the privacy policy to continue.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $step = $this->input('step', 1);

            // Only validate consent checkboxes on the final step
            if ($step == 4) {
                if (!$this->boolean('consent_to_treatment')) {
                    $validator->errors()->add('consent_to_treatment', 'You must consent to treatment to continue.');
                }

                if (!$this->boolean('privacy_policy_accepted')) {
                    $validator->errors()->add('privacy_policy_accepted', 'You must accept the privacy policy to continue.');
                }
            }
        });
    }
}
