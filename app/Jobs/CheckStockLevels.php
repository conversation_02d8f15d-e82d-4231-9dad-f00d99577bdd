<?php

namespace App\Jobs;

use App\Models\Inventory;
use App\Notifications\LowStockAlert;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;

class CheckStockLevels implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        $lowStockItems = Inventory::whereRaw('quantity <= reorder_point')->get();

        if ($lowStockItems->isNotEmpty()) {
            $recipients = explode(',', config('inventory.stock_alert_recipients'));
            Notification::route('mail', $recipients)
                ->notify(new LowStockAlert($lowStockItems));
        }
    }
}
