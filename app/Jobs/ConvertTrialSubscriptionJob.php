<?php

namespace App\Jobs;

use App\Models\Subscription;
use App\Services\AuthorizeNetService;
use App\Services\TrialPlanService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ConvertTrialSubscriptionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected Subscription $subscription)
    {}

    public function handle(TrialPlanService $trialService, AuthorizeNetService $paymentService)
    {
        try {
            // Attempt to charge the user
            $charged = $paymentService->chargeCreditCard($this->subscription->plan->price, $this->subscription->user, $this->subscription->id);

            if ($charged['success']) {
                // If charge is successful, convert the trial
                $trialService->convertTrialToRegularSubscription($this->subscription);
                Log::info("Trial subscription {$this->subscription->id} converted successfully.");
            } else {
                // If charge fails, you might want to mark the subscription as expired or send a notification
                $this->subscription->update(['status' => 'expired']);
                Log::warning("Failed to charge for trial subscription {$this->subscription->id}. Marked as expired.");
            }
        } catch (\Exception $e) {
            Log::error("Error converting trial subscription {$this->subscription->id}: " . $e->getMessage());
            // You might want to implement a retry mechanism or alert an admin here
        }
    }
}
