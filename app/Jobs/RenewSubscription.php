<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\AuthorizeNetService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RenewSubscription implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function handle(AuthorizeNetService $authorizeNetService)
    {
        $plan = $this->user->subscriptionPlan;

        $paymentResult = $authorizeNetService->chargeCreditCard(
            $plan->price,
            $this->user->card_number,
            $this->user->card_expiration,
            $this->user->card_cvv
        );

        if ($paymentResult['success']) {
            $this->user->update([
                'subscription_ends_at' => $this->user->subscription_ends_at->addMonths($plan->duration_months),
            ]);
        } else {
            // Handle failed renewal (e.g., send notification to user)
        }
    }
}