<?php

namespace App\Listeners;

use App\Events\QuoteRequested;
use App\Mail\QuoteRequested as QuoteRequestedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendQuoteEmail
{
    /**
     * Handle the event.
     */
    public function handle(QuoteRequested $event): void
    {
        Mail::to(config('mail.admin_email'))->send(new QuoteRequestedMail($event->quote));
    }
}
