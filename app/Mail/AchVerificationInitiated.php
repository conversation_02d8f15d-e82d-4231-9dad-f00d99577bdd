<?php

namespace App\Mail;

use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AchVerificationInitiated extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        public User $user,
        public PaymentMethod $paymentMethod
    ) {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Bank Account Verification Initiated',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.ach-verification.initiated',
            with: [
                'userName' => $this->user->name,
                'accountName' => $this->paymentMethod->ach_account_name,
                'accountType' => ucfirst($this->paymentMethod->ach_account_type),
                'accountLastFour' => $this->paymentMethod->ach_account_number_last_four,
                'verificationUrl' => route('ach-verification.show', $this->paymentMethod),
                'initiatedAt' => $this->paymentMethod->meta_data['verification_initiated_at'] ?? now()->toDateTimeString(),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
