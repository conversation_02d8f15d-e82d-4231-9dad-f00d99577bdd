<?php

namespace App\Notifications\Agent;

use App\Models\Certification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CertificationEarnedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The certification instance.
     *
     * @var \App\Models\Certification
     */
    protected $certification;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Certification  $certification
     * @return void
     */
    public function __construct(Certification $certification)
    {
        $this->certification = $certification;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/certifications');

        $mailMessage = (new MailMessage)
            ->subject('Congratulations! You\'ve Earned a New Certification')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Congratulations! You have successfully earned a new certification:')
            ->line($this->certification->name)
            ->line($this->certification->description)
            ->action('View Your Certifications', $url)
            ->line('This certification has been added to your profile.');

        // If there's a badge image, attach it to the email
        if ($this->certification->badge_image) {
            $mailMessage->attach(public_path($this->certification->badge_image), [
                'as' => 'certification-badge.png',
                'mime' => 'image/png',
            ]);
        }

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'certification_id' => $this->certification->id,
            'title' => 'Certification Earned',
            'message' => 'You have earned the ' . $this->certification->name . ' certification',
            'type' => 'certification_earned',
            'url' => '/agent/certifications',
        ];
    }
}
