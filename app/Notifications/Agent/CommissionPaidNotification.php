<?php

namespace App\Notifications\Agent;

use App\Models\Commission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommissionPaidNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The commission instance.
     *
     * @var \App\Models\Commission
     */
    protected $commission;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Commission  $commission
     * @return void
     */
    public function __construct(Commission $commission)
    {
        $this->commission = $commission;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/commissions');

        return (new MailMessage)
            ->subject('Commission Payment Processed')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We are pleased to inform you that a commission payment has been processed:')
            ->line('Amount: $' . number_format($this->commission->amount, 2))
            ->line('Type: ' . ucfirst($this->commission->type))
            ->line('Reference: ' . $this->commission->reference)
            ->line('Payment Date: ' . $this->commission->paid_at->format('F j, Y'))
            ->action('View Commission Details', $url)
            ->line('Thank you for your continued partnership!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'commission_id' => $this->commission->id,
            'title' => 'Commission Paid',
            'message' => 'A commission payment of $' . number_format($this->commission->amount, 2) . ' has been processed',
            'amount' => $this->commission->amount,
            'type' => 'commission_paid',
            'url' => '/agent/commissions',
        ];
    }
}
