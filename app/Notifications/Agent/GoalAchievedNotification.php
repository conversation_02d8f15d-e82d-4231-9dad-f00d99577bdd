<?php

namespace App\Notifications\Agent;

use App\Models\Goal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GoalAchievedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The goal instance.
     *
     * @var \App\Models\Goal
     */
    protected $goal;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Goal  $goal
     * @return void
     */
    public function __construct(Goal $goal)
    {
        $this->goal = $goal;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/goals');

        return (new MailMessage)
            ->subject('Congratulations! You\'ve Achieved Your Goal')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Congratulations! You have successfully achieved your goal:')
            ->line($this->goal->description)
            ->line('Target: $' . number_format($this->goal->target_value))
            ->action('View Your Goals', $url)
            ->line('Keep up the great work!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'goal_id' => $this->goal->id,
            'title' => 'Goal Achieved',
            'message' => 'You have achieved your goal: ' . $this->goal->description,
            'type' => 'goal_achieved',
            'url' => '/agent/goals',
        ];
    }
}
