<?php

namespace App\Notifications\Agent;

use App\Models\Goal;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GoalDeadlineReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The goal instance.
     *
     * @var \App\Models\Goal
     */
    protected $goal;

    /**
     * The number of days remaining.
     *
     * @var int
     */
    protected $daysRemaining;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Goal  $goal
     * @param  int  $daysRemaining
     * @return void
     */
    public function __construct(Goal $goal, int $daysRemaining)
    {
        $this->goal = $goal;
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/goals');
        $daysText = $this->daysRemaining === 1 ? 'day' : 'days';

        $mailMessage = (new MailMessage)
            ->subject('Goal Deadline Approaching: ' . $this->daysRemaining . ' ' . $daysText . ' Remaining')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a friendly reminder that your goal deadline is approaching:')
            ->line('Goal: ' . $this->goal->description)
            ->line('Target: $' . number_format($this->goal->target_value))
            ->line('Current Progress: $' . number_format($this->goal->progress_value) . ' (' . $this->goal->progress_percentage . '%)')
            ->line('Deadline: ' . $this->goal->target_date->format('F j, Y') . ' (' . $this->daysRemaining . ' ' . $daysText . ' remaining)')
            ->action('View Goal Details', $url);

        if ($this->goal->progress_percentage < 50) {
            $mailMessage->line('You\'re currently less than halfway to your goal. Consider focusing on strategies to accelerate your progress.');
        } elseif ($this->goal->progress_percentage < 80) {
            $mailMessage->line('You\'re making good progress! With a final push, you can achieve your goal before the deadline.');
        } else {
            $mailMessage->line('You\'re very close to achieving your goal! Just a little more effort to reach the finish line.');
        }

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $daysText = $this->daysRemaining === 1 ? 'day' : 'days';

        return [
            'goal_id' => $this->goal->id,
            'title' => 'Goal Deadline Approaching',
            'message' => 'Your goal "' . $this->goal->description . '" has ' . $this->daysRemaining . ' ' . $daysText . ' remaining',
            'days_remaining' => $this->daysRemaining,
            'progress_percentage' => $this->goal->progress_percentage,
            'type' => 'goal_deadline_reminder',
            'url' => '/agent/goals',
        ];
    }
}
