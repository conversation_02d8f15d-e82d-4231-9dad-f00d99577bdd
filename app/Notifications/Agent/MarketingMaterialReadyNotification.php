<?php

namespace App\Notifications\Agent;

use App\Models\MarketingMaterial;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MarketingMaterialReadyNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The marketing material instance.
     *
     * @var \App\Models\MarketingMaterial
     */
    protected $material;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\MarketingMaterial  $material
     * @return void
     */
    public function __construct(MarketingMaterial $material)
    {
        $this->material = $material;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/marketing');

        return (new MailMessage)
            ->subject('Your Requested Marketing Material is Ready')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Good news! The marketing material you requested is now ready:')
            ->line($this->material->title)
            ->line($this->material->description)
            ->action('View Marketing Materials', $url)
            ->line('You can now download, customize, and share this material with your clients.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'material_id' => $this->material->id,
            'title' => 'Marketing Material Ready',
            'message' => 'Your requested marketing material "' . $this->material->title . '" is now ready',
            'type' => 'marketing_material_ready',
            'url' => '/agent/marketing',
        ];
    }
}
