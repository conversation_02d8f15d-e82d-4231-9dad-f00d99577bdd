<?php

namespace App\Notifications\Agent;

use App\Models\Message;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewMessageNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The message instance.
     *
     * @var \App\Models\Message
     */
    protected $message;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Message  $message
     * @return void
     */
    public function __construct(Message $message)
    {
        $this->message = $message;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/messaging');
        $sender = $this->message->sender;

        return (new MailMessage)
            ->subject('New Message from ' . $sender->name)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have received a new message from ' . $sender->name . ':')
            ->line('"' . $this->truncateMessage($this->message->content) . '"')
            ->action('View Message', $url)
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $sender = $this->message->sender;

        return [
            'message_id' => $this->message->id,
            'conversation_id' => $this->message->conversation_id,
            'title' => 'New Message',
            'message' => 'New message from ' . $sender->name,
            'sender_name' => $sender->name,
            'sender_id' => $sender->id,
            'preview' => $this->truncateMessage($this->message->content),
            'type' => 'new_message',
            'url' => '/agent/messaging',
        ];
    }

    /**
     * Truncate the message content for preview.
     *
     * @param  string  $content
     * @return string
     */
    protected function truncateMessage($content)
    {
        if (strlen($content) <= 100) {
            return $content;
        }

        return substr($content, 0, 97) . '...';
    }
}
