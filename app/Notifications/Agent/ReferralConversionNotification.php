<?php

namespace App\Notifications\Agent;

use App\Models\Referral;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReferralConversionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The referral instance.
     *
     * @var \App\Models\Referral
     */
    protected $referral;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Referral  $referral
     * @return void
     */
    public function __construct(Referral $referral)
    {
        $this->referral = $referral;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url('/agent/referrals');
        $referredUser = $this->referral->referred_user;

        return (new MailMessage)
            ->subject('Good News! Your Referral Has Converted')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('We are excited to inform you that your referral has successfully converted:')
            ->line('Referral: ' . $referredUser->name)
            ->line('Conversion Type: ' . ucfirst($this->referral->conversion_type))
            ->line('Conversion Date: ' . $this->referral->converted_at->format('F j, Y'))
            ->line('Any associated commissions will be processed according to your commission schedule.')
            ->action('View Referral Details', $url)
            ->line('Thank you for growing our network!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $referredUser = $this->referral->referred_user;

        return [
            'referral_id' => $this->referral->id,
            'title' => 'Referral Converted',
            'message' => 'Your referral ' . $referredUser->name . ' has successfully converted',
            'conversion_type' => $this->referral->conversion_type,
            'type' => 'referral_conversion',
            'url' => '/agent/referrals',
        ];
    }
}
