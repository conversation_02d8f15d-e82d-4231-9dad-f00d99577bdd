<?php

namespace App\Notifications\Agent;

use App\Models\Report;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ReportGeneratedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The report instance.
     *
     * @var \App\Models\Report
     */
    protected $report;

    /**
     * The download URL for the report.
     *
     * @var string|null
     */
    protected $downloadUrl;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Report  $report
     * @param  string|null  $downloadUrl
     * @return void
     */
    public function __construct(Report $report, $downloadUrl = null)
    {
        $this->report = $report;
        $this->downloadUrl = $downloadUrl;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = $this->downloadUrl ?: url('/agent/reports');

        $mailMessage = (new MailMessage)
            ->subject('Your Report is Ready: ' . $this->report->name)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your scheduled report has been generated and is now ready:')
            ->line($this->report->name);

        if ($this->downloadUrl) {
            $mailMessage->action('Download Report', $url);
        } else {
            $mailMessage->action('View Reports', $url);
        }

        $mailMessage->line('This report will be available for download for the next 30 days.');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'report_id' => $this->report->id,
            'title' => 'Report Generated',
            'message' => 'Your report "' . $this->report->name . '" has been generated',
            'download_url' => $this->downloadUrl,
            'type' => 'report_generated',
            'url' => '/agent/reports',
        ];
    }
}
