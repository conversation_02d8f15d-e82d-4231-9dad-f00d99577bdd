<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BusinessEmployeeWelcomeNotification extends Notification
{
    // Removed ShouldQueue to ensure synchronous delivery for testing
    // use Queueable;

    protected $password;
    protected $businessName;
    protected $businessPlan;

    /**
     * Create a new notification instance.
     *
     * @param string $password
     * @param string|null $businessName
     * @param string|null $businessPlan
     * @return void
     */
    public function __construct(string $password, ?string $businessName = null, ?string $businessPlan = null)
    {
        $this->password = $password;
        $this->businessName = $businessName;
        $this->businessPlan = $businessPlan;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Log that we're sending an email
        \Illuminate\Support\Facades\Log::info('BusinessEmployeeWelcomeNotification::toMail called', [
            'notifiable_email' => $notifiable->email,
            'notifiable_name' => $notifiable->name,
            'business_name' => $this->businessName,
            'plan' => $this->businessPlan
        ]);

        // Log the mail configuration
        \Illuminate\Support\Facades\Log::info('Mail configuration in notification', [
            'driver' => config('mail.default'),
            'from_address' => config('mail.from.address'),
            'log_channel' => config('mail.mailers.log.channel')
        ]);

        // For testing, let's use a simpler approach
        $message = (new MailMessage)
            ->subject('Welcome to Your Telemedicine Benefits')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('An account has been created for you on our telemedicine platform.')
            ->line('Your temporary password is: ' . $this->password)
            ->line('Business: ' . $this->businessName)
            ->line('Plan: ' . $this->businessPlan)
            ->action('Login Now', url('/login'))
            ->line('Thank you for using our application!');

        // Log that we've created the message
        \Illuminate\Support\Facades\Log::info('Welcome email message created, about to return it');

        // Manually log the email content to ensure it's captured
        $emailContent = "Subject: Welcome to Your Telemedicine Benefits\n";
        $emailContent .= "To: {$notifiable->email}\n";
        $emailContent .= "From: " . config('mail.from.name') . " <" . config('mail.from.address') . ">\n";
        $emailContent .= "Content:\n";
        $emailContent .= "Hello {$notifiable->name}!\n";
        $emailContent .= "An account has been created for you on our telemedicine platform.\n";
        $emailContent .= "Your temporary password is: {$this->password}\n";
        $emailContent .= "Business: {$this->businessName}\n";
        $emailContent .= "Plan: {$this->businessPlan}\n";
        $emailContent .= "Thank you for using our application!";

        // Write to a specific file to ensure we capture the email
        try {
            $logFile = storage_path('logs/welcome_emails.log');
            $logContent = "[" . date('Y-m-d H:i:s') . "] " . $emailContent . "\n\n";

            \Illuminate\Support\Facades\Log::info('Writing to welcome_emails.log', [
                'log_file' => $logFile,
                'content_length' => strlen($logContent)
            ]);

            file_put_contents($logFile, $logContent, FILE_APPEND);

            \Illuminate\Support\Facades\Log::info('Successfully wrote to welcome_emails.log');
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to write to welcome_emails.log: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $message;

        // Uncomment this when testing is complete
        /*
        return (new MailMessage)
            ->subject('Welcome to Your Telemedicine Benefits')
            ->view('emails.business-employee-welcome', [
                'user' => $notifiable,
                'password' => $this->password,
                'businessName' => $this->businessName,
                'businessPlan' => $this->businessPlan
            ]);
        */
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'password' => $this->password,
            'business_name' => $this->businessName,
            'business_plan' => $this->businessPlan,
        ];
    }
}
