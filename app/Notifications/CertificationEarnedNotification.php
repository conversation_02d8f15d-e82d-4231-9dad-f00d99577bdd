<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CertificationEarnedNotification extends Notification
{
    use Queueable;

    /**
     * The certification that was earned.
     *
     * @var \App\Models\AgentCertification
     */
    protected $certification;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentCertification $certification)
    {
        $this->certification = $certification;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('Congratulations! You\'ve Earned a New Certification')
                    ->greeting('Congratulations, ' . $notifiable->name . '!')
                    ->line('You have successfully earned the ' . $this->certification->name . ' certification.')
                    ->line($this->certification->description)
                    ->action('View Your Certifications', url('/agent/training/certifications'))
                    ->line('Thank you for your dedication to professional development!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'certification_id' => $this->certification->id,
            'certification_name' => $this->certification->name,
            'message' => 'You have earned the ' . $this->certification->name . ' certification.',
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
