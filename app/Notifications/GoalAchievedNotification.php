<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GoalAchievedNotification extends Notification
{
    use Queueable;

    /**
     * The goal that was achieved.
     *
     * @var \App\Models\AgentGoal
     */
    protected $goal;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentGoal $goal)
    {
        $this->goal = $goal;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $goalType = ucfirst($this->goal->type);
        $targetValue = $this->goal->type === 'commissions'
            ? '$' . number_format($this->goal->target_value, 2)
            : number_format($this->goal->target_value);

        return (new MailMessage)
                    ->subject('Congratulations! You\'ve Achieved Your ' . $goalType . ' Goal')
                    ->greeting('Congratulations, ' . $notifiable->name . '!')
                    ->line('You have successfully achieved your ' . strtolower($goalType) . ' goal of ' . $targetValue . '.')
                    ->line('Keep up the great work!')
                    ->action('View Your Goals', url('/agent/goals'))
                    ->line('Thank you for being a valued agent in our network!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'goal_id' => $this->goal->id,
            'type' => $this->goal->type,
            'target_value' => $this->goal->target_value,
            'achieved_at' => $this->goal->achieved_at,
            'message' => 'You have achieved your ' . $this->goal->type . ' goal of ' .
                         ($this->goal->type === 'commissions' ? '$' . number_format($this->goal->target_value, 2) : number_format($this->goal->target_value)) . '.',
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
