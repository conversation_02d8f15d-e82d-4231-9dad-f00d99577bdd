<?php

namespace App\Notifications;

use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class HealthPlanWelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $subscription;
    protected $plan;

    /**
     * Create a new notification instance.
     *
     * @param Subscription $subscription
     * @param SubscriptionPlan $plan
     * @return void
     */
    public function __construct(Subscription $subscription, SubscriptionPlan $plan)
    {
        $this->subscription = $subscription;
        $this->plan = $plan;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        // Log that we're sending an email
        Log::info('HealthPlanWelcomeNotification::toM<PERSON> called', [
            'notifiable_email' => $notifiable->email,
            'notifiable_name' => $notifiable->name,
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->plan->name
        ]);

        // Log the mail configuration
        Log::info('Mail configuration in health plan welcome notification', [
            'driver' => config('mail.default'),
            'from_address' => config('mail.from.address'),
            'log_channel' => config('mail.mailers.log.channel')
        ]);

        // Use the custom email template
        return (new MailMessage)
            ->subject('Welcome to Your Health Plan')
            ->view('emails.health-plan-welcome', [
                'user' => $notifiable,
                'subscription' => $this->subscription,
                'plan' => $this->plan
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_id' => $this->plan->id,
            'plan_name' => $this->plan->name,
        ];
    }
}
