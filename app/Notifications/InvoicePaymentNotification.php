<?php

namespace App\Notifications;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class InvoicePaymentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public Transaction $transaction,
        public ?string $invoicePath = null
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject('Invoice Payment Due: #' . $this->transaction->transaction_id)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('An invoice has been generated for your recent subscription.')
            ->line('Invoice #: ' . $this->transaction->transaction_id)
            ->line('Amount: $' . number_format($this->transaction->amount, 2))
            ->line('Due Date: ' . $this->getDueDate())
            ->line('Please review the attached invoice and process payment according to your payment terms.');

        // Attach the invoice if available
        if ($this->invoicePath && Storage::exists($this->invoicePath)) {
            $mailMessage->attach(Storage::path($this->invoicePath));
        }

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'transaction_id' => $this->transaction->id,
            'invoice_number' => $this->transaction->transaction_id,
            'amount' => $this->transaction->amount,
            'due_date' => $this->getDueDate(),
            'type' => 'invoice_payment',
            'message' => 'Invoice #' . $this->transaction->transaction_id . ' for $' . number_format($this->transaction->amount, 2) . ' is due on ' . $this->getDueDate() . '.'
        ];
    }

    /**
     * Get the due date based on payment terms.
     */
    protected function getDueDate(): string
    {
        $paymentMethod = $this->transaction->user->paymentMethods()
            ->where('type', 'invoice')
            ->where('is_default', true)
            ->first();

        $terms = $paymentMethod ? $paymentMethod->invoice_payment_terms : 'net30';

        $days = match($terms) {
            'net15' => 15,
            'net30' => 30,
            'net45' => 45,
            'net60' => 60,
            'net90' => 90,
            default => 30
        };

        return now()->addDays($days)->format('F j, Y');
    }
}
