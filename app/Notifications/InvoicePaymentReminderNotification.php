<?php

namespace App\Notifications;

use App\Models\Transaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class InvoicePaymentReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        public Transaction $transaction,
        public ?string $invoicePath = null,
        public int $daysRemaining = 7
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mailMessage = (new MailMessage)
            ->subject('Payment Reminder: Invoice #' . $this->transaction->transaction_id . ' Due Soon')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('This is a friendly reminder that your invoice payment is due soon.')
            ->line('Invoice #: ' . $this->transaction->transaction_id)
            ->line('Amount: $' . number_format($this->transaction->amount, 2))
            ->line('Due Date: ' . $this->getDueDate())
            ->line($this->getReminderMessage());

        // Attach the invoice if available
        if ($this->invoicePath && Storage::exists($this->invoicePath)) {
            $mailMessage->attach(Storage::path($this->invoicePath));
        }

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'transaction_id' => $this->transaction->id,
            'invoice_number' => $this->transaction->transaction_id,
            'amount' => $this->transaction->amount,
            'due_date' => $this->getDueDate(),
            'days_remaining' => $this->daysRemaining,
            'type' => 'invoice_payment_reminder',
            'message' => 'Reminder: Invoice #' . $this->transaction->transaction_id . ' for $' . number_format($this->transaction->amount, 2) . ' is due in ' . $this->daysRemaining . ' days.'
        ];
    }

    /**
     * Get the due date based on payment terms.
     */
    protected function getDueDate(): string
    {
        $paymentMethod = $this->transaction->user->paymentMethods()
            ->where('type', 'invoice')
            ->where('is_default', true)
            ->first();

        $terms = $paymentMethod ? $paymentMethod->invoice_payment_terms : 'net30';

        $days = match($terms) {
            'net15' => 15,
            'net30' => 30,
            'net45' => 45,
            'net60' => 60,
            'net90' => 90,
            default => 30
        };

        return now()->addDays($days - $this->daysRemaining)->format('F j, Y');
    }

    /**
     * Get the reminder message based on days remaining.
     */
    protected function getReminderMessage(): string
    {
        if ($this->daysRemaining > 1) {
            return "Please ensure payment is made within the next {$this->daysRemaining} days to avoid any service interruptions.";
        } else {
            return "Please ensure payment is made today to avoid any service interruptions.";
        }
    }
}
