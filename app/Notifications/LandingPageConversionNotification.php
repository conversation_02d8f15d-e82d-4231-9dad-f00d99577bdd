<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LandingPageConversionNotification extends Notification
{
    use Queueable;

    /**
     * The landing page that received the conversion.
     *
     * @var \App\Models\AgentLandingPage
     */
    protected $landingPage;

    /**
     * The form data submitted by the lead.
     *
     * @var array
     */
    protected $formData;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentLandingPage $landingPage, array $formData)
    {
        $this->landingPage = $landingPage;
        $this->formData = $formData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('New Lead from Your Landing Page: ' . $this->landingPage->title)
                    ->greeting('Hello ' . $notifiable->name . '!')
                    ->line('You have received a new lead from your landing page: ' . $this->landingPage->title)
                    ->line('Lead Details:')
                    ->line('Name: ' . $this->formData['name'])
                    ->line('Email: ' . $this->formData['email'])
                    ->line('Phone: ' . ($this->formData['phone'] ?? 'Not provided'))
                    ->when(!empty($this->formData['message']), function ($message) {
                        return $message->line('Message: ' . $this->formData['message']);
                    })
                    ->action('View All Leads', url('/agent/marketing/leads'))
                    ->line('Contact this lead as soon as possible to maximize your conversion rate!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'landing_page_id' => $this->landingPage->id,
            'landing_page_title' => $this->landingPage->title,
            'lead_name' => $this->formData['name'],
            'lead_email' => $this->formData['email'],
            'lead_phone' => $this->formData['phone'] ?? null,
            'message' => 'New lead from landing page: ' . $this->landingPage->title,
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
