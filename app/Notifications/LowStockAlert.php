<?php

// app/Notifications/LowStockAlert.php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Collection;

class LowStockAlert extends Notification implements ShouldQueue
{
    use Queueable;

    protected $lowStockItems;

    public function __construct(Collection $lowStockItems)
    {
        $this->lowStockItems = $lowStockItems;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $mailMessage = (new MailMessage)
            ->subject('Low Stock Alert')
            ->line('The following items are currently low in stock:')
            ->line('');

        foreach ($this->lowStockItems as $item) {
            $mailMessage->line("{$item->medication->name}: {$item->quantity} remaining (Reorder point: {$item->reorder_point})");
        }

        $mailMessage->action('View Inventory', url('/inventory'))
            ->line('Please take action to restock these items.');

        return $mailMessage;
    }
}