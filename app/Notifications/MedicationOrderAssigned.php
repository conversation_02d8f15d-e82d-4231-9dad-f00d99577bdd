<?php

namespace App\Notifications;

use App\Models\MedicationOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MedicationOrderAssigned extends Notification implements ShouldQueue
{
    use Queueable;

    protected $medicationOrder;

    /**
     * Create a new notification instance.
     */
    public function __construct(MedicationOrder $medicationOrder)
    {
        $this->medicationOrder = $medicationOrder;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Medication Order Assigned')
            ->greeting('Hello Dr. ' . $this->medicationOrder->doctor->name . ',')
            ->line('You have been assigned a new medication order to review.')
            ->line('Order #: ' . $this->medicationOrder->id)
            ->line('Patient: ' . $this->medicationOrder->patient->name)
            ->line('Assigned at: ' . $this->medicationOrder->assigned_at->format('Y-m-d H:i:s'))
            ->action('View Order', route('doctor.medication-orders.show', $this->medicationOrder))
            ->line('Please review this order and create a prescription or reject the order as appropriate.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'medication_order_id' => $this->medicationOrder->id,
            'patient_id' => $this->medicationOrder->patient_id,
            'patient_name' => $this->medicationOrder->patient->name,
            'assigned_at' => $this->medicationOrder->assigned_at->format('Y-m-d H:i:s'),
            'status' => $this->medicationOrder->status,
            'message' => 'You have been assigned a new medication order to review.',
            'url' => route('doctor.medication-orders.show', $this->medicationOrder),
        ];
    }
}
