<?php

namespace App\Notifications;

use App\Models\MedicationOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MedicationOrderCreated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $medicationOrder;

    /**
     * Create a new notification instance.
     */
    public function __construct(MedicationOrder $medicationOrder)
    {
        $this->medicationOrder = $medicationOrder;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Medication Order Created')
            ->greeting('Hello Admin,')
            ->line('A new medication order has been created and requires your attention.')
            ->line('Order #: ' . $this->medicationOrder->id)
            ->line('Patient: ' . $this->medicationOrder->patient->name)
            ->line('Created at: ' . $this->medicationOrder->created_at->format('Y-m-d H:i:s'))
            ->action('View Order', route('admin.medication-orders.show', $this->medicationOrder))
            ->line('Please assign a doctor to this order as soon as possible.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'medication_order_id' => $this->medicationOrder->id,
            'patient_id' => $this->medicationOrder->patient_id,
            'patient_name' => $this->medicationOrder->patient->name,
            'created_at' => $this->medicationOrder->created_at->format('Y-m-d H:i:s'),
            'status' => $this->medicationOrder->status,
            'message' => 'A new medication order has been created and requires your attention.',
            'url' => route('admin.medication-orders.show', $this->medicationOrder),
        ];
    }
}
