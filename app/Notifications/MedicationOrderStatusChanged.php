<?php

namespace App\Notifications;

use App\Models\MedicationOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MedicationOrderStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    protected $medicationOrder;
    protected $previousStatus;

    /**
     * Create a new notification instance.
     */
    public function __construct(MedicationOrder $medicationOrder, string $previousStatus)
    {
        $this->medicationOrder = $medicationOrder;
        $this->previousStatus = $previousStatus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('Medication Order Status Updated')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your medication order status has been updated.')
            ->line('Order #: ' . $this->medicationOrder->id)
            ->line('Previous Status: ' . MedicationOrder::STATUSES[$this->previousStatus])
            ->line('New Status: ' . MedicationOrder::STATUSES[$this->medicationOrder->status])
            ->action('View Order', route('patient.medication-orders.show', $this->medicationOrder));

        if ($this->medicationOrder->status === 'rejected') {
            $message->line('Rejection Reason: ' . $this->medicationOrder->rejection_reason);
        } elseif ($this->medicationOrder->status === 'prescribed') {
            $message->line('Your prescription has been created and is ready for processing.');
        } elseif ($this->medicationOrder->status === 'completed') {
            $message->line('Your medication order has been completed.');
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'medication_order_id' => $this->medicationOrder->id,
            'previous_status' => $this->previousStatus,
            'new_status' => $this->medicationOrder->status,
            'message' => 'Your medication order status has been updated from ' . 
                MedicationOrder::STATUSES[$this->previousStatus] . ' to ' . 
                MedicationOrder::STATUSES[$this->medicationOrder->status] . '.',
            'url' => route('patient.medication-orders.show', $this->medicationOrder),
        ];
    }
}
