<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class NewMessageNotification extends Notification
{
    use Queueable;

    /**
     * The message that was sent.
     *
     * @var \App\Models\AgentMessage
     */
    protected $message;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentMessage $message)
    {
        $this->message = $message;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $sender = $this->message->sender;

        return (new MailMessage)
                    ->subject('New Message: ' . $this->message->subject)
                    ->greeting('Hello ' . $notifiable->name . '!')
                    ->line('You have received a new message from ' . $sender->name . '.')
                    ->line('Subject: ' . $this->message->subject)
                    ->line('Message Preview: ' . Str::limit($this->message->message, 100))
                    ->action('View Message', url('/agent/messages/' . $this->message->id))
                    ->line('Login to your account to reply to this message.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'message_id' => $this->message->id,
            'sender_id' => $this->message->sender_id,
            'sender_name' => $this->message->sender->name,
            'subject' => $this->message->subject,
            'preview' => Str::limit($this->message->message, 100),
            'message' => 'New message from ' . $this->message->sender->name . ': ' . Str::limit($this->message->subject, 50),
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
