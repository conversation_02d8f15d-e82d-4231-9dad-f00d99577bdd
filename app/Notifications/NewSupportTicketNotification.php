<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class NewSupportTicketNotification extends Notification
{
    use Queueable;

    /**
     * The support ticket.
     *
     * @var \App\Models\AgentSupportTicket
     */
    protected $ticket;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentSupportTicket $ticket)
    {
        $this->ticket = $ticket;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $agent = $this->ticket->agent->user;

        return (new MailMessage)
                    ->subject('New Support Ticket: ' . $this->ticket->subject)
                    ->greeting('Hello ' . $notifiable->name . '!')
                    ->line('A new support ticket has been created by ' . $agent->name . '.')
                    ->line('Subject: ' . $this->ticket->subject)
                    ->line('Description: ' . Str::limit($this->ticket->description, 200))
                    ->action('View Ticket', url('/admin/support/tickets/' . $this->ticket->id))
                    ->line('Please respond to this ticket as soon as possible.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'agent_id' => $this->ticket->agent_id,
            'agent_name' => $this->ticket->agent->user->name,
            'subject' => $this->ticket->subject,
            'message' => 'New support ticket from ' . $this->ticket->agent->user->name . ': ' . Str::limit($this->ticket->subject, 50),
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
