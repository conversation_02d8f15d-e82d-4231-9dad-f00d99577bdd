<?php

namespace App\Notifications;

use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionAutoRenewedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * The new end date.
     *
     * @var \Carbon\Carbon
     */
    protected $newEndDate;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription, Carbon $newEndDate)
    {
        $this->subscription = $subscription;
        $this->newEndDate = $newEndDate;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $amount = $this->subscription->is_discounted && $this->subscription->discounted_price 
            ? $this->subscription->discounted_price 
            : $this->subscription->plan->price;
            
        return (new MailMessage)
            ->subject('Your Subscription Has Been Automatically Renewed')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Good news! Your subscription to the ' . $this->subscription->plan->name . ' plan has been automatically renewed.')
            ->line('Your subscription is now active until ' . $this->newEndDate->format('F j, Y') . '.')
            ->line('You have been charged $' . number_format($amount, 2) . ' for this renewal.')
            ->line('Thank you for your continued subscription!')
            ->action('View Subscription Details', route('patient.subscriptions.index'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'new_end_date' => $this->newEndDate->format('Y-m-d'),
        ];
    }
}
