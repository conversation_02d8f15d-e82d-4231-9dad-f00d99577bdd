<?php

namespace App\Notifications;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionExpiredNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $renewUrl = route('patient.subscriptions.index');
        
        return (new MailMessage)
            ->subject('Your Subscription Has Expired')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your subscription to the ' . $this->subscription->plan->name . ' plan has expired.')
            ->line('You no longer have access to the benefits provided by your subscription.')
            ->action('Renew Your Subscription', $renewUrl)
            ->line('Thank you for using our service. We hope to see you again soon!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'expired_at' => $this->subscription->ends_at->format('Y-m-d H:i:s'),
        ];
    }
}
