<?php

namespace App\Notifications;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionExpiringNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $subscription;
    protected $daysRemaining;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription, int $daysRemaining)
    {
        $this->subscription = $subscription;
        $this->daysRemaining = $daysRemaining;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your Subscription is Expiring Soon')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('Your ' . $this->subscription->plan->name . ' subscription is expiring in ' . $this->daysRemaining . ' days.')
            ->line('To continue enjoying our services without interruption, please renew your subscription.')
            ->action('Renew Subscription', route('patient.subscriptions.index'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'days_remaining' => $this->daysRemaining,
            'expires_at' => $this->subscription->ends_at->format('Y-m-d H:i:s'),
            'message' => 'Your ' . $this->subscription->plan->name . ' subscription is expiring in ' . $this->daysRemaining . ' days.',
            'action_url' => route('patient.subscriptions.index'),
        ];
    }
}
