<?php

namespace App\Notifications;

use App\Models\Subscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionRenewalFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The subscription instance.
     *
     * @var \App\Models\Subscription
     */
    protected $subscription;

    /**
     * The error message.
     *
     * @var string
     */
    protected $errorMessage;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription, string $errorMessage)
    {
        $this->subscription = $subscription;
        $this->errorMessage = $errorMessage;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $expirationDate = $this->subscription->ends_at->format('F j, Y');
        $daysLeft = now()->diffInDays($this->subscription->ends_at, false);
        
        return (new MailMessage)
            ->subject('Action Required: Subscription Renewal Failed')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('We were unable to automatically renew your subscription to the ' . $this->subscription->plan->name . ' plan.')
            ->line('Reason: ' . $this->errorMessage)
            ->line('Your subscription is still active but will expire on ' . $expirationDate . ' (' . $daysLeft . ' days from now).')
            ->line('To ensure uninterrupted service, please update your payment information or manually renew your subscription before it expires.')
            ->action('Renew Subscription', route('patient.subscriptions.index'))
            ->line('If you need any assistance, please contact our support team.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'error_message' => $this->errorMessage,
            'expiration_date' => $this->subscription->ends_at->format('Y-m-d'),
        ];
    }
}
