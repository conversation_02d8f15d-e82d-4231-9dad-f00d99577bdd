<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class SupportTicketReplyNotification extends Notification
{
    use Queueable;

    /**
     * The support ticket.
     *
     * @var \App\Models\AgentSupportTicket
     */
    protected $ticket;

    /**
     * The ticket reply.
     *
     * @var \App\Models\AgentSupportTicketReply
     */
    protected $reply;

    /**
     * Create a new notification instance.
     */
    public function __construct(\App\Models\AgentSupportTicket $ticket, \App\Models\AgentSupportTicketReply $reply)
    {
        $this->ticket = $ticket;
        $this->reply = $reply;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $agent = $this->ticket->agent->user;

        return (new MailMessage)
                    ->subject('New Reply to Support Ticket: ' . $this->ticket->subject)
                    ->greeting('Hello ' . $notifiable->name . '!')
                    ->line('A new reply has been added to a support ticket by ' . $agent->name . '.')
                    ->line('Subject: ' . $this->ticket->subject)
                    ->line('Reply: ' . Str::limit($this->reply->message, 200))
                    ->action('View Ticket', url('/admin/support/tickets/' . $this->ticket->id))
                    ->line('Please respond to this ticket as soon as possible.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'reply_id' => $this->reply->id,
            'agent_id' => $this->ticket->agent_id,
            'agent_name' => $this->ticket->agent->user->name,
            'subject' => $this->ticket->subject,
            'message' => 'New reply from ' . $this->ticket->agent->user->name . ' on ticket: ' . Str::limit($this->ticket->subject, 50),
        ];
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
