<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class TemporaryPasswordNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $password;
    protected $businessName;

    /**
     * Create a new notification instance.
     *
     * @param string $password
     * @param string|null $businessName
     * @return void
     */
    public function __construct(string $password, ?string $businessName = null)
    {
        $this->password = $password;
        $this->businessName = $businessName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $message = (new MailMessage)
            ->subject('Welcome to GO MD USA — we\'re excited to have you on board!')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Welcome to GO MD USA — we\'re excited to have you on board!')
            ->line('You\'re now part of a nationwide network dedicated to revolutionizing healthcare access while creating a robust, sustainable income stream. Here\'s how you can get started:');

        // Add login credentials section
        $message->line('Your Login Credentials:')
            ->line('Username: ' . $notifiable->email)
            ->line('Password: ' . $this->password)
            ->action('Login Here', url('/login'));

        // Add training program section
        $message->line('📘 Training Program Access')
            ->line('Our comprehensive training course guides you through the entire GO MD USA system, designed specifically to:')
            ->line('• Help you generate unlimited leads (no need to purchase leads anymore!)')
            ->line('• Show you how to cross-sell your own ACA, health, life, Medicare, dental, or other ancillary products')
            ->line('• Equip you to achieve a high five- to six-figure income within your first 12 months');

        $message->line('Training Overview:')
            ->line('• Duration: Approximately 87 minutes')
            ->line('• Format: 13 self-paced modules')
            ->line('Access Training: GO MD USA Training Playlist');

        $message->line('Additional Resources Included:')
            ->line('• Downloadable PDF version of the training (perfect for reference or following along)')
            ->line('• Formulary & Program Brochure (combined into a single convenient file)');

        $message->line('CRM Training Video:')
            ->line('https://www.loom.com/share/3ea79a46adce49f5b700fb2fd6e4565d?sid=5ee8173b-38dc-493c-b5f6-5f0e335c787c');

        // Add contract completion section
        $message->line('📝 Contract Completion')
            ->line('Once logged into your dashboard, scroll down to complete your contract, and you\'ll be ready to start selling.');

        $message->line('Need assistance or have questions about the training materials? Reach out to Jack <NAME_EMAIL>.')
            ->line('Let\'s get started!');

        // Add attachments
        $formularyPath = storage_path('app/FORMULARY.pdf');
        $trainingPath = storage_path('app/welcomeemailtrainingcourse.docx');

        if (file_exists($formularyPath)) {
            $message->attach($formularyPath, [
                'as' => 'FORMULARY.pdf',
                'mime' => 'application/pdf',
            ]);
        }

        if (file_exists($trainingPath)) {
            $message->attach($trainingPath, [
                'as' => 'GO_MD_USA_Training_Course.docx',
                'mime' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            ]);
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'password' => $this->password,
            'business_name' => $this->businessName,
        ];
    }
}
