<?php

namespace App\Notifications;

use App\Models\Agent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The agent instance.
     *
     * @var \App\Models\Agent
     */
    protected $agent;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Agent  $agent
     * @return void
     */
    public function __construct(Agent $agent)
    {
        $this->agent = $agent;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Test Notification - Agent Referral System')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('This is a test notification from the Agent Referral System.')
            ->line('Your notification settings are working correctly.')
            ->line('You will receive notifications about:')
            ->line('- New referrals')
            ->line('- Commission earnings')
            ->line('- Payment status changes')
            ->action('View Your Dashboard', url('/agent/dashboard'))
            ->line('Thank you for being part of our agent network!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'agent_id' => $this->agent->id,
            'message' => 'This is a test notification.',
            'type' => 'test',
        ];
    }
}
