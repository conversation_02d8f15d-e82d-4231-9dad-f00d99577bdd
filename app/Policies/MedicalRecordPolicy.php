<?php

namespace App\Policies;

use App\Models\MedicalRecord;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MedicalRecordPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user, User $patient)
    {
        return $user->id === $patient->id 
            || $user->hasRole('doctor') 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('view medical records'));
    }

    public function view(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->id === $patient->id 
            || $user->id === $medicalRecord->doctor_id 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('view medical records'));
    }

    public function create(User $user, User $patient)
    {
        return $user->hasRole('doctor') 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('create medical records'));
    }

    public function update(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->id === $medicalRecord->doctor_id 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('edit medical records'));
    }

    public function delete(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->id === $medicalRecord->doctor_id 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('delete medical records'));
    }

    public function restore(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->hasRole('admin');
    }

    public function forceDelete(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->hasRole('admin');
    }

    public function attachFile(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->id === $medicalRecord->doctor_id 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('attach files to medical records'));
    }

    public function downloadFile(User $user, MedicalRecord $medicalRecord, User $patient)
    {
        return $user->id === $patient->id 
            || $user->id === $medicalRecord->doctor_id 
            || $user->hasRole('admin')
            || ($user->hasRole('staff') && $user->can('download medical record files'));
    }
}