<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class PatientPolicy
{
    use HandlesAuthorization;

    public function viewPatient(User $user, User $patient)
    {
        if ($user->hasAnyRole(['admin', 'staff', 'pharmacist'])) {
            return true;
        }

        return $user->assignedPatients()->where('id', $patient->id)->exists();
    }
}
