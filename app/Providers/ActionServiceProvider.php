<?php

namespace App\Providers;

use App\Actions\Auth\SendLoginLink;
use App\Actions\Subscriptions\CalculatePlanPrice;
use App\Actions\Subscriptions\ProcessSubscriptionPayment;
use App\Actions\Subscriptions\SubscribeToPlan;
use App\Actions\UploadEmployees;
use App\Actions\Medications\ProcessPayment;
use App\Actions\Medications\CreateSubscription;
use App\Actions\Medications\DetermineRelevantQuestions;
use App\Actions\Medications\HandleMedicalData;
use App\Actions\Medications\HandleUserCreation;
use App\Actions\Medications\HandleInsurance;
use App\Actions\Medications\HandleMeasurements;
use App\Actions\Medications\SaveMedicalQuestions;
use App\Actions\Medications\SecurelyStoreMedicalData;
use App\Actions\Medications\SelectPlan;
use App\Actions\Medications\TrackOrderProgress;
use App\Actions\UrgentCare\ProcessUrgentCarePayment;
use App\Actions\UrgentCare\HandleUrgentCareMeasurements;
use App\Actions\UrgentCare\HandleUrgentCareUserCreation;
use App\Actions\Agents\UploadAgentsAction;
use Illuminate\Support\ServiceProvider;

class ActionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register subscription actions
        $this->app->singleton(ProcessSubscriptionPayment::class);
        $this->app->singleton(SubscribeToPlan::class);
        $this->app->singleton(CalculatePlanPrice::class);

        // Register medication actions
        $this->app->singleton(ProcessPayment::class);
        $this->app->singleton(CreateSubscription::class);
        $this->app->singleton(HandleMedicalData::class);
        $this->app->singleton(HandleUserCreation::class);
        $this->app->singleton(HandleInsurance::class);
        $this->app->singleton(HandleMeasurements::class);
        $this->app->singleton(SaveMedicalQuestions::class);
        $this->app->singleton(SelectPlan::class);
        $this->app->singleton(DetermineRelevantQuestions::class);
        $this->app->singleton(SecurelyStoreMedicalData::class);
        $this->app->singleton(TrackOrderProgress::class);

        // Register business actions
        $this->app->singleton(\App\Actions\Business\RegisterBusinessAction::class);
        $this->app->singleton(\App\Actions\Business\AddBusinessPlan::class);
        $this->app->singleton(\App\Actions\Business\GetBusinessAdminAction::class);
        $this->app->singleton(\App\Actions\Business\AddCreditCardAction::class);
        $this->app->singleton(\App\Actions\Business\SetDefaultCreditCardAction::class);

        // Register auth actions
        $this->app->singleton(SendLoginLink::class);

        // Register employee actions
        $this->app->singleton(UploadEmployees::class);

        // Register agent actions
        $this->app->singleton(UploadAgentsAction::class);

        // Register urgent care actions
        $this->app->singleton(ProcessUrgentCarePayment::class);
        $this->app->singleton(HandleUrgentCareMeasurements::class);
        $this->app->singleton(HandleUrgentCareUserCreation::class);

        // Register medical record actions
        $this->app->singleton(\App\Actions\MedicalRecord\CreateMedicalCondition::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
