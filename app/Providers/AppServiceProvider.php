<?php

namespace App\Providers;

use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Opcodes\LogViewer\Facades\LogViewer;
use ProtoneMedia\Splade\Facades\Splade;
use Surfsidemedia\Shoppingcart\Facades\Cart;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register payment services are now handled in PaymentServiceProvider
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        DB::prohibitDestructiveCommands(
            app()->isProduction()
        );

        $this->app->resolving(EncryptCookies::class, function ($object) {
            $object->disableFor('gclid');
            $object->disableFor('LTClickID');
            $object->disableFor('AFID');
        });

        LogViewer::auth(function ($request) {
            return true;
        });

        Splade::share('cart_count', Cart::count());
        Splade::share('cart_content', Cart::content());

        // Configure default toast settings
        Splade::defaultToast(function ($toast) {
            $toast->info()->leftTop()->autoDismiss(10);
        });
    }
}
