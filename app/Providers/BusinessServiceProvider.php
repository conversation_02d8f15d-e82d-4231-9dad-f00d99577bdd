<?php

namespace App\Providers;

use App\Models\BusinessEmployee;
use App\Services\BusinessTrialService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class BusinessServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(BusinessTrialService::class, function ($app) {
            return new BusinessTrialService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Trial expiration check
        $this->app->booted(function () {
            // In a real app, this would be a scheduled command
            $this->checkTrialExpirations();
        });
        
        // Only register event listeners if the table exists
        if ($this->tableExists('business_employees')) {
            // Employee termination webhook
            BusinessEmployee::updated(function (BusinessEmployee $employee) {
                if ($employee->isDirty('status') && $employee->status === 'terminated' && $employee->transitioned_to_consumer) {
                    $this->triggerConsumerPlanTransition($employee);
                }
            });
        }
    }

    /**
     * Check for expired trials
     */
    protected function checkTrialExpirations(): void
    {
        if (!$this->tableExists('businesses')) {
            return;
        }

        try {
            $service = $this->app->make(BusinessTrialService::class);
            $count = $service->processExpiredTrials();
            
            if ($count > 0) {
                Log::info("Processed {$count} expired business trials");
            }
        } catch (\Exception $e) {
            Log::error("Error processing expired trials: {$e->getMessage()}");
        }
    }

    /**
     * Trigger consumer plan transition for terminated employee
     */
    protected function triggerConsumerPlanTransition(BusinessEmployee $employee): void
    {
        $webhookUrl = config('business.webhooks.employee_termination');
        
        if (!$webhookUrl || !$employee->user_id) {
            return;
        }
        
        try {
            // In a real app, this would send a webhook or trigger an event
            Log::info("Employee {$employee->id} terminated and marked for consumer plan transition");
        } catch (\Exception $e) {
            Log::error("Error triggering consumer plan transition: {$e->getMessage()}", [
                'employee_id' => $employee->id,
                'user_id' => $employee->user_id,
            ]);
        }
    }

    /**
     * Check if a table exists in the database
     */
    protected function tableExists(string $table): bool
    {
        return Schema::hasTable($table);
    }
}
