<?php

namespace App\Providers;

use App\Services\Api\AchPaymentService;
use App\Services\Api\AuthorizeNetService;
use App\Services\InvoiceGeneratorService;
use App\Services\InvoicePaymentService;
use App\Services\PaymentMethodService;
use App\Services\PaymentProcessorService;
use Illuminate\Support\ServiceProvider;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register AuthorizeNetService
        $this->app->singleton(AuthorizeNetService::class, function ($app) {
            return new AuthorizeNetService();
        });

        // Register AchPaymentService
        $this->app->singleton(AchPaymentService::class, function ($app) {
            return new AchPaymentService();
        });

        // Register InvoiceGeneratorService
        $this->app->singleton(InvoiceGeneratorService::class, function ($app) {
            return new InvoiceGeneratorService();
        });

        // Register InvoicePaymentService
        $this->app->singleton(InvoicePaymentService::class, function ($app) {
            return new InvoicePaymentService(
                $app->make(InvoiceGeneratorService::class)
            );
        });

        // Register PaymentMethodService
        $this->app->singleton(PaymentMethodService::class, function ($app) {
            return new PaymentMethodService(
                $app->make(AuthorizeNetService::class),
                $app->make(AchPaymentService::class),
                $app->make(InvoicePaymentService::class)
            );
        });

        // Register PaymentProcessorService
        $this->app->singleton(PaymentProcessorService::class, function ($app) {
            return new PaymentProcessorService(
                $app->make(AuthorizeNetService::class),
                $app->make(AchPaymentService::class),
                $app->make(InvoicePaymentService::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
