<?php

namespace App\Services\Agent;

use App\Models\Goal;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GoalsService
{
    /**
     * The notification service instance.
     *
     * @var \App\Services\Agent\NotificationService
     */
    protected $notificationService;

    /**
     * Create a new service instance.
     *
     * @param  \App\Services\Agent\NotificationService  $notificationService
     * @return void
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Create a new goal for a user.
     *
     * @param  \App\Models\User  $user
     * @param  array  $data
     * @return \App\Models\Goal
     */
    public function createGoal(User $user, array $data)
    {
        $goal = new Goal([
            'description' => $data['description'],
            'type' => $data['type'],
            'target_value' => $data['target_value'],
            'target_date' => $data['target_date'],
            'status' => 'active',
            'progress_value' => 0,
            'progress_percentage' => 0,
        ]);
        
        $user->goals()->save($goal);
        
        Log::info('Goal created', [
            'user_id' => $user->id,
            'goal_id' => $goal->id,
        ]);
        
        return $goal;
    }

    /**
     * Update a goal's progress.
     *
     * @param  \App\Models\Goal  $goal
     * @param  float  $progressValue
     * @return \App\Models\Goal
     */
    public function updateProgress(Goal $goal, float $progressValue)
    {
        $wasAchieved = $goal->status === 'achieved';
        
        $goal->progress_value = $progressValue;
        $goal->progress_percentage = min(100, round(($progressValue / $goal->target_value) * 100));
        
        // Check if goal is achieved
        if (!$wasAchieved && $goal->progress_value >= $goal->target_value) {
            $goal->status = 'achieved';
            $goal->achieved_at = Carbon::now();
            
            // Send notification
            $this->notificationService->sendGoalAchievedNotification($goal);
        }
        
        $goal->save();
        
        Log::info('Goal progress updated', [
            'goal_id' => $goal->id,
            'progress_value' => $progressValue,
            'progress_percentage' => $goal->progress_percentage,
            'status' => $goal->status,
        ]);
        
        return $goal;
    }

    /**
     * Check for expired goals and update their status.
     *
     * @return int Number of goals marked as expired
     */
    public function checkExpiredGoals()
    {
        $today = Carbon::today();
        
        $expiredGoals = Goal::where('status', 'active')
            ->whereDate('target_date', '<', $today)
            ->get();
        
        $count = 0;
        
        foreach ($expiredGoals as $goal) {
            $goal->status = 'expired';
            $goal->save();
            
            Log::info('Goal marked as expired', [
                'goal_id' => $goal->id,
                'user_id' => $goal->user_id,
            ]);
            
            $count++;
        }
        
        return $count;
    }

    /**
     * Get goals for a user with optional filtering.
     *
     * @param  \App\Models\User  $user
     * @param  string|null  $status
     * @param  int  $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getGoals(User $user, ?string $status = null, int $perPage = 10)
    {
        $query = $user->goals();
        
        if ($status) {
            $query->where('status', $status);
        }
        
        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get goal statistics for a user.
     *
     * @param  \App\Models\User  $user
     * @return array
     */
    public function getGoalStatistics(User $user)
    {
        $stats = [
            'total_goals' => $user->goals()->count(),
            'active_goals' => $user->goals()->where('status', 'active')->count(),
            'achieved_goals' => $user->goals()->where('status', 'achieved')->count(),
            'expired_goals' => $user->goals()->where('status', 'expired')->count(),
            'average_progress' => 0,
        ];
        
        // Calculate average progress for active goals
        $activeGoals = $user->goals()->where('status', 'active')->get();
        if ($activeGoals->count() > 0) {
            $stats['average_progress'] = round($activeGoals->avg('progress_percentage'));
        }
        
        return $stats;
    }
}
