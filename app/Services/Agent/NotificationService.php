<?php

namespace App\Services\Agent;

use App\Models\Certification;
use App\Models\Commission;
use App\Models\Goal;
use App\Models\MarketingMaterial;
use App\Models\Message;
use App\Models\Referral;
use App\Models\Report;
use App\Models\User;
use App\Notifications\Agent\CertificationEarnedNotification;
use App\Notifications\Agent\CommissionPaidNotification;
use App\Notifications\Agent\GoalAchievedNotification;
use App\Notifications\Agent\GoalDeadlineReminderNotification;
use App\Notifications\Agent\MarketingMaterialReadyNotification;
use App\Notifications\Agent\NewMessageNotification;
use App\Notifications\Agent\ReferralConversionNotification;
use App\Notifications\Agent\ReportGeneratedNotification;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send a notification for a goal achievement.
     *
     * @param  \App\Models\Goal  $goal
     * @return void
     */
    public function sendGoalAchievedNotification(Goal $goal)
    {
        try {
            $user = $goal->user;
            $user->notify(new GoalAchievedNotification($goal));
            
            Log::info('Goal achieved notification sent', [
                'user_id' => $user->id,
                'goal_id' => $goal->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send goal achieved notification', [
                'goal_id' => $goal->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a goal deadline reminder.
     *
     * @param  \App\Models\Goal  $goal
     * @param  int  $daysRemaining
     * @return void
     */
    public function sendGoalDeadlineReminderNotification(Goal $goal, int $daysRemaining)
    {
        try {
            $user = $goal->user;
            $user->notify(new GoalDeadlineReminderNotification($goal, $daysRemaining));
            
            Log::info('Goal deadline reminder notification sent', [
                'user_id' => $user->id,
                'goal_id' => $goal->id,
                'days_remaining' => $daysRemaining,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send goal deadline reminder notification', [
                'goal_id' => $goal->id,
                'days_remaining' => $daysRemaining,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a new message.
     *
     * @param  \App\Models\Message  $message
     * @return void
     */
    public function sendNewMessageNotification(Message $message)
    {
        try {
            $recipient = $message->recipient;
            
            // Only send notification if the recipient is not the sender
            if ($recipient->id !== $message->sender_id) {
                $recipient->notify(new NewMessageNotification($message));
                
                Log::info('New message notification sent', [
                    'recipient_id' => $recipient->id,
                    'message_id' => $message->id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send new message notification', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a certification earned.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Certification  $certification
     * @return void
     */
    public function sendCertificationEarnedNotification(User $user, Certification $certification)
    {
        try {
            $user->notify(new CertificationEarnedNotification($certification));
            
            Log::info('Certification earned notification sent', [
                'user_id' => $user->id,
                'certification_id' => $certification->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send certification earned notification', [
                'user_id' => $user->id,
                'certification_id' => $certification->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a report generation.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Report  $report
     * @param  string|null  $downloadUrl
     * @return void
     */
    public function sendReportGeneratedNotification(User $user, Report $report, $downloadUrl = null)
    {
        try {
            $user->notify(new ReportGeneratedNotification($report, $downloadUrl));
            
            Log::info('Report generated notification sent', [
                'user_id' => $user->id,
                'report_id' => $report->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send report generated notification', [
                'user_id' => $user->id,
                'report_id' => $report->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a marketing material ready.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\MarketingMaterial  $material
     * @return void
     */
    public function sendMarketingMaterialReadyNotification(User $user, MarketingMaterial $material)
    {
        try {
            $user->notify(new MarketingMaterialReadyNotification($material));
            
            Log::info('Marketing material ready notification sent', [
                'user_id' => $user->id,
                'material_id' => $material->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send marketing material ready notification', [
                'user_id' => $user->id,
                'material_id' => $material->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a commission payment.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Commission  $commission
     * @return void
     */
    public function sendCommissionPaidNotification(User $user, Commission $commission)
    {
        try {
            $user->notify(new CommissionPaidNotification($commission));
            
            Log::info('Commission paid notification sent', [
                'user_id' => $user->id,
                'commission_id' => $commission->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send commission paid notification', [
                'user_id' => $user->id,
                'commission_id' => $commission->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send a notification for a referral conversion.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Referral  $referral
     * @return void
     */
    public function sendReferralConversionNotification(User $user, Referral $referral)
    {
        try {
            $user->notify(new ReferralConversionNotification($referral));
            
            Log::info('Referral conversion notification sent', [
                'user_id' => $user->id,
                'referral_id' => $referral->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send referral conversion notification', [
                'user_id' => $user->id,
                'referral_id' => $referral->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
