<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentCommission;
use App\Models\Subscription;
use App\Models\Transaction;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AgentReferralService
{
    /**
     * Process a transaction and calculate agent commissions.
     *
     * @param Transaction $transaction
     * @param Subscription|null $subscription
     * @param Agent|null $agent
     * @return array
     */
    public function processTransaction(Transaction $transaction, ?Subscription $subscription = null, ?Agent $agent = null): array
    {
        try {
            // If no agent is provided, try to find one from the subscription metadata
            if (!$agent && $subscription && $subscription->hasFeature('agent_id')) {
                $agentId = $subscription->getFeature('agent_id');
                $agent = Agent::find($agentId);
            }

            // If still no agent, no commission to calculate
            if (!$agent) {
                return [
                    'success' => true,
                    'message' => 'No agent associated with this transaction',
                    'commission' => null
                ];
            }

            // Calculate agent's commission
            $totalAmount = $transaction->amount;
            $commissionAmount = $agent->calculateCommission($totalAmount);

            // Create commission record
            $commission = new AgentCommission([
                'agent_id' => $agent->id,
                'transaction_id' => $transaction->id,
                'subscription_id' => $subscription ? $subscription->id : null,
                'total_amount' => $totalAmount,
                'commission_amount' => $commissionAmount,
                'agent_rate' => $agent->commission_rate,
                'status' => 'pending'
            ]);

            // Check for upline commission
            if ($agent->referring_agent_id) {
                $uplineAgent = $agent->referrer;

                if ($uplineAgent) {
                    $uplineCommissionAmount = $uplineAgent->calculateUplineCommission($totalAmount, $agent);

                    if ($uplineCommissionAmount > 0) {
                        $commission->upline_agent_id = $uplineAgent->id;
                        $commission->upline_commission_amount = $uplineCommissionAmount;
                        $commission->upline_rate = $uplineAgent->commission_rate - $agent->commission_rate;
                    }
                }
            }

            $commission->save();

            return [
                'success' => true,
                'message' => 'Commission calculated successfully',
                'commission' => $commission
            ];
        } catch (\Exception $e) {
            Log::error('Commission calculation failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Commission calculation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate a unique referral URL for an agent.
     *
     * @param Agent $agent
     * @return string
     */
    public function generateReferralUrl(Agent $agent): string
    {
        // Ensure agent has a referral token
        if (!$agent->referral_token) {
            $agent->referral_token = (string) Str::uuid();
            $agent->save();
        }

        return url('/agent/register') . '?agent_ref=' . $agent->referral_token;
    }

    /**
     * Find an agent by referral token.
     *
     * @param string $token
     * @return Agent|null
     */
    public function findAgentByToken(string $token): ?Agent
    {
        return Agent::where('referral_token', $token)
            ->where('status', 'approved')
            ->first();
    }

    /**
     * Get allowed tiers for a referral based on the referring agent's tier.
     *
     * @param Agent $referringAgent
     * @return array
     */
    public function getAllowedTiersForReferral(Agent $referringAgent): array
    {
        return $referringAgent->getAllowedTiersForReferrals();
    }

    /**
     * Store agent ID in subscription metadata.
     *
     * @param Subscription $subscription
     * @param Agent $agent
     * @return void
     */
    public function attachAgentToSubscription(Subscription $subscription, Agent $agent): void
    {
        $subscription->setFeature('agent_id', $agent->id);
        $subscription->setFeature('referral_code', $agent->referral_code);
    }
}
