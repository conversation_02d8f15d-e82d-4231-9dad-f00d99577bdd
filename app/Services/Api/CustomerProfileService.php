<?php

namespace App\Services\Api;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class CustomerProfileService
{
    /**
     * @var AuthorizeNetApi
     */
    protected $api;

    /**
     * Constructor
     */
    public function __construct(AuthorizeNetApi $api)
    {
        $this->api = $api;
    }

    /**
     * Create a customer profile
     *
     * @param User $user
     * @return string The customer profile ID
     * @throws \Exception
     */
    public function createCustomerProfile(User $user): string
    {
        try {
            $response = $this->api->sendRequest('createCustomerProfileRequest', [
                'profile' => [
                    'merchantCustomerId' => 'M_' . $user->id,
                    'description' => 'Customer Profile for ' . $user->name,
                    'email' => $user->email
                ]
            ]);

            // Extract the customer profile ID from the response
            if (isset($response['customerProfileId'])) {
                return $response['customerProfileId'];
            } elseif (isset($response['messages']['message'][0]['text'])) {
                // Try to extract from the response message
                $message = $response['messages']['message'][0]['text'];
                if (preg_match('/ID: (\d+)/', $message, $matches)) {
                    return $matches[1];
                }
            }

            throw new \Exception("Could not extract customer profile ID from response");
        } catch (\Exception $e) {
            // Check for duplicate profile error
            if (strpos($e->getMessage(), 'E00039') !== false) {
                Log::info("Duplicate profile detected for user: {$user->id}");

                // Extract the profile ID from the error message
                if (preg_match('/ID: (\d+)/', $e->getMessage(), $matches)) {
                    return $matches[1];
                }
            }

            throw $e;
        }
    }

    /**
     * Get a customer profile
     *
     * @param string $profileId
     * @return array The customer profile data
     * @throws \Exception
     */
    public function getCustomerProfile(string $profileId): array
    {
        try {
            $response = $this->api->sendRequest('getCustomerProfileRequest', [
                'customerProfileId' => $profileId
            ]);

            if (isset($response['profile'])) {
                return $response['profile'];
            }

            throw new \Exception("Could not retrieve customer profile");
        } catch (\Exception $e) {
            // Check for profile not found error
            if (strpos($e->getMessage(), 'E00040') !== false) {
                Log::warning("Customer profile {$profileId} not found in Authorize.net.");
            }

            throw $e;
        }
    }

    /**
     * Check if a customer profile exists
     *
     * @param string $profileId
     * @return bool Whether the profile exists
     */
    public function customerProfileExists(string $profileId): bool
    {
        try {
            $this->getCustomerProfile($profileId);
            return true;
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'E00040') !== false) {
                return false;
            }
            throw $e;
        }
    }

    /**
     * Update a customer profile
     *
     * @param string $profileId
     * @param array $profileData
     * @return bool Success status
     * @throws \Exception
     */
    public function updateCustomerProfile(string $profileId, array $profileData): bool
    {
        $profile = array_merge(['customerProfileId' => $profileId], $profileData);

        $this->api->sendRequest('updateCustomerProfileRequest', [
            'profile' => $profile
        ]);

        return true;
    }

    /**
     * Delete a customer profile
     *
     * @param string $profileId
     * @return bool Success status
     * @throws \Exception
     */
    public function deleteCustomerProfile(string $profileId): bool
    {
        $this->api->sendRequest('deleteCustomerProfileRequest', [
            'customerProfileId' => $profileId
        ]);

        return true;
    }
}
