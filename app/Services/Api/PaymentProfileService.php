<?php

namespace App\Services\Api;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class PaymentProfileService
{
    /**
     * @var AuthorizeNetApi
     */
    protected $api;

    /**
     * Constructor
     */
    public function __construct(AuthorizeNetApi $api)
    {
        $this->api = $api;
    }

    /**
     * Create a payment profile for a customer
     *
     * @param string $customerProfileId
     * @param string $cardNumber
     * @param string $expirationMonth
     * @param string $expirationYear
     * @param string $cvv
     * @param User $user
     * @return string Payment profile ID
     * @throws \Exception
     */
    public function createPaymentProfile(
        string $customerProfileId,
        string $cardNumber,
        string $expirationMonth,
        string $expirationYear,
        string $cvv,
        User $user
    ): string {
        try {
            // Format expiration date as YYYY-MM
            // Make sure the year is 4 digits (YYYY)
            $expirationYear = strlen($expirationYear) == 2 ? '20' . $expirationYear : $expirationYear;
            $expirationDate = $expirationYear . '-' . str_pad($expirationMonth, 2, '0', STR_PAD_LEFT);

            // According to the Authorize.net schema, billTo should be at the same level as paymentProfile
            $response = $this->api->sendRequest('createCustomerPaymentProfileRequest', [
                'customerProfileId' => $customerProfileId,
                'paymentProfile' => [
                    'customerType' => 'individual',
                    'payment' => [
                        'creditCard' => [
                            'cardNumber' => $cardNumber,
                            'expirationDate' => $expirationDate,
                            'cardCode' => $cvv
                        ]
                    ],
                    // 'billTo' => [
                    //     'firstName' => $user->fname ?? '',
                    //     'lastName' => $user->lname ?? '',
                    //     'company' => $user->business->name ?? '',
                    //     'address' => $user->address1 ?? '',
                    //     'city' => $user->city ?? '',
                    //     'state' => $user->state ?? '',
                    //     'zip' => $user->zip ?? '',
                    //     'country' => 'US',
                    //     'phoneNumber' => $user->phone ?? '',
                    //     'email' => $user->email ?? ''
                    // ]
                ]
            ]);

            // Extract the payment profile ID from the response
            if (isset($response['customerPaymentProfileId'])) {
                return $response['customerPaymentProfileId'];
            } elseif (isset($response['messages']['message'][0]['text'])) {
                // Try to extract from the response message
                $message = $response['messages']['message'][0]['text'];
                if (preg_match('/ID: (\d+)/', $message, $matches)) {
                    return $matches[1];
                }
            }

            throw new \Exception("Could not extract payment profile ID from response");
        } catch (\Exception $e) {
            // Check for duplicate payment profile error
            if (strpos($e->getMessage(), 'E00039') !== false) {
                Log::info("Duplicate payment profile detected for user: {$user->id}");

                // Extract the profile ID from the error message
                if (preg_match('/ID: (\d+)/', $e->getMessage(), $matches)) {
                    return $matches[1];
                }
            }

            // Check for profile not found error
            if (strpos($e->getMessage(), 'E00040') !== false) {
                Log::warning("Customer profile {$customerProfileId} not found in Authorize.net. This should have been handled earlier.");

                // Try to verify if the customer profile exists
                try {
                    $this->api->sendRequest('getCustomerProfileRequest', [
                        'customerProfileId' => $customerProfileId
                    ]);

                    // If we get here, the profile exists but there was some other issue
                    Log::error("Customer profile exists but payment profile creation failed: {$e->getMessage()}");
                } catch (\Exception $verifyError) {
                    // If the profile doesn't exist, log it
                    if (strpos($verifyError->getMessage(), 'E00040') !== false) {
                        Log::error("Customer profile {$customerProfileId} confirmed not to exist in Authorize.net.");
                    }
                }
            }

            throw $e;
        }
    }

    /**
     * Get a payment profile
     *
     * @param string $customerProfileId
     * @param string $paymentProfileId
     * @return array The payment profile data
     * @throws \Exception
     */
    public function getPaymentProfile(string $customerProfileId, string $paymentProfileId): array
    {
        try {
            $response = $this->api->sendRequest('getCustomerPaymentProfileRequest', [
                'customerProfileId' => $customerProfileId,
                'customerPaymentProfileId' => $paymentProfileId
            ]);

            if (isset($response['paymentProfile'])) {
                return $response['paymentProfile'];
            }

            throw new \Exception("Could not retrieve payment profile");
        } catch (\Exception $e) {
            // Check for profile not found error
            if (strpos($e->getMessage(), 'E00040') !== false) {
                Log::warning("Payment profile or customer profile not found in Authorize.net. CustomerProfileId: {$customerProfileId}, PaymentProfileId: {$paymentProfileId}");

                // Try to verify if the customer profile exists
                try {
                    $this->api->sendRequest('getCustomerProfileRequest', [
                        'customerProfileId' => $customerProfileId
                    ]);

                    // If we get here, the customer profile exists but the payment profile doesn't
                    Log::error("Customer profile exists but payment profile {$paymentProfileId} not found.");
                } catch (\Exception $verifyError) {
                    // If the profile doesn't exist, log it
                    if (strpos($verifyError->getMessage(), 'E00040') !== false) {
                        Log::error("Customer profile {$customerProfileId} not found in Authorize.net.");
                    }
                }
            }

            throw $e;
        }
    }

    /**
     * Update a payment profile
     *
     * @param string $customerProfileId
     * @param string $paymentProfileId
     * @param array $paymentData
     * @return bool Success status
     * @throws \Exception
     */
    public function updatePaymentProfile(string $customerProfileId, string $paymentProfileId, array $paymentData): bool
    {
        $paymentProfile = array_merge(['customerPaymentProfileId' => $paymentProfileId], $paymentData);

        $this->api->sendRequest('updateCustomerPaymentProfileRequest', [
            'customerProfileId' => $customerProfileId,
            'paymentProfile' => $paymentProfile
        ]);

        return true;
    }

    /**
     * Delete a payment profile
     *
     * @param string $customerProfileId
     * @param string $paymentProfileId
     * @return bool Success status
     * @throws \Exception
     */
    public function deletePaymentProfile(string $customerProfileId, string $paymentProfileId): bool
    {
        $this->api->sendRequest('deleteCustomerPaymentProfileRequest', [
            'customerProfileId' => $customerProfileId,
            'customerPaymentProfileId' => $paymentProfileId
        ]);

        return true;
    }

    /**
     * Validate a payment profile
     *
     * @param string $customerProfileId
     * @param string $paymentProfileId
     * @param string $validationMode
     * @return bool Success status
     * @throws \Exception
     */
    public function validatePaymentProfile(
        string $customerProfileId,
        string $paymentProfileId,
        string $validationMode = 'testMode'
    ): bool {
        $this->api->sendRequest('validateCustomerPaymentProfileRequest', [
            'customerProfileId' => $customerProfileId,
            'customerPaymentProfileId' => $paymentProfileId,
            'validationMode' => $validationMode
        ]);

        return true;
    }

    /**
     * Create an ACH payment profile for a customer
     *
     * @param string $customerProfileId
     * @param string $accountName
     * @param string $accountType
     * @param string $routingNumber
     * @param string $accountNumber
     * @param string $bankName
     * @param User $user
     * @return string Payment profile ID
     * @throws \Exception
     */
    public function createAchPaymentProfile(
        string $customerProfileId,
        string $accountName,
        string $accountType,
        string $routingNumber,
        string $accountNumber,
        string $bankName,
        User $user
    ): string {
        try {
            // Create the payment profile with bank account information
            $response = $this->api->sendRequest('createCustomerPaymentProfileRequest', [
                'customerProfileId' => $customerProfileId,
                'paymentProfile' => [
                    'customerType' => 'individual',
                    'payment' => [
                        'bankAccount' => [
                            'accountType' => $accountType,
                            'routingNumber' => $routingNumber,
                            'accountNumber' => $accountNumber,
                            'nameOnAccount' => $accountName,
                            'bankName' => $bankName
                        ]
                    ],
                    // 'billTo' => [
                    //     'firstName' => $user->fname ?? '',
                    //     'lastName' => $user->lname ?? '',
                    //     'address' => $user->address1 ?? '',
                    //     'city' => $user->city ?? '',
                    //     'state' => $user->state ?? '',
                    //     'zip' => $user->zip ?? '',
                    //     'country' => 'US',
                    //     'phoneNumber' => $user->phone ?? '',
                    //     'email' => $user->email ?? ''
                    // ]
                ]
            ]);

            // Extract the payment profile ID from the response
            if (isset($response['customerPaymentProfileId'])) {
                return $response['customerPaymentProfileId'];
            } elseif (isset($response['messages']['message'][0]['text'])) {
                // Try to extract from the response message
                $message = $response['messages']['message'][0]['text'];
                if (preg_match('/ID: (\d+)/', $message, $matches)) {
                    return $matches[1];
                }
            }

            throw new \Exception("Could not extract payment profile ID from response");
        } catch (\Exception $e) {
            // Check for duplicate payment profile error
            if (strpos($e->getMessage(), 'E00039') !== false) {
                Log::info("Duplicate ACH payment profile detected for user: {$user->id}");

                // Extract the profile ID from the error message
                if (preg_match('/ID: (\d+)/', $e->getMessage(), $matches)) {
                    return $matches[1];
                }
            }

            // Check for profile not found error
            if (strpos($e->getMessage(), 'E00040') !== false) {
                Log::warning("Customer profile {$customerProfileId} not found in Authorize.net. This should have been handled earlier.");

                // Try to verify if the customer profile exists
                try {
                    $this->api->sendRequest('getCustomerProfileRequest', [
                        'customerProfileId' => $customerProfileId
                    ]);

                    // If we get here, the profile exists but there was some other issue
                    Log::error("Customer profile exists but ACH payment profile creation failed: {$e->getMessage()}");
                } catch (\Exception $verifyError) {
                    // If the profile doesn't exist, log it
                    if (strpos($verifyError->getMessage(), 'E00040') !== false) {
                        Log::error("Customer profile {$customerProfileId} confirmed not to exist in Authorize.net.");
                    }
                }
            }

            throw $e;
        }
    }
}
