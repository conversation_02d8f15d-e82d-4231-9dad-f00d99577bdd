# Authorize.net API Integration

This directory contains a JSON-based API integration for Authorize.net payment gateway. The implementation uses direct JSON requests and responses instead of relying on the Authorize.net SDK.

## Overview

The integration is organized into several service classes:

1. `AuthorizeNetApi.php` - Core API communication class that handles sending requests to Authorize.net and processing responses
2. `CustomerProfileService.php` - Manages customer profiles (create, get, update, delete)
3. `PaymentProfileService.php` - Manages payment profiles/credit cards (create, get, update, delete, validate)
4. `TransactionService.php` - Handles payment transactions (process, refund, void, get details)
5. `AuthorizeNetService.php` - Facade class that provides a simplified interface for common operations

## Configuration

The integration uses the following configuration values from `config/services.php`:

```php
// config/services.php
return [
    // ...
    'authorize_net' => [
        'login_id' => env('AUTHORIZE_NET_LOGIN_ID'),
        'transaction_key' => env('AUTHORIZE_NET_TRANSACTION_KEY'),
        'sandbox' => env('AUTHORIZE_NET_SANDBOX', true),
    ],
    // ...
];
```

Make sure to add these values to your `.env` file:

```
AUTHORIZE_NET_LOGIN_ID=your_login_id
AUTHORIZE_NET_TRANSACTION_KEY=your_transaction_key
AUTHORIZE_NET_SANDBOX=true  # Set to false for production
```

## Usage Examples

### Customer Profile Management

```php
use App\Services\Api\AuthorizeNetService;

// Create a new instance of the service
$authorizeNet = new AuthorizeNetService();

// Get or create a customer profile
$profileResult = $authorizeNet->getOrCreateCustomerProfile($user);

if ($profileResult['success']) {
    $customerProfileId = $profileResult['profile_id'];
    // Store or use the customer profile ID
} else {
    // Handle error
    $errorMessage = $profileResult['message'];
}
```

### Adding a Credit Card

```php
use App\Services\Api\AuthorizeNetService;

// Create a new instance of the service
$authorizeNet = new AuthorizeNetService();

// Add a credit card
$cardResult = $authorizeNet->addCreditCard(
    $user,
    '****************', // Card number
    '12',               // Expiration month
    '2025',             // Expiration year
    '123'               // CVV
);

if ($cardResult['success']) {
    // Card added successfully
    $paymentProfileId = $cardResult['payment_profile_id'];
    $lastFour = $cardResult['last_four'];
    $brand = $cardResult['brand'];
    
    // Store the payment profile ID and card details in your database
} else {
    // Handle error
    $errorMessage = $cardResult['message'];
}
```

### Processing a Transaction

```php
use App\Services\Api\AuthorizeNetService;

// Create a new instance of the service
$authorizeNet = new AuthorizeNetService();

// Process a transaction
$transactionResult = $authorizeNet->processTransaction(
    99.99,                  // Amount
    $customerProfileId,     // Customer profile ID
    $paymentProfileId,      // Payment profile ID
    'Monthly subscription'  // Description
);

if ($transactionResult['success']) {
    // Transaction processed successfully
    $transactionId = $transactionResult['transaction_id'];
    $authCode = $transactionResult['auth_code'];
    
    // Store the transaction details in your database
} else {
    // Handle error
    $errorMessage = $transactionResult['message'];
}
```

### Refunding a Transaction

```php
use App\Services\Api\AuthorizeNetService;

// Create a new instance of the service
$authorizeNet = new AuthorizeNetService();

// Refund a transaction
$refundResult = $authorizeNet->refundTransaction(
    $transactionId,     // Original transaction ID
    99.99,              // Amount to refund
    '1111',             // Last 4 digits of card
    '2025-12'           // Expiration date (YYYY-MM)
);

if ($refundResult['success']) {
    // Refund processed successfully
    $refundTransactionId = $refundResult['transaction_id'];
    
    // Store the refund details in your database
} else {
    // Handle error
    $errorMessage = $refundResult['message'];
}
```

### Voiding a Transaction

```php
use App\Services\Api\AuthorizeNetService;

// Create a new instance of the service
$authorizeNet = new AuthorizeNetService();

// Void a transaction
$voidResult = $authorizeNet->voidTransaction($transactionId);

if ($voidResult['success']) {
    // Transaction voided successfully
    $voidTransactionId = $voidResult['transaction_id'];
    
    // Update the transaction status in your database
} else {
    // Handle error
    $errorMessage = $voidResult['message'];
}
```

## Advanced Usage

For more advanced use cases, you can use the individual service classes directly:

```php
use App\Services\Api\AuthorizeNetApi;
use App\Services\Api\CustomerProfileService;
use App\Services\Api\PaymentProfileService;
use App\Services\Api\TransactionService;

// Create the API instance
$api = new AuthorizeNetApi();

// Create service instances
$customerProfileService = new CustomerProfileService($api);
$paymentProfileService = new PaymentProfileService($api);
$transactionService = new TransactionService($api);

// Use the services directly
$profileId = $customerProfileService->createCustomerProfile($user);
$paymentProfileId = $paymentProfileService->createPaymentProfile($profileId, $cardNumber, $expirationMonth, $expirationYear, $cvv, $user);
$transactionResult = $transactionService->processProfileTransaction($amount, $profileId, $paymentProfileId);
```

## Error Handling

All methods return an array with a `success` key that indicates whether the operation was successful. If `success` is `false`, the array will also contain `message` and `error` keys with details about the error.

```php
$result = $authorizeNet->processTransaction($amount, $customerProfileId, $paymentProfileId);

if (!$result['success']) {
    // Log the error
    Log::error('Transaction failed: ' . $result['error']);
    
    // Display a user-friendly message
    return back()->with('error', $result['message']);
}
```

## Logging

All API requests and responses are logged for debugging purposes. You can find these logs in your Laravel log files.