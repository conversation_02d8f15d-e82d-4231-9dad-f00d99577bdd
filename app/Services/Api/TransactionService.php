<?php

namespace App\Services\Api;

use App\Models\Transaction;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class TransactionService
{
    /**
     * @var AuthorizeNetApi
     */
    protected $api;

    /**
     * Transaction types
     */
    const TRANSACTION_TYPE_AUTH_ONLY = 'authOnlyTransaction';
    const TRANSACTION_TYPE_AUTH_CAPTURE = 'authCaptureTransaction';
    const TRANSACTION_TYPE_CAPTURE_ONLY = 'captureOnlyTransaction';
    const TRANSACTION_TYPE_REFUND = 'refundTransaction';
    const TRANSACTION_TYPE_VOID = 'voidTransaction';

    /**
     * Constructor
     */
    public function __construct(AuthorizeNetApi $api)
    {
        $this->api = $api;
    }

    /**
     * Process a transaction using a customer profile
     *
     * @param float $amount
     * @param string $customerProfileId
     * @param string $paymentProfileId
     * @param string $transactionType
     * @param string $description
     * @param int|null $userId
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @param string $currency
     * @return array Transaction details
     * @throws \Exception
     */
    public function processProfileTransaction(
        float $amount,
        string $customerProfileId,
        string $paymentProfileId,
        string $transactionType = self::TRANSACTION_TYPE_AUTH_ONLY,
        string $description = '',
        int $userId = null,
        int $subscriptionId = null,
        bool $isDiscounted = false,
        string $currency = 'USD'
    ): array {
        $response = $this->api->sendRequest('createTransactionRequest', [
            'refId' => 'ref' . time(),
            'transactionRequest' => [
                'transactionType' => $transactionType,
                'amount' => number_format($amount, 2, '.', ''),
                'profile' => [
                    'customerProfileId' => $customerProfileId,
                    'paymentProfile' => [
                        'paymentProfileId' => $paymentProfileId
                    ]
                ],
                'order' => [
                    'description' => $description
                ]
            ]
        ]);

        // Extract transaction details from the response
        if (isset($response['transactionResponse'])) {
            $transactionResponse = $response['transactionResponse'];

            if (isset($transactionResponse['responseCode']) && $transactionResponse['responseCode'] == "1") {
                $result = [
                    'success' => true,
                    'transaction_id' => $transactionResponse['transId'] ?? null,
                    'auth_code' => $transactionResponse['authCode'] ?? null,
                    'response_code' => $transactionResponse['responseCode'],
                    'message' => $transactionResponse['messages'][0]['description'] ?? 'Transaction successful',
                    'raw_response' => $transactionResponse
                ];

                // Record the transaction in the database
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    $amount,
                    $isDiscounted,
                    $currency,
                    $result['transaction_id'],
                    'credit_card',
                    'success',
                    null
                );

                return $result;
            } else {
                $errorText = "Unknown error";
                if (isset($transactionResponse['errors']) &&
                    isset($transactionResponse['errors'][0]) &&
                    isset($transactionResponse['errors'][0]['errorText'])) {
                    $errorText = $transactionResponse['errors'][0]['errorText'];
                }

                // Record the failed transaction
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    $amount,
                    $isDiscounted,
                    $currency,
                    null,
                    'credit_card',
                    'failed',
                    $errorText
                );

                throw new \Exception("Transaction failed: " . $errorText);
            }
        }

        throw new \Exception("Invalid transaction response format");
    }

    /**
     * Process a transaction using a credit card
     *
     * @param float $amount
     * @param string $cardNumber
     * @param string $expirationMonth
     * @param string $expirationYear
     * @param string $cvv
     * @param array $billingInfo
     * @param string $transactionType
     * @param string $description
     * @param int|null $userId
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @param string $currency
     * @return array Transaction details
     * @throws \Exception
     */
    public function processCreditCardTransaction(
        float $amount,
        string $cardNumber,
        string $expirationMonth,
        string $expirationYear,
        string $cvv,
        array $billingInfo = [],
        string $transactionType = self::TRANSACTION_TYPE_AUTH_ONLY,
        string $description = '',
        int $userId = null,
        int $subscriptionId = null,
        bool $isDiscounted = false,
        string $currency = 'USD'
    ): array {
        // Format expiration date as YYYY-MM
        // Make sure the year is 4 digits (YYYY)
        $expirationYear = strlen($expirationYear) == 2 ? '20' . $expirationYear : $expirationYear;
        $expirationDate = $expirationYear . '-' . str_pad($expirationMonth, 2, '0', STR_PAD_LEFT);

        $transactionRequest = [
            'transactionType' => $transactionType,
            'amount' => number_format($amount, 2, '.', ''),
            'payment' => [
                'creditCard' => [
                    'cardNumber' => $cardNumber,
                    'expirationDate' => $expirationDate,
                    'cardCode' => $cvv
                ]
            ],
            'order' => [
                'description' => $description
            ]
        ];

        // Add billing information if provided
        if (!empty($billingInfo)) {
            $transactionRequest['billTo'] = $billingInfo;
        }

        $response = $this->api->sendRequest('createTransactionRequest', [
            'refId' => 'ref' . time(),
            'transactionRequest' => $transactionRequest
        ]);

        // Extract transaction details from the response
        if (isset($response['transactionResponse'])) {
            $transactionResponse = $response['transactionResponse'];

            if (isset($transactionResponse['responseCode']) && $transactionResponse['responseCode'] == "1") {
                $result = [
                    'success' => true,
                    'transaction_id' => $transactionResponse['transId'] ?? null,
                    'auth_code' => $transactionResponse['authCode'] ?? null,
                    'response_code' => $transactionResponse['responseCode'],
                    'message' => $transactionResponse['messages'][0]['description'] ?? 'Transaction successful',
                    'raw_response' => $transactionResponse
                ];

                // Record the transaction in the database
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    $amount,
                    $isDiscounted,
                    $currency,
                    $result['transaction_id'],
                    'credit_card',
                    'success',
                    null
                );

                return $result;
            } else {
                $errorText = "Unknown error";
                if (isset($transactionResponse['errors']) &&
                    isset($transactionResponse['errors'][0]) &&
                    isset($transactionResponse['errors'][0]['errorText'])) {
                    $errorText = $transactionResponse['errors'][0]['errorText'];
                }

                // Record the failed transaction
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    $amount,
                    $isDiscounted,
                    $currency,
                    null,
                    'credit_card',
                    'failed',
                    $errorText
                );

                throw new \Exception("Transaction failed: " . $errorText);
            }
        }

        throw new \Exception("Invalid transaction response format");
    }

    /**
     * Refund a transaction
     *
     * @param string $transactionId
     * @param float $amount
     * @param string $cardNumber
     * @param string $expirationDate
     * @param int|null $userId
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @param string $currency
     * @return array Refund details
     * @throws \Exception
     */
    public function refundTransaction(
        string $transactionId,
        float $amount,
        string $cardNumber,
        string $expirationDate,
        int $userId = null,
        int $subscriptionId = null,
        bool $isDiscounted = false,
        string $currency = 'USD'
    ): array {
        $response = $this->api->sendRequest('createTransactionRequest', [
            'refId' => 'ref' . time(),
            'transactionRequest' => [
                'transactionType' => self::TRANSACTION_TYPE_REFUND,
                'amount' => number_format($amount, 2, '.', ''),
                'payment' => [
                    'creditCard' => [
                        'cardNumber' => $cardNumber,
                        'expirationDate' => $expirationDate
                    ]
                ],
                'refTransId' => $transactionId
            ]
        ]);

        // Extract transaction details from the response
        if (isset($response['transactionResponse'])) {
            $transactionResponse = $response['transactionResponse'];

            if (isset($transactionResponse['responseCode']) && $transactionResponse['responseCode'] == "1") {
                $result = [
                    'success' => true,
                    'transaction_id' => $transactionResponse['transId'] ?? null,
                    'auth_code' => $transactionResponse['authCode'] ?? null,
                    'response_code' => $transactionResponse['responseCode'],
                    'message' => $transactionResponse['messages'][0]['description'] ?? 'Refund successful',
                    'raw_response' => $transactionResponse
                ];

                // Record the refund transaction in the database
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    -$amount, // Negative amount for refunds
                    $isDiscounted,
                    $currency,
                    $result['transaction_id'],
                    'credit_card',
                    'refunded',
                    null
                );

                return $result;
            } else {
                $errorText = "Unknown error";
                if (isset($transactionResponse['errors']) &&
                    isset($transactionResponse['errors'][0]) &&
                    isset($transactionResponse['errors'][0]['errorText'])) {
                    $errorText = $transactionResponse['errors'][0]['errorText'];
                }

                // Record the failed refund transaction
                $this->recordTransaction(
                    $userId ?? Auth::id(),
                    $subscriptionId,
                    -$amount, // Negative amount for refunds
                    $isDiscounted,
                    $currency,
                    null,
                    'credit_card',
                    'failed',
                    $errorText
                );

                throw new \Exception("Refund failed: " . $errorText);
            }
        }

        throw new \Exception("Invalid refund response format");
    }

    /**
     * Void a transaction
     *
     * @param string $transactionId
     * @param int|null $userId
     * @param int|null $subscriptionId
     * @param float|null $amount
     * @param bool $isDiscounted
     * @param string $currency
     * @return array Void details
     * @throws \Exception
     */
    public function voidTransaction(
        string $transactionId,
        int $userId = null,
        int $subscriptionId = null,
        float $amount = null,
        bool $isDiscounted = false,
        string $currency = 'USD'
    ): array {
        $response = $this->api->sendRequest('createTransactionRequest', [
            'refId' => 'ref' . time(),
            'transactionRequest' => [
                'transactionType' => self::TRANSACTION_TYPE_VOID,
                'refTransId' => $transactionId
            ]
        ]);

        // Extract transaction details from the response
        if (isset($response['transactionResponse'])) {
            $transactionResponse = $response['transactionResponse'];

            if (isset($transactionResponse['responseCode']) && $transactionResponse['responseCode'] == "1") {
                $result = [
                    'success' => true,
                    'transaction_id' => $transactionResponse['transId'] ?? null,
                    'auth_code' => $transactionResponse['authCode'] ?? null,
                    'response_code' => $transactionResponse['responseCode'],
                    'message' => $transactionResponse['messages'][0]['description'] ?? 'Void successful',
                    'raw_response' => $transactionResponse
                ];

                // Record the void transaction in the database
                if ($amount !== null) {
                    $this->recordTransaction(
                        $userId ?? Auth::id(),
                        $subscriptionId,
                        0, // Amount is 0 for voids
                        $isDiscounted,
                        $currency,
                        $result['transaction_id'],
                        'credit_card',
                        'voided',
                        null
                    );
                }

                return $result;
            } else {
                $errorText = "Unknown error";
                if (isset($transactionResponse['errors']) &&
                    isset($transactionResponse['errors'][0]) &&
                    isset($transactionResponse['errors'][0]['errorText'])) {
                    $errorText = $transactionResponse['errors'][0]['errorText'];
                }

                // Record the failed void transaction
                if ($amount !== null) {
                    $this->recordTransaction(
                        $userId ?? Auth::id(),
                        $subscriptionId,
                        0, // Amount is 0 for voids
                        $isDiscounted,
                        $currency,
                        null,
                        'credit_card',
                        'failed',
                        $errorText
                    );
                }

                throw new \Exception("Void failed: " . $errorText);
            }
        }

        throw new \Exception("Invalid void response format");
    }

    /**
     * Get transaction details
     *
     * @param string $transactionId
     * @return array Transaction details
     * @throws \Exception
     */
    public function getTransactionDetails(string $transactionId): array
    {
        $response = $this->api->sendRequest('getTransactionDetailsRequest', [
            'transId' => $transactionId
        ]);

        if (isset($response['transaction'])) {
            return $response['transaction'];
        }

        throw new \Exception("Could not retrieve transaction details");
    }

    /**
     * Record a transaction in the database
     *
     * @param int $userId
     * @param int|null $subscriptionId
     * @param float $amount
     * @param bool $isDiscounted
     * @param string $currency
     * @param string|null $transactionId
     * @param string $paymentMethod
     * @param string $status
     * @param string|null $errorMessage
     * @return Transaction
     */
    protected function recordTransaction(
        int $userId,
        ?int $subscriptionId,
        float $amount,
        bool $isDiscounted = false,
        string $currency = 'USD',
        ?string $transactionId = null,
        string $paymentMethod = 'credit_card',
        string $status = 'success',
        ?string $errorMessage = null
    ): Transaction {
        try {
            // Check if a transaction with this ID already exists
            if ($transactionId) {
                $existingTransaction = Transaction::where('transaction_id', $transactionId)->first();

                if ($existingTransaction) {
                    // Update the existing transaction instead of creating a new one
                    $data = [
                        'status' => $status,
                        'error_message' => $errorMessage,
                    ];

                    // Only update subscription_id if it's provided and not already set
                    if ($subscriptionId !== null && $existingTransaction->subscription_id === null) {
                        $data['subscription_id'] = $subscriptionId;
                    }

                    $existingTransaction->update($data);
                    Log::info("Updated existing transaction record: {$transactionId}");
                    return $existingTransaction;
                }
            }

            // Create a new transaction record
            $data = [
                'user_id' => $userId,
                'amount' => $amount,
                'is_discounted' => $isDiscounted,
                'currency' => $currency,
                'transaction_id' => $transactionId,
                'payment_method' => $paymentMethod,
                'status' => $status,
                'error_message' => $errorMessage,
            ];

            // Only add subscription_id if it's not null
            if ($subscriptionId !== null) {
                $data['subscription_id'] = $subscriptionId;
            }

            $transaction = Transaction::create($data);
            Log::info("Created new transaction record: " . ($transactionId ?? 'no_id'));
            return $transaction;
        } catch (\Exception $e) {
            Log::error("Error recording transaction: {$e->getMessage()}", [
                'user_id' => $userId,
                'transaction_id' => $transactionId,
                'error' => $e->getMessage()
            ]);

            // If we can't create a transaction record, try to find an existing one
            if ($transactionId) {
                $existingTransaction = Transaction::where('transaction_id', $transactionId)->first();
                if ($existingTransaction) {
                    return $existingTransaction;
                }
            }

            // Generate a unique transaction ID if needed
            $uniqueTransactionId = $transactionId ?? ('manual_' . time() . '_' . rand(1000, 9999));

            // Create a minimal transaction record as a last resort
            return Transaction::create([
                'user_id' => $userId,
                'amount' => $amount,
                'currency' => $currency,
                'transaction_id' => $uniqueTransactionId,
                'payment_method' => $paymentMethod,
                'status' => $status,
                'error_message' => $errorMessage ?? $e->getMessage(),
            ]);
        }
    }
}
