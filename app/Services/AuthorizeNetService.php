<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;
use Illuminate\Support\Facades\Log;

class AuthorizeNetService
{
    private $merchantAuthentication;

    public function __construct()
    {
        $this->merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
        $this->merchantAuthentication->setName(config('services.authorize.login_id'));
        $this->merchantAuthentication->setTransactionKey(config('services.authorize.transaction_key'));
    }

    public function chargeCreditCard($amount, User $user, $subscriptionId = null)
    {
        $transaction = new Transaction([
            'user_id' => $user->id,
            'subscription_id' => $subscriptionId,
            'amount' => $amount,
            'payment_method' => 'credit_card',
            'status' => 'pending',
        ]);

        try {
            $defaultCard = $user->defaultCreditCard();

            if (!$defaultCard) {
                throw new \Exception('No default credit card found.');
            }

            if (empty($defaultCard->token)) {
                throw new \Exception('Invalid payment profile ID for the default credit card.');
            }

            // Get or create customer profile
            $profileId = $this->getOrCreateCustomerProfile($user);

            Log::info("Attempting to charge card for user: {$user->id}, Amount: {$amount}, Token: {$defaultCard->token}, Profile ID: {$profileId}");

            $transactionRequest = new AnetAPI\TransactionRequestType();
            $transactionRequest->setTransactionType("authOnlyTransaction");
            $transactionRequest->setAmount($amount);

            $paymentProfile = new AnetAPI\PaymentProfileType();
            $paymentProfile->setPaymentProfileId($defaultCard->token);

            $transactionRequest->setProfile(new AnetAPI\CustomerProfilePaymentType());
            $transactionRequest->getProfile()->setCustomerProfileId($profileId);
            $transactionRequest->getProfile()->setPaymentProfile($paymentProfile);

            $request = new AnetAPI\CreateTransactionRequest();
            $request->setMerchantAuthentication($this->merchantAuthentication);
            $request->setRefId("ref" . time());
            $request->setTransactionRequest($transactionRequest);

            $controller = new AnetController\CreateTransactionController($request);
            $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::PRODUCTION);

            if ($response != null && $response->getMessages()->getResultCode() == "Ok") {
                $tresponse = $response->getTransactionResponse();
                if ($tresponse != null && $tresponse->getResponseCode() == "1") {
                    $transaction->status = 'success';
                    $transaction->transaction_id = $tresponse->getTransId();
                    $transaction->save();

                    Log::info("Transaction successful for user: {$user->id}, Transaction ID: {$tresponse->getTransId()}");

                    return [
                        'success' => true,
                        'transaction_id' => $tresponse->getTransId()
                    ];
                } else {
                    $this->logTransactionError($tresponse);
                    throw new \Exception("Transaction failed: " . $tresponse->getErrors()[0]->getErrorText());
                }
            } else {
                $this->logError($response);
                throw new \Exception("Failed to process transaction.");
            }
        } catch (\Exception $e) {
            Log::error('Error in chargeCreditCard: ' . $e->getMessage());
            
            $transaction->status = 'failed';
            $transaction->error_message = $e->getMessage();
            $transaction->save();

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function getOrCreateCustomerProfile(User $user)
    {
        if ($user->authorize_net_customer_id) {
            Log::info("Using existing Authorize.net customer profile for user: {$user->id}");
            return $user->authorize_net_customer_id;
        }

        Log::info("No existing Authorize.net customer profile found for user: {$user->id}. Creating new profile.");
        
        try {
            $profileId = $this->createCustomerProfile($user);
            $user->authorize_net_customer_id = $profileId;
            $user->save();
            return $profileId;
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'E00039') !== false) {
                Log::info("Duplicate profile detected. Attempting to retrieve existing profile.");
                $profileId = $this->getExistingCustomerProfile($user);
                $user->authorize_net_customer_id = $profileId;
                $user->save();
                return $profileId;
            }
            throw $e;
        }
    }

    private function getExistingCustomerProfile(User $user)
    {
        $request = new AnetAPI\GetCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setEmail($user->email);

        $controller = new AnetController\GetCustomerProfileController($request);
        $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::PRODUCTION);

        if ($response != null && $response->getMessages()->getResultCode() == "Ok") {
            $profileIds = $response->getProfile();
            if (!empty($profileIds)) {
                return $profileIds[0]->getCustomerProfileId();
            }
        }

        throw new \Exception("Could not retrieve existing customer profile.");
    }

    private function createCustomerProfile(User $user)
    {
        $customerProfile = new AnetAPI\CustomerProfileType();
        $customerProfile->setDescription("Customer Profile for " . $user->name);
        $customerProfile->setMerchantCustomerId("M_" . $user->id);
        $customerProfile->setEmail($user->email);

        // Create customer address
        $customerAddress = new AnetAPI\CustomerAddressType();
        $customerAddress->setFirstName($user->fname);
        $customerAddress->setLastName($user->lname);
        // $customerAddress->setCompany($user->company ?? '');
        $customerAddress->setAddress($user->address1);
        $customerAddress->setCity($user->city);
        $customerAddress->setState($user->state);
        $customerAddress->setZip($user->zip);
        // $customerAddress->setCountry($user->country);
        $customerAddress->setPhoneNumber($user->phone);
        // $customerAddress->setFaxNumber($user->fax ?? '');

        // Create customer payment profile
        $paymentProfile = new AnetAPI\CustomerPaymentProfileType();
        $paymentProfile->setCustomerType('individual');
        $paymentProfile->setBillTo($customerAddress);

        // Add payment profile to customer profile
        $customerProfile->setPaymentProfiles([$paymentProfile]);

        $request = new AnetAPI\CreateCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setProfile($customerProfile);

        $controller = new AnetController\CreateCustomerProfileController($request);
        $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::PRODUCTION);

        if ($response != null && $response->getMessages()->getResultCode() == "Ok") {
            return $response->getCustomerProfileId();
        } else {
            $this->logError($response);
            throw new \Exception("Failed to create customer profile: " . $response->getMessages()->getMessage()[0]->getText());
        }
    }

    private function logError($response)
    {
        $errorMessages = $response->getMessages()->getMessage();
        $errorCode = $errorMessages[0]->getCode();
        $errorText = $errorMessages[0]->getText();
        
        Log::error("Authorize.net Error: Code: $errorCode, Message: $errorText");
    }

    private function logTransactionError($tresponse)
    {
        $errors = $tresponse->getErrors();
        $errorCode = $errors[0]->getErrorCode();
        $errorText = $errors[0]->getErrorText();
        
        Log::error("Authorize.net Transaction Error: Code: $errorCode, Message: $errorText");
    }
}