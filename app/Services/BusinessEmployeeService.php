<?php

namespace App\Services;

use App\Models\Business;
use App\Models\BusinessEmployee;
use App\Models\BusinessPlan;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BusinessEmployeeService
{
    /**
     * Enroll an employee to a business plan
     *
     * @param Business $business
     * @param array $employeeData
     * @return array
     */
    public function enrollEmployee(Business $business, array $employeeData)
    {
        // 1. Find active business plan
        $businessPlan = $business->plans()
            ->where('active', true)
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>=', now());
            })
            ->first();

        if (!$businessPlan) {
            return [
                'success' => false,
                'message' => 'No active business plan found'
            ];
        }

        // 2. Check if business has available seats
        $activeEmployeesCount = $business->employees()
            ->where('status', 'active')
            ->count();

        // If no available seats and trying to add as active, return error
        // But allow adding with pending status
        $requestedStatus = $employeeData['status'] ?? 'active';
        if ($activeEmployeesCount >= $businessPlan->plan_quantity && $requestedStatus === 'active') {
            return [
                'success' => false,
                'message' => 'All available seats are used. Please upgrade your plan or add employees with pending status.'
            ];
        }

        // 3. Create or find user account
        $user = User::firstOrCreate(
            ['email' => $employeeData['email']],
            [
                'name' => $employeeData['first_name'] . ' ' . $employeeData['last_name'],
                'password' => Hash::make(Str::random(12)), // Random password
                'business_id' => $business->id,
                'fname' => $employeeData['first_name'],
                'lname' => $employeeData['last_name'],
                'phone' => $employeeData['phone'] ?? null,
                'status' => 'active',
            ]
        );

        // Assign patient role
        $user->assignRole('patient');

        // 4. Create business employee record
        $employee = BusinessEmployee::create([
            'business_id' => $business->id,
            'user_id' => $user->id,
            'email' => $employeeData['email'],
            'first_name' => $employeeData['first_name'],
            'last_name' => $employeeData['last_name'],
            'phone' => $employeeData['phone'] ?? null,
            'status' => $employeeData['status'] ?? 'active',
        ]);

        // 5. Create subscription record
        // Find appropriate subscription plan (could be a specific business employee plan)
        $subscriptionPlan = SubscriptionPlan::where('group_id', 4) // Business Plans group
            ->where('is_active', true)
            ->first();

        if (!$subscriptionPlan) {
            // Fallback to premium plans if no specific business plan exists
            $subscriptionPlan = SubscriptionPlan::premium()
                ->where('is_active', true)
                ->first();
        }

        // Set subscription status based on employee status
        $subscriptionStatus = $employee->status === 'active' ? 'active' : 'pending_activation';

        $subscription = $user->subscriptions()->create([
            'plan_id' => $subscriptionPlan->id,
            'starts_at' => now(),
            'ends_at' => $businessPlan->ends_at,
            'status' => $subscriptionStatus,
            'user_type' => 'business_employee',
            'is_primary_account' => true, // Allow employees to have dependents
            'family_role' => 'primary',
            'meta_data' => [
                'business_plan_id' => $businessPlan->id,
                'business_id' => $business->id,
                'employee_id' => $employee->id
            ]
        ]);

        return [
            'success' => true,
            'user' => $user,
            'employee' => $employee,
            'subscription' => $subscription
        ];
    }

    /**
     * Update employee subscriptions when business plan changes
     *
     * @param Business $business
     * @param BusinessPlan $newBusinessPlan
     * @return array
     */
    public function updateEmployeeSubscriptions(Business $business, BusinessPlan $newBusinessPlan)
    {
        // Get all active employees
        $activeEmployees = $business->employees()
            ->where('status', 'active')
            ->whereNotNull('user_id')
            ->get();

        $updatedCount = 0;

        foreach ($activeEmployees as $employee) {
            // Find current subscription
            $subscription = Subscription::where('user_id', $employee->user_id)
                ->where('user_type', 'business_employee')
                ->where('status', 'active')
                ->first();

            if ($subscription) {
                // Update subscription end date to match new business plan
                $subscription->update([
                    'ends_at' => $newBusinessPlan->ends_at,
                    'meta_data' => array_merge($subscription->meta_data ?? [], [
                        'business_plan_id' => $newBusinessPlan->id
                    ])
                ]);
                $updatedCount++;
            } else {
                // Create new subscription if none exists
                $subscriptionPlan = SubscriptionPlan::where('group_id', 4) // Business Plans group
                    ->where('is_active', true)
                    ->first();

                if (!$subscriptionPlan) {
                    // Fallback to premium plans if no specific business plan exists
                    $subscriptionPlan = SubscriptionPlan::premium()
                        ->where('is_active', true)
                        ->first();
                }

                $user = User::find($employee->user_id);
                if ($user) {
                    $user->subscriptions()->create([
                        'plan_id' => $subscriptionPlan->id,
                        'starts_at' => now(),
                        'ends_at' => $newBusinessPlan->ends_at,
                        'status' => 'active',
                        'user_type' => 'business_employee',
                        'is_primary_account' => true, // Allow employees to have dependents
                        'family_role' => 'primary',
                        'meta_data' => [
                            'business_plan_id' => $newBusinessPlan->id,
                            'business_id' => $business->id,
                            'employee_id' => $employee->id
                        ]
                    ]);
                    $updatedCount++;
                }
            }
        }

        return [
            'success' => true,
            'updated_count' => $updatedCount
        ];
    }

    /**
     * Handle employee termination
     *
     * @param BusinessEmployee $employee
     * @param string|null $reason
     * @return array
     */
    public function terminateEmployee(BusinessEmployee $employee, $reason = null)
    {
        // 1. Update employee status
        $employee->update([
            'status' => 'terminated',
            'terminated_at' => now(),
            'termination_reason' => $reason
        ]);

        // 2. Find and cancel subscription
        if ($employee->user_id) {
            $subscription = Subscription::where('user_id', $employee->user_id)
                ->where('user_type', 'business_employee')
                ->where('status', 'active')
                ->first();

            if ($subscription) {
                $subscription->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now()
                ]);
            }
        }

        return [
            'success' => true,
            'employee' => $employee
        ];
    }
}
