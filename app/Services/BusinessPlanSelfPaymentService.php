<?php

namespace App\Services;

use App\Models\BusinessEmployee;
use App\Models\BusinessPlan;
use App\Models\BusinessPlanSelfPayment;
use App\Models\BusinessPlanAuditLog;
use App\Models\Transaction;
use App\Services\Api\AuthorizeNetApi;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BusinessPlanSelfPaymentService
{
    public function __construct(
        protected AuthorizeNetApi $authorizeNetApi
    ) {}

    /**
     * Validate if an employee can make a self-payment.
     */
    public function validateSelfPayment(BusinessEmployee $employee): array
    {
        // Check if employee can make self-payment
        if (!$employee->canMakeSelfPayment()) {
            return [
                'success' => false,
                'message' => 'Employee is not eligible for self-payment. Employee must be active and have a user account.'
            ];
        }

        // Get the active business plan
        $activePlan = $employee->business->plans()
            ->where('active', true)
            ->where(function ($query) {
                $query->whereNull('ends_at')
                    ->orWhere('ends_at', '>=', now());
            })
            ->first();

        if (!$activePlan) {
            return [
                'success' => false,
                'message' => 'No active business plan found for your company.'
            ];
        }

        // Check if employee can self-pay for this plan with detailed eligibility info
        $eligibilityInfo = $activePlan->getEmployeeSelfPayEligibility($employee);
        if (!$eligibilityInfo['eligible']) {
            return [
                'success' => false,
                'message' => $eligibilityInfo['message'],
                'reason' => $eligibilityInfo['reason'],
                'existing_payment' => $eligibilityInfo['existing_payment'] ?? null,
            ];
        }

        return [
            'success' => true,
            'business_plan' => $activePlan,
            'amount' => $activePlan->price_per_plan, // Amount in cents
        ];
    }

    /**
     * Create a pending self-payment record.
     */
    public function createPendingSelfPayment(
        BusinessEmployee $employee,
        BusinessPlan $businessPlan,
        array $paymentData
    ): BusinessPlanSelfPayment {
        // Clean up any stale pending payments first
        $this->cleanupStalePendingPayments($employee, $businessPlan);

        return BusinessPlanSelfPayment::create([
            'business_plan_id' => $businessPlan->id,
            'business_employee_id' => $employee->id,
            'user_id' => $employee->user_id,
            'amount_paid' => $businessPlan->price_per_plan,
            'payment_method' => $paymentData['payment_method'] ?? 'credit_card',
            'status' => BusinessPlanSelfPayment::STATUS_PENDING,
            'meta_data' => [
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'payment_data' => $paymentData,
            ],
        ]);
    }

    /**
     * Process the self-payment using AuthorizeNet.
     */
    public function processPayment(
        BusinessPlanSelfPayment $selfPayment,
        array $paymentData
    ): array {
        try {
            DB::beginTransaction();

            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $selfPayment->user_id,
                'amount' => $selfPayment->amount_paid / 100, // Convert to dollars
                'payment_method' => $selfPayment->payment_method,
                'status' => 'pending',
                'meta_data' => [
                    'business_plan_self_payment_id' => $selfPayment->id,
                    'business_plan_id' => $selfPayment->business_plan_id,
                    'business_employee_id' => $selfPayment->business_employee_id,
                ],
            ]);

            // Update self-payment with transaction ID
            $selfPayment->update(['transaction_id' => $transaction->id]);

            // Process payment through AuthorizeNet
            $paymentResult = $this->authorizeNetApi->sendRequest('createTransactionRequest', [
                'transactionRequest' => [
                    'transactionType' => 'authCaptureTransaction',
                    'amount' => number_format($selfPayment->amount_paid / 100, 2, '.', ''),
                    'payment' => [
                        'creditCard' => [
                            'cardNumber' => $paymentData['card_number'],
                            'expirationDate' => $paymentData['expiration_month'] . '/' . $paymentData['expiration_year'],
                            'cardCode' => $paymentData['cvv'],
                        ]
                    ],
                    'order' => [
                        'invoiceNumber' => 'SELF-PAY-' . $selfPayment->id,
                        'description' => 'Business Plan Self-Payment - $20.00',
                    ],
                    'billTo' => [
                        'firstName' => $selfPayment->businessEmployee->first_name,
                        'lastName' => $selfPayment->businessEmployee->last_name,
                        'address' => $selfPayment->businessEmployee->address1,
                        'city' => $selfPayment->businessEmployee->city,
                        'state' => $selfPayment->businessEmployee->state,
                        'zip' => $selfPayment->businessEmployee->zip,
                    ],
                ]
            ]);

            if (isset($paymentResult['transactionResponse']['responseCode']) &&
                $paymentResult['transactionResponse']['responseCode'] == '1') {

                // Payment successful
                $transactionId = $paymentResult['transactionResponse']['transId'];

                // Update transaction
                $transaction->update([
                    'status' => 'completed',
                    'transaction_id' => $transactionId,
                ]);

                // Mark self-payment as completed
                $selfPayment->markAsCompleted($transactionId);

                // Update business plan (decrease quantity and total price only)
                $planChanges = $selfPayment->businessPlan->decreaseAfterSelfPayment();

                // Convert employee's subscription to individual coverage with extended end date
                $subscriptionChanges = $this->convertToIndividualSubscription($selfPayment->businessEmployee, $selfPayment->businessPlan);

                // Create audit logs
                BusinessPlanAuditLog::logSelfPaymentCompleted(
                    $selfPayment->businessPlan,
                    $selfPayment->businessEmployee,
                    $selfPayment,
                    [
                        'plan_quantity' => $planChanges['old_quantity'],
                        'total_price' => $planChanges['old_total_price'],
                    ],
                    [
                        'plan_quantity' => $planChanges['new_quantity'],
                        'total_price' => $planChanges['new_total_price'],
                    ],
                    request()->ip(),
                    request()->userAgent()
                );

                BusinessPlanAuditLog::logQuantityDecrease(
                    $selfPayment->businessPlan,
                    $planChanges['old_quantity'],
                    $planChanges['new_quantity'],
                    $selfPayment,
                    BusinessPlanAuditLog::SOURCE_EMPLOYEE_SELF_PAYMENT
                );

                BusinessPlanAuditLog::logTotalPriceDecrease(
                    $selfPayment->businessPlan,
                    $planChanges['old_total_price'],
                    $planChanges['new_total_price'],
                    $selfPayment,
                    BusinessPlanAuditLog::SOURCE_EMPLOYEE_SELF_PAYMENT
                );

                // Log the employee subscription conversion
                if ($subscriptionChanges) {
                    Log::info('Employee subscription converted to individual coverage after self-payment', [
                        'business_plan_id' => $selfPayment->businessPlan->id,
                        'employee_id' => $selfPayment->business_employee_id,
                        'self_payment_id' => $selfPayment->id,
                        'subscription_id' => $subscriptionChanges['subscription_id'],
                        'old_ends_at' => $subscriptionChanges['old_ends_at'],
                        'new_ends_at' => $subscriptionChanges['new_ends_at'],
                        'duration_months' => $selfPayment->businessPlan->duration_months,
                    ]);
                }

                DB::commit();

                return [
                    'success' => true,
                    'message' => 'Payment processed successfully!',
                    'transaction_id' => $transactionId,
                    'self_payment' => $selfPayment->fresh(),
                ];

            } else {
                // Payment failed
                $errorMessage = $paymentResult['transactionResponse']['errors'][0]['errorText'] ?? 'Payment failed';

                $transaction->update([
                    'status' => 'failed',
                    'error_message' => $errorMessage,
                ]);

                $selfPayment->markAsFailed($errorMessage);

                DB::commit();

                return [
                    'success' => false,
                    'message' => 'Payment failed: ' . $errorMessage,
                ];
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Self-payment processing failed: ' . $e->getMessage(), [
                'self_payment_id' => $selfPayment->id,
                'employee_id' => $selfPayment->business_employee_id,
                'exception' => $e,
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing your payment. Please try again.',
            ];
        }
    }

    /**
     * Get self-payment history for an employee.
     */
    public function getEmployeeSelfPaymentHistory(BusinessEmployee $employee): array
    {
        $selfPayments = $employee->selfPayments()
            ->with(['businessPlan', 'transaction'])
            ->orderBy('created_at', 'desc')
            ->get();

        return $selfPayments->map(function ($selfPayment) {
            return [
                'id' => $selfPayment->id,
                'amount' => $selfPayment->amount_in_dollars,
                'status' => $selfPayment->status_label,
                'payment_method' => $selfPayment->payment_method,
                'paid_at' => $selfPayment->paid_at?->format('M j, Y g:i A'),
                'transaction_reference' => $selfPayment->transaction_reference,
                'business_plan' => [
                    'id' => $selfPayment->businessPlan->id,
                    'duration_months' => $selfPayment->businessPlan->duration_months,
                    'starts_at' => $selfPayment->businessPlan->starts_at->format('M j, Y'),
                    'ends_at' => $selfPayment->businessPlan->ends_at?->format('M j, Y'),
                ],
            ];
        })->toArray();
    }

    /**
     * Process self-payment atomically (create record and process payment in one transaction).
     */
    public function processAtomicSelfPayment(
        BusinessEmployee $employee,
        BusinessPlan $businessPlan,
        array $paymentData
    ): array {
        try {
            DB::beginTransaction();

            // Create the self-payment record
            $selfPayment = BusinessPlanSelfPayment::create([
                'business_plan_id' => $businessPlan->id,
                'business_employee_id' => $employee->id,
                'user_id' => $employee->user_id,
                'amount_paid' => $businessPlan->price_per_plan,
                'payment_method' => $paymentData['payment_method'] ?? 'credit_card',
                'status' => BusinessPlanSelfPayment::STATUS_PENDING,
                'meta_data' => [
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                    'payment_data' => $paymentData,
                ],
            ]);

            Log::info('Created pending self-payment record', [
                'self_payment_id' => $selfPayment->id,
                'employee_id' => $employee->id,
                'business_plan_id' => $businessPlan->id,
                'amount' => $selfPayment->amount_paid,
            ]);

            // Commit the self-payment record creation
            DB::commit();

            // Process the payment (this has its own transaction handling)
            $paymentResult = $this->processPayment($selfPayment, $paymentData);

            if ($paymentResult['success']) {
                return [
                    'success' => true,
                    'message' => $paymentResult['message'],
                    'transaction_id' => $paymentResult['transaction_id'],
                    'self_payment' => $selfPayment->fresh(),
                ];
            } else {
                // Payment failed - clean up the self-payment record
                try {
                    $selfPayment->delete();
                } catch (\Exception $cleanupException) {
                    Log::error('Failed to cleanup self-payment after payment failure', [
                        'self_payment_id' => $selfPayment->id,
                        'cleanup_error' => $cleanupException->getMessage(),
                    ]);
                }

                return [
                    'success' => false,
                    'message' => $paymentResult['message'],
                ];
            }

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Atomic self-payment processing failed', [
                'employee_id' => $employee->id,
                'business_plan_id' => $businessPlan->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'An error occurred while processing your payment. Please try again.',
            ];
        }
    }

    /**
     * Convert employee's subscription to individual coverage with extended end date.
     * The employee "buys out" their slot from the business plan and gets independent coverage.
     */
    protected function convertToIndividualSubscription(BusinessEmployee $employee, BusinessPlan $businessPlan): ?array
    {
        if (!$employee->user_id) {
            Log::warning('Cannot convert subscription for employee without user account', [
                'employee_id' => $employee->id,
            ]);
            return null;
        }

        // Find the employee's active business subscription
        $subscription = $employee->user->subscriptions()
            ->where('user_type', 'business_employee')
            ->where('status', 'active')
            ->where('meta_data->employee_id', $employee->id)
            ->first();

        if ($subscription) {
            $oldEndDate = $subscription->ends_at;

            // Calculate new end date: extend from business plan end date by plan duration
            $businessPlanEndDate = $businessPlan->ends_at ?: now();
            $newEndDate = $businessPlanEndDate->copy()->addMonths($businessPlan->duration_months);

            // Update subscription to individual coverage with extended end date
            $subscription->update([
                'ends_at' => $newEndDate,
                'user_type' => 'individual', // Convert from business_employee to individual
                'meta_data' => array_merge($subscription->meta_data ?? [], [
                    'converted_from_business_plan' => true,
                    'original_business_plan_id' => $businessPlan->id,
                    'original_employee_id' => $employee->id,
                    'conversion_date' => now()->toISOString(),
                    'original_end_date' => $oldEndDate?->toISOString(),
                ])
            ]);

            Log::info('Converted employee subscription to individual coverage after self-payment', [
                'employee_id' => $employee->id,
                'user_id' => $employee->user_id,
                'subscription_id' => $subscription->id,
                'old_end_date' => $oldEndDate,
                'new_end_date' => $newEndDate,
                'business_plan_end_date' => $businessPlanEndDate,
                'extension_months' => $businessPlan->duration_months,
            ]);

            return [
                'subscription_id' => $subscription->id,
                'old_ends_at' => $oldEndDate,
                'new_ends_at' => $newEndDate,
            ];
        } else {
            Log::warning('No active business subscription found for employee', [
                'employee_id' => $employee->id,
                'user_id' => $employee->user_id,
            ]);
            return null;
        }
    }

    /**
     * Clean up stale pending payments for an employee.
     */
    public function cleanupStalePendingPayments(BusinessEmployee $employee, BusinessPlan $businessPlan): void
    {
        $staleThreshold = now()->subHours(2); // Consider payments stale after 2 hours

        $stalePayments = BusinessPlanSelfPayment::where('business_plan_id', $businessPlan->id)
            ->where('business_employee_id', $employee->id)
            ->where('status', BusinessPlanSelfPayment::STATUS_PENDING)
            ->where('created_at', '<', $staleThreshold)
            ->get();

        foreach ($stalePayments as $stalePayment) {
            try {
                // Remove associated transaction if exists
                if ($stalePayment->transaction_id) {
                    $stalePayment->transaction()->delete();
                }

                // Delete the stale payment
                $stalePayment->delete();

                Log::info('Cleaned up stale pending self-payment', [
                    'payment_id' => $stalePayment->id,
                    'employee_id' => $employee->id,
                    'created_at' => $stalePayment->created_at,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to cleanup stale pending payment', [
                    'payment_id' => $stalePayment->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
