<?php

namespace App\Services;

use App\Actions\Subscriptions\CreatePendingSubscription;
use App\Models\Business;
use App\Models\BusinessPlan;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class BusinessPlanService
{
    protected $businessEmployeeService;
    protected $createPendingSubscription;

    public function __construct(
        BusinessEmployeeService $businessEmployeeService,
        CreatePendingSubscription $createPendingSubscription
    ) {
        $this->businessEmployeeService = $businessEmployeeService;
        $this->createPendingSubscription = $createPendingSubscription;
    }

    /**
     * Create a new business plan
     *
     * @param Business $business
     * @param array $planData
     * @return array
     */
    public function createPlan(Business $business, array $planData)
    {
        try {
            // Calculate total price
            $planQuantity = $planData['employee_count'];
            $pricePerPlan = $planData['price_per_plan'] ?? 2000; // Default $20.00 per plan stored as 2000 cents

            // Always calculate total price to ensure consistency
            $totalPrice = $planQuantity * $pricePerPlan;

            // Set duration (default to 1 month if not specified)
            $durationMonths = $planData['duration_months'] ?? 1;

            // Calculate end date based on duration
            $startDate = now();
            $endDate = $startDate->copy()->addMonths($durationMonths);

            // Create business plan
            $businessPlan = $business->plans()->create([
                'plan_quantity' => $planQuantity,
                'price_per_plan' => $pricePerPlan,
                'total_price' => $totalPrice,
                'starts_at' => $startDate,
                'ends_at' => $endDate,
                'active' => $planData['active'] ?? false,
                'duration_months' => $durationMonths,
                'discount_percent' => $planData['discount_percent'] ?? 0,
                'click_id' => $planData['click_id'] ?? null,
                'afid' => $planData['afid'] ?? null,
            ]);

            // Update existing employee subscriptions if this is a renewal
            if (isset($planData['is_renewal']) && $planData['is_renewal']) {
                $this->businessEmployeeService->updateEmployeeSubscriptions($business, $businessPlan);
            }

            // Create a subscription for the business admin
            $subscription = null;

            // Get the business admin user
            $adminUser = $business->users()
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'business_admin');
                })
                ->first();

            // If no admin user is found, use the first user associated with the business
            if (!$adminUser) {
                $adminUser = $business->users()->first();
            }

            // Only create subscription if we have a user
            if ($adminUser) {
                // Get a premium business plan (using the first premium plan)
                $premiumPlan = SubscriptionPlan::where('group_id', 3) // Premium Plans group
                    ->where('is_active', true)
                    ->first();

                if ($premiumPlan) {
                    // Create subscription with pending_payment status
                    $subscriptionResult = $this->createPendingSubscription->execute([
                        'user' => $adminUser,
                        'plan' => $premiumPlan,
                        'session_data' => [
                            'source' => 'business_plan_creation'
                        ]
                    ]);

                    if ($subscriptionResult['success']) {
                        $subscription = $subscriptionResult['subscription'];

                        // Update subscription with business-specific data
                        $subscription->update([
                            'ends_at' => $businessPlan->ends_at, // Use the same end date as the business plan
                            'user_type' => 'business_admin',
                            'meta_data' => array_merge($subscription->meta_data ?? [], [
                                'business_id' => $business->id,
                                'business_plan_id' => $businessPlan->id,
                                'enrollment_type' => 'business'
                            ])
                        ]);

                        Log::info('Created subscription for business plan', [
                            'business_id' => $business->id,
                            'business_plan_id' => $businessPlan->id,
                            'subscription_id' => $subscription->id,
                            'user_id' => $adminUser->id
                        ]);
                    } else {
                        Log::warning('Failed to create subscription for business plan', [
                            'business_id' => $business->id,
                            'business_plan_id' => $businessPlan->id,
                            'error' => $subscriptionResult['error'] ?? 'Unknown error'
                        ]);
                    }
                } else {
                    Log::warning('No premium subscription plan found for business subscription', [
                        'business_id' => $business->id,
                        'business_plan_id' => $businessPlan->id
                    ]);
                }
            } else {
                Log::warning('No admin user found for business when creating subscription', [
                    'business_id' => $business->id,
                    'business_plan_id' => $businessPlan->id
                ]);
            }

            return [
                'success' => true,
                'business_plan' => $businessPlan,
                'subscription' => $subscription
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create business plan: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to create business plan: ' . $e->getMessage(),
                'subscription' => null
            ];
        }
    }

    /**
     * Renew a business plan
     *
     * @param BusinessPlan $currentPlan
     * @param array $renewalData
     * @return array
     */
    public function renewPlan(BusinessPlan $currentPlan, array $renewalData)
    {
        try {
            // Mark current plan as inactive
            $currentPlan->update(['active' => false]);

            // Create new plan with renewal data
            $renewalData['is_renewal'] = true;

            return $this->createPlan($currentPlan->business, $renewalData);
        } catch (\Exception $e) {
            Log::error('Failed to renew business plan: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to renew business plan: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Cancel a business plan
     *
     * @param BusinessPlan $plan
     * @return array
     */
    public function cancelPlan(BusinessPlan $plan)
    {
        try {
            // Mark plan as inactive
            $plan->update(['active' => false]);

            // Cancel all associated employee subscriptions
            $business = $plan->business;
            $employeeIds = $business->employees()
                ->where('status', 'active')
                ->whereNotNull('user_id')
                ->pluck('user_id')
                ->toArray();

            Subscription::whereIn('user_id', $employeeIds)
                ->where('user_type', 'business_employee')
                ->where('status', 'active')
                ->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now()
                ]);

            return [
                'success' => true,
                'message' => 'Business plan cancelled successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Failed to cancel business plan: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Failed to cancel business plan: ' . $e->getMessage()
            ];
        }
    }
}
