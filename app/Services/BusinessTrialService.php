<?php

namespace App\Services;

use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BusinessTrialService
{
    /**
     * Start a 30-day trial for a business
     *
     * @param Business $business
     * @return void
     */
    public function startTrial(Business $business): void
    {
        $business->update([
            'trial_enabled' => true,
            'trial_started_at' => Carbon::now(),
            'trial_ends_at' => Carbon::now()->addDays(config('business.trial.days', 30)),
        ]);

        // Log the trial start
        Log::info("Trial started for business: {$business->id} - {$business->name}");
        
        // Send trial started notification (email, webhook, etc.)
        $this->sendTrialNotification($business, 'started');
    }

    /**
     * Process expired trials
     *
     * @return int Number of processed trials
     */
    public function processExpiredTrials(): int
    {
        $expiredTrials = Business::where('trial_enabled', true)
            ->whereNotNull('trial_ends_at')
            ->where('trial_ends_at', '<', Carbon::now())
            ->get();
        
        $count = 0;
        
        foreach ($expiredTrials as $business) {
            // Log the expiration
            Log::info("Trial expired for business: {$business->id} - {$business->name}");
            
            // Update the business record
            $business->update([
                'trial_enabled' => false,
            ]);
            
            // Send expiration notification
            $this->sendTrialNotification($business, 'expired');
            
            $count++;
        }
        
        return $count;
    }

    /**
     * Send trial notification via webhook
     *
     * @param Business $business
     * @param string $event
     * @return void
     */
    protected function sendTrialNotification(Business $business, string $event): void
    {
        // Get the webhook URL from config
        $webhookUrl = config('business.webhooks.trial_notifications');
        
        if (!$webhookUrl) {
            return;
        }
        
        try {
            Http::post($webhookUrl, [
                'event' => "trial.{$event}",
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_email' => $business->email,
                'trial_started_at' => $business->trial_started_at?->toIso8601String(),
                'trial_ends_at' => $business->trial_ends_at?->toIso8601String(),
                'timestamp' => Carbon::now()->toIso8601String(),
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to send trial notification: {$e->getMessage()}", [
                'business_id' => $business->id,
                'event' => $event,
            ]);
        }
    }
}
