<?php

namespace App\Services;

use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

class CreditCardService
{
    private $merchantAuthentication;

    public function __construct()
    {
        $this->merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
        $this->merchantAuthentication->setName(config('services.authorize.login_id'));
        $this->merchantAuthentication->setTransactionKey(config('services.authorize.transaction_key'));
    }

    private function findExistingCreditCard(User $user, $cardNumber)
    {
        return $user->creditCards()->where('last_four', substr($cardNumber, -4))->first();
    }

    private function getOrCreateCustomerProfile(User $user)
    {
        if ($user->authorize_net_customer_id) {
            return $user->authorize_net_customer_id;
        }

        // Create a new customer profile
        $customerProfile = new AnetAPI\CustomerProfileType();
        $customerProfile->setDescription("Customer Profile for " . $user->name);
        $customerProfile->setMerchantCustomerId("M_" . $user->id);
        $customerProfile->setEmail($user->email);

        $request = new AnetAPI\CreateCustomerProfileRequest();
        $request->setMerchantAuthentication($this->merchantAuthentication);
        $request->setProfile($customerProfile);

        $controller = new AnetController\CreateCustomerProfileController($request);
        $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::PRODUCTION);

        if ($response != null && $response->getMessages()->getResultCode() == "Ok") {
            /** @var \net\authorize\api\contract\v1\CreateCustomerPaymentProfileResponse $response */
            $customerProfileId = $response->getCustomerProfileId();
            $user->authorize_net_customer_id = $customerProfileId;
            $user->save();
            return $customerProfileId;
        } else {
            $this->logError($response);
            throw new \Exception("Failed to create customer profile.");
        }
    }

    public function addCreditCard(User $user, $cardNumber, $expirationMonth, $expirationYear, $cvv)
    {
        try {
            // Get or create customer profile in Authorize.net
            $customerProfileId = $this->getOrCreateCustomerProfile($user);

            // Set up credit card information
            $creditCard = new AnetAPI\CreditCardType();
            $creditCard->setCardNumber($cardNumber);
            $creditCard->setExpirationDate($expirationYear . '-' . $expirationMonth);
            $creditCard->setCardCode($cvv);

            // Add the payment data to a paymentType object
            $paymentCreditCard = new AnetAPI\PaymentType();
            $paymentCreditCard->setCreditCard($creditCard);

            // Create payment profile
            $paymentProfile = new AnetAPI\CustomerPaymentProfileType();
            $paymentProfile->setCustomerType('individual');
            $paymentProfile->setPayment($paymentCreditCard);

            // Set billing information
            $billTo = new AnetAPI\CustomerAddressType();
            $billTo->setFirstName($user->first_name);
            $billTo->setLastName($user->last_name);
            $paymentProfile->setBillTo($billTo);

            // Set up the request
            $request = new AnetAPI\CreateCustomerPaymentProfileRequest();
            $request->setMerchantAuthentication($this->merchantAuthentication);
            $request->setCustomerProfileId($customerProfileId);
            $request->setPaymentProfile($paymentProfile);

            // Execute the request
            $controller = new AnetController\CreateCustomerPaymentProfileController($request);
            $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::PRODUCTION);

            if ($response != null && $response->getMessages()->getResultCode() == "Ok") {
                // Successfully created payment profile
                /** @var \net\authorize\api\contract\v1\CreateCustomerPaymentProfileResponse $response */
                $paymentProfileId = $response->getCustomerPaymentProfileId();
                
                if (empty($paymentProfileId)) {
                    throw new \Exception("Received empty paymentProfileId from Authorize.net");
                }

                // Save credit card information to our database
                $creditCard = new CreditCard([
                    'user_id' => $user->id,
                    'last_four' => $cardNumber,
                    'brand' => $this->getCardBrand($cardNumber),
                    'expiration_month' => $expirationMonth,
                    'expiration_year' => $expirationYear,
                    'cvv' => $cvv,
                    'token' => $paymentProfileId,
                    'is_default' => $user->creditCards()->count() == 0,
                ]);

                $creditCard->save();

                return $creditCard;
            } else {
                // Handle errors
                $errorCode = $response->getMessages()->getMessage()[0]->getCode();
                if ($errorCode == "E00039") {  // Duplicate payment profile
                    Log::info("CCServiceAttempted to add a duplicate payment profile for user: " . $user->id);
                    return $this->findExistingCreditCard($user, $cardNumber);
                } else {
                    $this->logError($response);
                    throw new \Exception("Failed to add credit card.");
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in addCreditCard: ' . $e->getMessage());
            throw $e;
        }
    }

    private function logError($response)
    {
        $errorMessages = $response->getMessages()->getMessage();
        $errorCode = $errorMessages[0]->getCode();
        $errorText = $errorMessages[0]->getText();
        
        Log::error("Authorize.net Error: Code: $errorCode, Message: $errorText");
        
        // Only try to log transaction response if it's available
        if (method_exists($response, 'getTransactionResponse') && $response->getTransactionResponse() != null) {
            $tresponse = $response->getTransactionResponse();
            Log::error("Transaction Response Code: " . $tresponse->getResponseCode());
            Log::error("Transaction Response Reason Code: " . $tresponse->getReasonCode());
        }
    }

    private function getCardBrand($cardNumber)
    {
        $brand = 'Unknown';
        if (preg_match('/^4/', $cardNumber)) {
            $brand = 'Visa';
        } elseif (preg_match('/^5[1-5]/', $cardNumber)) {
            $brand = 'Mastercard';
        } elseif (preg_match('/^3[47]/', $cardNumber)) {
            $brand = 'American Express';
        } elseif (preg_match('/^6(?:011|5)/', $cardNumber)) {
            $brand = 'Discover';
        }
        return $brand;
    }

    public function setDefaultCreditCard(User $user, CreditCard $creditCard)
    {
        $user->creditCards()->update(['is_default' => false]);
        $creditCard->update(['is_default' => true]);
    }
}