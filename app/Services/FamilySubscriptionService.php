<?php

namespace App\Services;

use App\Models\FamilyMember;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class FamilySubscriptionService
{
    /**
     * Create a new family subscription with the primary account holder.
     *
     * @param User $primaryUser
     * @param SubscriptionPlan $familyPlan
     * @return Subscription
     * @throws \Exception
     */
    public function createFamilySubscription(User $primaryUser, SubscriptionPlan $familyPlan): Subscription
    {
        if (!$familyPlan->isFamilyPlan()) {
            throw new \Exception('The selected plan is not a family plan.');
        }

        // Create the primary subscription
        $subscription = new Subscription();
        $subscription->user_id = $primaryUser->id;
        $subscription->plan_id = $familyPlan->id;
        $subscription->starts_at = now();
        $subscription->ends_at = now()->addMonths($familyPlan->duration_months);
        $subscription->status = Subscription::STATUS_ACTIVE;
        $subscription->is_primary_account = true;
        $subscription->family_role = Subscription::FAMILY_ROLE_PRIMARY;
        $subscription->save();

        return $subscription;
    }

    /**
     * Add a dependent to a family subscription.
     *
     * @param Subscription $primarySubscription
     * @param array $dependentData
     * @param bool $overrideAgeRestrictions Whether to override age restrictions (admin only)
     * @return array
     * @throws \Exception
     */
    public function addDependentToFamily(Subscription $primarySubscription, array $dependentData, bool $overrideAgeRestrictions = false): array
    {
        // Validate the primary subscription
        if (!$primarySubscription->isPrimaryAccount()) {
            throw new \Exception('Only primary subscriptions can add dependents.');
        }

        if (!$primarySubscription->plan->isFamilyPlan()) {
            throw new \Exception('Only family plans can add dependents.');
        }

        // Determine if the dependent is an adult, minor, or older dependent
        $dateOfBirth = isset($dependentData['dob']) ? Carbon::parse($dependentData['dob']) : null;
        $isAdult = $dateOfBirth && $dateOfBirth->age >= 18 && $dateOfBirth->age <= 23;
        $isMinor = $dateOfBirth && $dateOfBirth->age < 18;
        $isOlderDependent = $dateOfBirth && $dateOfBirth->age >= 24;

        // If not overriding age restrictions, check limits
        if (!$overrideAgeRestrictions) {
            // Get custom limits if they exist (set by admin)
            $maxTotalDependents = $primarySubscription->getFeature('admin_overrides.max_total_dependents', 5);
            $maxOlderDependents = $primarySubscription->getFeature('admin_overrides.max_older_dependents', 1);
            $maxYoungerDependents = $primarySubscription->getFeature('admin_overrides.max_younger_dependents', 4);

            // Check if we've reached the maximum number of dependents
            if ($primarySubscription->getDependentCount() >= $maxTotalDependents) {
                throw new \Exception("Maximum number of dependents ({$maxTotalDependents}) reached for this family plan.");
            }

            // Validate age eligibility for older dependents
            if ($isOlderDependent) {
                if ($primarySubscription->getOlderDependentCount() >= $maxOlderDependents) {
                    throw new \Exception("Maximum number of older dependents (24+ years old) reached for this family plan. Only {$maxOlderDependents} older dependent(s) allowed.");
                }
            } else {
                // This is a younger dependent (23 or younger)
                if ($primarySubscription->getYoungerDependentCount() >= $maxYoungerDependents) {
                    throw new \Exception("Maximum number of younger dependents (23 years old or younger) reached for this family plan. You can add up to {$maxYoungerDependents} younger dependents.");
                }
            }
        }

        // Begin transaction
        return DB::transaction(function () use ($primarySubscription, $dependentData, $isAdult, $isMinor, $isOlderDependent) {
            // Create the dependent user
            $user = new User();
            $user->fname = $dependentData['fname'];
            $user->lname = $dependentData['lname'];
            $user->name = $dependentData['fname'] . ' ' . $dependentData['lname'];
            $user->email = $dependentData['email'] ?? null;
            $user->dob = $dependentData['dob'] ?? null;
            $user->gender = $dependentData['gender'] ?? null;
            $user->address1 = $dependentData['address1'] ?? null;
            $user->address2 = $dependentData['address2'] ?? null;
            $user->city = $dependentData['city'] ?? null;
            $user->state = $dependentData['state'] ?? null;
            $user->zip = $dependentData['zip'] ?? null;
            $user->phone = $dependentData['phone'] ?? null;
            $user->mobile_phone = $dependentData['mobile_phone'] ?? null;

            // Generate password for adult and older dependents
            if ($isAdult || $isOlderDependent) {
                $password = Str::random(10);
                $user->password = Hash::make($password);
                $user->status = 'active';
            } else {
                // Minor dependents get a random password but don't use it for login
                $password = null;
                $user->password = Hash::make(Str::random(32)); // Random password required by users table
                $user->status = 'inactive';
            }

            $user->save();

            // Assign the patient role
            $user->assignRole('patient');

            // Create the dependent subscription
            $dependentSubscription = new Subscription();
            $dependentSubscription->user_id = $user->id;
            $dependentSubscription->plan_id = $primarySubscription->plan_id;
            $dependentSubscription->starts_at = $primarySubscription->starts_at;
            $dependentSubscription->ends_at = $primarySubscription->ends_at;
            $dependentSubscription->status = $primarySubscription->status;
            $dependentSubscription->is_primary_account = false;
            $dependentSubscription->primary_subscription_id = $primarySubscription->id;

            // Determine the family role based on age
            if ($isOlderDependent) {
                $dependentSubscription->family_role = Subscription::FAMILY_ROLE_DEPENDENT_OLDER;
            } elseif ($isAdult) {
                $dependentSubscription->family_role = Subscription::FAMILY_ROLE_DEPENDENT_ADULT;
            } else {
                $dependentSubscription->family_role = Subscription::FAMILY_ROLE_DEPENDENT_MINOR;
            }

            $dependentSubscription->save();

            // Create the family member relationship
            $familyMember = new FamilyMember();
            $familyMember->subscription_id = $primarySubscription->id;
            $familyMember->primary_user_id = $primarySubscription->user_id;
            $familyMember->dependent_user_id = $user->id;
            $familyMember->relationship_type = $dependentData['relationship_type'] ?? 'other';
            $familyMember->date_of_birth = $dependentData['dob'] ?? null;
            $familyMember->save();

            return [
                'user' => $user,
                'subscription' => $dependentSubscription,
                'family_member' => $familyMember,
                'password' => $password, // Only set for adult dependents
            ];
        });
    }

    /**
     * Remove a dependent from a family subscription.
     *
     * @param Subscription $primarySubscription
     * @param User $dependent
     * @return bool
     * @throws \Exception
     */
    public function removeDependentFromFamily(Subscription $primarySubscription, User $dependent): bool
    {
        // Validate the primary subscription
        if (!$primarySubscription->isPrimaryAccount()) {
            throw new \Exception('Only primary subscriptions can remove dependents.');
        }

        // Find the family member relationship
        $familyMember = FamilyMember::where('subscription_id', $primarySubscription->id)
            ->where('primary_user_id', $primarySubscription->user_id)
            ->where('dependent_user_id', $dependent->id)
            ->first();

        if (!$familyMember) {
            throw new \Exception('The specified user is not a dependent in this family plan.');
        }

        // Begin transaction
        return DB::transaction(function () use ($primarySubscription, $dependent, $familyMember) {
            // Find and delete the dependent subscription
            $dependentSubscription = Subscription::where('user_id', $dependent->id)
                ->where('primary_subscription_id', $primarySubscription->id)
                ->first();

            if ($dependentSubscription) {
                $dependentSubscription->delete();
            }

            // Delete the family member relationship
            $familyMember->delete();

            // Set the dependent's user account status to 'deleted'
            // This prevents them from logging in due to the check in AuthenticatedSessionController
            $dependent->status = 'deleted';

            // Invalidate their password by setting it to a random value
            // This provides an additional layer of security
            $dependent->password = Hash::make(Str::random(32));

            // Save the changes
            $dependent->save();

            return true;
        });
    }
}
