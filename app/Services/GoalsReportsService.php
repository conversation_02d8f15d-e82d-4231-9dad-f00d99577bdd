<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentGoal;
use App\Models\AgentGoalProgress;
use App\Models\AgentCustomReport;
use App\Models\AgentReportExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GoalsReportsService
{
    /**
     * Generate a report.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCustomReport  $report
     * @param  string|null  $dateFrom
     * @param  string|null  $dateTo
     * @return array
     */
    public function generateReport(int $agentId, AgentCustomReport $report, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        // Parse filters
        $filters = json_decode($report->filters, true) ?: [];

        // Override date filters if provided
        if ($dateFrom) {
            $filters['date_from'] = $dateFrom;
        }

        if ($dateTo) {
            $filters['date_to'] = $dateTo;
        }

        // Set default dates if not provided
        if (!isset($filters['date_from'])) {
            $filters['date_from'] = now()->startOfMonth()->format('Y-m-d');
        }

        if (!isset($filters['date_to'])) {
            $filters['date_to'] = now()->format('Y-m-d');
        }

        // Generate report based on type
        switch ($report->type) {
            case 'commission':
                return $this->generateCommissionReport($agentId, $report, $filters);
            case 'referral':
                return $this->generateReferralReport($agentId, $report, $filters);
            case 'performance':
                return $this->generatePerformanceReport($agentId, $report, $filters);
            default:
                return [
                    'report_id' => $report->id,
                    'name' => $report->name,
                    'type' => $report->type,
                    'generated_at' => now(),
                    'filters_applied' => $filters,
                    'results' => [],
                    'summary' => []
                ];
        }
    }

    /**
     * Generate a commission report.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCustomReport  $report
     * @param  array  $filters
     * @return array
     */
    protected function generateCommissionReport(int $agentId, AgentCustomReport $report, array $filters): array
    {
        // Parse columns and sorting
        $columns = json_decode($report->columns, true) ?: ['created_at', 'amount', 'status', 'source'];
        $sorting = json_decode($report->sorting, true) ?: [['column' => 'created_at', 'direction' => 'desc']];

        // Build query
        $query = DB::table('commissions')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$filters['date_from'], $filters['date_to']]);

        // Apply additional filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['source'])) {
            $query->where('source', $filters['source']);
        }

        if (isset($filters['min_amount'])) {
            $query->where('amount', '>=', $filters['min_amount']);
        }

        if (isset($filters['max_amount'])) {
            $query->where('amount', '<=', $filters['max_amount']);
        }

        // Apply sorting
        foreach ($sorting as $sort) {
            $query->orderBy($sort['column'], $sort['direction']);
        }

        // Get results
        $results = $query->get();

        // Calculate summary
        $totalCount = $results->count();
        $totalAmount = $results->sum('amount');
        $averageAmount = $totalCount > 0 ? $totalAmount / $totalCount : 0;

        // Group by source
        $bySource = [];
        foreach ($results->groupBy('source') as $source => $items) {
            $bySource[$source] = $items->sum('amount');
        }

        return [
            'report_id' => $report->id,
            'name' => $report->name,
            'type' => 'commission',
            'generated_at' => now(),
            'filters_applied' => $filters,
            'results' => $results->toArray(),
            'summary' => [
                'total_count' => $totalCount,
                'total_amount' => $totalAmount,
                'average_amount' => $averageAmount,
                'by_source' => $bySource
            ]
        ];
    }

    /**
     * Generate a referral report.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCustomReport  $report
     * @param  array  $filters
     * @return array
     */
    protected function generateReferralReport(int $agentId, AgentCustomReport $report, array $filters): array
    {
        // Parse columns and sorting
        $columns = json_decode($report->columns, true) ?: ['created_at', 'email', 'status', 'converted_at'];
        $sorting = json_decode($report->sorting, true) ?: [['column' => 'created_at', 'direction' => 'desc']];

        // Build query
        $query = DB::table('referrals')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$filters['date_from'], $filters['date_to']]);

        // Apply additional filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['source'])) {
            $query->where('source', $filters['source']);
        }

        // Apply sorting
        foreach ($sorting as $sort) {
            $query->orderBy($sort['column'], $sort['direction']);
        }

        // Get results
        $results = $query->get();

        // Calculate summary
        $totalCount = $results->count();
        $convertedCount = $results->where('status', 'converted')->count();
        $pendingCount = $results->where('status', 'pending')->count();
        $expiredCount = $results->where('status', 'expired')->count();
        $conversionRate = $totalCount > 0 ? round(($convertedCount / $totalCount) * 100, 1) : 0;

        // Group by source
        $bySource = [];
        foreach ($results->groupBy('source') as $source => $items) {
            $bySource[$source] = $items->count();
        }

        return [
            'report_id' => $report->id,
            'name' => $report->name,
            'type' => 'referral',
            'generated_at' => now(),
            'filters_applied' => $filters,
            'results' => $results->toArray(),
            'summary' => [
                'total_count' => $totalCount,
                'converted_count' => $convertedCount,
                'pending_count' => $pendingCount,
                'expired_count' => $expiredCount,
                'conversion_rate' => $conversionRate,
                'by_source' => $bySource
            ]
        ];
    }

    /**
     * Generate a performance report.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCustomReport  $report
     * @param  array  $filters
     * @return array
     */
    protected function generatePerformanceReport(int $agentId, AgentCustomReport $report, array $filters): array
    {
        // Parse columns and sorting
        $columns = json_decode($report->columns, true) ?: ['period', 'commission_amount', 'referral_count', 'conversion_rate'];
        $sorting = json_decode($report->sorting, true) ?: [['column' => 'period', 'direction' => 'asc']];

        // Determine period type
        $periodType = $filters['period_type'] ?? 'monthly';

        // Generate periods
        $startDate = Carbon::parse($filters['date_from']);
        $endDate = Carbon::parse($filters['date_to']);
        $periods = $this->generatePeriods($startDate, $endDate, $periodType);

        // Get performance metrics for each period
        $results = [];

        foreach ($periods as $period) {
            $periodStart = $period['start'];
            $periodEnd = $period['end'];
            $periodLabel = $period['label'];

            // Get commission amount
            $commissionAmount = DB::table('commissions')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$periodStart, $periodEnd])
                ->sum('amount');

            // Get referral metrics
            $referrals = DB::table('referrals')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$periodStart, $periodEnd])
                ->get();

            $referralCount = $referrals->count();
            $convertedCount = $referrals->where('status', 'converted')->count();
            $conversionRate = $referralCount > 0 ? round(($convertedCount / $referralCount) * 100, 1) : 0;

            // Calculate growth rates
            $previousPeriodStart = Carbon::parse($periodStart)->subDays($periodEnd->diffInDays($periodStart) + 1);
            $previousPeriodEnd = Carbon::parse($periodStart)->subDay();

            $previousCommissionAmount = DB::table('commissions')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                ->sum('amount');

            $previousReferralCount = DB::table('referrals')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
                ->count();

            $commissionGrowthRate = $previousCommissionAmount > 0
                ? round((($commissionAmount - $previousCommissionAmount) / $previousCommissionAmount) * 100, 1)
                : 0;

            $referralGrowthRate = $previousReferralCount > 0
                ? round((($referralCount - $previousReferralCount) / $previousReferralCount) * 100, 1)
                : 0;

            $results[] = [
                'period' => $periodLabel,
                'period_start' => $periodStart,
                'period_end' => $periodEnd,
                'commission_amount' => $commissionAmount,
                'referral_count' => $referralCount,
                'conversion_rate' => $conversionRate,
                'commission_growth_rate' => $commissionGrowthRate,
                'referral_growth_rate' => $referralGrowthRate
            ];
        }

        // Apply sorting
        usort($results, function ($a, $b) use ($sorting) {
            foreach ($sorting as $sort) {
                $column = $sort['column'];
                $direction = $sort['direction'];

                if ($a[$column] == $b[$column]) {
                    continue;
                }

                if ($direction === 'asc') {
                    return $a[$column] <=> $b[$column];
                } else {
                    return $b[$column] <=> $a[$column];
                }
            }

            return 0;
        });

        // Calculate summary
        $totalCommissionAmount = array_sum(array_column($results, 'commission_amount'));
        $totalReferralCount = array_sum(array_column($results, 'referral_count'));
        $averageConversionRate = count($results) > 0
            ? round(array_sum(array_column($results, 'conversion_rate')) / count($results), 1)
            : 0;

        return [
            'report_id' => $report->id,
            'name' => $report->name,
            'type' => 'performance',
            'generated_at' => now(),
            'filters_applied' => $filters,
            'results' => $results,
            'summary' => [
                'total_commission_amount' => $totalCommissionAmount,
                'total_referral_count' => $totalReferralCount,
                'average_conversion_rate' => $averageConversionRate,
                'period_count' => count($results)
            ]
        ];
    }

    /**
     * Generate periods based on start date, end date, and period type.
     *
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @param  string  $periodType
     * @return array
     */
    protected function generatePeriods(Carbon $startDate, Carbon $endDate, string $periodType): array
    {
        $periods = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $periodStart = $current->copy();

            switch ($periodType) {
                case 'daily':
                    $periodEnd = $current->copy()->endOfDay();
                    $periodLabel = $current->format('M d, Y');
                    $current->addDay();
                    break;

                case 'weekly':
                    $periodEnd = $current->copy()->endOfWeek();
                    $periodLabel = 'Week of ' . $current->format('M d, Y');
                    $current->addWeek();
                    break;

                case 'monthly':
                    $periodEnd = $current->copy()->endOfMonth();
                    $periodLabel = $current->format('M Y');
                    $current->addMonth();
                    break;

                case 'quarterly':
                    $periodEnd = $current->copy()->endOfQuarter();
                    $periodLabel = 'Q' . $current->quarter . ' ' . $current->year;
                    $current->addQuarter();
                    break;

                case 'yearly':
                    $periodEnd = $current->copy()->endOfYear();
                    $periodLabel = $current->format('Y');
                    $current->addYear();
                    break;

                default:
                    $periodEnd = $current->copy()->endOfMonth();
                    $periodLabel = $current->format('M Y');
                    $current->addMonth();
                    break;
            }

            // Ensure period end doesn't exceed the overall end date
            if ($periodEnd > $endDate) {
                $periodEnd = $endDate->copy();
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $periodLabel
            ];
        }

        return $periods;
    }
    /**
     * Export a report.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCustomReport  $report
     * @param  string  $format
     * @param  string|null  $dateFrom
     * @param  string|null  $dateTo
     * @return array
     */
    public function exportReport(int $agentId, AgentCustomReport $report, string $format, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        // Generate the report data
        $reportData = $this->generateReport($agentId, $report, $dateFrom, $dateTo);

        // Create a unique filename
        $filename = Str::slug($report->name) . '_' . now()->format('Y-m-d_His');
        $path = 'exports/' . $agentId . '/' . $filename . '.' . $format;

        // Create the export based on format
        if ($format === 'csv') {
            $this->exportToCsv($reportData, $path);
        } else if ($format === 'pdf') {
            $this->exportToPdf($reportData, $path);
        }

        // Create export record
        $export = AgentReportExport::create([
            'report_id' => $report->id,
            'format' => $format,
            'file_path' => $path,
            'file_size' => Storage::size($path),
            'generated_at' => now(),
            'expires_at' => now()->addDays(30)
        ]);

        return [
            'id' => $export->id,
            'report_id' => $report->id,
            'format' => $format,
            'file_path' => $path,
            'file_size' => $export->file_size,
            'download_url' => url('/api/agent/reports/download/' . $export->id),
            'generated_at' => $export->generated_at,
            'expires_at' => $export->expires_at
        ];
    }

    /**
     * Export report data to CSV.
     *
     * @param  array  $reportData
     * @param  string  $path
     * @return void
     */
    protected function exportToCsv(array $reportData, string $path): void
    {
        $results = $reportData['results'];

        if (empty($results)) {
            // Create empty CSV with headers
            $headers = ['No data found'];
            $csv = implode(',', $headers) . "\n";
            Storage::put($path, $csv);
            return;
        }

        // Get headers from first result
        $headers = array_keys((array) $results[0]);

        // Create CSV content
        $csv = implode(',', $headers) . "\n";

        foreach ($results as $row) {
            $rowData = [];
            foreach ($headers as $header) {
                $value = is_object($row) ? $row->{$header} : $row[$header];

                // Format value for CSV
                if (is_null($value)) {
                    $value = '';
                } else if (is_string($value)) {
                    $value = '"' . str_replace('"', '""', $value) . '"';
                } else if ($value instanceof \DateTime) {
                    $value = $value->format('Y-m-d H:i:s');
                }

                $rowData[] = $value;
            }

            $csv .= implode(',', $rowData) . "\n";
        }

        // Add summary section
        $csv .= "\n\nSummary:\n";
        foreach ($reportData['summary'] as $key => $value) {
            if (is_array($value)) {
                $csv .= $key . ":\n";
                foreach ($value as $subKey => $subValue) {
                    $csv .= "  " . $subKey . ": " . $subValue . "\n";
                }
            } else {
                $csv .= $key . ": " . $value . "\n";
            }
        }

        Storage::put($path, $csv);
    }

    /**
     * Export report data to PDF.
     *
     * @param  array  $reportData
     * @param  string  $path
     * @return void
     */
    protected function exportToPdf(array $reportData, string $path): void
    {
        // For simplicity, we'll create a basic HTML representation and convert it to PDF
        // In a real implementation, you would use a PDF library like DOMPDF or TCPDF

        $html = '<html><head><title>' . $reportData['name'] . '</title>';
        $html .= '<style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            h1, h2 { color: #333; }
            .summary { margin-top: 30px; }
            .summary h2 { border-bottom: 1px solid #ddd; padding-bottom: 5px; }
            .summary table { width: auto; }
        </style>';
        $html .= '</head><body>';

        // Report header
        $html .= '<h1>' . $reportData['name'] . '</h1>';
        $html .= '<p>Generated: ' . $reportData['generated_at'] . '</p>';
        $html .= '<p>Type: ' . ucfirst($reportData['type']) . ' Report</p>';

        // Filters applied
        $html .= '<h2>Filters</h2><ul>';
        foreach ($reportData['filters_applied'] as $key => $value) {
            $html .= '<li>' . $key . ': ' . $value . '</li>';
        }
        $html .= '</ul>';

        // Results table
        $results = $reportData['results'];
        if (!empty($results)) {
            $html .= '<h2>Results</h2>';
            $html .= '<table><thead><tr>';

            // Table headers
            $headers = array_keys((array) $results[0]);
            foreach ($headers as $header) {
                $html .= '<th>' . ucfirst(str_replace('_', ' ', $header)) . '</th>';
            }
            $html .= '</tr></thead><tbody>';

            // Table rows
            foreach ($results as $row) {
                $html .= '<tr>';
                foreach ($headers as $header) {
                    $value = is_object($row) ? $row->{$header} : $row[$header];

                    // Format value
                    if (is_null($value)) {
                        $value = '';
                    } else if ($value instanceof \DateTime) {
                        $value = $value->format('Y-m-d H:i:s');
                    }

                    $html .= '<td>' . $value . '</td>';
                }
                $html .= '</tr>';
            }

            $html .= '</tbody></table>';
        } else {
            $html .= '<p>No results found.</p>';
        }

        // Summary section
        $html .= '<div class="summary">';
        $html .= '<h2>Summary</h2>';
        $html .= '<table>';

        foreach ($reportData['summary'] as $key => $value) {
            if (is_array($value)) {
                $html .= '<tr><td colspan="2"><strong>' . ucfirst(str_replace('_', ' ', $key)) . '</strong></td></tr>';
                foreach ($value as $subKey => $subValue) {
                    $html .= '<tr><td>&nbsp;&nbsp;' . ucfirst(str_replace('_', ' ', $subKey)) . '</td><td>' . $subValue . '</td></tr>';
                }
            } else {
                $html .= '<tr><td><strong>' . ucfirst(str_replace('_', ' ', $key)) . '</strong></td><td>' . $value . '</td></tr>';
            }
        }

        $html .= '</table>';
        $html .= '</div>';
        $html .= '</body></html>';

        // In a real implementation, you would convert HTML to PDF here
        // For now, we'll just save the HTML with a .pdf extension
        Storage::put($path, $html);
    }

    /**
     * Get performance metrics.
     *
     * @param  int  $agentId
     * @param  string  $period
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    public function getPerformanceMetrics(int $agentId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        // Determine date range
        $dates = $this->getDateRange($period, $startDate, $endDate);
        $start = $dates['start'];
        $end = $dates['end'];
        $previousStart = $dates['previous_start'];
        $previousEnd = $dates['previous_end'];

        // Get commission metrics
        $commissionMetrics = $this->getCommissionMetrics($agentId, $start, $end, $previousStart, $previousEnd);

        // Get referral metrics
        $referralMetrics = $this->getReferralMetrics($agentId, $start, $end, $previousStart, $previousEnd);

        // Get goal metrics
        $goalMetrics = $this->getGoalMetrics($agentId);

        // Get time series data
        $timeSeriesData = $this->getTimeSeriesData($agentId, $start, $end, $period);

        return [
            'commissions' => $commissionMetrics,
            'referrals' => $referralMetrics,
            'goals' => $goalMetrics,
            'time_series' => $timeSeriesData,
            'comparison' => [
                'commissions' => [
                    'current' => $commissionMetrics['total'],
                    'previous' => $commissionMetrics['previous_total'],
                    'change' => $commissionMetrics['change_percentage']
                ],
                'referrals' => [
                    'current' => $referralMetrics['total'],
                    'previous' => $referralMetrics['previous_total'],
                    'change' => $referralMetrics['change_percentage']
                ],
                'conversion_rate' => [
                    'current' => $referralMetrics['conversion_rate'],
                    'previous' => $referralMetrics['previous_conversion_rate'],
                    'change' => $referralMetrics['conversion_rate_change']
                ]
            ]
        ];
    }

    /**
     * Get date range based on period.
     *
     * @param  string  $period
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    protected function getDateRange(string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        $now = Carbon::now();

        // If custom dates are provided, use them
        if ($startDate && $endDate) {
            $start = Carbon::parse($startDate)->startOfDay();
            $end = Carbon::parse($endDate)->endOfDay();

            $duration = $end->diffInDays($start);
            $previousStart = $start->copy()->subDays($duration);
            $previousEnd = $start->copy()->subDay();

            return [
                'start' => $start,
                'end' => $end,
                'previous_start' => $previousStart,
                'previous_end' => $previousEnd
            ];
        }

        // Otherwise, use the specified period
        switch ($period) {
            case 'week':
                $start = $now->copy()->startOfWeek();
                $end = $now->copy()->endOfWeek();
                $previousStart = $start->copy()->subWeek();
                $previousEnd = $start->copy()->subDay();
                break;

            case 'month':
                $start = $now->copy()->startOfMonth();
                $end = $now->copy()->endOfMonth();
                $previousStart = $start->copy()->subMonth();
                $previousEnd = $start->copy()->subDay();
                break;

            case 'quarter':
                $start = $now->copy()->startOfQuarter();
                $end = $now->copy()->endOfQuarter();
                $previousStart = $start->copy()->subQuarter();
                $previousEnd = $start->copy()->subDay();
                break;

            case 'year':
                $start = $now->copy()->startOfYear();
                $end = $now->copy()->endOfYear();
                $previousStart = $start->copy()->subYear();
                $previousEnd = $start->copy()->subDay();
                break;

            case 'all':
                $start = Carbon::parse('2000-01-01');
                $end = $now;
                $previousStart = null;
                $previousEnd = null;
                break;

            default:
                $start = $now->copy()->startOfMonth();
                $end = $now->copy()->endOfMonth();
                $previousStart = $start->copy()->subMonth();
                $previousEnd = $start->copy()->subDay();
                break;
        }

        return [
            'start' => $start,
            'end' => $end,
            'previous_start' => $previousStart,
            'previous_end' => $previousEnd
        ];
    }

    /**
     * Get commission metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $start
     * @param  \Carbon\Carbon  $end
     * @param  \Carbon\Carbon|null  $previousStart
     * @param  \Carbon\Carbon|null  $previousEnd
     * @return array
     */
    protected function getCommissionMetrics(int $agentId, Carbon $start, Carbon $end, ?Carbon $previousStart, ?Carbon $previousEnd): array
    {
        // Get current period commissions
        $commissions = DB::table('commissions')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$start, $end])
            ->get();

        $total = $commissions->sum('amount');
        $count = $commissions->count();
        $average = $count > 0 ? $total / $count : 0;

        // Group by source
        $bySource = [];
        foreach ($commissions->groupBy('source') as $source => $items) {
            $bySource[$source] = $items->sum('amount');
        }

        // Group by status
        $byStatus = [];
        foreach ($commissions->groupBy('status') as $status => $items) {
            $byStatus[$status] = $items->sum('amount');
        }

        // Get previous period commissions
        $previousTotal = 0;
        $previousCount = 0;

        if ($previousStart && $previousEnd) {
            $previousCommissions = DB::table('commissions')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$previousStart, $previousEnd])
                ->get();

            $previousTotal = $previousCommissions->sum('amount');
            $previousCount = $previousCommissions->count();
        }

        // Calculate change percentage
        $changePercentage = $previousTotal > 0
            ? round((($total - $previousTotal) / $previousTotal) * 100, 1)
            : 0;

        return [
            'total' => $total,
            'count' => $count,
            'average' => $average,
            'by_source' => $bySource,
            'by_status' => $byStatus,
            'previous_total' => $previousTotal,
            'previous_count' => $previousCount,
            'change_percentage' => $changePercentage
        ];
    }

    /**
     * Get referral metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $start
     * @param  \Carbon\Carbon  $end
     * @param  \Carbon\Carbon|null  $previousStart
     * @param  \Carbon\Carbon|null  $previousEnd
     * @return array
     */
    protected function getReferralMetrics(int $agentId, Carbon $start, Carbon $end, ?Carbon $previousStart, ?Carbon $previousEnd): array
    {
        // Get current period referrals
        $referrals = DB::table('referrals')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$start, $end])
            ->get();

        $total = $referrals->count();
        $converted = $referrals->where('status', 'converted')->count();
        $conversionRate = $total > 0 ? round(($converted / $total) * 100, 1) : 0;

        // Group by status
        $byStatus = [];
        foreach ($referrals->groupBy('status') as $status => $items) {
            $byStatus[$status] = $items->count();
        }

        // Get previous period referrals
        $previousTotal = 0;
        $previousConverted = 0;
        $previousConversionRate = 0;

        if ($previousStart && $previousEnd) {
            $previousReferrals = DB::table('referrals')
                ->where('agent_id', $agentId)
                ->whereBetween('created_at', [$previousStart, $previousEnd])
                ->get();

            $previousTotal = $previousReferrals->count();
            $previousConverted = $previousReferrals->where('status', 'converted')->count();
            $previousConversionRate = $previousTotal > 0 ? round(($previousConverted / $previousTotal) * 100, 1) : 0;
        }

        // Calculate change percentages
        $changePercentage = $previousTotal > 0
            ? round((($total - $previousTotal) / $previousTotal) * 100, 1)
            : 0;

        $conversionRateChange = $previousConversionRate > 0
            ? round((($conversionRate - $previousConversionRate) / $previousConversionRate) * 100, 1)
            : 0;

        return [
            'total' => $total,
            'converted' => $converted,
            'conversion_rate' => $conversionRate,
            'by_status' => $byStatus,
            'previous_total' => $previousTotal,
            'previous_converted' => $previousConverted,
            'previous_conversion_rate' => $previousConversionRate,
            'change_percentage' => $changePercentage,
            'conversion_rate_change' => $conversionRateChange
        ];
    }

    /**
     * Get goal metrics.
     *
     * @param  int  $agentId
     * @return array
     */
    protected function getGoalMetrics(int $agentId): array
    {
        $goals = AgentGoal::where('agent_id', $agentId)->get();

        $total = $goals->count();
        $active = $goals->where('status', 'active')->count();
        $achieved = $goals->where('status', 'achieved')->count();
        $expired = $goals->where('status', 'expired')->count();

        $completedGoals = $goals->whereIn('status', ['achieved', 'expired'])->count();
        $achievementRate = $completedGoals > 0
            ? round(($achieved / $completedGoals) * 100)
            : 0;

        return [
            'total' => $total,
            'active' => $active,
            'achieved' => $achieved,
            'expired' => $expired,
            'achievement_rate' => $achievementRate
        ];
    }

    /**
     * Get time series data.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $start
     * @param  \Carbon\Carbon  $end
     * @param  string  $period
     * @return array
     */
    protected function getTimeSeriesData(int $agentId, Carbon $start, Carbon $end, string $period): array
    {
        // Determine grouping format based on period
        $groupFormat = '%Y-%m-%d';
        $labelFormat = 'M d';

        if ($period === 'year') {
            $groupFormat = '%Y-%m';
            $labelFormat = 'M Y';
        } elseif ($period === 'all' && $end->diffInDays($start) > 365) {
            $groupFormat = '%Y-%m';
            $labelFormat = 'M Y';
        }

        // Get commissions by date
        $commissionsByDate = DB::table('commissions')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$start, $end])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('SUM(amount) as amount'))
            ->groupBy('date')
            ->get()
            ->pluck('amount', 'date')
            ->toArray();

        // Get referrals by date
        $referralsByDate = DB::table('referrals')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$start, $end])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();

        // Generate date range
        $dates = [];
        $current = $start->copy();

        while ($current <= $end) {
            $dateKey = $current->format($period === 'year' || ($period === 'all' && $end->diffInDays($start) > 365) ? 'Y-m' : 'Y-m-d');
            $dateLabel = $current->format($labelFormat);

            $dates[$dateKey] = $dateLabel;

            if ($period === 'year' || ($period === 'all' && $end->diffInDays($start) > 365)) {
                $current->addMonth();
            } else {
                $current->addDay();
            }
        }

        // Build time series data
        $labels = array_values($dates);
        $commissions = [];
        $referrals = [];

        foreach ($dates as $dateKey => $dateLabel) {
            $commissions[] = $commissionsByDate[$dateKey] ?? 0;
            $referrals[] = $referralsByDate[$dateKey] ?? 0;
        }

        return [
            'labels' => $labels,
            'commissions' => $commissions,
            'referrals' => $referrals
        ];
    }

    /**
     * Get goal progress history.
     *
     * @param  int  $goalId
     * @return array
     */
    public function getGoalProgressHistory(int $goalId): array
    {
        $progressHistory = AgentGoalProgress::where('goal_id', $goalId)
                                          ->orderBy('date', 'asc')
                                          ->get();

        return $progressHistory->map(function ($progress) {
            return [
                'date' => $progress->date,
                'value' => $progress->value,
                'percentage' => $progress->percentage
            ];
        })->toArray();
    }

    /**
     * Update goal progress.
     *
     * @param  int  $agentId
     * @param  string  $type
     * @param  float  $value
     * @return void
     */
    public function updateGoalProgress(int $agentId, string $type, float $value): void
    {
        // Get active goals of the specified type
        $goals = AgentGoal::where('agent_id', $agentId)
                         ->where('type', $type)
                         ->where('status', 'active')
                         ->get();

        foreach ($goals as $goal) {
            // Update progress value
            $newProgressValue = $goal->progress_value + $value;
            $newProgressPercentage = $goal->target_value > 0
                ? round(($newProgressValue / $goal->target_value) * 100)
                : 0;

            $goal->progress_value = $newProgressValue;
            $goal->progress_percentage = $newProgressPercentage;

            // Check if goal is achieved
            if ($newProgressValue >= $goal->target_value) {
                $goal->status = 'achieved';
                $goal->achieved_at = now();
            }

            $goal->save();

            // Record progress history
            AgentGoalProgress::create([
                'goal_id' => $goal->id,
                'date' => now()->format('Y-m-d'),
                'value' => $newProgressValue,
                'percentage' => $newProgressPercentage
            ]);
        }
    }

    /**
     * Check for expired goals.
     *
     * @return void
     */
    public function checkExpiredGoals(): void
    {
        $today = now()->format('Y-m-d');

        // Get active goals with target dates in the past
        $expiredGoals = AgentGoal::where('status', 'active')
                                ->where('target_date', '<', $today)
                                ->get();

        foreach ($expiredGoals as $goal) {
            $goal->status = 'expired';
            $goal->save();
        }
    }
}
