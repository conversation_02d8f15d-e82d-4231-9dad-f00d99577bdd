<?php

namespace App\Services;

use InvalidArgumentException;

class HeightConverterService
{
    public function inchesToFeetInches(int $inches): string
    {
        $feet = floor($inches / 12);
        $remainingInches = $inches % 12;
        return "{$feet}'{$remainingInches}\"";
    }

    public function feetInchesToInches(int $feet, int $inches): int
    {
        return ($feet * 12) + $inches;
    }

    public function stringToInches(string $heightString): int
    {
        preg_match("/(\d+)'(\d+)\"/", $heightString, $matches);
        if (count($matches) === 3) {
            return $this->feetInchesToInches((int)$matches[1], (int)$matches[2]);
        }
        throw new InvalidArgumentException("Invalid height format. Expected format: 5'11\"");
    }
}