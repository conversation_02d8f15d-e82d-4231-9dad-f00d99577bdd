<?php

namespace App\Services;

use App\Models\User;

class InsuranceService
{
    public function createUserInsurance(User $user, array $insuranceData): void
    {
        if ($insuranceData['add_later'] ?? false) {
            $user->insurances()->create([
                'provider_name' => '',
                'policy_number' => '',
                'plan_type' => '',
                'status' => 'add-later'
            ]);
            return;
        }

        $user->insurances()->create($insuranceData);
    }
}