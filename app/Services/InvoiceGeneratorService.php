<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use App\Notifications\InvoicePaymentNotification;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class InvoiceGeneratorService
{
    /**
     * Generate an invoice PDF for a transaction
     *
     * @param Transaction $transaction
     * @return array
     */
    public function generateInvoice(Transaction $transaction): array
    {
        try {
            // Load the user and payment method
            $user = $transaction->user;
            $paymentMethod = $user->paymentMethods()
                ->where('type', 'invoice')
                ->where('is_default', true)
                ->first();

            if (!$paymentMethod) {
                throw new \Exception('No invoice payment method found for this user');
            }

            // Generate the invoice number if not already set
            if (empty($transaction->transaction_id)) {
                $transaction->transaction_id = 'INV-' . Str::random(10);
                $transaction->save();
            }

            // Calculate due date based on payment terms
            $terms = $paymentMethod->invoice_payment_terms ?? 'net30';
            $days = match($terms) {
                'net15' => 15,
                'net30' => 30,
                'net45' => 45,
                'net60' => 60,
                'net90' => 90,
                default => 30
            };
            $dueDate = now()->addDays($days);

            // Get subscription details if available
            $subscription = $transaction->subscription;
            $planName = $subscription ? $subscription->plan->name : 'Custom Service';

            // Generate the PDF
            $pdf = Pdf::loadView('pdfs.invoice', [
                'transaction' => $transaction,
                'user' => $user,
                'paymentMethod' => $paymentMethod,
                'dueDate' => $dueDate,
                'planName' => $planName,
                'invoiceDate' => $transaction->created_at,
                'companyName' => config('app.name'),
                'companyAddress' => config('company.address'),
                'companyPhone' => config('company.phone'),
                'companyEmail' => config('company.email'),
            ]);

            // Save the PDF to storage
            $fileName = 'invoice_' . $transaction->transaction_id . '.pdf';
            $path = 'invoices/' . $user->id . '/' . $fileName;
            Storage::put('public/' . $path, $pdf->output());

            // Update transaction with invoice path
            $transaction->meta_data = array_merge($transaction->meta_data ?? [], [
                'invoice_path' => $path,
                'due_date' => $dueDate->format('Y-m-d'),
            ]);
            $transaction->save();

            // Send notification to user
            $user->notify(new InvoicePaymentNotification($transaction, 'public/' . $path));

            return [
                'success' => true,
                'message' => 'Invoice generated successfully',
                'path' => $path,
                'transaction' => $transaction
            ];
        } catch (\Exception $e) {
            Log::error('Error generating invoice: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to generate invoice: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get the full path to an invoice
     *
     * @param Transaction $transaction
     * @return string|null
     */
    public function getInvoicePath(Transaction $transaction): ?string
    {
        if (isset($transaction->meta_data['invoice_path'])) {
            return $transaction->meta_data['invoice_path'];
        }

        return null;
    }

    /**
     * Check if an invoice exists for a transaction
     *
     * @param Transaction $transaction
     * @return bool
     */
    public function invoiceExists(Transaction $transaction): bool
    {
        $path = $this->getInvoicePath($transaction);
        
        if (!$path) {
            return false;
        }

        return Storage::exists('public/' . $path);
    }
}
