<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class InvoicePaymentService
{
    protected $invoiceGeneratorService;

    public function __construct(InvoiceGeneratorService $invoiceGeneratorService)
    {
        $this->invoiceGeneratorService = $invoiceGeneratorService;
    }
    /**
     * Add an invoice payment method to a user's account
     *
     * @param User $user
     * @param array $invoiceData
     * @return array
     */
    public function addInvoicePaymentMethod(User $user, array $invoiceData): array
    {
        try {
            // Check if invoice payment method already exists
            $existingMethod = $user->paymentMethods()
                ->where('type', 'invoice')
                ->where('invoice_email', $invoiceData['email'])
                ->first();

            if ($existingMethod) {
                return [
                    'success' => true,
                    'message' => 'This invoice payment method is already on file',
                    'payment_method' => $existingMethod,
                    'is_new' => false
                ];
            }

            // Create invoice payment method
            $paymentMethod = new PaymentMethod([
                'user_id' => $user->id,
                'type' => 'invoice',
                'invoice_email' => $invoiceData['email'],
                'invoice_company_name' => $invoiceData['company_name'],
                'invoice_contact_name' => $invoiceData['contact_name'],
                'invoice_phone' => $invoiceData['phone'] ?? null,
                'invoice_billing_address' => $invoiceData['billing_address'] ?? null,
                'invoice_payment_terms' => $invoiceData['payment_terms'] ?? 'net30',
                'is_default' => $user->paymentMethods()->count() === 0 ? true : false
            ]);

            $paymentMethod->save();

            return [
                'success' => true,
                'message' => 'Invoice payment method added successfully',
                'payment_method' => $paymentMethod,
                'is_new' => true
            ];
        } catch (\Exception $e) {
            Log::error('Error adding invoice payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to add invoice payment method: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process an invoice payment
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processInvoicePayment(User $user, PaymentMethod $paymentMethod, float $amount, string $description = '', int $subscriptionId = null, bool $isDiscounted = false): array
    {
        try {
            // Create a transaction record
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'subscription_id' => $subscriptionId,
                'amount' => $amount,
                'is_discounted' => $isDiscounted,
                'currency' => 'USD',
                'transaction_id' => 'INV-' . Str::random(10),
                'payment_method' => 'invoice',
                'status' => 'pending',
                'meta_data' => [
                    'invoice_email' => $paymentMethod->invoice_email,
                    'invoice_company_name' => $paymentMethod->invoice_company_name,
                    'invoice_contact_name' => $paymentMethod->invoice_contact_name,
                    'invoice_payment_terms' => $paymentMethod->invoice_payment_terms,
                    'description' => $description,
                    'due_date' => now()->addDays($this->getPaymentTermDays($paymentMethod->invoice_payment_terms))->format('Y-m-d')
                ]
            ]);

            // Generate and send the invoice
            $invoiceResult = $this->invoiceGeneratorService->generateInvoice($transaction);

            if (!$invoiceResult['success']) {
                Log::warning("Failed to generate invoice for transaction {$transaction->id}: {$invoiceResult['message']}");
            }

            return [
                'success' => true,
                'transaction_id' => $transaction->transaction_id,
                'transaction' => $transaction,
                'invoice_path' => $invoiceResult['success'] ? $invoiceResult['path'] : null,
                'message' => 'Invoice payment processed successfully!'
            ];
        } catch (\Exception $e) {
            Log::error('Error processing invoice payment: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to process invoice payment: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get the number of days for a payment term
     *
     * @param string $paymentTerm
     * @return int
     */
    private function getPaymentTermDays(string $paymentTerm): int
    {
        switch ($paymentTerm) {
            case 'net15':
                return 15;
            case 'net45':
                return 45;
            case 'net60':
                return 60;
            case 'net90':
                return 90;
            case 'net30':
            default:
                return 30;
        }
    }
}
