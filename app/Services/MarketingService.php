<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentMarketingMaterial;
use App\Models\AgentMarketingUsage;
use App\Models\AgentLandingPage;
use App\Models\AgentLandingPageVisit;
use App\Models\AgentLandingPageConversion;
use App\Models\Referral;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MarketingService
{
    /**
     * Personalize content for an agent.
     *
     * @param  string  $content
     * @param  \App\Models\Agent  $agent
     * @return string
     */
    public function personalizeContent(string $content, Agent $agent): string
    {
        $user = $agent->user;
        
        $replacements = [
            '[AGENT_NAME]' => $user->name,
            '[AGENT_EMAIL]' => $user->email,
            '[AGENT_PHONE]' => $user->phone ?? 'your phone number',
            '[REFERRAL_CODE]' => $agent->referral_code,
            '[COMPANY_NAME]' => config('app.name'),
            '[CURRENT_DATE]' => now()->format('F j, Y'),
            '[CURRENT_MONTH]' => now()->format('F'),
            '[CURRENT_YEAR]' => now()->format('Y')
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $content);
    }
    
    /**
     * Get performance metrics for an agent.
     *
     * @param  int  $agentId
     * @param  string  $period
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    public function getPerformanceMetrics(int $agentId, string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        // Determine date range based on period
        $dates = $this->getDateRange($period, $startDate, $endDate);
        $startDate = $dates['start'];
        $endDate = $dates['end'];
        $previousStartDate = $dates['previous_start'];
        $previousEndDate = $dates['previous_end'];
        
        // Get material usage metrics
        $materialUsage = $this->getMaterialUsageMetrics($agentId, $startDate, $endDate);
        
        // Get landing page metrics
        $landingPageMetrics = $this->getLandingPageMetrics($agentId, $startDate, $endDate);
        
        // Get referral metrics
        $referralMetrics = $this->getReferralMetrics($agentId, $startDate, $endDate);
        
        // Get time series data
        $timeSeriesData = $this->getTimeSeriesData($agentId, $startDate, $endDate, $period);
        
        // Get comparison metrics
        $comparisonMetrics = $this->getComparisonMetrics(
            $agentId,
            $startDate,
            $endDate,
            $previousStartDate,
            $previousEndDate
        );
        
        return [
            'material_usage' => $materialUsage,
            'landing_pages' => $landingPageMetrics,
            'referrals' => $referralMetrics,
            'time_series' => $timeSeriesData,
            'comparison' => $comparisonMetrics
        ];
    }
    
    /**
     * Get date range based on period.
     *
     * @param  string  $period
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    protected function getDateRange(string $period, ?string $startDate = null, ?string $endDate = null): array
    {
        $now = Carbon::now();
        
        // If custom dates are provided, use them
        if ($startDate && $endDate) {
            $start = Carbon::parse($startDate)->startOfDay();
            $end = Carbon::parse($endDate)->endOfDay();
            
            $duration = $end->diffInDays($start);
            $previousStart = $start->copy()->subDays($duration);
            $previousEnd = $start->copy()->subDay();
            
            return [
                'start' => $start,
                'end' => $end,
                'previous_start' => $previousStart,
                'previous_end' => $previousEnd
            ];
        }
        
        // Otherwise, use the specified period
        switch ($period) {
            case 'week':
                $start = $now->copy()->startOfWeek();
                $end = $now->copy()->endOfWeek();
                $previousStart = $start->copy()->subWeek();
                $previousEnd = $start->copy()->subDay();
                break;
                
            case 'month':
                $start = $now->copy()->startOfMonth();
                $end = $now->copy()->endOfMonth();
                $previousStart = $start->copy()->subMonth();
                $previousEnd = $start->copy()->subDay();
                break;
                
            case 'quarter':
                $start = $now->copy()->startOfQuarter();
                $end = $now->copy()->endOfQuarter();
                $previousStart = $start->copy()->subQuarter();
                $previousEnd = $start->copy()->subDay();
                break;
                
            case 'year':
                $start = $now->copy()->startOfYear();
                $end = $now->copy()->endOfYear();
                $previousStart = $start->copy()->subYear();
                $previousEnd = $start->copy()->subDay();
                break;
                
            case 'all':
                $start = Carbon::parse('2000-01-01');
                $end = $now;
                $previousStart = null;
                $previousEnd = null;
                break;
                
            default:
                $start = $now->copy()->startOfMonth();
                $end = $now->copy()->endOfMonth();
                $previousStart = $start->copy()->subMonth();
                $previousEnd = $start->copy()->subDay();
                break;
        }
        
        return [
            'start' => $start,
            'end' => $end,
            'previous_start' => $previousStart,
            'previous_end' => $previousEnd
        ];
    }
    
    /**
     * Get material usage metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    protected function getMaterialUsageMetrics(int $agentId, Carbon $startDate, Carbon $endDate): array
    {
        // Get total usage counts
        $usageCounts = AgentMarketingUsage::where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('usage_type', DB::raw('count(*) as count'))
            ->groupBy('usage_type')
            ->get()
            ->pluck('count', 'usage_type')
            ->toArray();
        
        // Calculate totals
        $totalViews = $usageCounts['view'] ?? 0;
        $totalDownloads = $usageCounts['download'] ?? 0;
        
        // Calculate shares
        $totalShares = 0;
        $sharesByPlatform = [];
        
        foreach ($usageCounts as $type => $count) {
            if (strpos($type, 'share_') === 0) {
                $platform = substr($type, 6);
                $sharesByPlatform[$platform] = $count;
                $totalShares += $count;
            }
        }
        
        // Get usage by material type
        $usageByType = [];
        $materialTypes = ['social', 'email', 'print', 'video'];
        
        foreach ($materialTypes as $type) {
            $materialIds = AgentMarketingMaterial::where('type', $type)
                ->pluck('id')
                ->toArray();
            
            if (empty($materialIds)) {
                $usageByType[$type] = [
                    'views' => 0,
                    'downloads' => 0,
                    'shares' => 0
                ];
                continue;
            }
            
            $typeUsage = AgentMarketingUsage::where('agent_id', $agentId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereIn('marketing_material_id', $materialIds)
                ->select('usage_type', DB::raw('count(*) as count'))
                ->groupBy('usage_type')
                ->get()
                ->pluck('count', 'usage_type')
                ->toArray();
            
            $typeViews = $typeUsage['view'] ?? 0;
            $typeDownloads = $typeUsage['download'] ?? 0;
            
            $typeShares = 0;
            foreach ($typeUsage as $usageType => $count) {
                if (strpos($usageType, 'share_') === 0) {
                    $typeShares += $count;
                }
            }
            
            $usageByType[$type] = [
                'views' => $typeViews,
                'downloads' => $typeDownloads,
                'shares' => $typeShares
            ];
        }
        
        return [
            'total_views' => $totalViews,
            'total_downloads' => $totalDownloads,
            'total_shares' => $totalShares,
            'by_type' => $usageByType,
            'by_platform' => $sharesByPlatform
        ];
    }
    
    /**
     * Get landing page metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    protected function getLandingPageMetrics(int $agentId, Carbon $startDate, Carbon $endDate): array
    {
        // Get landing pages for this agent
        $landingPages = AgentLandingPage::where('agent_id', $agentId)->get();
        
        if ($landingPages->isEmpty()) {
            return [
                'total_visits' => 0,
                'total_conversions' => 0,
                'overall_conversion_rate' => 0,
                'by_page' => []
            ];
        }
        
        $landingPageIds = $landingPages->pluck('id')->toArray();
        
        // Get total visits
        $totalVisits = AgentLandingPageVisit::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        // Get total conversions
        $totalConversions = AgentLandingPageConversion::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        // Calculate overall conversion rate
        $overallConversionRate = $totalVisits > 0 ? round(($totalConversions / $totalVisits) * 100, 1) : 0;
        
        // Get metrics by page
        $byPage = [];
        
        foreach ($landingPages as $page) {
            $visits = AgentLandingPageVisit::where('landing_page_id', $page->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
            
            $conversions = AgentLandingPageConversion::where('landing_page_id', $page->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
            
            $conversionRate = $visits > 0 ? round(($conversions / $visits) * 100, 1) : 0;
            
            $byPage[] = [
                'id' => $page->id,
                'title' => $page->title,
                'visits' => $visits,
                'conversions' => $conversions,
                'conversion_rate' => $conversionRate
            ];
        }
        
        // Sort by visits (descending)
        usort($byPage, function ($a, $b) {
            return $b['visits'] - $a['visits'];
        });
        
        return [
            'total_visits' => $totalVisits,
            'total_conversions' => $totalConversions,
            'overall_conversion_rate' => $overallConversionRate,
            'by_page' => $byPage
        ];
    }
    
    /**
     * Get referral metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    protected function getReferralMetrics(int $agentId, Carbon $startDate, Carbon $endDate): array
    {
        // Get total referrals
        $totalReferrals = Referral::where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        // Get landing page conversions
        $landingPageIds = AgentLandingPage::where('agent_id', $agentId)->pluck('id')->toArray();
        $totalConversions = AgentLandingPageConversion::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        // Calculate conversion to referral rate
        $conversionToReferralRate = $totalConversions > 0 ? round(($totalReferrals / $totalConversions) * 100, 1) : 0;
        
        // Get referrals by source
        $referralsBySource = Referral::where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('source', DB::raw('count(*) as count'))
            ->groupBy('source')
            ->get()
            ->pluck('count', 'source')
            ->toArray();
        
        return [
            'total_referrals' => $totalReferrals,
            'conversion_to_referral_rate' => $conversionToReferralRate,
            'by_source' => $referralsBySource
        ];
    }
    
    /**
     * Get time series data.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @param  string  $period
     * @return array
     */
    protected function getTimeSeriesData(int $agentId, Carbon $startDate, Carbon $endDate, string $period): array
    {
        // Determine grouping format based on period
        $groupFormat = '%Y-%m-%d';
        $labelFormat = 'M d';
        
        if ($period === 'year') {
            $groupFormat = '%Y-%m';
            $labelFormat = 'M Y';
        } elseif ($period === 'all' && $endDate->diffInDays($startDate) > 365) {
            $groupFormat = '%Y-%m';
            $labelFormat = 'M Y';
        }
        
        // Get landing page IDs for this agent
        $landingPageIds = AgentLandingPage::where('agent_id', $agentId)->pluck('id')->toArray();
        
        // Get visits by date
        $visitsByDate = AgentLandingPageVisit::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();
        
        // Get conversions by date
        $conversionsByDate = AgentLandingPageConversion::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();
        
        // Get referrals by date
        $referralsByDate = Referral::where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw("DATE_FORMAT(created_at, '{$groupFormat}') as date"), DB::raw('count(*) as count'))
            ->groupBy('date')
            ->get()
            ->pluck('count', 'date')
            ->toArray();
        
        // Generate date range
        $dates = [];
        $current = $startDate->copy();
        
        while ($current <= $endDate) {
            $dateKey = $current->format($period === 'year' || ($period === 'all' && $endDate->diffInDays($startDate) > 365) ? 'Y-m' : 'Y-m-d');
            $dateLabel = $current->format($labelFormat);
            
            $dates[$dateKey] = $dateLabel;
            
            if ($period === 'year' || ($period === 'all' && $endDate->diffInDays($startDate) > 365)) {
                $current->addMonth();
            } else {
                $current->addDay();
            }
        }
        
        // Build time series data
        $labels = array_values($dates);
        $visits = [];
        $conversions = [];
        $referrals = [];
        
        foreach ($dates as $dateKey => $dateLabel) {
            $visits[] = $visitsByDate[$dateKey] ?? 0;
            $conversions[] = $conversionsByDate[$dateKey] ?? 0;
            $referrals[] = $referralsByDate[$dateKey] ?? 0;
        }
        
        return [
            'labels' => $labels,
            'visits' => $visits,
            'conversions' => $conversions,
            'referrals' => $referrals
        ];
    }
    
    /**
     * Get comparison metrics.
     *
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @param  \Carbon\Carbon|null  $previousStartDate
     * @param  \Carbon\Carbon|null  $previousEndDate
     * @return array
     */
    protected function getComparisonMetrics(int $agentId, Carbon $startDate, Carbon $endDate, ?Carbon $previousStartDate, ?Carbon $previousEndDate): array
    {
        if (!$previousStartDate || !$previousEndDate) {
            return [
                'commissions' => [
                    'current' => 0,
                    'previous' => 0,
                    'change' => 0
                ],
                'referrals' => [
                    'current' => 0,
                    'previous' => 0,
                    'change' => 0
                ],
                'conversion_rate' => [
                    'current' => 0,
                    'previous' => 0,
                    'change' => 0
                ]
            ];
        }
        
        // Get landing page IDs for this agent
        $landingPageIds = AgentLandingPage::where('agent_id', $agentId)->pluck('id')->toArray();
        
        // Current period metrics
        $currentCommissions = DB::table('commissions')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
        
        $currentReferrals = Referral::where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        $currentVisits = AgentLandingPageVisit::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        $currentConversions = AgentLandingPageConversion::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
        
        $currentConversionRate = $currentVisits > 0 ? round(($currentConversions / $currentVisits) * 100, 1) : 0;
        
        // Previous period metrics
        $previousCommissions = DB::table('commissions')
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');
        
        $previousReferrals = Referral::where('agent_id', $agentId)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();
        
        $previousVisits = AgentLandingPageVisit::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();
        
        $previousConversions = AgentLandingPageConversion::whereIn('landing_page_id', $landingPageIds)
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();
        
        $previousConversionRate = $previousVisits > 0 ? round(($previousConversions / $previousVisits) * 100, 1) : 0;
        
        // Calculate changes
        $commissionsChange = $previousCommissions > 0 ? round((($currentCommissions - $previousCommissions) / $previousCommissions) * 100, 1) : 0;
        $referralsChange = $previousReferrals > 0 ? round((($currentReferrals - $previousReferrals) / $previousReferrals) * 100, 1) : 0;
        $conversionRateChange = $previousConversionRate > 0 ? round((($currentConversionRate - $previousConversionRate) / $previousConversionRate) * 100, 1) : 0;
        
        return [
            'commissions' => [
                'current' => $currentCommissions,
                'previous' => $previousCommissions,
                'change' => $commissionsChange
            ],
            'referrals' => [
                'current' => $currentReferrals,
                'previous' => $previousReferrals,
                'change' => $referralsChange
            ],
            'conversion_rate' => [
                'current' => $currentConversionRate,
                'previous' => $previousConversionRate,
                'change' => $conversionRateChange
            ]
        ];
    }
}
