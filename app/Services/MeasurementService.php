<?php

namespace App\Services;

use App\Models\User;

class MeasurementService
{
    public function __construct(private HeightConverterService $heightConverter) {}

    public function updateUserMeasurements(User $user, array $measurementData): void
    {
        $height = $this->heightConverter->feetInchesToInches(
            $measurementData['height_ft'],
            $measurementData['height_in']
        );

        $measurements = [
            'height' => $height,
            'weight' => $measurementData['weight'],
            'measured_at' => now(),
        ];

        if (!$user->measurements()->exists()) {
            $user->measurements()->create($measurements);
        } else {
            $user->measurements()->update($measurements);
        }
    }

    /**
     * Create a measurement record for a user
     *
     * @param User $user
     * @param float|int $height
     * @param float|int $weight
     * @param string $heightUnit
     * @param string $weightUnit
     * @return void
     */
    public function createMeasurement(User $user, $height, $weight, string $heightUnit = 'in', string $weightUnit = 'lb'): void
    {
        // Convert height to inches if needed
        if ($heightUnit === 'ft') {
            $height = $height * 12;
        } elseif ($heightUnit === 'cm') {
            $height = $height / 2.54;
        }

        // Convert weight to pounds if needed
        if ($weightUnit === 'kg') {
            $weight = $weight * 2.20462;
        }

        $measurements = [
            'height' => $height,
            'weight' => $weight,
            'measured_at' => now(),
        ];

        if (!$user->measurements()->exists()) {
            $user->measurements()->create($measurements);
        } else {
            $user->measurements()->update($measurements);
        }
    }

    /**
     * Calculate BMI for given height and weight
     *
     * @param float|int $height Height in inches
     * @param float|int $weight Weight in pounds
     * @return float BMI value
     */
    public function calculateBMI($height, $weight): float
    {
        // BMI formula: (weight in pounds * 703) / (height in inches * height in inches)
        if ($height <= 0) {
            return 0;
        }

        return ($weight * 703) / ($height * $height);
    }

    /**
     * Get BMI category and color for display
     *
     * @param float $bmi BMI value
     * @return array Array with category and color
     */
    public function getBMICategory(float $bmi): array
    {
        $category = '';
        $color = '';

        if ($bmi < 18.5) {
            $category = 'Underweight';
            $color = 'text-blue-600';
        } elseif ($bmi >= 18.5 && $bmi < 25) {
            $category = 'Normal weight';
            $color = 'text-green-600';
        } elseif ($bmi >= 25 && $bmi < 30) {
            $category = 'Overweight';
            $color = 'text-yellow-600';
        } else {
            $category = 'Obese';
            $color = 'text-red-600';
        }

        return [
            'category' => $category,
            'color' => $color
        ];
    }
}
