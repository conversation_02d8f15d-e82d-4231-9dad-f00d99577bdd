<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentMessage;
use App\Models\AgentAnnouncement;
use App\Models\AgentAnnouncementRead;
use App\Models\AgentSupportTicket;
use App\Models\AgentSupportTicketReply;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MessagingService
{
    /**
     * Get unread message count for an agent.
     *
     * @param  int  $userId
     * @return int
     */
    public function getUnreadMessageCount(int $userId): int
    {
        return AgentMessage::where('recipient_id', $userId)
                          ->where('is_read', false)
                          ->count();
    }
    
    /**
     * Get unread announcement count for an agent.
     *
     * @param  int  $agentId
     * @return int
     */
    public function getUnreadAnnouncementCount(int $agentId): int
    {
        // Get read announcement IDs
        $readAnnouncementIds = AgentAnnouncementRead::where('agent_id', $agentId)
                                                   ->pluck('announcement_id')
                                                   ->toArray();
        
        // Count unread announcements
        return AgentAnnouncement::where('is_active', true)
                               ->where('publish_at', '<=', now())
                               ->where(function ($q) {
                                   $q->whereNull('expires_at')
                                     ->orWhere('expires_at', '>=', now());
                               })
                               ->whereNotIn('id', $readAnnouncementIds)
                               ->count();
    }
    
    /**
     * Get open support ticket count for an agent.
     *
     * @param  int  $agentId
     * @return int
     */
    public function getOpenSupportTicketCount(int $agentId): int
    {
        return AgentSupportTicket::where('agent_id', $agentId)
                                ->where('status', 'open')
                                ->count();
    }
    
    /**
     * Get notification summary for an agent.
     *
     * @param  int  $userId
     * @param  int  $agentId
     * @return array
     */
    public function getNotificationSummary(int $userId, int $agentId): array
    {
        $unreadMessages = $this->getUnreadMessageCount($userId);
        $unreadAnnouncements = $this->getUnreadAnnouncementCount($agentId);
        $openTickets = $this->getOpenSupportTicketCount($agentId);
        
        return [
            'unread_messages' => $unreadMessages,
            'unread_announcements' => $unreadAnnouncements,
            'open_tickets' => $openTickets,
            'total' => $unreadMessages + $unreadAnnouncements + $openTickets
        ];
    }
    
    /**
     * Get recent messages for an agent.
     *
     * @param  int  $userId
     * @param  int  $limit
     * @return array
     */
    public function getRecentMessages(int $userId, int $limit = 5): array
    {
        $messages = AgentMessage::with('sender')
                               ->where('recipient_id', $userId)
                               ->orderBy('created_at', 'desc')
                               ->limit($limit)
                               ->get();
        
        return $messages->map(function ($message) {
            return [
                'id' => $message->id,
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'role' => $message->sender->roles->first() ? $message->sender->roles->first()->name : 'user'
                ],
                'subject' => $message->subject,
                'is_read' => (bool) $message->is_read,
                'created_at' => $message->created_at
            ];
        })->toArray();
    }
    
    /**
     * Get recent announcements for an agent.
     *
     * @param  int  $agentId
     * @param  int  $limit
     * @return array
     */
    public function getRecentAnnouncements(int $agentId, int $limit = 5): array
    {
        // Get read announcement IDs
        $readAnnouncementIds = AgentAnnouncementRead::where('agent_id', $agentId)
                                                   ->pluck('announcement_id')
                                                   ->toArray();
        
        $announcements = AgentAnnouncement::where('is_active', true)
                                         ->where('publish_at', '<=', now())
                                         ->where(function ($q) {
                                             $q->whereNull('expires_at')
                                               ->orWhere('expires_at', '>=', now());
                                         })
                                         ->orderBy('priority', 'desc')
                                         ->orderBy('publish_at', 'desc')
                                         ->limit($limit)
                                         ->get();
        
        return $announcements->map(function ($announcement) use ($readAnnouncementIds) {
            return [
                'id' => $announcement->id,
                'title' => $announcement->title,
                'priority' => $announcement->priority,
                'is_read' => in_array($announcement->id, $readAnnouncementIds),
                'publish_at' => $announcement->publish_at
            ];
        })->toArray();
    }
    
    /**
     * Get recent support tickets for an agent.
     *
     * @param  int  $agentId
     * @param  int  $limit
     * @return array
     */
    public function getRecentSupportTickets(int $agentId, int $limit = 5): array
    {
        $tickets = AgentSupportTicket::where('agent_id', $agentId)
                                    ->orderBy('created_at', 'desc')
                                    ->limit($limit)
                                    ->get();
        
        return $tickets->map(function ($ticket) {
            return [
                'id' => $ticket->id,
                'subject' => $ticket->subject,
                'status' => $ticket->status,
                'priority' => $ticket->priority,
                'created_at' => $ticket->created_at
            ];
        })->toArray();
    }
    
    /**
     * Get communication statistics for an agent.
     *
     * @param  int  $userId
     * @param  int  $agentId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return array
     */
    public function getCommunicationStats(int $userId, int $agentId, Carbon $startDate, Carbon $endDate): array
    {
        // Messages sent
        $messagesSent = AgentMessage::where('sender_id', $userId)
                                   ->whereBetween('created_at', [$startDate, $endDate])
                                   ->count();
        
        // Messages received
        $messagesReceived = AgentMessage::where('recipient_id', $userId)
                                       ->whereBetween('created_at', [$startDate, $endDate])
                                       ->count();
        
        // Announcements read
        $announcementsRead = AgentAnnouncementRead::where('agent_id', $agentId)
                                                 ->whereBetween('read_at', [$startDate, $endDate])
                                                 ->count();
        
        // Support tickets created
        $ticketsCreated = AgentSupportTicket::where('agent_id', $agentId)
                                           ->whereBetween('created_at', [$startDate, $endDate])
                                           ->count();
        
        // Support ticket replies
        $ticketReplies = AgentSupportTicketReply::whereHas('ticket', function ($query) use ($agentId) {
                                                    $query->where('agent_id', $agentId);
                                                })
                                               ->where('user_id', $userId)
                                               ->whereBetween('created_at', [$startDate, $endDate])
                                               ->count();
        
        // Average response time (in hours)
        $avgResponseTime = $this->calculateAverageResponseTime($userId, $startDate, $endDate);
        
        return [
            'messages_sent' => $messagesSent,
            'messages_received' => $messagesReceived,
            'announcements_read' => $announcementsRead,
            'tickets_created' => $ticketsCreated,
            'ticket_replies' => $ticketReplies,
            'avg_response_time_hours' => $avgResponseTime
        ];
    }
    
    /**
     * Calculate average response time for messages.
     *
     * @param  int  $userId
     * @param  \Carbon\Carbon  $startDate
     * @param  \Carbon\Carbon  $endDate
     * @return float
     */
    protected function calculateAverageResponseTime(int $userId, Carbon $startDate, Carbon $endDate): float
    {
        // Get messages that were replied to
        $messages = AgentMessage::where('recipient_id', $userId)
                               ->where('is_read', true)
                               ->whereBetween('created_at', [$startDate, $endDate])
                               ->get();
        
        if ($messages->isEmpty()) {
            return 0;
        }
        
        $totalResponseTime = 0;
        $messageCount = 0;
        
        foreach ($messages as $message) {
            // Find a reply to this message
            $reply = AgentMessage::where('sender_id', $userId)
                                ->where('recipient_id', $message->sender_id)
                                ->where('subject', 'like', 'Re: ' . $message->subject)
                                ->where('created_at', '>', $message->created_at)
                                ->orderBy('created_at', 'asc')
                                ->first();
            
            if ($reply) {
                $responseTime = $reply->created_at->diffInHours($message->created_at);
                $totalResponseTime += $responseTime;
                $messageCount++;
            }
        }
        
        return $messageCount > 0 ? round($totalResponseTime / $messageCount, 1) : 0;
    }
}
