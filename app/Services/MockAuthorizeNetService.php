<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;

class MockAuthorizeNetService
{
    public function chargeCreditCard($amount, User $user, $subscriptionId = null)
    {
        // Create a transaction record
        $transaction = new Transaction([
            'user_id' => $user->id,
            'subscription_id' => $subscriptionId,
            'amount' => $amount,
            'payment_method' => 'credit_card',
        ]);

        // Simulate a declined transaction
        if ($amount > 1000) {
            $errorMessage = 'Mock charge declined: Amount too high';
            $transaction->error_message = $errorMessage;
            $transaction->save();

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        }

        // Simulate a network error
        if (rand(1, 10) === 1) {
            $errorMessage = 'Mock network error';
            $transaction->error_message = $errorMessage;

            return [
                'success' => false,
                'error' => $errorMessage
            ];
        }

        // Simulate a successful transaction
        if ($amount > 0) {
            $transactionId = 'MOCK_' . uniqid();
            $transaction->status = 'success';
            $transaction->transaction_id = $transactionId;
            $transaction->save();

            return [
                'success' => true,
                'transaction_id' => $transactionId
            ];
        }

        // Simulate a failed transaction
        $errorMessage = 'Mock charge failed';
        $transaction->error_message = $errorMessage;
        $transaction->save();

        return [
            'success' => false,
            'error' => $errorMessage
        ];
    }
}