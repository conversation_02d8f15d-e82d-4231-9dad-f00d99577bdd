<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Models\User;
use App\Services\Api\AchPaymentService;
use App\Services\Api\AuthorizeNetService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentMethodService
{
    protected $authorizeNetService;
    protected $achPaymentService;
    protected $invoicePaymentService;

    public function __construct(
        AuthorizeNetService $authorizeNetService,
        AchPaymentService $achPaymentService,
        InvoicePaymentService $invoicePaymentService
    ) {
        $this->authorizeNetService = $authorizeNetService;
        $this->achPaymentService = $achPaymentService;
        $this->invoicePaymentService = $invoicePaymentService;
    }

    /**
     * Add a credit card payment method
     *
     * @param User $user
     * @param string $cardNumber
     * @param string $expirationMonth
     * @param string $expirationYear
     * @param string $cvv
     * @return array
     */
    public function addCreditCardPaymentMethod(User $user, string $cardNumber, string $expirationMonth, string $expirationYear, string $cvv): array
    {
        try {
            // Add card using AuthorizeNetService
            $cardResult = $this->authorizeNetService->addCreditCard(
                $user,
                $cardNumber,
                $expirationMonth,
                $expirationYear,
                $cvv
            );

            if (!$cardResult['success']) {
                throw new \Exception($cardResult['message'] ?? 'Failed to add credit card');
            }

            // Check if payment method already exists
            $existingMethod = $user->paymentMethods()
                ->where('type', 'credit_card')
                ->where('cc_last_four', substr($cardNumber, -4))
                ->first();

            if ($existingMethod) {
                return [
                    'success' => true,
                    'message' => 'This credit card is already on file',
                    'payment_method' => $existingMethod,
                    'is_new' => false
                ];
            }

            // Create payment method record in database
            $paymentMethod = new PaymentMethod([
                'user_id' => $user->id,
                'type' => 'credit_card',
                'cc_last_four' => substr($cardNumber, -4),
                'cc_brand' => $cardResult['brand'],
                'cc_expiration_month' => $expirationMonth,
                'cc_expiration_year' => $expirationYear,
                'cc_token' => $cardResult['payment_profile_id'],
                'is_default' => $user->paymentMethods()->count() === 0 ? true : false
            ]);

            $paymentMethod->save();

            return [
                'success' => true,
                'message' => 'Credit card added successfully',
                'payment_method' => $paymentMethod,
                'is_new' => true
            ];
        } catch (\Exception $e) {
            Log::error('Error adding credit card payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to add credit card: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Add an ACH payment method
     *
     * @param User $user
     * @param string $accountName
     * @param string $accountType
     * @param string $routingNumber
     * @param string $accountNumber
     * @param string $bankName
     * @return array
     */
    public function addAchPaymentMethod(User $user, string $accountName, string $accountType, string $routingNumber, string $accountNumber, string $bankName): array
    {
        try {
            // Add ACH using AchPaymentService
            $achResult = $this->achPaymentService->addAchPaymentMethod(
                $user,
                $accountName,
                $accountType,
                $routingNumber,
                $accountNumber,
                $bankName
            );

            if (!$achResult['success']) {
                throw new \Exception($achResult['message'] ?? 'Failed to add ACH payment method');
            }

            // Check if payment method already exists
            $existingMethod = $user->paymentMethods()
                ->where('type', 'ach')
                ->where('ach_routing_number_last_four', substr($routingNumber, -4))
                ->where('ach_account_number_last_four', substr($accountNumber, -4))
                ->first();

            if ($existingMethod) {
                return [
                    'success' => true,
                    'message' => 'This bank account is already on file',
                    'payment_method' => $existingMethod,
                    'is_new' => false
                ];
            }

            // Create payment method record in database
            $paymentMethod = new PaymentMethod([
                'user_id' => $user->id,
                'type' => 'ach',
                'ach_account_name' => $accountName,
                'ach_account_type' => $accountType,
                'ach_routing_number_last_four' => substr($routingNumber, -4),
                'ach_account_number_last_four' => substr($accountNumber, -4),
                'ach_token' => $achResult['payment_profile_id'],
                'is_default' => $user->paymentMethods()->count() === 0 ? true : false
            ]);

            $paymentMethod->save();

            // If verification is required, initiate it
            $verificationRequired = config('services.authorize_net.ach_verification_required', true);
            $verificationResult = null;

            if ($verificationRequired) {
                $verificationResult = $this->achPaymentService->initiateVerification($paymentMethod);

                if (!$verificationResult['success']) {
                    Log::warning("Failed to initiate ACH verification: " . ($verificationResult['message'] ?? 'Unknown error'));
                }
            }

            return [
                'success' => true,
                'message' => 'Bank account added successfully',
                'payment_method' => $paymentMethod,
                'is_new' => true,
                'verification_required' => $verificationRequired,
                'verification_result' => $verificationResult
            ];
        } catch (\Exception $e) {
            Log::error('Error adding ACH payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to add bank account: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Add an invoice payment method
     *
     * @param User $user
     * @param array $invoiceData
     * @return array
     */
    public function addInvoicePaymentMethod(User $user, array $invoiceData): array
    {
        return $this->invoicePaymentService->addInvoicePaymentMethod($user, $invoiceData);
    }

    /**
     * Verify an ACH payment method with micro-deposit amounts
     *
     * @param PaymentMethod $paymentMethod
     * @param float $amount1
     * @param float $amount2
     * @return array
     */
    public function verifyAchPaymentMethod(PaymentMethod $paymentMethod, float $amount1, float $amount2): array
    {
        try {
            // Ensure this is an ACH payment method
            if (!$paymentMethod->isAch()) {
                return [
                    'success' => false,
                    'message' => 'This is not an ACH payment method'
                ];
            }

            // Verify the micro-deposits
            $result = $this->achPaymentService->verifyMicroDeposits($paymentMethod, $amount1, $amount2);

            return $result;
        } catch (\Exception $e) {
            Log::error('Error verifying ACH payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to verify ACH payment method: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get verification status for an ACH payment method
     *
     * @param PaymentMethod $paymentMethod
     * @return array
     */
    public function getAchVerificationStatus(PaymentMethod $paymentMethod): array
    {
        try {
            // Ensure this is an ACH payment method
            if (!$paymentMethod->isAch()) {
                return [
                    'success' => false,
                    'message' => 'This is not an ACH payment method',
                    'status' => 'not_applicable'
                ];
            }

            // Get verification status
            return $this->achPaymentService->getVerificationStatus($paymentMethod);
        } catch (\Exception $e) {
            Log::error('Error getting ACH verification status: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to get verification status: ' . $e->getMessage(),
                'status' => 'error'
            ];
        }
    }

    /**
     * Set a payment method as default
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @return array
     */
    public function setDefaultPaymentMethod(User $user, PaymentMethod $paymentMethod): array
    {
        try {
            DB::beginTransaction();

            // Ensure the payment method belongs to the user
            if ($paymentMethod->user_id !== $user->id) {
                throw new \Exception('Payment method does not belong to this user');
            }

            // Reset all payment methods to non-default
            $user->paymentMethods()->update(['is_default' => false]);

            // Set the specified payment method as default
            $paymentMethod->is_default = true;
            $paymentMethod->save();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Default payment method updated successfully'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error setting default payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to set default payment method: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Remove a payment method
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @return array
     */
    public function removePaymentMethod(User $user, PaymentMethod $paymentMethod): array
    {
        try {
            // Ensure the payment method belongs to the user
            if ($paymentMethod->user_id !== $user->id) {
                throw new \Exception('Payment method does not belong to this user');
            }

            // If this is the default payment method, we need to set another one as default
            if ($paymentMethod->is_default) {
                $newDefault = $user->paymentMethods()
                    ->where('id', '!=', $paymentMethod->id)
                    ->first();

                if ($newDefault) {
                    $newDefault->is_default = true;
                    $newDefault->save();
                }
            }

            // Soft delete the payment method
            $paymentMethod->delete();

            return [
                'success' => true,
                'message' => 'Payment method removed successfully'
            ];
        } catch (\Exception $e) {
            Log::error('Error removing payment method: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to remove payment method: ' . $e->getMessage()
            ];
        }
    }
}
