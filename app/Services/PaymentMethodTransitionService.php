<?php

namespace App\Services;

use App\Models\CreditCard;
use App\Models\PaymentMethod;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentMethodTransitionService
{
    /**
     * Get payment methods for a user, combining both credit_cards and payment_methods tables.
     * This is a transitional method to support both tables during migration.
     *
     * @param User $user
     * @return array
     */
    public function getPaymentMethodsForUser(User $user): array
    {
        $paymentMethods = [];
        
        // Get payment methods from the new table
        $newPaymentMethods = $user->paymentMethods()->get();
        
        foreach ($newPaymentMethods as $method) {
            $paymentMethods[] = [
                'id' => $method->id,
                'type' => $method->type,
                'display_name' => $method->getDisplayName(),
                'is_default' => (bool) $method->is_default,
                'details' => $this->getPaymentMethodDetails($method),
                'source' => 'payment_methods',
            ];
        }
        
        // Get credit cards from the old table that don't have a corresponding payment method
        $creditCards = $user->creditCards()->get();
        
        foreach ($creditCards as $card) {
            // Check if this card already exists in the payment_methods table
            $exists = $newPaymentMethods->contains(function ($method) use ($card) {
                return $method->type === 'credit_card' && 
                       $method->cc_last_four === $card->last_four && 
                       $method->cc_token === $card->token;
            });
            
            if (!$exists) {
                $paymentMethods[] = [
                    'id' => 'cc_' . $card->id, // Prefix to distinguish from payment_methods IDs
                    'type' => 'credit_card',
                    'display_name' => "{$card->brand} ending in {$card->last_four}",
                    'is_default' => (bool) $card->is_default,
                    'details' => [
                        'brand' => $card->brand,
                        'last_four' => $card->last_four,
                        'expiration_month' => $card->expiration_month,
                        'expiration_year' => $card->expiration_year,
                    ],
                    'source' => 'credit_cards',
                ];
            }
        }
        
        return $paymentMethods;
    }
    
    /**
     * Process a payment using either a payment method or a credit card.
     * This is a transitional method to support both tables during migration.
     *
     * @param string $id The ID of the payment method or credit card (prefixed with 'cc_')
     * @param User $user
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processPayment(string $id, User $user, float $amount, string $description = '', int $subscriptionId = null, bool $isDiscounted = false): array
    {
        // Check if this is a credit card from the old table
        if (strpos($id, 'cc_') === 0) {
            $creditCardId = substr($id, 3); // Remove 'cc_' prefix
            $creditCard = CreditCard::find($creditCardId);
            
            if (!$creditCard || $creditCard->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Invalid credit card'
                ];
            }
            
            // Process using the old credit card service
            // This is just a placeholder - you'll need to replace with your actual credit card processing code
            return app(CreditCardService::class)->processPayment(
                $user,
                $creditCard,
                $amount,
                $description,
                $subscriptionId,
                $isDiscounted
            );
        } else {
            // This is a payment method from the new table
            $paymentMethod = PaymentMethod::find($id);
            
            if (!$paymentMethod || $paymentMethod->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Invalid payment method'
                ];
            }
            
            // Process using the new payment processor service
            return app(PaymentProcessorService::class)->processPayment(
                $user,
                $paymentMethod,
                $amount,
                $description,
                $subscriptionId,
                $isDiscounted
            );
        }
    }
    
    /**
     * Get payment method details based on type
     *
     * @param PaymentMethod $paymentMethod
     * @return array
     */
    private function getPaymentMethodDetails(PaymentMethod $paymentMethod): array
    {
        if ($paymentMethod->isCreditCard()) {
            return [
                'brand' => $paymentMethod->cc_brand,
                'last_four' => $paymentMethod->cc_last_four,
                'expiration_month' => $paymentMethod->cc_expiration_month,
                'expiration_year' => $paymentMethod->cc_expiration_year,
            ];
        } elseif ($paymentMethod->isAch()) {
            return [
                'account_name' => $paymentMethod->ach_account_name,
                'account_type' => $paymentMethod->ach_account_type,
                'routing_number_last_four' => $paymentMethod->ach_routing_number_last_four,
                'account_number_last_four' => $paymentMethod->ach_account_number_last_four,
            ];
        } elseif ($paymentMethod->isInvoice()) {
            return [
                'email' => $paymentMethod->invoice_email,
                'company_name' => $paymentMethod->invoice_company_name,
                'contact_name' => $paymentMethod->invoice_contact_name,
                'payment_terms' => $paymentMethod->invoice_payment_terms,
            ];
        }

        return [];
    }
}
