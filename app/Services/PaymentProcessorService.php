<?php

namespace App\Services;

use App\Models\PaymentMethod;
use App\Models\Transaction;
use App\Models\User;
use App\Services\Api\AchPaymentService;
use App\Services\Api\AuthorizeNetService;
use Illuminate\Support\Facades\Log;

class PaymentProcessorService
{
    protected $authorizeNetService;
    protected $achPaymentService;
    protected $invoicePaymentService;

    public function __construct(
        AuthorizeNetService $authorizeNetService,
        AchPaymentService $achPaymentService,
        InvoicePaymentService $invoicePaymentService
    ) {
        $this->authorizeNetService = $authorizeNetService;
        $this->achPaymentService = $achPaymentService;
        $this->invoicePaymentService = $invoicePaymentService;
    }

    /**
     * Process a payment using the specified payment method
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processPayment(User $user, PaymentMethod $paymentMethod, float $amount, string $description = '', int $subscriptionId = null, bool $isDiscounted = false): array
    {
        try {
            // Ensure the payment method belongs to the user
            if ($paymentMethod->user_id !== $user->id) {
                throw new \Exception('Payment method does not belong to this user');
            }

            // Process payment based on payment method type
            if ($paymentMethod->isCreditCard()) {
                return $this->processCreditCardPayment($user, $paymentMethod, $amount, $description, $subscriptionId, $isDiscounted);
            } elseif ($paymentMethod->isAch()) {
                return $this->processAchPayment($user, $paymentMethod, $amount, $description, $subscriptionId, $isDiscounted);
            } elseif ($paymentMethod->isInvoice()) {
                return $this->processInvoicePayment($user, $paymentMethod, $amount, $description, $subscriptionId, $isDiscounted);
            } else {
                throw new \Exception('Unsupported payment method type');
            }
        } catch (\Exception $e) {
            Log::error('Error processing payment: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to process payment: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process a credit card payment
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processCreditCardPayment(User $user, PaymentMethod $paymentMethod, float $amount, string $description, ?int $subscriptionId, bool $isDiscounted): array
    {
        // Create a transaction record
        $transaction = Transaction::create([
            'user_id' => $user->id,
            'subscription_id' => $subscriptionId,
            'amount' => $amount,
            'is_discounted' => $isDiscounted,
            'payment_method' => 'credit_card',
            'status' => 'pending',
        ]);

        try {
            // Get customer profile ID
            $customerProfileId = $user->authorize_net_customer_id;
            if (empty($customerProfileId)) {
                throw new \Exception('No Authorize.net customer profile found for this user');
            }

            // Process the payment
            $paymentResult = $this->authorizeNetService->processTransaction(
                $amount,
                $customerProfileId,
                $paymentMethod->cc_token,
                $description,
                $user->id,
                $subscriptionId,
                $isDiscounted
            );

            if (!$paymentResult['success']) {
                throw new \Exception($paymentResult['message'] ?? 'Failed to process credit card payment');
            }

            // Update transaction record
            $transaction->transaction_id = $paymentResult['transaction_id'];
            $transaction->status = 'success';
            $transaction->save();

            return [
                'success' => true,
                'transaction_id' => $paymentResult['transaction_id'],
                'transaction' => $transaction,
                'message' => 'Credit card payment processed successfully!'
            ];
        } catch (\Exception $e) {
            // Update transaction record with error
            $transaction->status = 'failed';
            $transaction->error_message = $e->getMessage();
            $transaction->save();

            Log::error('Error processing credit card payment: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to process credit card payment: ' . $e->getMessage(),
                'transaction' => $transaction
            ];
        }
    }

    /**
     * Process an ACH payment
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processAchPayment(User $user, PaymentMethod $paymentMethod, float $amount, string $description, ?int $subscriptionId, bool $isDiscounted): array
    {
        // Create a transaction record
        $transaction = Transaction::create([
            'user_id' => $user->id,
            'subscription_id' => $subscriptionId,
            'amount' => $amount,
            'is_discounted' => $isDiscounted,
            'payment_method' => 'ach',
            'status' => 'pending',
        ]);

        try {
            // Get customer profile ID
            $customerProfileId = $user->authorize_net_customer_id;
            if (empty($customerProfileId)) {
                throw new \Exception('No Authorize.net customer profile found for this user');
            }

            // Process the payment
            $paymentResult = $this->achPaymentService->processAchTransaction(
                $amount,
                $customerProfileId,
                $paymentMethod->ach_token,
                $description,
                $user->id,
                $subscriptionId,
                $isDiscounted
            );

            if (!$paymentResult['success']) {
                throw new \Exception($paymentResult['message'] ?? 'Failed to process ACH payment');
            }

            // Update transaction record
            $transaction->transaction_id = $paymentResult['transaction_id'];
            $transaction->status = 'success';
            $transaction->save();

            return [
                'success' => true,
                'transaction_id' => $paymentResult['transaction_id'],
                'transaction' => $transaction,
                'message' => 'ACH payment processed successfully!'
            ];
        } catch (\Exception $e) {
            // Update transaction record with error
            $transaction->status = 'failed';
            $transaction->error_message = $e->getMessage();
            $transaction->save();

            Log::error('Error processing ACH payment: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to process ACH payment: ' . $e->getMessage(),
                'transaction' => $transaction
            ];
        }
    }

    /**
     * Process an invoice payment
     *
     * @param User $user
     * @param PaymentMethod $paymentMethod
     * @param float $amount
     * @param string $description
     * @param int|null $subscriptionId
     * @param bool $isDiscounted
     * @return array
     */
    public function processInvoicePayment(User $user, PaymentMethod $paymentMethod, float $amount, string $description, ?int $subscriptionId, bool $isDiscounted): array
    {
        return $this->invoicePaymentService->processInvoicePayment(
            $user,
            $paymentMethod,
            $amount,
            $description,
            $subscriptionId,
            $isDiscounted
        );
    }
}
