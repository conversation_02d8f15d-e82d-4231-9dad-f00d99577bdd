<?php

namespace App\Services;

use App\Models\Transaction;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class PaymentReportService
{
    /**
     * Get payment summary by payment method
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getPaymentMethodSummary(string $startDate, string $endDate): array
    {
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();

        $summary = Transaction::where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total_amount'))
            ->groupBy('payment_method')
            ->get();

        $totalAmount = $summary->sum('total_amount');
        $totalCount = $summary->sum('count');

        return [
            'summary' => $summary,
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
    }

    /**
     * Get payment summary by status
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getPaymentStatusSummary(string $startDate, string $endDate): array
    {
        $startDate = Carbon::parse($startDate)->startOfDay();
        $endDate = Carbon::parse($endDate)->endOfDay();

        $summary = Transaction::where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->select('status', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total_amount'))
            ->groupBy('status')
            ->get();

        return [
            'summary' => $summary,
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
    }

    /**
     * Get payment transactions for a specific user
     *
     * @param User $user
     * @param string|null $startDate
     * @param string|null $endDate
     * @param string|null $paymentMethod
     * @param string|null $status
     * @return Collection
     */
    public function getUserTransactions(
        User $user,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $paymentMethod = null,
        ?string $status = null
    ): Collection {
        $query = Transaction::where('user_id', $user->id);

        if ($startDate) {
            $query->where('created_at', '>=', Carbon::parse($startDate)->startOfDay());
        }

        if ($endDate) {
            $query->where('created_at', '<=', Carbon::parse($endDate)->endOfDay());
        }

        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }

        if ($status) {
            $query->where('status', $status);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get all transactions with filters
     *
     * @param array $filters
     * @return Collection
     */
    public function getFilteredTransactions(array $filters = []): Collection
    {
        $query = Transaction::query();

        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', Carbon::parse($filters['start_date'])->startOfDay());
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', Carbon::parse($filters['end_date'])->endOfDay());
        }

        if (isset($filters['payment_method'])) {
            $query->where('payment_method', $filters['payment_method']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['min_amount'])) {
            $query->where('amount', '>=', $filters['min_amount']);
        }

        if (isset($filters['max_amount'])) {
            $query->where('amount', '<=', $filters['max_amount']);
        }

        if (isset($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        return $query->with('user')->orderBy('created_at', 'desc')->get();
    }

    /**
     * Generate a payment report PDF
     *
     * @param array $filters
     * @return string Path to the generated PDF
     */
    public function generatePaymentReportPdf(array $filters = []): string
    {
        $transactions = $this->getFilteredTransactions($filters);
        
        $methodSummary = [];
        $statusSummary = [];
        
        // Group by payment method
        $methodGroups = $transactions->groupBy('payment_method');
        foreach ($methodGroups as $method => $group) {
            $methodSummary[] = [
                'method' => $method,
                'count' => $group->count(),
                'total' => $group->sum('amount')
            ];
        }
        
        // Group by status
        $statusGroups = $transactions->groupBy('status');
        foreach ($statusGroups as $status => $group) {
            $statusSummary[] = [
                'status' => $status,
                'count' => $group->count(),
                'total' => $group->sum('amount')
            ];
        }
        
        $pdf = Pdf::loadView('pdfs.payment-report', [
            'transactions' => $transactions,
            'methodSummary' => $methodSummary,
            'statusSummary' => $statusSummary,
            'filters' => $filters,
            'totalAmount' => $transactions->sum('amount'),
            'totalCount' => $transactions->count(),
            'generatedAt' => now()
        ]);
        
        $fileName = 'payment_report_' . now()->format('Y-m-d_H-i-s') . '.pdf';
        $path = 'reports/' . $fileName;
        Storage::put('public/' . $path, $pdf->output());
        
        return $path;
    }
}
