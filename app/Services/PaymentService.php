<?php

namespace App\Services;

use App\Models\CreditCard;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use ProtoneMedia\Splade\Facades\Toast;

class PaymentService
{
    public function __construct(
        private CreditCardService $creditCardService,
        private AuthorizeNetService $authorizeNetService
    ) {}

    public function processPayment(User $user, array $cardData, float $amount): array
    {
        $this->handleCreditCard($user, $cardData);
        return $this->authorizeNetService->chargeCreditCard($amount, $user);
    }

    private function handleCreditCard(User $user, array $cardData): void
    {
        info($cardData);
        if ($user->creditCards()->where('last_four', $cardData['card_number'])->exists()) {
            return;
        }

        $creditCard = $this->creditCardService->addCreditCard(
            $user,
            $cardData['card_number'],
            date('m', strtotime($cardData['expiry_date'])),
            date('Y', strtotime($cardData['expiry_date'])),
            $cardData['cvv']
        );

        if (!$creditCard) {
            throw new \Exception('Failed to process credit card');
        }

        if ($creditCard instanceof CreditCard) {
            $message = $creditCard->wasRecentlyCreated 
                ? 'Credit card added successfully' 
                : 'This credit card is already on file';
            
            Log::info($message, ['user' => $user->id]);
            Toast::success($message)->autoDismiss();
        } else {
            throw new \Exception('Invalid credit card response');
        }
    }
}