<?php

namespace App\Services;

use App\Models\Agent;
use App\Models\AgentTrainingMaterial;
use App\Models\AgentTrainingProgress;
use App\Models\AgentCertification;
use App\Models\AgentCertificationRequirement;
use App\Models\AgentEarnedCertification;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class TrainingService
{
    /**
     * Get a map of training progress for an agent.
     *
     * @param  int  $agentId
     * @return array
     */
    public function getProgressMap(int $agentId): array
    {
        $progress = AgentTrainingProgress::where('agent_id', $agentId)->get();
        
        $progressMap = [];
        foreach ($progress as $item) {
            $progressMap[$item->training_material_id] = [
                'completed' => $item->completed,
                'score' => $item->score,
                'completed_at' => $item->completed_at
            ];
        }
        
        return $progressMap;
    }
    
    /**
     * Mark a training material as completed for an agent.
     *
     * @param  int  $agentId
     * @param  int  $materialId
     * @return \App\Models\AgentTrainingProgress
     */
    public function markAsCompleted(int $agentId, int $materialId): AgentTrainingProgress
    {
        $progress = AgentTrainingProgress::updateOrCreate(
            [
                'agent_id' => $agentId,
                'training_material_id' => $materialId
            ],
            [
                'completed' => true,
                'completed_at' => now()
            ]
        );
        
        return $progress;
    }
    
    /**
     * Grade a quiz for an agent.
     *
     * @param  int  $agentId
     * @param  int  $materialId
     * @param  array  $answers
     * @return array
     */
    public function gradeQuiz(int $agentId, int $materialId, array $answers): array
    {
        $material = AgentTrainingMaterial::findOrFail($materialId);
        $quizQuestions = json_decode($material->quiz_questions, true);
        
        $totalQuestions = count($quizQuestions);
        $correctAnswers = 0;
        $results = [];
        
        foreach ($quizQuestions as $question) {
            $questionId = $question['id'];
            $correctAnswer = $question['correct_answer'];
            
            $userAnswer = $answers[$questionId] ?? null;
            $isCorrect = $userAnswer !== null && $userAnswer == $correctAnswer;
            
            if ($isCorrect) {
                $correctAnswers++;
            }
            
            $results[] = [
                'question_id' => $questionId,
                'correct' => $isCorrect,
                'correct_answer' => $correctAnswer
            ];
        }
        
        $score = $totalQuestions > 0 ? round(($correctAnswers / $totalQuestions) * 100) : 0;
        $passed = $score >= $material->passing_score;
        
        // Update the progress
        if ($passed) {
            $progress = AgentTrainingProgress::updateOrCreate(
                [
                    'agent_id' => $agentId,
                    'training_material_id' => $materialId
                ],
                [
                    'completed' => true,
                    'score' => $score,
                    'completed_at' => now()
                ]
            );
        } else {
            $progress = AgentTrainingProgress::updateOrCreate(
                [
                    'agent_id' => $agentId,
                    'training_material_id' => $materialId
                ],
                [
                    'completed' => false,
                    'score' => $score,
                    'completed_at' => null
                ]
            );
        }
        
        return [
            'id' => $progress->id,
            'training_material_id' => $materialId,
            'completed' => $progress->completed,
            'completed_at' => $progress->completed_at,
            'score' => $score,
            'passed' => $passed,
            'results' => $results
        ];
    }
    
    /**
     * Check if an agent has earned any certifications.
     *
     * @param  int  $agentId
     * @return array
     */
    public function checkCertifications(int $agentId): array
    {
        $earnedCertifications = [];
        
        // Get all active certifications
        $certifications = AgentCertification::where('is_active', true)->get();
        
        foreach ($certifications as $certification) {
            // Check if the agent already has this certification
            $alreadyEarned = AgentEarnedCertification::where('agent_id', $agentId)
                                                    ->where('certification_id', $certification->id)
                                                    ->exists();
            
            if ($alreadyEarned) {
                continue;
            }
            
            // Get the requirements for this certification
            $requirements = AgentCertificationRequirement::where('certification_id', $certification->id)->get();
            $requiredCount = $certification->required_training_count;
            
            if ($requirements->isEmpty() || $requiredCount <= 0) {
                continue;
            }
            
            // Check how many requirements the agent has completed
            $completedCount = 0;
            foreach ($requirements as $requirement) {
                $completed = AgentTrainingProgress::where('agent_id', $agentId)
                                                ->where('training_material_id', $requirement->training_material_id)
                                                ->where('completed', true)
                                                ->exists();
                
                if ($completed) {
                    $completedCount++;
                }
            }
            
            // If the agent has completed enough requirements, award the certification
            if ($completedCount >= $requiredCount) {
                $earnedCertification = AgentEarnedCertification::create([
                    'agent_id' => $agentId,
                    'certification_id' => $certification->id,
                    'earned_at' => now()
                ]);
                
                $earnedCertifications[] = [
                    'id' => $earnedCertification->id,
                    'certification_id' => $certification->id,
                    'certification_name' => $certification->name,
                    'earned_at' => $earnedCertification->earned_at
                ];
            }
        }
        
        return $earnedCertifications;
    }
    
    /**
     * Get certifications for an agent.
     *
     * @param  int  $agentId
     * @param  string|null  $status
     * @param  int  $perPage
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAgentCertifications(int $agentId, ?string $status = null, int $perPage = 10): LengthAwarePaginator
    {
        $query = AgentCertification::where('is_active', true);
        
        // Get earned certifications for this agent
        $earnedCertifications = AgentEarnedCertification::where('agent_id', $agentId)
                                                        ->pluck('certification_id')
                                                        ->toArray();
        
        // Get the agent's progress for all training materials
        $completedMaterials = AgentTrainingProgress::where('agent_id', $agentId)
                                                  ->where('completed', true)
                                                  ->pluck('training_material_id')
                                                  ->toArray();
        
        // Filter by status if provided
        if ($status) {
            if ($status === 'earned') {
                $query->whereIn('id', $earnedCertifications);
            } elseif ($status === 'in-progress') {
                $query->whereNotIn('id', $earnedCertifications);
                // Only include certifications where the agent has made some progress
                $query->whereHas('requirements', function ($q) use ($completedMaterials) {
                    $q->whereIn('training_material_id', $completedMaterials);
                });
            } elseif ($status === 'available') {
                $query->whereNotIn('id', $earnedCertifications);
            }
        }
        
        $certifications = $query->paginate($perPage);
        
        // Add progress information to each certification
        $certifications->getCollection()->transform(function ($certification) use ($agentId, $earnedCertifications, $completedMaterials) {
            // Get requirements for this certification
            $requirements = AgentCertificationRequirement::where('certification_id', $certification->id)->get();
            $requiredCount = $certification->required_training_count;
            
            // Count completed requirements
            $completedCount = 0;
            foreach ($requirements as $requirement) {
                if (in_array($requirement->training_material_id, $completedMaterials)) {
                    $completedCount++;
                }
            }
            
            // Calculate progress percentage
            $progressPercentage = $requiredCount > 0 ? round(($completedCount / $requiredCount) * 100) : 0;
            
            // Determine status
            $status = 'available';
            $earnedAt = null;
            
            if (in_array($certification->id, $earnedCertifications)) {
                $status = 'earned';
                $earnedCertification = AgentEarnedCertification::where('agent_id', $agentId)
                                                              ->where('certification_id', $certification->id)
                                                              ->first();
                $earnedAt = $earnedCertification ? $earnedCertification->earned_at : null;
            } elseif ($completedCount > 0) {
                $status = 'in-progress';
            }
            
            $certification->completed_training_count = $completedCount;
            $certification->progress_percentage = $progressPercentage;
            $certification->status = $status;
            $certification->earned_at = $earnedAt;
            
            return $certification;
        });
        
        return $certifications;
    }
    
    /**
     * Get detailed information about a certification for an agent.
     *
     * @param  int  $agentId
     * @param  \App\Models\AgentCertification  $certification
     * @return array
     */
    public function getAgentCertificationDetails(int $agentId, AgentCertification $certification): array
    {
        // Get requirements with training material information
        $requirements = $certification->requirements;
        $requiredCount = $certification->required_training_count;
        
        // Get the agent's progress for these requirements
        $progress = AgentTrainingProgress::where('agent_id', $agentId)
                                        ->whereIn('training_material_id', $requirements->pluck('training_material_id'))
                                        ->get()
                                        ->keyBy('training_material_id');
        
        // Count completed requirements
        $completedCount = 0;
        $requirementDetails = [];
        
        foreach ($requirements as $requirement) {
            $materialId = $requirement->training_material_id;
            $material = $requirement->trainingMaterial;
            
            $completed = isset($progress[$materialId]) && $progress[$materialId]->completed;
            $completedAt = $completed ? $progress[$materialId]->completed_at : null;
            
            if ($completed) {
                $completedCount++;
            }
            
            $requirementDetails[] = [
                'id' => $requirement->id,
                'training_material_id' => $materialId,
                'training_material_title' => $material ? $material->title : 'Unknown Material',
                'completed' => $completed,
                'completed_at' => $completedAt
            ];
        }
        
        // Calculate progress percentage
        $progressPercentage = $requiredCount > 0 ? round(($completedCount / $requiredCount) * 100) : 0;
        
        // Determine status
        $status = 'available';
        $earnedAt = null;
        
        $earnedCertification = AgentEarnedCertification::where('agent_id', $agentId)
                                                      ->where('certification_id', $certification->id)
                                                      ->first();
        
        if ($earnedCertification) {
            $status = 'earned';
            $earnedAt = $earnedCertification->earned_at;
        } elseif ($completedCount > 0) {
            $status = 'in-progress';
        }
        
        return [
            'id' => $certification->id,
            'name' => $certification->name,
            'description' => $certification->description,
            'badge_image' => $certification->badge_image,
            'required_training_count' => $requiredCount,
            'completed_training_count' => $completedCount,
            'status' => $status,
            'progress_percentage' => $progressPercentage,
            'earned_at' => $earnedAt,
            'requirements' => $requirementDetails,
            'created_at' => $certification->created_at,
            'updated_at' => $certification->updated_at
        ];
    }
    
    /**
     * Get the agent's overall training progress.
     *
     * @param  int  $agentId
     * @return array
     */
    public function getAgentProgress(int $agentId): array
    {
        // Get total and completed training materials
        $totalMaterials = AgentTrainingMaterial::where('is_active', true)->count();
        $completedMaterials = AgentTrainingProgress::where('agent_id', $agentId)
                                                  ->where('completed', true)
                                                  ->count();
        
        // Get total and completed required materials
        $totalRequiredMaterials = AgentTrainingMaterial::where('is_active', true)
                                                      ->where('is_required', true)
                                                      ->count();
        $completedRequiredMaterials = AgentTrainingProgress::where('agent_id', $agentId)
                                                          ->where('completed', true)
                                                          ->whereHas('trainingMaterial', function ($q) {
                                                              $q->where('is_required', true);
                                                          })
                                                          ->count();
        
        // Get total and earned certifications
        $totalCertifications = AgentCertification::where('is_active', true)->count();
        $earnedCertifications = AgentEarnedCertification::where('agent_id', $agentId)->count();
        
        // Calculate progress percentages
        $progressPercentage = $totalMaterials > 0 ? round(($completedMaterials / $totalMaterials) * 100) : 0;
        $requiredProgressPercentage = $totalRequiredMaterials > 0 ? round(($completedRequiredMaterials / $totalRequiredMaterials) * 100) : 0;
        $certificationProgressPercentage = $totalCertifications > 0 ? round(($earnedCertifications / $totalCertifications) * 100) : 0;
        
        // Get recent activity
        $recentActivity = $this->getRecentActivity($agentId);
        
        return [
            'total_materials' => $totalMaterials,
            'completed_materials' => $completedMaterials,
            'progress_percentage' => $progressPercentage,
            'total_required_materials' => $totalRequiredMaterials,
            'completed_required_materials' => $completedRequiredMaterials,
            'required_progress_percentage' => $requiredProgressPercentage,
            'total_certifications' => $totalCertifications,
            'earned_certifications' => $earnedCertifications,
            'certification_progress_percentage' => $certificationProgressPercentage,
            'recent_activity' => $recentActivity
        ];
    }
    
    /**
     * Get recent training activity for an agent.
     *
     * @param  int  $agentId
     * @param  int  $limit
     * @return array
     */
    protected function getRecentActivity(int $agentId, int $limit = 5): array
    {
        $activity = [];
        
        // Get recent training completions
        $completions = AgentTrainingProgress::with('trainingMaterial')
                                           ->where('agent_id', $agentId)
                                           ->where('completed', true)
                                           ->orderBy('completed_at', 'desc')
                                           ->limit($limit)
                                           ->get();
        
        foreach ($completions as $completion) {
            $material = $completion->trainingMaterial;
            
            if (!$material) {
                continue;
            }
            
            $type = $completion->score ? 'quiz_passed' : 'training_completed';
            
            $activity[] = [
                'id' => $completion->id,
                'type' => $type,
                'training_material_id' => $material->id,
                'training_material_title' => $material->title,
                'score' => $completion->score,
                'completed_at' => $completion->completed_at
            ];
        }
        
        // Get recent certification earnings
        $certifications = AgentEarnedCertification::with('certification')
                                                 ->where('agent_id', $agentId)
                                                 ->orderBy('earned_at', 'desc')
                                                 ->limit($limit)
                                                 ->get();
        
        foreach ($certifications as $certification) {
            $cert = $certification->certification;
            
            if (!$cert) {
                continue;
            }
            
            $activity[] = [
                'id' => $certification->id,
                'type' => 'certification_earned',
                'certification_id' => $cert->id,
                'certification_name' => $cert->name,
                'earned_at' => $certification->earned_at
            ];
        }
        
        // Sort by date (descending)
        usort($activity, function ($a, $b) {
            $dateA = $a['completed_at'] ?? $a['earned_at'];
            $dateB = $b['completed_at'] ?? $b['earned_at'];
            
            return strtotime($dateB) - strtotime($dateA);
        });
        
        // Limit the results
        return array_slice($activity, 0, $limit);
    }
}
