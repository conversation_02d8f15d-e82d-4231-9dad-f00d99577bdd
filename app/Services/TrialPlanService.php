<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\PromoCode;
use Carbon\Carbon;

class TrialPlanService
{
    public function startTrial(User $user)
    {
        $trialPromo = PromoCode::where('code', '7DAY-TRIAL')->firstOrFail();

        $subscription = new Subscription();
        $subscription->user_id = $user->id;
        $subscription->plan_id = 5; // 1 Month, 1 Service
        $subscription->starts_at = Carbon::now();
        $subscription->ends_at = Carbon::now()->addDays(7);
        $subscription->status = 'active';
        $subscription->is_discounted = true;
        $subscription->discounted_price = 1.99; // Trial price
        $subscription->is_trial = true;
        $subscription->save();

        $subscription->setFeature('promo', $trialPromo);
        if (session()->has('source')) {
            $subscription->setFeature('source', session()->get('source'));
        }

        // Increment the used_count for the promo code
        $trialPromo->increment('times_used');

        return $subscription;
    }

    public function startFree(User $user)
    {
        $freePromo = PromoCode::where('code', '7DAY-FREE')->firstOrFail();

        $subscription = new Subscription();
        $subscription->user_id = $user->id;
        $subscription->plan_id = 6;
        $subscription->starts_at = Carbon::now();
        $subscription->ends_at = Carbon::now()->addDays(7);
        $subscription->status = 'active';
        $subscription->is_discounted = true;
        $subscription->discounted_price = 0;
        $subscription->is_trial = true;
        $subscription->save();

        $subscription->setFeature('promo', $freePromo);
        if (session()->has('source')) {
            $subscription->setFeature('source', session()->get('source'));
        }

        // Increment the used_count for the promo code
        $freePromo->increment('times_used');

        return $subscription;
    }

    public function convertTrialToRegularSubscription(Subscription $subscription, $planId = 1)
    {
        if ($subscription->ends_at && $subscription->ends_at->isFuture()) {
            $subscription->ends_at = $subscription->ends_at->addMonth();
        } else {
            $subscription->ends_at = Carbon::now()->addMonth();
        }
        $subscription->plan_id = $planId;
        $subscription->is_trial = false;
        $subscription->is_discounted = false;
        $subscription->save();
    }
}