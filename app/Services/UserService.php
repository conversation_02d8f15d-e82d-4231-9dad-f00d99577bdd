<?php

namespace App\Services;

use App\Models\Allergy;
use App\Models\User;
use App\Models\UserReportedMedication;

class UserService
{
    public function createUser(array $userData): User
    {
        $user = User::create([
            'name' => $userData['first_name'] . ' ' . $userData['last_name'],
            'fname' => $userData['first_name'],
            'lname' => $userData['last_name'],
            'phone' => $userData['phone'],
            'email' => $userData['email'],
            'dob' => $userData['dob'],
            'password' => bcrypt($userData['phone']),
            'address1' => $userData['address1'],
            'address2' => $userData['address2'] ?? null,
            'city' => $userData['city'],
            'state' => $userData['state'],
            'zip' => $userData['zip'],
        ]);
        
        $user->assignRole('patient');
        return $user;
    }

    public function createUserMedications(User $user, array $medications): void
    {
        foreach($medications as $medication) {
            UserReportedMedication::create([
                'user_id' => $user->id,
                'medication_name' => $medication['medication_name'],
                'dosage' => $medication['dosage'],
                'frequency' => $medication['frequency'],
                'reaction' => $medication['reaction'],
                'side_effects' => $medication['side_effects'] ?? null,
            ]);
        }
    }

    public function createUserAllergies(User $user, array $allergies): void
    {
        foreach($allergies as $allergy) {
            Allergy::create([
                'user_id' => $user->id,
                'allergen' => $allergy['allergen'],
                'reaction' => $allergy['reaction'],
            ]);
        }
    }
}