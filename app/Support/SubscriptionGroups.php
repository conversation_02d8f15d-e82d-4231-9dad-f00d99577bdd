<?php

namespace App\Support;

class SubscriptionGroups
{
    // Define subscription groups
    const GROUPS = [
        1 => "Original Plans", // IDs 1-4
        2 => "Trial Plans",    // IDs 5-6
        3 => "Premium Plans"   // IDs 7-11
    ];

    // Define plan ID ranges for each group
    const GROUP_RANGES = [
        1 => [1, 4],   // Original Plans: IDs 1-4
        2 => [5, 6],   // Trial Plans: IDs 5-6
        3 => [7, 11],  // Premium Plans: IDs 7-11
    ];

    /**
     * Get the group ID for a given plan ID
     *
     * @param int $planId
     * @return int|null
     */
    public static function getGroupForPlan(int $planId): ?int
    {
        foreach (self::GROUP_RANGES as $groupId => $range) {
            if ($planId >= $range[0] && $planId <= $range[1]) {
                return $groupId;
            }
        }
        
        return null;
    }

    /**
     * Get the group name for a given plan ID
     *
     * @param int $planId
     * @return string|null
     */
    public static function getGroupNameForPlan(int $planId): ?string
    {
        $groupId = self::getGroupForPlan($planId);
        
        return $groupId ? self::GROUPS[$groupId] : null;
    }

    /**
     * Get all plan IDs for a specific group
     *
     * @param int $groupId
     * @return array
     */
    public static function getPlanIdsForGroup(int $groupId): array
    {
        if (!isset(self::GROUP_RANGES[$groupId])) {
            return [];
        }
        
        $range = self::GROUP_RANGES[$groupId];
        return range($range[0], $range[1]);
    }

    /**
     * Get premium plan IDs (for agents)
     *
     * @return array
     */
    public static function getPremiumPlanIds(): array
    {
        return self::getPlanIdsForGroup(3); // Premium Plans group ID is 3
    }
}
