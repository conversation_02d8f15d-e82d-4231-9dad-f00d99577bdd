<?php

namespace App\Tables;

use App\Models\OfflineConversion;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Excel;
use ProtoneMedia\Splade\AbstractTable;
use ProtoneMedia\Splade\Facades\Toast;
use ProtoneMedia\Splade\SpladeTable;

class OfflineConversions extends AbstractTable
{
    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the user is authorized to perform bulk actions and exports.
     *
     * @return bool
     */
    public function authorize(Request $request)
    {
        return true;
    }

    /**
     * The resource or query builder.
     *
     * @return mixed
     */
    public function for()
    {
        return OfflineConversion::query();
    }

    /**
     * Configure the given SpladeTable.
     *
     * @param \ProtoneMedia\Splade\SpladeTable $table
     * @return void
     */
    public function configure(SpladeTable $table)
    {
        // $conversion->gclid ?? '',
        //             $conversion->conversion_name,
        //             $conversion->created_at->format('Y-m-d H:i:s'),
        //             $conversion->conversion_value,
        //             $conversion->conversion_currency,
        //             '', // Ad User Data - left empty
        //             ''  // Ad Personalization - left empty
        $table
            ->column('gclid', label: 'Google Click ID', sortable: true)
            ->column('conversion_name', sortable: true)
            ->column('created_at', label: 'Conversion Time', sortable: true, as: fn ($created_at) => $created_at->format('Y-m-d H:i:s'))
            ->column('conversion_value', sortable: true)
            ->column('conversion_currency', sortable: true)
            ->column('ad_user_data', label: 'Ad User Data', sortable: true)
            ->column('ad_personalization', label: 'Ad Personalization', sortable: true)
            ->column('user.email', label: 'Email')
            ->column('user.phone', label: 'Phone')
            ->selectFilter('is_synced', [true => 'Synced', false => 'Not Synced'], 'Synced')
            ->bulkAction(
                label: 'Sync',
                each: fn (OfflineConversion $oc) => $oc->sync(),
                before: fn () => Toast::info('Synchronizing')->autoDismiss(10),
                after: fn () => Toast::info('Synchronized')->autoDismiss(10)
            )
            ->bulkAction(
                label: 'Desync',
                each: fn (OfflineConversion $oc) => $oc->desync(),
                before: fn () => Toast::info('Desynchronizing')->autoDismiss(10),
                after: fn () => Toast::info('Desynchronized')->autoDismiss(10)
            )
            ->export(
                label: 'CSV export',
                filename: 'offline-conversions-'.date('Y-m-d').'.csv',
                type: Excel::CSV
            )
            ->export()
            ->simplePaginate();
    }
}
