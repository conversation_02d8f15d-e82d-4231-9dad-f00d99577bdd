<?php

namespace App\Tables;

use App\Models\Service;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use ProtoneMedia\Splade\AbstractTable;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class Patients extends AbstractTable
{
    public $plans;

    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->plans = SubscriptionPlan::query()
            ->select('id', 'name')
            ->get()
            ->keyBy('id');
    }

    /**
     * Determine if the user is authorized to perform bulk actions and exports.
     *
     * @return bool
     */
    public function authorize(Request $request)
    {
        return $request->user()->hasAnyRole(['admin', 'staff', 'doctor']);
    }

    /**
     * The resource or query builder.
     *
     * @return mixed
     */
    public function for()
    {
        return User::role(['patient', 'employee'])->orderByDesc('id');
    }

    /**
     * Configure the given SpladeTable.
     *
     * @param \ProtoneMedia\Splade\SpladeTable $table
     * @return void
     */
    public function configure(SpladeTable $table)
    {
        $table
            ->column('name', sortable: true)
            ->column('email', sortable: true)
            ->column(
                key: 'phone',
                hidden: true,
            )
            ->column('assignedDoctors.name', label: 'Assigned Doctor')
            ->column(
                key: 'status',
                sortable: true,
                as: fn ($user) => User::STATUSES[$user]
            )
            ->column(
                key: 'services.name',
                label: 'Services',
            )
            ->column('subscriptions')
            // ->column(
            //     key: 'subscriptions',
            //     label: 'Subscriptions',
            //     as: function ($subscriptions) {
            //         return new HtmlString(
            //             Blade::render('components.subscription-list', [
            //                 'subscriptions' => $subscriptions,
            //                 'plans' => $this->plans
            //             ])
            //         );
            //     }
            // )
            ->column('gender', sortable: true, hidden: true)
            ->column(
                key: 'dob',
                label: 'Age',
                sortable: true,
                hidden: true,
                as: fn ($dob) => $dob ? Carbon::parse($dob)->age : null,
            )
            ->column(
                key: 'address1',
                hidden: true,
            )
            ->column(
                key: 'address2',
                hidden: true,
            )
            ->column(
                key: 'city',
                hidden: true,
            )
            ->column(
                'state',
                as: fn ($state) => $state ? config('states')[$state] : null,
            )
            ->column(
                key: 'zip',
                hidden: true,
            )
            ->column('preferredMedications')
            ->column('medicationOrders')
            ->column(
                key: 'created_at',
                sortable: true,
                as: fn ($user) => $user->isoFormat('LLL')
            )
            ->column(
                label: 'Actions',
                alignment: 'right'
            )

            ->selectFilter('state', config('states'))
            ->selectFilter('assignedDoctors.name', ['Gerald Stipanuk MD' => 'Gerald Stipanuk MD'], 'Doctor')
            ->selectFilter('status', User::STATUSES)
            ->selectFilter('services.name', Service::select('name')->pluck('name', 'name')->toArray(), 'Services')
            ->withGlobalSearch(columns: ['name', 'email'])
            ->bulkAction(
                label: 'Activate',
                each: fn (User $user) => $user->reactivate(),
                before: fn () => Toast::info('Activating selected users')->autoDismiss(10),
                after: fn () => Toast::info('Activated')->autoDismiss(10)
            )

            ->export()
            ->simplePaginate();
    }
}
