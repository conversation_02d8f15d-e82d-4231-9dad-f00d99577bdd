<?php

namespace App\Tables;

use App\Models\Prescription;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\AbstractTable;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class PendingPrescriptions extends AbstractTable
{
    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the user is authorized to perform bulk actions and exports.
     *
     * @return bool
     */
    public function authorize(Request $request)
    {
        return $request->user()->hasAnyRole(['admin', 'staff', 'pharmacist']);
    }

    /**
     * The resource or query builder.
     *
     * @return mixed
     */
    public function for()
    {
        return  Prescription::query()
        ->where('status', 'pending')
        ->orderBy('created_at', 'desc')
        ->take(10);
    }

    /**
     * Configure the given SpladeTable.
     *
     * @param \ProtoneMedia\Splade\SpladeTable $table
     * @return void
     */
    public function configure(SpladeTable $table)
    {
        $table
            ->column('id', 'Prescription ID')
            ->column('user.name', 'Patient Name')
            ->column('doctor.name', 'Doctor Name')
            ->column('status', 'Status')
            ->column('notes', 'Notes')
            ->column(
                key: 'created_at',
                sortable: true,
                as: fn ($user) => $user->isoFormat('LLL')
            )
            ->column('actions', 'Actions')
            ->bulkAction(
                label: 'Approve',
                each:  fn ($prescription) => $prescription->approve(),
                before: fn () => Toast::info('Approving prescriptions')->autoDismiss(10),
                after: fn () => Toast::info('Approved')->autoDismiss(10)
            );
    }
}
