<?php

namespace App\Tables;

use App\Models\Prescription;
use App\Models\User;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\AbstractTable;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class Prescriptions extends AbstractTable
{
    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the user is authorized to perform bulk actions and exports.
     *
     * @return bool
     */
    public function authorize(Request $request)
    {
        return $request->user()->hasAnyRole(['admin', 'staff', 'pharmacist', 'doctor']);
    }

    /**
     * The resource or query builder.
     *
     * @return mixed
     */
    public function for()
    {
        return  Prescription::query();
    }

    /**
     * Configure the given SpladeTable.
     *
     * @param \ProtoneMedia\Splade\SpladeTable $table
     * @return void
     */
    public function configure(SpladeTable $table)
    {
        $doctors = User::role('doctor')->pluck('name', 'id')->toArray();

        $table
            // ->column('id', 'Prescription ID')
            ->column('patient_name', label: 'Patient')
            ->column('doctor_name', label: 'Doctor')
            ->column('is_non_standard', 'Standard')
            ->column('status', 'Status')
            ->column('items')
            // ->column('dispensed_items', 'Dispensed Items')
            // ->column('last_dispensed_at', 'Last Dispensed At')
            ->column(
                key: 'notes',
                searchable: true,
            )
            ->column(
                key: 'created_at',
                sortable: true,
                as: fn ($prescription) => $prescription->isoFormat('LLL')
            )
            ->column('actions', 'Actions')
            ->selectFilter('status', config('status.prescription'))
            ->selectFilter('doctor_id', $doctors, label: 'Doctor')
            ->withGlobalSearch(columns: ['user.name', 'user.email'])
            ->bulkAction(
                label: 'Approve',
                each:  fn ($prescription) => $prescription->approve(),
                before: fn () => Toast::info('Approving prescriptions')->autoDismiss(10),
                after: fn () => Toast::info('Approved')->autoDismiss(10)
            )
            ->bulkAction(
                label: 'Reject',
                each:  fn ($prescription) => $prescription->reject(),
                before: fn () => Toast::info('Rejecting prescriptions')->autoDismiss(10),
                after: fn () => Toast::info('Rejected')->autoDismiss(10)
            )
            ->bulkAction(
                label: 'Cancel',
                each:  fn ($prescription) => $prescription->reject(),
                before: fn () => Toast::info('Cancel prescriptions')->autoDismiss(10),
                after: fn () => Toast::info('Cancelled')->autoDismiss(10)
            )
            ->defaultSortDesc('created_at')
            ->simplePaginate();
    }
}
