<?php

namespace App\Tables;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use ProtoneMedia\Splade\AbstractTable;
use ProtoneMedia\Splade\SpladeForm;
use ProtoneMedia\Splade\Components\Form\Select;
use ProtoneMedia\Splade\SpladeTable;
use ProtoneMedia\Splade\Facades\Toast;

class Users extends AbstractTable
{
    /**
     * Create a new instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the user is authorized to perform bulk actions and exports.
     *
     * @return bool
     */
    public function authorize(Request $request)
    {
        return $request->user()->hasRole('admin');
    }

    /**
     * The resource or query builder.
     *
     * @return mixed
     */
    public function for()
    {
        return User::query();
    }

    /**
     * Configure the given SpladeTable.
     *
     * @param \ProtoneMedia\Splade\SpladeTable $table
     * @return void
     */
    public function configure(SpladeTable $table)
    {
        $table
            ->column('name', sortable: true)
            ->column('email', sortable: true)
            ->column(
                key: 'phone',
                hidden: true,
            )
            ->column(
                key: 'roles.name',
                sortable: true,
                // as: fn ($user) => $user->roles
            )
            ->column(
                key: 'status',
                sortable: true,
                as: fn ($user) => User::STATUSES[$user]
            )
            ->column(
                key: 'created_at',
                sortable: true,
                as: fn ($user) => $user->isoFormat('LLL')
            )
            ->column(
                label: 'Actions',
                alignment: 'right'
            )

            ->selectFilter('status', User::STATUSES)
            ->withGlobalSearch(columns: ['name', 'email'])

            ->bulkAction(
                label: 'Delete',
                each: fn (User $user) => $user->delete(),
                before: fn () => Toast::info('Deleting selected users')->autoDismiss(10),
                after: fn () => Toast::info('Deleted')->autoDismiss(10)
            )
            ->bulkAction(
                label: 'Reactivate',
                each: fn (User $user) => $user->reactivate(),
                before: fn () => Toast::info('Activating selected users')->autoDismiss(10),
                after: fn () => Toast::info('Activated')->autoDismiss(10)
            )
            ->export()
            ->defaultSortDesc('created_at')
            ->simplePaginate();
    }
}
