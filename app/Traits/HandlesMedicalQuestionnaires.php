<?php

namespace App\Traits;

use App\Models\MedicalCondition;
use App\Models\MedicationOrder;
use App\Models\MedicalQuestionnaire;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProtoneMedia\Splade\Facades\Toast;

trait HandlesMedicalQuestionnaires
{
    /**
     * Get user's medications, conditions, and allergies for form data
     *
     * @param User $user
     * @return array
     */
    protected function getFormData(User $user)
    {
        $userMedications = $user->userReportedMedication()->get()->toArray();
        $userAllergies = $user->allergies()->get()->toArray();
        $userConditions = $user->medicalConditions()->get()->toArray() ?? [];

        // Get medication orders for this user
        $medicationOrders = $user->medicationOrders()->with('items.medication')->get();

        // Get medical conditions from the database
        $medicalConditions = $user->medicalConditions()->get()->toArray();

        // Set default values for empty arrays
        if (empty($userMedications)) {
            $userMedications = [
                [
                    'medication_name' => '',
                    'type' => '',
                    'dosage' => '',
                    'frequency' => '',
                    'duration' => '',
                    'reason' => '',
                    'side_effects' => ''
                ]
            ];
        }

        if (empty($userAllergies)) {
            $userAllergies = [
                [
                    'allergen' => '',
                    'type' => '',
                    'reaction' => '',
                    'severity' => '',
                    'diagnosed_at' => ''
                ]
            ];
        }

        if (empty($userConditions)) {
            $userConditions = [
                [
                    'condition_name' => '',
                    'diagnosed_at' => '',
                    'notes' => '',
                    'had_condition_before' => false,
                    'is_chronic' => false
                ]
            ];
        }

        return [
            'medications' => $userMedications,
            'allergies' => $userAllergies,
            'conditions' => $userConditions,
            'medication_orders' => $medicationOrders,
            'medical_conditions' => $medicalConditions,
        ];
    }

    /**
     * Store a medical questionnaire for a user
     *
     * @param Request $request
     * @param User $user
     * @return MedicalQuestionnaire
     */
    protected function storeQuestionnaire(Request $request, User $user)
    {
        // Validate the request
        $request->validate([
            'allergies.*.allergen' => 'required',
            'medications.*.medication_name' => 'required',
            'cart_session_id' => 'sometimes|required',
            'user_id' => 'sometimes|required',
            'treatment_id' => 'sometimes|required',
            'subscription_plan_id' => 'sometimes|required',
            'cardiovascular_diagnosis' => 'sometimes|required',
            'cardiovascular_symptoms' => 'sometimes|required',
            'cardiovascular_medications' => 'sometimes|required',
            'cardiovascular_family' => 'sometimes|required',
            'cardiovascular_diet' => 'sometimes|required',
            'cardiovascular_lifestyle' => 'sometimes|required',
            'cardiovascular_monitoring' => 'sometimes|required',
            'neuro_diagnosis' => 'sometimes|required',
            'neuro_frequency' => 'sometimes|required',
            'neuro_triggers' => 'sometimes|required',
            'neuro_sleep' => 'sometimes|required',
            'neuro_daily_impact' => 'sometimes|required',
            'neuro_medications' => 'sometimes|required',
            'neuro_side_effects' => 'sometimes|required',
            'gi_symptoms' => 'sometimes|required',
            'gi_frequency' => 'sometimes|required',
            'gi_diet' => 'sometimes|required',
            'gi_medications' => 'sometimes|required',
            'gi_procedures' => 'sometimes|required',
            'gi_weight' => 'sometimes|required',
            'endocrine_diagnosis' => 'sometimes|required',
            'endocrine_symptoms' => 'sometimes|required',
            'endocrine_labs' => 'sometimes|required',
            'endocrine_medications' => 'sometimes|required',
            'endocrine_monitoring' => 'sometimes|required',
            'preventive_risk' => 'sometimes|required',
            'preventive_diet' => 'sometimes|required',
            'preventive_exercise' => 'sometimes|required',
            'preventive_screenings' => 'sometimes|required',
            'preventive_falls' => 'sometimes|required',
            'prophylaxis_history' => 'sometimes|required',
            'prophylaxis_risk' => 'sometimes|required',
            'prophylaxis_immunity' => 'sometimes|required',
            'prophylaxis_allergies' => 'sometimes|required',
            'prophylaxis_current' => 'sometimes|required',
            'skin_conditions' => 'sometimes|required',
            'skin_symptoms' => 'sometimes|required',
            'skin_triggers' => 'sometimes|required',
            'skin_treatments' => 'sometimes|required',
            'skin_impact' => 'sometimes|required',
            'immune_conditions' => 'sometimes|required',
            'immune_allergies' => 'sometimes|required',
            'immune_symptoms' => 'sometimes|required',
            'immune_treatments' => 'sometimes|required',
            'immune_triggers' => 'sometimes|required',
            'immune_emergency' => 'sometimes|required',
            'mh_symptoms_severity' => 'sometimes|required',
            'mh_sleep_patterns' => 'sometimes|required',
            'mh_concentration' => 'sometimes|required',
            'mh_support_system' => 'sometimes|required',
            'mh_coping_methods' => 'sometimes|required',
            'mh_suicidal_thoughts' => 'sometimes|required',
            'mh_treatment_history' => 'sometimes|required',
            'pain_location_type' => 'sometimes|required',
            'pain_frequency' => 'sometimes|required',
            'pain_severity' => 'sometimes|required',
            'pain_triggers' => 'sometimes|required',
            'pain_relief' => 'sometimes|required',
            'pain_impact' => 'sometimes|required',
            'pain_associated_symptoms' => 'sometimes|required',
            'respiratory_symptoms' => 'sometimes|required',
            'respiratory_triggers' => 'sometimes|required',
            'respiratory_sleep' => 'sometimes|required',
            'respiratory_exercise' => 'sometimes|required',
            'respiratory_treatments' => 'sometimes|required',
            'respiratory_smoking' => 'sometimes|required',
            'prevention_risk_factors' => 'sometimes|required',
            'prevention_history' => 'sometimes|required',
            'prevention_medications' => 'sometimes|required',
            'prevention_lifestyle' => 'sometimes|required',
            'prevention_monitoring' => 'sometimes|required',
            'prevention_family_history' => 'sometimes|required',
            'additional_symptoms' => 'sometimes|required',
            'quality_of_life' => 'sometimes|required',
            'treatment_goals' => 'sometimes|required',
            'treatment_preference' => 'sometimes|required',
            'medication_preference' => 'sometimes|required',
            'concerns' => 'sometimes|required',
            'weight_history' => 'sometimes|required',
            'current_weight_goals' => 'sometimes|required',
            'lifestyle_factors' => 'sometimes|required',
            'underlying_conditions' => 'sometimes|required',
            'previous_attempts' => 'sometimes|required',
            'weight_medication_history' => 'sometimes|required',
            'barriers_to_weight_loss' => 'sometimes|required',
            'family_weight_history' => 'sometimes|required',
            'status' => 'sometimes|required',
            'transaction_id' => 'sometimes|required',
            'amount' => 'sometimes|required',
            'data' => 'sometimes|required',
            'health_concerns' => 'sometimes|required',
            'symptoms' => 'sometimes|required',
            'symptom_duration' => 'sometimes|required',
            'previous_treatments' => 'sometimes|required',
            'medical_history' => 'sometimes|required',
            'additional_info' => 'sometimes|required',
            'medication_effectiveness' => 'sometimes|required',
            'medication_adherence' => 'sometimes|required',
            'medication_side_effects' => 'sometimes|required',
            'medication_interactions' => 'sometimes|required',
            'medication_refills' => 'sometimes|required',
            'medication_changes' => 'sometimes|required',
        ]);

        // Create a new questionnaire
        $questionnaire = new MedicalQuestionnaire();
        $questionnaire->user_id = $user->id;
        $questionnaire->cart_session_id = $request->cart_session_id ?? 'general-' . uniqid();
        $questionnaire->status = 'completed';
        $questionnaire->fill($request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]));

        // Store all form data in the data column as well
        $data = $request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]);

        // Ensure these specific fields are included in the data
        $specificFields = [
            'surgical_history',
            'family_history',
            'social_history',
            'tobacco_use',
            'alcohol_use',
            'drug_use',
            'exercise_frequency',
            'stress_level',
            'diet',
            'sleep_patterns',
            'occupation'
        ];

        foreach ($specificFields as $field) {
            if ($request->has($field)) {
                $data[$field] = $request->input($field);
            }
        }

        $questionnaire->data = $data;

        $questionnaire->save();

        // Update user's medications
        if ($request->has('medications')) {
            // Delete existing medications
            $user->userReportedMedication()->delete();

            // Create new medications
            foreach ($request->medications as $medication) {
                if (!empty($medication['medication_name'])) {
                    $user->userReportedMedication()->create($medication);
                }
            }
        }

        // Update user's allergies
        if ($request->has('allergies')) {
            // Delete existing allergies
            $user->allergies()->delete();

            // Create new allergies
            foreach ($request->allergies as $allergy) {
                if (!empty($allergy['allergen'])) {
                    $user->allergies()->create($allergy);
                }
            }
        }

        // Update user's medical conditions
        if ($request->has('conditions')) {
            // Delete existing conditions
            $user->medicalConditions()->delete();

            // Create new conditions
            foreach ($request->conditions as $condition) {
                if (!empty($condition['condition_name'])) {
                    $user->medicalConditions()->create($condition);
                }
            }
        }

        return $questionnaire;
    }

    /**
     * Update a medical questionnaire
     *
     * @param Request $request
     * @param User $user
     * @param MedicalQuestionnaire $questionnaire
     * @return MedicalQuestionnaire
     */
    protected function updateQuestionnaire(Request $request, User $user, MedicalQuestionnaire $questionnaire)
    {
        // Validate the request
        $request->validate([
            'allergies.*.allergen' => 'required',
            'medications.*.medication_name' => 'required',
            'conditions.*.condition_name' => 'nullable',
            'cart_session_id' => 'sometimes|required',
            'user_id' => 'sometimes|required',
            'treatment_id' => 'sometimes|required',
            'subscription_plan_id' => 'sometimes|required',
            'cardiovascular_diagnosis' => 'sometimes|required',
            'cardiovascular_symptoms' => 'sometimes|required',
            'cardiovascular_medications' => 'sometimes|required',
            'cardiovascular_family' => 'sometimes|required',
            'cardiovascular_diet' => 'sometimes|required',
            'cardiovascular_lifestyle' => 'sometimes|required',
            'cardiovascular_monitoring' => 'sometimes|required',
            'neuro_diagnosis' => 'sometimes|required',
            'neuro_frequency' => 'sometimes|required',
            'neuro_triggers' => 'sometimes|required',
            'neuro_sleep' => 'sometimes|required',
            'neuro_daily_impact' => 'sometimes|required',
            'neuro_medications' => 'sometimes|required',
            'neuro_side_effects' => 'sometimes|required',
            'gi_symptoms' => 'sometimes|required',
            'gi_frequency' => 'sometimes|required',
            'gi_diet' => 'sometimes|required',
            'gi_medications' => 'sometimes|required',
            'gi_procedures' => 'sometimes|required',
            'gi_weight' => 'sometimes|required',
            'endocrine_diagnosis' => 'sometimes|required',
            'endocrine_symptoms' => 'sometimes|required',
            'endocrine_labs' => 'sometimes|required',
            'endocrine_medications' => 'sometimes|required',
            'endocrine_monitoring' => 'sometimes|required',
            'preventive_risk' => 'sometimes|required',
            'preventive_diet' => 'sometimes|required',
            'preventive_exercise' => 'sometimes|required',
            'preventive_screenings' => 'sometimes|required',
            'preventive_falls' => 'sometimes|required',
            'prophylaxis_history' => 'sometimes|required',
            'prophylaxis_risk' => 'sometimes|required',
            'prophylaxis_immunity' => 'sometimes|required',
            'prophylaxis_allergies' => 'sometimes|required',
            'prophylaxis_current' => 'sometimes|required',
            'skin_conditions' => 'sometimes|required',
            'skin_symptoms' => 'sometimes|required',
            'skin_triggers' => 'sometimes|required',
            'skin_treatments' => 'sometimes|required',
            'skin_impact' => 'sometimes|required',
            'immune_conditions' => 'sometimes|required',
            'immune_allergies' => 'sometimes|required',
            'immune_symptoms' => 'sometimes|required',
            'immune_treatments' => 'sometimes|required',
            'immune_triggers' => 'sometimes|required',
            'immune_emergency' => 'sometimes|required',
            'mh_symptoms_severity' => 'sometimes|required',
            'mh_sleep_patterns' => 'sometimes|required',
            'mh_concentration' => 'sometimes|required',
            'mh_support_system' => 'sometimes|required',
            'mh_coping_methods' => 'sometimes|required',
            'mh_suicidal_thoughts' => 'sometimes|required',
            'mh_treatment_history' => 'sometimes|required',
            'pain_location_type' => 'sometimes|required',
            'pain_frequency' => 'sometimes|required',
            'pain_severity' => 'sometimes|required',
            'pain_triggers' => 'sometimes|required',
            'pain_relief' => 'sometimes|required',
            'pain_impact' => 'sometimes|required',
            'pain_associated_symptoms' => 'sometimes|required',
            'respiratory_symptoms' => 'sometimes|required',
            'respiratory_triggers' => 'sometimes|required',
            'respiratory_sleep' => 'sometimes|required',
            'respiratory_exercise' => 'sometimes|required',
            'respiratory_treatments' => 'sometimes|required',
            'respiratory_smoking' => 'sometimes|required',
            'prevention_risk_factors' => 'sometimes|required',
            'prevention_history' => 'sometimes|required',
            'prevention_medications' => 'sometimes|required',
            'prevention_lifestyle' => 'sometimes|required',
            'prevention_monitoring' => 'sometimes|required',
            'prevention_family_history' => 'sometimes|required',
            'additional_symptoms' => 'sometimes|required',
            'quality_of_life' => 'sometimes|required',
            'treatment_goals' => 'sometimes|required',
            'treatment_preference' => 'sometimes|required',
            'medication_preference' => 'sometimes|required',
            'concerns' => 'sometimes|required',
            'weight_history' => 'sometimes|required',
            'current_weight_goals' => 'sometimes|required',
            'lifestyle_factors' => 'sometimes|required',
            'underlying_conditions' => 'sometimes|required',
            'previous_attempts' => 'sometimes|required',
            'weight_medication_history' => 'sometimes|required',
            'barriers_to_weight_loss' => 'sometimes|required',
            'family_weight_history' => 'sometimes|required',
            'status' => 'sometimes|required',
            'transaction_id' => 'sometimes|required',
            'amount' => 'sometimes|required',
            'data' => 'sometimes|required',
            'health_concerns' => 'sometimes|required',
            'symptoms' => 'sometimes|required',
            'symptom_duration' => 'sometimes|required',
            'previous_treatments' => 'sometimes|required',
            'medical_history' => 'sometimes|required',
            'additional_info' => 'sometimes|required',
            'medication_effectiveness' => 'sometimes|required',
            'medication_adherence' => 'sometimes|required',
            'medication_side_effects' => 'sometimes|required',
            'medication_interactions' => 'sometimes|required',
            'medication_refills' => 'sometimes|required',
            'medication_changes' => 'sometimes|required',
        ]);

        // Update the questionnaire
        $questionnaire->fill($request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]));

        // Store all form data in the data column as well
        $data = $request->except([
            'medications',
            'allergies',
            'conditions',
            'medication_orders',
            'medical_conditions',
            '_token',
            '_method'
        ]);

        // Ensure these specific fields are included in the data
        $specificFields = [
            'surgical_history',
            'family_history',
            'social_history',
            'tobacco_use',
            'alcohol_use',
            'drug_use',
            'exercise_frequency',
            'stress_level',
            'diet',
            'sleep_patterns',
            'occupation'
        ];

        foreach ($specificFields as $field) {
            if ($request->has($field)) {
                $data[$field] = $request->input($field);
            }
        }

        $questionnaire->data = $data;

        $questionnaire->save();

        // Update user's medications
        if ($request->has('medications')) {
            // Delete existing medications
            $user->userReportedMedication()->delete();

            // Create new medications
            foreach ($request->medications as $medication) {
                if (!empty($medication['medication_name'])) {
                    $user->userReportedMedication()->create($medication);
                }
            }
        }

        // Update user's allergies
        if ($request->has('allergies')) {
            // Delete existing allergies
            $user->allergies()->delete();

            // Create new allergies
            foreach ($request->allergies as $allergy) {
                if (!empty($allergy['allergen'])) {
                    $user->allergies()->create($allergy);
                }
            }
        }

        // Update user's medical conditions
        if ($request->has('conditions')) {
            // Delete existing conditions
            $user->medicalConditions()->delete();

            // Create new conditions
            foreach ($request->conditions as $condition) {
                if (!empty($condition['condition_name'])) {
                    $user->medicalConditions()->create($condition);
                }
            }
        }

        return $questionnaire;
    }

    /**
     * Check if a patient was enrolled by an agent
     *
     * @param User $patient
     * @param \App\Models\Agent $agent
     * @return bool
     */
    protected function isPatientEnrolledByAgent(User $patient, $agent)
    {
        // Check if the patient has any subscriptions with the agent's ID in the agent_id field
        return $patient->subscriptions()
            ->where('agent_id', $agent->id)
            ->exists();
    }
}
