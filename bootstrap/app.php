<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ActionServiceProvider::class,
        App\Providers\BusinessServiceProvider::class,
        App\Providers\PaymentServiceProvider::class,
        Laravel\Sanctum\SanctumServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->group('splade', [\ProtoneMedia\Splade\Http\SpladeMiddleware::class]);

        $middleware->alias([
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role' => \App\Http\Middleware\CheckRole::class,
            'redirect.role' => \App\Http\Middleware\RedirectBasedOnRole::class,
            'urgent-care.steps' => \App\Http\Middleware\UrgentCareFormStepMiddleware::class,
            'active.subscription' => \App\Http\Middleware\VerifyActiveSubscription::class,
            'ensure.cart.user' => \App\Http\Middleware\EnsureCartUserIdMiddleware::class,
            'store.cart.session' => \App\Http\Middleware\StoreCartSessionMiddleware::class,
            'profile.completion' => \App\Http\Middleware\CheckProfileCompletion::class,
            'medical.questionnaire' => \App\Http\Middleware\CheckMedicalQuestionnaireCompletion::class,
            'abilities' => \Laravel\Sanctum\Http\Middleware\CheckAbilities::class,
            'ability' => \Laravel\Sanctum\Http\Middleware\CheckForAnyAbility::class,
        ]);

        $middleware->web(append: [
            \App\Http\Middleware\DetectBots::class,
            \App\Http\Middleware\StoreGclidInCookie::class,
            \App\Http\Middleware\StoreLinkTrustClickIdInCookie::class,
            \App\Http\Middleware\StoreAgentReferralInCookie::class,
            \App\Http\Middleware\RedirectToNewLandingPage::class,
            \App\Http\Middleware\PromoCodeMiddleware::class,
        ]);

        $middleware->api(append: [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(\ProtoneMedia\Splade\SpladeCore::exceptionHandler($exceptions->handler));
        //
    })->create();
