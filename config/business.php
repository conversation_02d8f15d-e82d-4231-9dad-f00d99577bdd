<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Plan Settings
    |--------------------------------------------------------------------------
    |
    | These values define the default settings for business plans.
    |
    */
    'plans' => [
        'price_per_plan' => 2000, // $20.00 stored as 2000 cents
        'seats_per_block' => 5,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Trial Settings
    |--------------------------------------------------------------------------
    |
    | Settings for the free trial feature.
    |
    */
    'trial' => [
        'enabled' => true,
        'days' => 30,
        'check_expiration_cron' => '0 1 * * *', // Run at 1:00 AM daily
    ],

    /*
    |--------------------------------------------------------------------------
    | Discount Settings
    |--------------------------------------------------------------------------
    |
    | Settings for plan duration discounts.
    |
    */
    'discounts' => [
        'monthly' => [
            'duration_months' => 1,
            'discount_percent' => 0,
        ],
        'quarterly' => [
            'duration_months' => 3,
            'discount_percent' => 5,
        ],
        'semi_annual' => [
            'duration_months' => 6,
            'discount_percent' => 10,
        ],
        'annual' => [
            'duration_months' => 12,
            'discount_percent' => 15,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Settings
    |--------------------------------------------------------------------------
    |
    | URLs for various webhook notifications.
    |
    */
    'webhooks' => [
        'trial_notifications' => env('BUSINESS_TRIAL_WEBHOOK_URL'),
        'employee_termination' => env('EMPLOYEE_TERMINATION_WEBHOOK_URL'),
    ],
];
