<?php

return [
    'consultation' => [
        'scheduled' => 'Scheduled',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ],
    /**
     *  active: The discount is currently valid within the start_date and end_date.
     *  expired: The discount has passed its end_date.
     *  inactive: The discount is not currently active, either manually set or before the start_date.
     *  upcoming: The discount will be valid in the future, after the start_date.
     *  suspended and revoked: These reflect manually or automatically disabling the discount.
     *  limited: The discount may have conditions or limited applicability.
     *  pending: The discount is awaiting activation.
     *  redeemable: The discount is valid but may have specific usage conditions.
     *  restricted: The discount is restricted to certain users, products, or conditions.
     *  invalid: The discount is not recognized or is no longer applicable.
     */
    'discount' => [
        'active'      => 'Active',
        'expired'     => 'Expired',
        'inactive'    => 'Inactive',
        'upcoming'    => 'Upcoming',
        'suspended'   => 'Suspended',
        'revoked'     => 'Revoked',
        'limited'     => 'Limited',
        'pending'     => 'Pending',
        'redeemable'  => 'Redeemable',
        'restricted'  => 'Restricted',
        'invalid'     => 'Invalid',
    ],
    'prescription' => [
        'pending' => 'Pending',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'partially_dispensed' => 'Partially Dispensed',
        'dispensed' => 'Dispensed',
        'cancelled' => 'Cancelled',
    ],
    'prescription_item' => [
        'active' => 'Active',
        'discontinued' => 'Discontinued',
        'as_needed' => 'As Needed',
        'temporary' => 'Temporary',
        'tapering' => 'Tapering',
        'on_hold' => 'On Hold',
        'scheduled' => 'Scheduled',
        'long_term' => 'Long Term',
    ],
    /**
     *  active: When the promo code is valid and falls within the start_date and end_date range.
     *  expired: When the current date is past the end_date.
     *  used: Based on usage_limit and times_used. If the promo code has reached its limit, it could be marked as used.
     *  inactive: When a promo code is manually disabled or not yet active (before the start_date).
     *  upcoming: If the start_date is in the future, the promo code could be marked as upcoming.
     *  suspended and revoked: These could be set manually if a promo code is temporarily or permanently disabled.
     *  limited: Could be based on usage limits, such as if the promo code is still active but nearing its usage_limit.
     *  pending: If there is a process for approval or activation before the promo code becomes usable.
     *  restricted: Could be set if the promo code is meant for specific users, products, or conditions.
     */
    'promo_code' => [
        'active'      => 'Active',          // The promo code is currently valid and can be used.
        'expired'     => 'Expired',         // The promo code is no longer valid due to the expiration date.
        'used'        => 'Used',            // The promo code has already been redeemed.
        'inactive'    => 'Inactive',        // The promo code is temporarily disabled or not active yet.
        'invalid'     => 'Invalid',         // The promo code is not recognized or is incorrect.
        'upcoming'    => 'Upcoming',        // The promo code is scheduled to be active in the future.
        'suspended'   => 'Suspended',       // The promo code is temporarily suspended, possibly due to misuse.
        'revoked'     => 'Revoked',         // The promo code has been manually canceled or withdrawn.
        'limited'     => 'Limited',         // The promo code is available, but under certain conditions or usage limits.
        'pending'     => 'Pending',         // The promo code is awaiting approval or activation.
        'redeemable'  => 'Redeemable',      // The promo code can be redeemed, but there may be conditions.
        'restricted'  => 'Restricted',      // The promo code is only applicable to certain users or products.
    ],
    'user' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'pending_approval' => 'Pending Approval',
        'deleted' => 'Deleted',
        'awaiting_verification' => 'Awaiting Verification',
        'payment_due' => 'Payment Due',
        'on_hold' => 'On Hold',
    ],
];