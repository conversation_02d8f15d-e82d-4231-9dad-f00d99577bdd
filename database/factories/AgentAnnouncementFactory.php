<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentAnnouncement>
 */
class AgentAnnouncementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $publishAt = $this->faker->dateTimeBetween('-1 month', '+1 week');
        $expiresAt = (clone $publishAt)->modify('+' . $this->faker->numberBetween(7, 30) . ' days');

        return [
            'title' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(3, true),
            'is_active' => $this->faker->boolean(80),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high']),
            'publish_at' => $publishAt,
            'expires_at' => $expiresAt,
            'created_at' => $this->faker->dateTimeBetween('-2 months', '-1 month'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }
}
