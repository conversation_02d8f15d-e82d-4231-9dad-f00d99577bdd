<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentCustomReport>
 */
class AgentCustomReportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['commission', 'referral', 'performance']);
        $isScheduled = $this->faker->boolean(40);

        $columns = [];
        if ($type === 'commission') {
            $columns = $this->faker->randomElements(['created_at', 'amount', 'status', 'source', 'description'], $this->faker->numberBetween(2, 5));
        } elseif ($type === 'referral') {
            $columns = $this->faker->randomElements(['created_at', 'status', 'referred_user_name', 'referred_user_email', 'source'], $this->faker->numberBetween(2, 5));
        } else {
            $columns = $this->faker->randomElements(['period', 'commission_amount', 'referral_count', 'conversion_rate', 'growth_rate'], $this->faker->numberBetween(2, 5));
        }

        return [
            'agent_id' => \App\Models\Agent::factory(),
            'name' => $this->faker->words(3, true) . ' Report',
            'type' => $type,
            'columns' => json_encode($columns),
            'filters' => json_encode([
                'date_from' => $this->faker->date(),
                'date_to' => $this->faker->date(),
            ]),
            'sorting' => json_encode([
                [
                    'column' => $columns[0],
                    'direction' => $this->faker->randomElement(['asc', 'desc']),
                ],
            ]),
            'is_scheduled' => $isScheduled,
            'schedule_frequency' => $isScheduled ? $this->faker->randomElement(['daily', 'weekly', 'monthly']) : null,
            'schedule_time' => $isScheduled ? $this->faker->time('H:i') : null,
            'created_at' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }
}
