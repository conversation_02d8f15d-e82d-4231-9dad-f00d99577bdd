<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentGoal>
 */
class AgentGoalFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['commissions', 'referrals']);
        $targetValue = $type === 'commissions'
            ? $this->faker->numberBetween(1000, 10000)
            : $this->faker->numberBetween(5, 50);

        $createdAt = $this->faker->dateTimeBetween('-6 months', '-1 month');
        $targetDate = (clone $createdAt)->modify('+' . $this->faker->numberBetween(1, 6) . ' months');

        $status = $this->faker->randomElement(['active', 'achieved', 'expired']);
        $achievedAt = $status === 'achieved'
            ? (clone $createdAt)->modify('+' . $this->faker->numberBetween(1, 30) . ' days')
            : null;

        return [
            'agent_id' => \App\Models\Agent::factory(),
            'type' => $type,
            'target_value' => $targetValue,
            'target_date' => $targetDate,
            'description' => $type === 'commissions'
                ? "Earn $" . number_format($targetValue) . " in commissions"
                : "Get " . $targetValue . " referrals",
            'status' => $status,
            'achieved_at' => $achievedAt,
            'created_at' => $createdAt,
            'updated_at' => $achievedAt ?? $createdAt,
        ];
    }
}
