<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentLandingPage>
 */
class AgentLandingPageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->words(3, true) . ' Landing Page';
        return [
            'agent_id' => \App\Models\Agent::factory(),
            'title' => $title,
            'slug' => \Illuminate\Support\Str::slug($title) . '-' . $this->faker->randomNumber(5),
            'template' => $this->faker->randomElement(['basic', 'professional', 'modern']),
            'content' => '<h1>' . $title . '</h1><p>' . $this->faker->paragraph() . '</p>',
            'settings' => json_encode([
                'header_color' => $this->faker->hexColor(),
                'button_color' => $this->faker->hexColor(),
                'show_logo' => $this->faker->boolean(),
                'show_contact_form' => $this->faker->boolean(80),
            ]),
            'is_active' => $this->faker->boolean(90),
            'created_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
            'updated_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ];
    }
}
