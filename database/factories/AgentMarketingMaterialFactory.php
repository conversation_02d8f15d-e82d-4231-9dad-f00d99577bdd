<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentMarketingMaterial>
 */
class AgentMarketingMaterialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(['social', 'email', 'print', 'video']),
            'template_content' => $this->faker->boolean(70) ? $this->faker->paragraphs(3, true) : null,
            'file_path' => $this->faker->boolean(50) ? 'marketing/' . $this->faker->word() . '.pdf' : null,
            'thumbnail_path' => $this->faker->boolean(60) ? 'thumbnails/' . $this->faker->word() . '.jpg' : null,
            'is_active' => $this->faker->boolean(90),
            'created_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
            'updated_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ];
    }
}
