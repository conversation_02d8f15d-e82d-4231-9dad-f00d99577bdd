<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentReportExport>
 */
class AgentReportExportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $format = $this->faker->randomElement(['csv', 'pdf', 'xlsx']);
        $agent = \App\Models\Agent::factory()->create();

        return [
            'agent_id' => $agent->id,
            'report_id' => \App\Models\AgentCustomReport::factory()->create([
                'agent_id' => $agent->id,
            ])->id,
            'report_type' => $this->faker->randomElement(['commission', 'referral', 'performance']),
            'format' => $format,
            'file_path' => 'exports/' . $agent->id . '/' . $this->faker->uuid() . '.' . $format,
            'file_size' => $this->faker->numberBetween(1024, 1024 * 1024),
            'generated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'created_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'updated_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ];
    }
}
