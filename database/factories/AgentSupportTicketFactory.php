<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentSupportTicket>
 */
class AgentSupportTicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isOpen = $this->faker->boolean(70);
        $createdAt = $this->faker->dateTimeBetween('-3 months', '-1 day');
        $resolvedAt = $isOpen ? null : (clone $createdAt)->modify('+' . $this->faker->numberBetween(1, 14) . ' days');

        return [
            'agent_id' => \App\Models\Agent::factory(),
            'subject' => $this->faker->sentence(),
            'description' => $this->faker->paragraphs(3, true),
            'status' => $isOpen ? 'open' : 'closed',
            'priority' => $this->faker->randomElement(['low', 'medium', 'high']),
            'category' => $this->faker->randomElement(['technical', 'billing', 'account', 'other']),
            'resolved_at' => $resolvedAt,
            'created_at' => $createdAt,
            'updated_at' => $resolvedAt ?? $createdAt,
        ];
    }
}
