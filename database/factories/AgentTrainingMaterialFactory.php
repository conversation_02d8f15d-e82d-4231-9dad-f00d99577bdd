<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentTrainingMaterial>
 */
class AgentTrainingMaterialFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'content' => $this->faker->paragraphs(5, true),
            'type' => $this->faker->randomElement(['video', 'document', 'interactive']),
            'is_required' => $this->faker->boolean(70),
            'has_quiz' => $this->faker->boolean(50),
            'quiz_questions' => $this->faker->boolean(50) ? json_encode([
                [
                    'id' => 1,
                    'question' => $this->faker->sentence(6) . '?',
                    'options' => [
                        $this->faker->word(),
                        $this->faker->word(),
                        $this->faker->word(),
                        $this->faker->word(),
                    ],
                    'correct_answer' => $this->faker->numberBetween(0, 3),
                ],
                [
                    'id' => 2,
                    'question' => $this->faker->sentence(6) . '?',
                    'options' => [
                        $this->faker->word(),
                        $this->faker->word(),
                        $this->faker->word(),
                        $this->faker->word(),
                    ],
                    'correct_answer' => $this->faker->numberBetween(0, 3),
                ],
            ]) : null,
            'passing_score' => 70,
            'duration_minutes' => $this->faker->numberBetween(10, 60),
            'order' => $this->faker->numberBetween(1, 100),
            'is_active' => $this->faker->boolean(90),
            'created_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
            'updated_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ];
    }
}
