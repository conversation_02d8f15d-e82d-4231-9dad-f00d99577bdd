<?php

namespace Database\Factories;

use App\Models\Business;
use App\Models\BusinessEmployee;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BusinessEmployee>
 */
class BusinessEmployeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BusinessEmployee::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'business_id' => Business::factory(),
            'user_id' => null,
            'email' => $this->faker->unique()->safeEmail(),
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'phone' => $this->faker->phoneNumber(),
            'mobile_phone' => $this->faker->optional()->phoneNumber(),
            'dob' => $this->faker->date('Y-m-d', '-18 years'),
            'address1' => $this->faker->streetAddress(),
            'address2' => $this->faker->optional()->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'status' => 'active',
            'terminated_at' => null,
            'termination_reason' => null,
            'transitioned_to_consumer' => false,
        ];
    }

    /**
     * Indicate that the employee is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the employee is terminated.
     */
    public function terminated(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'terminated',
            'terminated_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'termination_reason' => $this->faker->optional()->sentence(),
        ]);
    }

    /**
     * Indicate that the employee has a user account.
     */
    public function withUser(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => User::factory(),
        ]);
    }

    /**
     * Indicate that the employee has transitioned to consumer.
     */
    public function transitionedToConsumer(): static
    {
        return $this->state(fn (array $attributes) => [
            'transitioned_to_consumer' => true,
        ]);
    }
}
