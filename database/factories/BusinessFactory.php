<?php

namespace Database\Factories;

use App\Models\Business;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Business>
 */
class BusinessFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Business::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'email' => $this->faker->unique()->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address1' => $this->faker->streetAddress(),
            'address2' => $this->faker->optional()->secondaryAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'zip' => $this->faker->postcode(),
            'trial_enabled' => false,
            'trial_started_at' => null,
            'trial_ends_at' => null,
            'referring_agent_id' => null,
        ];
    }

    /**
     * Indicate that the business has an active trial.
     */
    public function withTrial(): static
    {
        return $this->state(fn (array $attributes) => [
            'trial_enabled' => true,
            'trial_started_at' => now()->subDays(5),
            'trial_ends_at' => now()->addDays(25),
        ]);
    }

    /**
     * Indicate that the business trial has expired.
     */
    public function withExpiredTrial(): static
    {
        return $this->state(fn (array $attributes) => [
            'trial_enabled' => true,
            'trial_started_at' => now()->subDays(35),
            'trial_ends_at' => now()->subDays(5),
        ]);
    }
}
