<?php

namespace Database\Factories;

use App\Models\Business;
use App\Models\BusinessPlan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BusinessPlan>
 */
class BusinessPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = BusinessPlan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $planQuantity = $this->faker->numberBetween(1, 20);
        $pricePerPlan = 2000; // $20.00 in cents
        $totalPrice = $planQuantity * $pricePerPlan;

        return [
            'business_id' => Business::factory(),
            'plan_quantity' => $planQuantity,
            'price_per_plan' => $pricePerPlan,
            'total_price' => $totalPrice,
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
            'active' => true,
            'duration_months' => 1,
            'discount_percent' => 0,
            'click_id' => null,
            'afid' => null,
        ];
    }

    /**
     * Indicate that the plan is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => false,
        ]);
    }

    /**
     * Indicate that the plan has expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'starts_at' => now()->subMonths(2),
            'ends_at' => now()->subMonth(),
            'active' => false,
        ]);
    }

    /**
     * Set a specific plan quantity.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'plan_quantity' => $quantity,
            'total_price' => $quantity * $attributes['price_per_plan'],
        ]);
    }

    /**
     * Set a specific duration.
     */
    public function withDuration(int $months): static
    {
        return $this->state(fn (array $attributes) => [
            'duration_months' => $months,
            'ends_at' => now()->addMonths($months),
        ]);
    }

    /**
     * Set a discount.
     */
    public function withDiscount(int $discountPercent): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_percent' => $discountPercent,
        ]);
    }

    /**
     * Set tracking fields.
     */
    public function withTracking(string $clickId = null, string $afid = null): static
    {
        return $this->state(fn (array $attributes) => [
            'click_id' => $clickId ?? $this->faker->uuid(),
            'afid' => $afid ?? $this->faker->uuid(),
        ]);
    }
}
