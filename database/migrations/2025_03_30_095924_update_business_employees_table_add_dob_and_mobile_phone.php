<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_employees', function(Blueprint $table) {
            $table->string('mobile_phone')->nullable()->after('phone');
            $table->date('dob')->nullable()->after('mobile_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_employees', function(Blueprint $table) {
            $table->dropColumn('mobile_phone', 'dob');
        });
    }
};
