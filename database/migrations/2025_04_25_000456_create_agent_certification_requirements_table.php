<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agent_certification_requirements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('certification_id')->constrained('agent_certifications')->onDelete('cascade');
            $table->foreignId('training_material_id')->constrained('agent_training_materials')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agent_certification_requirements');
    }
};
