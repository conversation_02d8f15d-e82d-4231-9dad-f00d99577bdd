# Populate Medication-Condition Command

This command populates the `medication_condition` pivot table with relationships between medications and conditions.

## Command Signature

```bash
php artisan medication:populate-conditions [options]
```

## Options

- `--clear` : Clear existing medication-condition relationships before populating
- `--dry-run` : Show what would be created without actually creating records
- `--medication=ID` : Populate for a specific medication ID
- `--condition=ID` : Populate for a specific condition ID

## Usage Examples

### Basic Usage

Populate all medication-condition relationships:
```bash
php artisan medication:populate-conditions
```

### Dry Run

See what would be created without actually creating records:
```bash
php artisan medication:populate-conditions --dry-run
```

### Clear and Repopulate

Clear existing relationships and create new ones:
```bash
php artisan medication:populate-conditions --clear
```

### Specific Medication

Populate relationships for a specific medication:
```bash
php artisan medication:populate-conditions --medication=1
```

### Specific Condition

Populate relationships for a specific condition:
```bash
php artisan medication:populate-conditions --condition=5
```

### Combined Options

Clear existing data and run a dry run for a specific medication:
```bash
php artisan medication:populate-conditions --clear --dry-run --medication=1
```

## How It Works

The command uses predefined mappings to associate medications with conditions based on:

1. **Medication Name Matching**: Matches medication names or generic names with predefined drug mappings
2. **Primary vs Secondary Uses**: Distinguishes between primary therapeutic uses (true) and off-label/secondary uses (false)
3. **Intelligent Fallback**: For medications without specific mappings, creates default relationships with common conditions

## Medication-Condition Mappings

The command includes mappings for common medication categories:

### Diabetes Medications
- Metformin → Diabetes (primary), Prediabetes (secondary)
- Insulin → Type 1/2 Diabetes (primary)
- Glipizide → Type 2 Diabetes (primary)

### Hypertension Medications
- Lisinopril → Hypertension (primary), Heart failure (secondary)
- Amlodipine → Hypertension (primary), Angina (secondary)
- Hydrochlorothiazide → Hypertension (primary), Edema (secondary)

### Pain Medications
- Ibuprofen → Pain, Inflammation, Arthritis (primary), Fever (secondary)
- Acetaminophen → Pain, Fever (primary)
- Naproxen → Pain, Inflammation, Arthritis (primary)

### Antibiotics
- Amoxicillin → Bacterial infections, Pneumonia, UTI (primary)
- Azithromycin → Bacterial infections, Pneumonia, Bronchitis (primary)

### Mental Health Medications
- Sertraline → Depression, Anxiety, Panic disorder (primary)
- Fluoxetine → Depression (primary), Bulimia nervosa (primary)

### And more...

## Database Structure

The command populates the `medication_condition` table with:

- `medication_id` (foreign key to medications table)
- `condition_id` (foreign key to conditions table)
- `is_primary_use` (boolean indicating if this is a primary therapeutic use)
- `created_at` and `updated_at` timestamps

## Output

The command provides detailed output including:

- Progress information for each medication processed
- Summary table showing:
  - Relationships created
  - Relationships skipped (already exist)
  - Total medications processed
  - Total conditions available

## Error Handling

The command handles various scenarios:

- **No medications found**: Returns exit code 1
- **No conditions found**: Returns exit code 1
- **Existing relationships**: Skips duplicates and continues
- **Missing mappings**: Creates default relationships with common conditions

## Testing

Run the test suite for this command:

```bash
php artisan test tests/Feature/PopulateMedicationConditionCommandTest.php
```

## Notes

- The command is safe to run multiple times as it checks for existing relationships
- Use `--dry-run` first to preview changes before actual execution
- The `--clear` option will remove ALL existing medication-condition relationships
- Mappings can be extended by modifying the `getMedicationConditionMappings()` method in the command class
