import{C as n}from"./auto-1vNCvF_S.js";document.addEventListener("DOMContentLoaded",function(){const t=document.getElementById("subscriptionChart"),a=document.getElementById("planDistributionChart");if(t){const e=t.getContext("2d"),s=JSON.parse(t.dataset.labels||"[]"),r=JSON.parse(t.dataset.values||"[]");new n(e,{type:"bar",data:{labels:s,datasets:[{label:"New Subscriptions",data:r,backgroundColor:"rgba(79, 70, 229, 0.6)",borderColor:"rgba(79, 70, 229, 1)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}if(a){const e=a.getContext("2d"),s=JSON.parse(a.dataset.labels||"[]"),r=JSON.parse(a.dataset.values||"[]");new n(e,{type:"doughnut",data:{labels:s,datasets:[{data:r,backgroundColor:["rgba(79, 70, 229, 0.6)","rgba(16, 185, 129, 0.6)","rgba(245, 158, 11, 0.6)","rgba(239, 68, 68, 0.6)","rgba(217, 70, 239, 0.6)","rgba(14, 165, 233, 0.6)"],borderColor:["rgba(79, 70, 229, 1)","rgba(16, 185, 129, 1)","rgba(245, 158, 11, 1)","rgba(239, 68, 68, 1)","rgba(217, 70, 239, 1)","rgba(14, 165, 233, 1)"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})}});
