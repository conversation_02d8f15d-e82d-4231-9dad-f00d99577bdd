/*!
 * FilePondPluginFileValidateType 1.2.9
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const D=({addFilter:a,utils:d})=>{const{Type:T,isString:u,replaceInString:A,guesstimateMimeType:f,getExtensionFromFilename:I,getFilenameFromURL:P}=d,y=(t,e)=>{const n=(/^[^/]+/.exec(t)||[]).pop(),i=e.slice(0,-2);return n===i},_=(t,e)=>t.some(n=>/\*$/.test(n)?y(e,n):n===e),m=t=>{let e="";if(u(t)){const n=P(t),i=I(n);i&&(e=f(i))}else e=t.type;return e},c=(t,e,n)=>{if(e.length===0)return!0;const i=m(t);return n?new Promise((s,l)=>{n(t,i).then(E=>{_(e,E)?s():l()}).catch(l)}):_(e,i)},F=t=>e=>t[e]===null?!1:t[e]||e;return a("SET_ATTRIBUTE_TO_OPTION_MAP",t=>Object.assign(t,{accept:"acceptedFileTypes"})),a("ALLOW_HOPPER_ITEM",(t,{query:e})=>e("GET_ALLOW_FILE_TYPE_VALIDATION")?c(t,e("GET_ACCEPTED_FILE_TYPES")):!0),a("LOAD_FILE",(t,{query:e})=>new Promise((n,i)=>{if(!e("GET_ALLOW_FILE_TYPE_VALIDATION")){n(t);return}const s=e("GET_ACCEPTED_FILE_TYPES"),l=e("GET_FILE_VALIDATE_TYPE_DETECT_TYPE"),E=c(t,s,l),L=()=>{const r=s.map(F(e("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES_MAP"))).filter(p=>p!==!1),o=r.filter((p,O)=>r.indexOf(p)===O);i({status:{main:e("GET_LABEL_FILE_TYPE_NOT_ALLOWED"),sub:A(e("GET_FILE_VALIDATE_TYPE_LABEL_EXPECTED_TYPES"),{allTypes:o.join(", "),allButLastType:o.slice(0,-1).join(", "),lastType:o[o.length-1]})}})};if(typeof E=="boolean")return E?n(t):L();E.then(()=>{n(t)}).catch(L)})),{options:{allowFileTypeValidation:[!0,T.BOOLEAN],acceptedFileTypes:[[],T.ARRAY],labelFileTypeNotAllowed:["File is of invalid type",T.STRING],fileValidateTypeLabelExpectedTypes:["Expects {allButLastType} or {lastType}",T.STRING],fileValidateTypeLabelExpectedTypesMap:[{},T.OBJECT],fileValidateTypeDetectType:[null,T.FUNCTION]}}},G=typeof window<"u"&&typeof window.document<"u";G&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:D}));export{D as default};
