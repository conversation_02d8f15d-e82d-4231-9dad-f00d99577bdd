/*!
 * FilePond 4.31.1
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */const dr=e=>e instanceof HTMLElement,ur=(e,t=[],n=[])=>{const r={...e},s=[],o=[],i=()=>({...r}),a=()=>{const u=[...s];return s.length=0,u},l=()=>{const u=[...o];o.length=0,u.forEach(({type:_,data:D})=>{d(_,D)})},d=(u,_,D)=>{if(D&&!document.hidden){o.push({type:u,data:_});return}T[u]&&T[u](_),s.push({type:u,data:_})},c=(u,..._)=>E[u]?E[u](..._):null,f={getState:i,processActionQueue:a,processDispatchQueue:l,dispatch:d,query:c};let E={};t.forEach(u=>{E={...u(r),...E}});let T={};return n.forEach(u=>{T={...u(d,c,r),...T}}),f},fr=(e,t,n)=>{if(typeof n=="function"){e[t]=n;return}Object.defineProperty(e,t,{...n})},q=(e,t)=>{for(const n in e)e.hasOwnProperty(n)&&t(n,e[n])},fe=e=>{const t={};return q(e,n=>{fr(t,n,e[n])}),t},Y=(e,t,n=null)=>{if(n===null)return e.getAttribute(t)||e.hasAttribute(t);e.setAttribute(t,n)},Er="http://www.w3.org/2000/svg",pr=["svg","path"],Pt=e=>pr.includes(e),xe=(e,t,n={})=>{typeof t=="object"&&(n=t,t=null);const r=Pt(e)?document.createElementNS(Er,e):document.createElement(e);return t&&(Pt(e)?Y(r,"class",t):r.className=t),q(n,(s,o)=>{Y(r,s,o)}),r},Ir=e=>(t,n)=>{typeof n<"u"&&e.children[n]?e.insertBefore(t,e.children[n]):e.appendChild(t)},_r=(e,t)=>(n,r)=>(typeof r<"u"?t.splice(r,0,n):t.push(n),n),Tr=(e,t)=>n=>(t.splice(t.indexOf(n),1),n.element.parentNode&&e.removeChild(n.element),n),mr=typeof window<"u"&&typeof window.document<"u",hn=()=>mr,gr=hn()?xe("svg"):{},Rr="children"in gr?e=>e.children.length:e=>e.childNodes.length,On=(e,t,n,r)=>{const s=n[0]||e.left,o=n[1]||e.top,i=s+e.width,a=o+e.height*(r[1]||1),l={element:{...e},inner:{left:e.left,top:e.top,right:e.right,bottom:e.bottom},outer:{left:s,top:o,right:i,bottom:a}};return t.filter(d=>!d.isRectIgnored()).map(d=>d.rect).forEach(d=>{Mt(l.inner,{...d.inner}),Mt(l.outer,{...d.outer})}),bt(l.inner),l.outer.bottom+=l.element.marginBottom,l.outer.right+=l.element.marginRight,bt(l.outer),l},Mt=(e,t)=>{t.top+=e.top,t.right+=e.left,t.bottom+=e.top,t.left+=e.left,t.bottom>e.bottom&&(e.bottom=t.bottom),t.right>e.right&&(e.right=t.right)},bt=e=>{e.width=e.right-e.left,e.height=e.bottom-e.top},Ee=e=>typeof e=="number",hr=(e,t,n,r=.001)=>Math.abs(e-t)<r&&Math.abs(n)<r,Or=({stiffness:e=.5,damping:t=.75,mass:n=10}={})=>{let r=null,s=null,o=0,i=!1;const d=fe({interpolate:(c,f)=>{if(i)return;if(!(Ee(r)&&Ee(s))){i=!0,o=0;return}const E=-(s-r)*e;o+=E/n,s+=o,o*=t,hr(s,r,o)||f?(s=r,o=0,i=!0,d.onupdate(s),d.oncomplete(s)):d.onupdate(s)},target:{set:c=>{if(Ee(c)&&!Ee(s)&&(s=c),r===null&&(r=c,s=c),r=c,s===r||typeof r>"u"){i=!0,o=0,d.onupdate(s),d.oncomplete(s);return}i=!1},get:()=>r},resting:{get:()=>i},onupdate:c=>{},oncomplete:c=>{}});return d},Dr=e=>e<.5?2*e*e:-1+(4-2*e)*e,Sr=({duration:e=500,easing:t=Dr,delay:n=0}={})=>{let r=null,s,o,i=!0,a=!1,l=null;const c=fe({interpolate:(f,E)=>{i||l===null||(r===null&&(r=f),!(f-r<n)&&(s=f-r-n,s>=e||E?(s=1,o=a?0:1,c.onupdate(o*l),c.oncomplete(o*l),i=!0):(o=s/e,c.onupdate((s>=0?t(a?1-o:o):0)*l))))},target:{get:()=>a?0:l,set:f=>{if(l===null){l=f,c.onupdate(f),c.oncomplete(f);return}f<l?(l=1,a=!0):(a=!1,l=f),i=!1,r=null}},resting:{get:()=>i},onupdate:f=>{},oncomplete:f=>{}});return c},Ct={spring:Or,tween:Sr},Ar=(e,t,n)=>{const r=e[t]&&typeof e[t][n]=="object"?e[t][n]:e[t]||e,s=typeof r=="string"?r:r.type,o=typeof r=="object"?{...r}:{};return Ct[s]?Ct[s](o):null},It=(e,t,n,r=!1)=>{t=Array.isArray(t)?t:[t],t.forEach(s=>{e.forEach(o=>{let i=o,a=()=>n[o],l=d=>n[o]=d;typeof o=="object"&&(i=o.key,a=o.getter||a,l=o.setter||l),!(s[i]&&!r)&&(s[i]={get:a,set:l})})})},yr=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r})=>{const s={...t},o=[];return q(e,(i,a)=>{const l=Ar(a);if(!l)return;l.onupdate=c=>{t[i]=c},l.target=s[i],It([{key:i,setter:c=>{l.target!==c&&(l.target=c)},getter:()=>t[i]}],[n,r],t,!0),o.push(l)}),{write:i=>{let a=document.hidden,l=!0;return o.forEach(d=>{d.resting||(l=!1),d.interpolate(i,a)}),l},destroy:()=>{}}},Lr=e=>(t,n)=>{e.addEventListener(t,n)},Pr=e=>(t,n)=>{e.removeEventListener(t,n)},Mr=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,viewState:s,view:o})=>{const i=[],a=Lr(o.element),l=Pr(o.element);return r.on=(d,c)=>{i.push({type:d,fn:c}),a(d,c)},r.off=(d,c)=>{i.splice(i.findIndex(f=>f.type===d&&f.fn===c),1),l(d,c)},{write:()=>!0,destroy:()=>{i.forEach(d=>{l(d.type,d.fn)})}}},br=({mixinConfig:e,viewProps:t,viewExternalAPI:n})=>{It(e,n,t)},Q=e=>e!=null,Cr={opacity:1,scaleX:1,scaleY:1,translateX:0,translateY:0,rotateX:0,rotateY:0,rotateZ:0,originX:0,originY:0},Nr=({mixinConfig:e,viewProps:t,viewInternalAPI:n,viewExternalAPI:r,view:s})=>{const o={...t},i={};It(e,[n,r],t);const a=()=>[t.translateX||0,t.translateY||0],l=()=>[t.scaleX||0,t.scaleY||0],d=()=>s.rect?On(s.rect,s.childViews,a(),l()):null;return n.rect={get:d},r.rect={get:d},e.forEach(c=>{t[c]=typeof o[c]>"u"?Cr[c]:o[c]}),{write:()=>{if(wr(i,t))return vr(s.element,t),Object.assign(i,{...t}),!0},destroy:()=>{}}},wr=(e,t)=>{if(Object.keys(e).length!==Object.keys(t).length)return!0;for(const n in t)if(t[n]!==e[n])return!0;return!1},vr=(e,{opacity:t,perspective:n,translateX:r,translateY:s,scaleX:o,scaleY:i,rotateX:a,rotateY:l,rotateZ:d,originX:c,originY:f,width:E,height:T})=>{let u="",_="";(Q(c)||Q(f))&&(_+=`transform-origin: ${c||0}px ${f||0}px;`),Q(n)&&(u+=`perspective(${n}px) `),(Q(r)||Q(s))&&(u+=`translate3d(${r||0}px, ${s||0}px, 0) `),(Q(o)||Q(i))&&(u+=`scale3d(${Q(o)?o:1}, ${Q(i)?i:1}, 1) `),Q(d)&&(u+=`rotateZ(${d}rad) `),Q(a)&&(u+=`rotateX(${a}rad) `),Q(l)&&(u+=`rotateY(${l}rad) `),u.length&&(_+=`transform:${u};`),Q(t)&&(_+=`opacity:${t};`,t===0&&(_+="visibility:hidden;"),t<1&&(_+="pointer-events:none;")),Q(T)&&(_+=`height:${T}px;`),Q(E)&&(_+=`width:${E}px;`);const D=e.elementCurrentStyle||"";(_.length!==D.length||_!==D)&&(e.style.cssText=_,e.elementCurrentStyle=_)},Gr={styles:Nr,listeners:Mr,animations:yr,apis:br},Nt=(e={},t={},n={})=>(t.layoutCalculated||(e.paddingTop=parseInt(n.paddingTop,10)||0,e.marginTop=parseInt(n.marginTop,10)||0,e.marginRight=parseInt(n.marginRight,10)||0,e.marginBottom=parseInt(n.marginBottom,10)||0,e.marginLeft=parseInt(n.marginLeft,10)||0,t.layoutCalculated=!0),e.left=t.offsetLeft||0,e.top=t.offsetTop||0,e.width=t.offsetWidth||0,e.height=t.offsetHeight||0,e.right=e.left+e.width,e.bottom=e.top+e.height,e.scrollTop=t.scrollTop,e.hidden=t.offsetParent===null,e),$=({tag:e="div",name:t=null,attributes:n={},read:r=()=>{},write:s=()=>{},create:o=()=>{},destroy:i=()=>{},filterFrameActionsForChild:a=(T,u)=>u,didCreateView:l=()=>{},didWriteView:d=()=>{},ignoreRect:c=!1,ignoreRectUpdate:f=!1,mixins:E=[]}={})=>(T,u={})=>{const _=xe(e,`filepond--${t}`,n),D=window.getComputedStyle(_,null),R=Nt();let O=null,P=!1;const M=[],A=[],C={},B={},h=[s],b=[r],G=[i],y=()=>_,w=()=>M.concat(),X=()=>C,m=v=>(k,le)=>k(v,le),U=()=>O||(O=On(R,M,[0,0],[1,1]),O),I=()=>D,g=()=>{O=null,M.forEach(le=>le._read()),!(f&&R.width&&R.height)&&Nt(R,_,D);const k={root:de,props:u,rect:R};b.forEach(le=>le(k))},L=(v,k,le)=>{let Oe=k.length===0;return h.forEach(Z=>{Z({props:u,root:de,actions:k,timestamp:v,shouldOptimize:le})===!1&&(Oe=!1)}),A.forEach(Z=>{Z.write(v)===!1&&(Oe=!1)}),M.filter(Z=>!!Z.element.parentNode).forEach(Z=>{Z._write(v,a(Z,k),le)||(Oe=!1)}),M.forEach((Z,we)=>{Z.element.parentNode||(de.appendChild(Z.element,we),Z._read(),Z._write(v,a(Z,k),le),Oe=!1)}),P=Oe,d({props:u,root:de,actions:k,timestamp:v}),Oe},S=()=>{A.forEach(v=>v.destroy()),G.forEach(v=>{v({root:de,props:u})}),M.forEach(v=>v._destroy())},F={element:{get:y},style:{get:I},childViews:{get:w}},V={...F,rect:{get:U},ref:{get:X},is:v=>t===v,appendChild:Ir(_),createChildView:m(T),linkView:v=>(M.push(v),v),unlinkView:v=>{M.splice(M.indexOf(v),1)},appendChildView:_r(_,M),removeChildView:Tr(_,M),registerWriter:v=>h.push(v),registerReader:v=>b.push(v),registerDestroyer:v=>G.push(v),invalidateLayout:()=>_.layoutCalculated=!1,dispatch:T.dispatch,query:T.query},Ze={element:{get:y},childViews:{get:w},rect:{get:U},resting:{get:()=>P},isRectIgnored:()=>c,_read:g,_write:L,_destroy:S},ar={...F,rect:{get:()=>R}};Object.keys(E).sort((v,k)=>v==="styles"?1:k==="styles"?-1:0).forEach(v=>{const k=Gr[v]({mixinConfig:E[v],viewProps:u,viewState:B,viewInternalAPI:V,viewExternalAPI:Ze,view:fe(ar)});k&&A.push(k)});const de=fe(V);o({root:de,props:u});const cr=Rr(_);return M.forEach((v,k)=>{de.appendChild(v.element,cr+k)}),l(de),fe(Ze)},Ur=(e,t,n=60)=>{const r="__framePainter";if(window[r]){window[r].readers.push(e),window[r].writers.push(t);return}window[r]={readers:[e],writers:[t]};const s=window[r],o=1e3/n;let i=null,a=null,l=null,d=null;const c=()=>{document.hidden?(l=()=>window.setTimeout(()=>f(performance.now()),o),d=()=>window.clearTimeout(a)):(l=()=>window.requestAnimationFrame(f),d=()=>window.cancelAnimationFrame(a))};document.addEventListener("visibilitychange",()=>{d&&d(),c(),f(performance.now())});const f=E=>{a=l(f),i||(i=E);const T=E-i;T<=o||(i=E-T%o,s.readers.forEach(u=>u()),s.writers.forEach(u=>u(E)))};return c(),f(performance.now()),{pause:()=>{d(a)}}},K=(e,t)=>({root:n,props:r,actions:s=[],timestamp:o,shouldOptimize:i})=>{s.filter(a=>e[a.type]).forEach(a=>e[a.type]({root:n,props:r,action:a.data,timestamp:o,shouldOptimize:i})),t&&t({root:n,props:r,actions:s,timestamp:o,shouldOptimize:i})},wt=(e,t)=>t.parentNode.insertBefore(e,t),vt=(e,t)=>t.parentNode.insertBefore(e,t.nextSibling),We=e=>Array.isArray(e),ae=e=>e==null,Fr=e=>e.trim(),ze=e=>""+e,Br=(e,t=",")=>ae(e)?[]:We(e)?e:ze(e).split(t).map(Fr).filter(n=>n.length),Dn=e=>typeof e=="boolean",Sn=e=>Dn(e)?e:e==="true",j=e=>typeof e=="string",An=e=>Ee(e)?e:j(e)?ze(e).replace(/[a-z]+/gi,""):0,qe=e=>parseInt(An(e),10),Gt=e=>parseFloat(An(e)),Le=e=>Ee(e)&&isFinite(e)&&Math.floor(e)===e,Ut=(e,t=1e3)=>{if(Le(e))return e;let n=ze(e).trim();return/MB$/i.test(n)?(n=n.replace(/MB$i/,"").trim(),qe(n)*t*t):/KB/i.test(n)?(n=n.replace(/KB$i/,"").trim(),qe(n)*t):qe(n)},pe=e=>typeof e=="function",Vr=e=>{let t=self,n=e.split("."),r=null;for(;r=n.shift();)if(t=t[r],!t)return null;return t},Ft={process:"POST",patch:"PATCH",revert:"DELETE",fetch:"GET",restore:"GET",load:"GET"},qr=e=>{const t={};return t.url=j(e)?e:e.url||"",t.timeout=e.timeout?parseInt(e.timeout,10):0,t.headers=e.headers?e.headers:{},q(Ft,n=>{t[n]=xr(n,e[n],Ft[n],t.timeout,t.headers)}),t.process=e.process||j(e)||e.url?t.process:null,t.remove=e.remove||null,delete t.headers,t},xr=(e,t,n,r,s)=>{if(t===null)return null;if(typeof t=="function")return t;const o={url:n==="GET"||n==="PATCH"?`?${e}=`:"",method:n,headers:s,withCredentials:!1,timeout:r,onload:null,ondata:null,onerror:null};if(j(t))return o.url=t,o;if(Object.assign(o,t),j(o.headers)){const i=o.headers.split(/:(.+)/);o.headers={header:i[0],value:i[1]}}return o.withCredentials=Sn(o.withCredentials),o},Hr=e=>qr(e),Yr=e=>e===null,z=e=>typeof e=="object"&&e!==null,$r=e=>z(e)&&j(e.url)&&z(e.process)&&z(e.revert)&&z(e.restore)&&z(e.fetch),lt=e=>We(e)?"array":Yr(e)?"null":Le(e)?"int":/^[0-9]+ ?(?:GB|MB|KB)$/gi.test(e)?"bytes":$r(e)?"api":typeof e,Wr=e=>e.replace(/{\s*'/g,'{"').replace(/'\s*}/g,'"}').replace(/'\s*:/g,'":').replace(/:\s*'/g,':"').replace(/,\s*'/g,',"').replace(/'\s*,/g,'",'),zr={array:Br,boolean:Sn,int:e=>lt(e)==="bytes"?Ut(e):qe(e),number:Gt,float:Gt,bytes:Ut,string:e=>pe(e)?e:ze(e),function:e=>Vr(e),serverapi:Hr,object:e=>{try{return JSON.parse(Wr(e))}catch{return null}}},Xr=(e,t)=>zr[t](e),yn=(e,t,n)=>{if(e===t)return e;let r=lt(e);if(r!==n){const s=Xr(e,n);if(r=lt(s),s===null)throw`Trying to assign value with incorrect type to "${option}", allowed type: "${n}"`;e=s}return e},kr=(e,t)=>{let n=e;return{enumerable:!0,get:()=>n,set:r=>{n=yn(r,e,t)}}},Qr=e=>{const t={};return q(e,n=>{const r=e[n];t[n]=kr(r[0],r[1])}),fe(t)},jr=e=>({items:[],listUpdateTimeout:null,itemUpdateTimeout:null,processingQueue:[],options:Qr(e)}),Xe=(e,t="-")=>e.split(/(?=[A-Z])/).map(n=>n.toLowerCase()).join(t),Kr=(e,t)=>{const n={};return q(t,r=>{n[r]={get:()=>e.getState().options[r],set:s=>{e.dispatch(`SET_${Xe(r,"_").toUpperCase()}`,{value:s})}}}),n},Zr=e=>(t,n,r)=>{const s={};return q(e,o=>{const i=Xe(o,"_").toUpperCase();s[`SET_${i}`]=a=>{try{r.options[o]=a.value}catch{}t(`DID_SET_${i}`,{value:r.options[o]})}}),s},Jr=e=>t=>{const n={};return q(e,r=>{n[`GET_${Xe(r,"_").toUpperCase()}`]=s=>t.options[r]}),n},re={API:1,DROP:2,BROWSE:3,PASTE:4,NONE:5},_t=()=>Math.random().toString(36).substring(2,11),Tt=(e,t)=>e.splice(t,1),es=(e,t)=>{t?e():document.hidden?Promise.resolve(1).then(e):setTimeout(e,0)},ke=()=>{const e=[],t=(r,s)=>{Tt(e,e.findIndex(o=>o.event===r&&(o.cb===s||!s)))},n=(r,s,o)=>{e.filter(i=>i.event===r).map(i=>i.cb).forEach(i=>es(()=>i(...s),o))};return{fireSync:(r,...s)=>{n(r,s,!0)},fire:(r,...s)=>{n(r,s,!1)},on:(r,s)=>{e.push({event:r,cb:s})},onOnce:(r,s)=>{e.push({event:r,cb:(...o)=>{t(r,s),s(...o)}})},off:t}},Ln=(e,t,n)=>{Object.getOwnPropertyNames(e).filter(r=>!n.includes(r)).forEach(r=>Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r)))},ts=["fire","process","revert","load","on","off","onOnce","retryLoad","extend","archive","archived","release","released","requestProcessing","freeze"],J=e=>{const t={};return Ln(e,t,ts),t},ns=e=>{e.forEach((t,n)=>{t.released&&Tt(e,n)})},N={INIT:1,IDLE:2,PROCESSING_QUEUED:9,PROCESSING:3,PROCESSING_COMPLETE:5,PROCESSING_ERROR:6,PROCESSING_REVERT_ERROR:10,LOADING:7,LOAD_ERROR:8},W={INPUT:1,LIMBO:2,LOCAL:3},Pn=e=>/[^0-9]+/.exec(e),Mn=()=>Pn(1.1.toLocaleString())[0],rs=()=>{const e=Mn(),t=1e3.toLocaleString();return t!=="1000"?Pn(t)[0]:e==="."?",":"."},p={BOOLEAN:"boolean",INT:"int",NUMBER:"number",STRING:"string",ARRAY:"array",OBJECT:"object",FUNCTION:"function",ACTION:"action",SERVER_API:"serverapi",REGEX:"regex"},mt=[],se=(e,t,n)=>new Promise((r,s)=>{const o=mt.filter(a=>a.key===e).map(a=>a.cb);if(o.length===0){r(t);return}const i=o.shift();o.reduce((a,l)=>a.then(d=>l(d,n)),i(t,n)).then(a=>r(a)).catch(a=>s(a))}),Re=(e,t,n)=>mt.filter(r=>r.key===e).map(r=>r.cb(t,n)),ss=(e,t)=>mt.push({key:e,cb:t}),is=e=>Object.assign(De,e),He=()=>({...De}),os=e=>{q(e,(t,n)=>{De[t]&&(De[t][0]=yn(n,De[t][0],De[t][1]))})},De={id:[null,p.STRING],name:["filepond",p.STRING],disabled:[!1,p.BOOLEAN],className:[null,p.STRING],required:[!1,p.BOOLEAN],captureMethod:[null,p.STRING],allowSyncAcceptAttribute:[!0,p.BOOLEAN],allowDrop:[!0,p.BOOLEAN],allowBrowse:[!0,p.BOOLEAN],allowPaste:[!0,p.BOOLEAN],allowMultiple:[!1,p.BOOLEAN],allowReplace:[!0,p.BOOLEAN],allowRevert:[!0,p.BOOLEAN],allowRemove:[!0,p.BOOLEAN],allowProcess:[!0,p.BOOLEAN],allowReorder:[!1,p.BOOLEAN],allowDirectoriesOnly:[!1,p.BOOLEAN],storeAsFile:[!1,p.BOOLEAN],forceRevert:[!1,p.BOOLEAN],maxFiles:[null,p.INT],checkValidity:[!1,p.BOOLEAN],itemInsertLocationFreedom:[!0,p.BOOLEAN],itemInsertLocation:["before",p.STRING],itemInsertInterval:[75,p.INT],dropOnPage:[!1,p.BOOLEAN],dropOnElement:[!0,p.BOOLEAN],dropValidation:[!1,p.BOOLEAN],ignoredFiles:[[".ds_store","thumbs.db","desktop.ini"],p.ARRAY],instantUpload:[!0,p.BOOLEAN],maxParallelUploads:[2,p.INT],allowMinimumUploadDuration:[!0,p.BOOLEAN],chunkUploads:[!1,p.BOOLEAN],chunkForce:[!1,p.BOOLEAN],chunkSize:[5e6,p.INT],chunkRetryDelays:[[500,1e3,3e3],p.ARRAY],server:[null,p.SERVER_API],fileSizeBase:[1e3,p.INT],labelFileSizeBytes:["bytes",p.STRING],labelFileSizeKilobytes:["KB",p.STRING],labelFileSizeMegabytes:["MB",p.STRING],labelFileSizeGigabytes:["GB",p.STRING],labelDecimalSeparator:[Mn(),p.STRING],labelThousandsSeparator:[rs(),p.STRING],labelIdle:['Drag & Drop your files or <span class="filepond--label-action">Browse</span>',p.STRING],labelInvalidField:["Field contains invalid files",p.STRING],labelFileWaitingForSize:["Waiting for size",p.STRING],labelFileSizeNotAvailable:["Size not available",p.STRING],labelFileCountSingular:["file in list",p.STRING],labelFileCountPlural:["files in list",p.STRING],labelFileLoading:["Loading",p.STRING],labelFileAdded:["Added",p.STRING],labelFileLoadError:["Error during load",p.STRING],labelFileRemoved:["Removed",p.STRING],labelFileRemoveError:["Error during remove",p.STRING],labelFileProcessing:["Uploading",p.STRING],labelFileProcessingComplete:["Upload complete",p.STRING],labelFileProcessingAborted:["Upload cancelled",p.STRING],labelFileProcessingError:["Error during upload",p.STRING],labelFileProcessingRevertError:["Error during revert",p.STRING],labelTapToCancel:["tap to cancel",p.STRING],labelTapToRetry:["tap to retry",p.STRING],labelTapToUndo:["tap to undo",p.STRING],labelButtonRemoveItem:["Remove",p.STRING],labelButtonAbortItemLoad:["Abort",p.STRING],labelButtonRetryItemLoad:["Retry",p.STRING],labelButtonAbortItemProcessing:["Cancel",p.STRING],labelButtonUndoItemProcessing:["Undo",p.STRING],labelButtonRetryItemProcessing:["Retry",p.STRING],labelButtonProcessItem:["Upload",p.STRING],iconRemove:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M11.586 13l-2.293 2.293a1 1 0 0 0 1.414 1.414L13 14.414l2.293 2.293a1 1 0 0 0 1.414-1.414L14.414 13l2.293-2.293a1 1 0 0 0-1.414-1.414L13 11.586l-2.293-2.293a1 1 0 0 0-1.414 1.414L11.586 13z" fill="currentColor" fill-rule="nonzero"/></svg>',p.STRING],iconProcess:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M14 10.414v3.585a1 1 0 0 1-2 0v-3.585l-1.293 1.293a1 1 0 0 1-1.414-1.415l3-3a1 1 0 0 1 1.414 0l3 3a1 1 0 0 1-1.414 1.415L14 10.414zM9 18a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2H9z" fill="currentColor" fill-rule="evenodd"/></svg>',p.STRING],iconRetry:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M10.81 9.185l-.038.02A4.997 4.997 0 0 0 8 13.683a5 5 0 0 0 5 5 5 5 0 0 0 5-5 1 1 0 0 1 2 0A7 7 0 1 1 9.722 7.496l-.842-.21a.999.999 0 1 1 .484-1.94l3.23.806c.535.133.86.675.73 1.21l-.804 3.233a.997.997 0 0 1-1.21.73.997.997 0 0 1-.73-1.21l.23-.928v-.002z" fill="currentColor" fill-rule="nonzero"/></svg>',p.STRING],iconUndo:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M9.185 10.81l.02-.038A4.997 4.997 0 0 1 13.683 8a5 5 0 0 1 5 5 5 5 0 0 1-5 5 1 1 0 0 0 0 2A7 7 0 1 0 7.496 9.722l-.21-.842a.999.999 0 1 0-1.94.484l.806 3.23c.133.535.675.86 1.21.73l3.233-.803a.997.997 0 0 0 .73-1.21.997.997 0 0 0-1.21-.73l-.928.23-.002-.001z" fill="currentColor" fill-rule="nonzero"/></svg>',p.STRING],iconDone:['<svg width="26" height="26" viewBox="0 0 26 26" xmlns="http://www.w3.org/2000/svg"><path d="M18.293 9.293a1 1 0 0 1 1.414 1.414l-7.002 7a1 1 0 0 1-1.414 0l-3.998-4a1 1 0 1 1 1.414-1.414L12 15.586l6.294-6.293z" fill="currentColor" fill-rule="nonzero"/></svg>',p.STRING],oninit:[null,p.FUNCTION],onwarning:[null,p.FUNCTION],onerror:[null,p.FUNCTION],onactivatefile:[null,p.FUNCTION],oninitfile:[null,p.FUNCTION],onaddfilestart:[null,p.FUNCTION],onaddfileprogress:[null,p.FUNCTION],onaddfile:[null,p.FUNCTION],onprocessfilestart:[null,p.FUNCTION],onprocessfileprogress:[null,p.FUNCTION],onprocessfileabort:[null,p.FUNCTION],onprocessfilerevert:[null,p.FUNCTION],onprocessfile:[null,p.FUNCTION],onprocessfiles:[null,p.FUNCTION],onremovefile:[null,p.FUNCTION],onpreparefile:[null,p.FUNCTION],onupdatefiles:[null,p.FUNCTION],onreorderfiles:[null,p.FUNCTION],beforeDropFile:[null,p.FUNCTION],beforeAddFile:[null,p.FUNCTION],beforeRemoveFile:[null,p.FUNCTION],beforePrepareFile:[null,p.FUNCTION],stylePanelLayout:[null,p.STRING],stylePanelAspectRatio:[null,p.STRING],styleItemPanelAspectRatio:[null,p.STRING],styleButtonRemoveItemPosition:["left",p.STRING],styleButtonProcessItemPosition:["right",p.STRING],styleLoadIndicatorPosition:["right",p.STRING],styleProgressIndicatorPosition:["right",p.STRING],styleButtonRemoveItemAlign:[!1,p.BOOLEAN],files:[[],p.ARRAY],credits:[["https://pqina.nl/","Powered by PQINA"],p.ARRAY]},Ie=(e,t)=>ae(t)?e[0]||null:Le(t)?e[t]||null:(typeof t=="object"&&(t=t.id),e.find(n=>n.id===t)||null),bn=e=>{if(ae(e))return e;if(/:/.test(e)){const t=e.split(":");return t[1]/t[0]}return parseFloat(e)},ie=e=>e.filter(t=>!t.archived),Cn={EMPTY:0,IDLE:1,ERROR:2,BUSY:3,READY:4};let ve=null;const ls=()=>{if(ve===null)try{const e=new DataTransfer;e.items.add(new File(["hello world"],"This_Works.txt"));const t=document.createElement("input");t.setAttribute("type","file"),t.files=e.files,ve=t.files.length===1}catch{ve=!1}return ve},as=[N.LOAD_ERROR,N.PROCESSING_ERROR,N.PROCESSING_REVERT_ERROR],cs=[N.LOADING,N.PROCESSING,N.PROCESSING_QUEUED,N.INIT],ds=[N.PROCESSING_COMPLETE],us=e=>as.includes(e.status),fs=e=>cs.includes(e.status),Es=e=>ds.includes(e.status),Bt=e=>z(e.options.server)&&(z(e.options.server.process)||pe(e.options.server.process)),ps=e=>({GET_STATUS:()=>{const t=ie(e.items),{EMPTY:n,ERROR:r,BUSY:s,IDLE:o,READY:i}=Cn;return t.length===0?n:t.some(us)?r:t.some(fs)?s:t.some(Es)?i:o},GET_ITEM:t=>Ie(e.items,t),GET_ACTIVE_ITEM:t=>Ie(ie(e.items),t),GET_ACTIVE_ITEMS:()=>ie(e.items),GET_ITEMS:()=>e.items,GET_ITEM_NAME:t=>{const n=Ie(e.items,t);return n?n.filename:null},GET_ITEM_SIZE:t=>{const n=Ie(e.items,t);return n?n.fileSize:null},GET_STYLES:()=>Object.keys(e.options).filter(t=>/^style/.test(t)).map(t=>({name:t,value:e.options[t]})),GET_PANEL_ASPECT_RATIO:()=>/circle/.test(e.options.stylePanelLayout)?1:bn(e.options.stylePanelAspectRatio),GET_ITEM_PANEL_ASPECT_RATIO:()=>e.options.styleItemPanelAspectRatio,GET_ITEMS_BY_STATUS:t=>ie(e.items).filter(n=>n.status===t),GET_TOTAL_ITEMS:()=>ie(e.items).length,SHOULD_UPDATE_FILE_INPUT:()=>e.options.storeAsFile&&ls()&&!Bt(e),IS_ASYNC:()=>Bt(e),GET_FILE_SIZE_LABELS:t=>({labelBytes:t("GET_LABEL_FILE_SIZE_BYTES")||void 0,labelKilobytes:t("GET_LABEL_FILE_SIZE_KILOBYTES")||void 0,labelMegabytes:t("GET_LABEL_FILE_SIZE_MEGABYTES")||void 0,labelGigabytes:t("GET_LABEL_FILE_SIZE_GIGABYTES")||void 0})}),Is=e=>{const t=ie(e.items).length;if(!e.options.allowMultiple)return t===0;const n=e.options.maxFiles;return n===null||t<n},Nn=(e,t,n)=>Math.max(Math.min(n,e),t),_s=(e,t,n)=>e.splice(t,0,n),Ts=(e,t,n)=>ae(t)?null:typeof n>"u"?(e.push(t),t):(n=Nn(n,0,e.length),_s(e,n,t),t),at=e=>/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*)\s*$/i.test(e),Ne=e=>`${e}`.split("/").pop().split("?").shift(),Qe=e=>e.split(".").pop(),ms=e=>{if(typeof e!="string")return"";const t=e.split("/").pop();return/svg/.test(t)?"svg":/zip|compressed/.test(t)?"zip":/plain/.test(t)?"txt":/msword/.test(t)?"doc":/[a-z]+/.test(t)?t==="jpeg"?"jpg":t:""},Pe=(e,t="")=>(t+e).slice(-t.length),wn=(e=new Date)=>`${e.getFullYear()}-${Pe(e.getMonth()+1,"00")}-${Pe(e.getDate(),"00")}_${Pe(e.getHours(),"00")}-${Pe(e.getMinutes(),"00")}-${Pe(e.getSeconds(),"00")}`,ye=(e,t,n=null,r=null)=>{const s=typeof n=="string"?e.slice(0,e.size,n):e.slice(0,e.size,e.type);return s.lastModifiedDate=new Date,e._relativePath&&(s._relativePath=e._relativePath),j(t)||(t=wn()),t&&r===null&&Qe(t)?s.name=t:(r=r||ms(s.type),s.name=t+(r?"."+r:"")),s},gs=()=>window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,vn=(e,t)=>{const n=gs();if(n){const r=new n;return r.append(e),r.getBlob(t)}return new Blob([e],{type:t})},Rs=(e,t)=>{const n=new ArrayBuffer(e.length),r=new Uint8Array(n);for(let s=0;s<e.length;s++)r[s]=e.charCodeAt(s);return vn(n,t)},Gn=e=>(/^data:(.+);/.exec(e)||[])[1]||null,hs=e=>e.split(",")[1].replace(/\s/g,""),Os=e=>atob(hs(e)),Ds=e=>{const t=Gn(e),n=Os(e);return Rs(n,t)},Ss=(e,t,n)=>ye(Ds(e),t,null,n),As=e=>{if(!/^content-disposition:/i.test(e))return null;const t=e.split(/filename=|filename\*=.+''/).splice(1).map(n=>n.trim().replace(/^["']|[;"']{0,2}$/g,"")).filter(n=>n.length);return t.length?decodeURI(t[t.length-1]):null},ys=e=>{if(/content-length:/i.test(e)){const t=e.match(/[0-9]+/)[0];return t?parseInt(t,10):null}return null},Ls=e=>/x-content-transfer-id:/i.test(e)&&(e.split(":")[1]||"").trim()||null,gt=e=>{const t={source:null,name:null,size:null},n=e.split(`
`);for(let r of n){const s=As(r);if(s){t.name=s;continue}const o=ys(r);if(o){t.size=o;continue}const i=Ls(r);if(i){t.source=i;continue}}return t},Ps=e=>{const t={source:null,complete:!1,progress:0,size:null,timestamp:null,duration:0,request:null},n=()=>t.progress,r=()=>{t.request&&t.request.abort&&t.request.abort()},s=()=>{const a=t.source;i.fire("init",a),a instanceof File?i.fire("load",a):a instanceof Blob?i.fire("load",ye(a,a.name)):at(a)?i.fire("load",Ss(a)):o(a)},o=a=>{if(!e){i.fire("error",{type:"error",body:"Can't load URL",code:400});return}t.timestamp=Date.now(),t.request=e(a,l=>{t.duration=Date.now()-t.timestamp,t.complete=!0,l instanceof Blob&&(l=ye(l,l.name||Ne(a))),i.fire("load",l instanceof Blob?l:l?l.body:null)},l=>{i.fire("error",typeof l=="string"?{type:"error",code:0,body:l}:l)},(l,d,c)=>{if(c&&(t.size=c),t.duration=Date.now()-t.timestamp,!l){t.progress=null;return}t.progress=d/c,i.fire("progress",t.progress)},()=>{i.fire("abort")},l=>{const d=gt(typeof l=="string"?l:l.headers);i.fire("meta",{size:t.size||d.size,filename:d.name,source:d.source})})},i={...ke(),setSource:a=>t.source=a,getProgress:n,abort:r,load:s};return i},Vt=e=>/GET|HEAD/.test(e),_e=(e,t,n)=>{const r={onheaders:()=>{},onprogress:()=>{},onload:()=>{},ontimeout:()=>{},onerror:()=>{},onabort:()=>{},abort:()=>{s=!0,i.abort()}};let s=!1,o=!1;n={method:"POST",headers:{},withCredentials:!1,...n},t=encodeURI(t),Vt(n.method)&&e&&(t=`${t}${encodeURIComponent(typeof e=="string"?e:JSON.stringify(e))}`);const i=new XMLHttpRequest,a=Vt(n.method)?i:i.upload;return a.onprogress=l=>{s||r.onprogress(l.lengthComputable,l.loaded,l.total)},i.onreadystatechange=()=>{i.readyState<2||i.readyState===4&&i.status===0||o||(o=!0,r.onheaders(i))},i.onload=()=>{i.status>=200&&i.status<300?r.onload(i):r.onerror(i)},i.onerror=()=>r.onerror(i),i.onabort=()=>{s=!0,r.onabort()},i.ontimeout=()=>r.ontimeout(i),i.open(n.method,t,!0),Le(n.timeout)&&(i.timeout=n.timeout),Object.keys(n.headers).forEach(l=>{const d=unescape(encodeURIComponent(n.headers[l]));i.setRequestHeader(l,d)}),n.responseType&&(i.responseType=n.responseType),n.withCredentials&&(i.withCredentials=!0),i.send(e),r},x=(e,t,n,r)=>({type:e,code:t,body:n,headers:r}),Te=e=>t=>{e(x("error",0,"Timeout",t.getAllResponseHeaders()))},qt=e=>/\?/.test(e),Ce=(...e)=>{let t="";return e.forEach(n=>{t+=qt(t)&&qt(n)?n.replace(/\?/,"&"):n}),t},Je=(e="",t)=>{if(typeof t=="function")return t;if(!t||!j(t.url))return null;const n=t.onload||(s=>s),r=t.onerror||(s=>null);return(s,o,i,a,l,d)=>{const c=_e(s,Ce(e,t.url),{...t,responseType:"blob"});return c.onload=f=>{const E=f.getAllResponseHeaders(),T=gt(E).name||Ne(s);o(x("load",f.status,t.method==="HEAD"?null:ye(n(f.response),T),E))},c.onerror=f=>{i(x("error",f.status,r(f.response)||f.statusText,f.getAllResponseHeaders()))},c.onheaders=f=>{d(x("headers",f.status,null,f.getAllResponseHeaders()))},c.ontimeout=Te(i),c.onprogress=a,c.onabort=l,c}},te={QUEUED:0,COMPLETE:1,PROCESSING:2,ERROR:3,WAITING:4},Ms=(e,t,n,r,s,o,i,a,l,d,c)=>{const f=[],{chunkTransferId:E,chunkServer:T,chunkSize:u,chunkRetryDelays:_}=c,D={serverId:E,aborted:!1},R=t.ondata||(m=>m),O=t.onload||((m,U)=>U==="HEAD"?m.getResponseHeader("Upload-Offset"):m.response),P=t.onerror||(m=>null),M=m=>{const U=new FormData;z(s)&&U.append(n,JSON.stringify(s));const I=typeof t.headers=="function"?t.headers(r,s):{...t.headers,"Upload-Length":r.size},g={...t,headers:I},L=_e(R(U),Ce(e,t.url),g);L.onload=S=>m(O(S,g.method)),L.onerror=S=>i(x("error",S.status,P(S.response)||S.statusText,S.getAllResponseHeaders())),L.ontimeout=Te(i)},A=m=>{const U=Ce(e,T.url,D.serverId),g={headers:typeof t.headers=="function"?t.headers(D.serverId):{...t.headers},method:"HEAD"},L=_e(null,U,g);L.onload=S=>m(O(S,g.method)),L.onerror=S=>i(x("error",S.status,P(S.response)||S.statusText,S.getAllResponseHeaders())),L.ontimeout=Te(i)},C=Math.floor(r.size/u);for(let m=0;m<=C;m++){const U=m*u,I=r.slice(U,U+u,"application/offset+octet-stream");f[m]={index:m,size:I.size,offset:U,data:I,file:r,progress:0,retries:[..._],status:te.QUEUED,error:null,request:null,timeout:null}}const B=()=>o(D.serverId),h=m=>m.status===te.QUEUED||m.status===te.ERROR,b=m=>{if(D.aborted)return;if(m=m||f.find(h),!m){f.every(F=>F.status===te.COMPLETE)&&B();return}m.status=te.PROCESSING,m.progress=null;const U=T.ondata||(F=>F),I=T.onerror||(F=>null),g=Ce(e,T.url,D.serverId),L=typeof T.headers=="function"?T.headers(m):{...T.headers,"Content-Type":"application/offset+octet-stream","Upload-Offset":m.offset,"Upload-Length":r.size,"Upload-Name":r.name},S=m.request=_e(U(m.data),g,{...T,headers:L});S.onload=()=>{m.status=te.COMPLETE,m.request=null,w()},S.onprogress=(F,V,Ze)=>{m.progress=F?V:null,y()},S.onerror=F=>{m.status=te.ERROR,m.request=null,m.error=I(F.response)||F.statusText,G(m)||i(x("error",F.status,I(F.response)||F.statusText,F.getAllResponseHeaders()))},S.ontimeout=F=>{m.status=te.ERROR,m.request=null,G(m)||Te(i)(F)},S.onabort=()=>{m.status=te.QUEUED,m.request=null,l()}},G=m=>m.retries.length===0?!1:(m.status=te.WAITING,clearTimeout(m.timeout),m.timeout=setTimeout(()=>{b(m)},m.retries.shift()),!0),y=()=>{const m=f.reduce((I,g)=>I===null||g.progress===null?null:I+g.progress,0);if(m===null)return a(!1,0,0);const U=f.reduce((I,g)=>I+g.size,0);a(!0,m,U)},w=()=>{f.filter(U=>U.status===te.PROCESSING).length>=1||b()},X=()=>{f.forEach(m=>{clearTimeout(m.timeout),m.request&&m.request.abort()})};return D.serverId?A(m=>{D.aborted||(f.filter(U=>U.offset<m).forEach(U=>{U.status=te.COMPLETE,U.progress=U.size}),w())}):M(m=>{D.aborted||(d(m),D.serverId=m,w())}),{abort:()=>{D.aborted=!0,X()}}},bs=(e,t,n,r)=>(s,o,i,a,l,d,c)=>{if(!s)return;const f=r.chunkUploads,E=f&&s.size>r.chunkSize,T=f&&(E||r.chunkForce);if(s instanceof Blob&&T)return Ms(e,t,n,s,o,i,a,l,d,c,r);const u=t.ondata||(A=>A),_=t.onload||(A=>A),D=t.onerror||(A=>null),R=typeof t.headers=="function"?t.headers(s,o)||{}:{...t.headers},O={...t,headers:R};var P=new FormData;z(o)&&P.append(n,JSON.stringify(o)),(s instanceof Blob?[{name:null,file:s}]:s).forEach(A=>{P.append(n,A.file,A.name===null?A.file.name:`${A.name}${A.file.name}`)});const M=_e(u(P),Ce(e,t.url),O);return M.onload=A=>{i(x("load",A.status,_(A.response),A.getAllResponseHeaders()))},M.onerror=A=>{a(x("error",A.status,D(A.response)||A.statusText,A.getAllResponseHeaders()))},M.ontimeout=Te(a),M.onprogress=l,M.onabort=d,M},Cs=(e="",t,n,r)=>typeof t=="function"?(...s)=>t(n,...s,r):!t||!j(t.url)?null:bs(e,t,n,r),Me=(e="",t)=>{if(typeof t=="function")return t;if(!t||!j(t.url))return(s,o)=>o();const n=t.onload||(s=>s),r=t.onerror||(s=>null);return(s,o,i)=>{const a=_e(s,e+t.url,t);return a.onload=l=>{o(x("load",l.status,n(l.response),l.getAllResponseHeaders()))},a.onerror=l=>{i(x("error",l.status,r(l.response)||l.statusText,l.getAllResponseHeaders()))},a.ontimeout=Te(i),a}},Un=(e=0,t=1)=>e+Math.random()*(t-e),Ns=(e,t=1e3,n=0,r=25,s=250)=>{let o=null;const i=Date.now(),a=()=>{let l=Date.now()-i,d=Un(r,s);l+d>t&&(d=l+d-t);let c=l/t;if(c>=1||document.hidden){e(1);return}e(c),o=setTimeout(a,d)};return t>0&&a(),{clear:()=>{clearTimeout(o)}}},ws=(e,t)=>{const n={complete:!1,perceivedProgress:0,perceivedPerformanceUpdater:null,progress:null,timestamp:null,perceivedDuration:0,duration:0,request:null,response:null},{allowMinimumUploadDuration:r}=t,s=(c,f)=>{const E=()=>{n.duration===0||n.progress===null||d.fire("progress",d.getProgress())},T=()=>{n.complete=!0,d.fire("load-perceived",n.response.body)};d.fire("start"),n.timestamp=Date.now(),n.perceivedPerformanceUpdater=Ns(u=>{n.perceivedProgress=u,n.perceivedDuration=Date.now()-n.timestamp,E(),n.response&&n.perceivedProgress===1&&!n.complete&&T()},r?Un(750,1500):0),n.request=e(c,f,u=>{n.response=z(u)?u:{type:"load",code:200,body:`${u}`,headers:{}},n.duration=Date.now()-n.timestamp,n.progress=1,d.fire("load",n.response.body),(!r||r&&n.perceivedProgress===1)&&T()},u=>{n.perceivedPerformanceUpdater.clear(),d.fire("error",z(u)?u:{type:"error",code:0,body:`${u}`})},(u,_,D)=>{n.duration=Date.now()-n.timestamp,n.progress=u?_/D:null,E()},()=>{n.perceivedPerformanceUpdater.clear(),d.fire("abort",n.response?n.response.body:null)},u=>{d.fire("transfer",u)})},o=()=>{n.request&&(n.perceivedPerformanceUpdater.clear(),n.request.abort&&n.request.abort(),n.complete=!0)},i=()=>{o(),n.complete=!1,n.perceivedProgress=0,n.progress=0,n.timestamp=null,n.perceivedDuration=0,n.duration=0,n.request=null,n.response=null},a=r?()=>n.progress?Math.min(n.progress,n.perceivedProgress):null:()=>n.progress||null,l=r?()=>Math.min(n.duration,n.perceivedDuration):()=>n.duration,d={...ke(),process:s,abort:o,getProgress:a,getDuration:l,reset:i};return d},Fn=e=>e.substring(0,e.lastIndexOf("."))||e,vs=e=>{let t=[e.name,e.size,e.type];return e instanceof Blob||at(e)?t[0]=e.name||wn():at(e)?(t[1]=e.length,t[2]=Gn(e)):j(e)&&(t[0]=Ne(e),t[1]=0,t[2]="application/octet-stream"),{name:t[0],size:t[1],type:t[2]}},me=e=>!!(e instanceof File||e instanceof Blob&&e.name),Bn=e=>{if(!z(e))return e;const t=We(e)?[]:{};for(const n in e){if(!e.hasOwnProperty(n))continue;const r=e[n];t[n]=r&&z(r)?Bn(r):r}return t},Gs=(e=null,t=null,n=null)=>{const r=_t(),s={archived:!1,frozen:!1,released:!1,source:null,file:n,serverFileReference:t,transferId:null,processingAborted:!1,status:t?N.PROCESSING_COMPLETE:N.INIT,activeLoader:null,activeProcessor:null};let o=null;const i={},a=h=>s.status=h,l=(h,...b)=>{s.released||s.frozen||C.fire(h,...b)},d=()=>Qe(s.file.name),c=()=>s.file.type,f=()=>s.file.size,E=()=>s.file,T=(h,b,G)=>{if(s.source=h,C.fireSync("init"),s.file){C.fireSync("load-skip");return}s.file=vs(h),b.on("init",()=>{l("load-init")}),b.on("meta",y=>{s.file.size=y.size,s.file.filename=y.filename,y.source&&(e=W.LIMBO,s.serverFileReference=y.source,s.status=N.PROCESSING_COMPLETE),l("load-meta")}),b.on("progress",y=>{a(N.LOADING),l("load-progress",y)}),b.on("error",y=>{a(N.LOAD_ERROR),l("load-request-error",y)}),b.on("abort",()=>{a(N.INIT),l("load-abort")}),b.on("load",y=>{s.activeLoader=null;const w=m=>{s.file=me(m)?m:s.file,e===W.LIMBO&&s.serverFileReference?a(N.PROCESSING_COMPLETE):a(N.IDLE),l("load")},X=m=>{s.file=y,l("load-meta"),a(N.LOAD_ERROR),l("load-file-error",m)};if(s.serverFileReference){w(y);return}G(y,w,X)}),b.setSource(h),s.activeLoader=b,b.load()},u=()=>{s.activeLoader&&s.activeLoader.load()},_=()=>{if(s.activeLoader){s.activeLoader.abort();return}a(N.INIT),l("load-abort")},D=(h,b)=>{if(s.processingAborted){s.processingAborted=!1;return}if(a(N.PROCESSING),o=null,!(s.file instanceof Blob)){C.on("load",()=>{D(h,b)});return}h.on("load",w=>{s.transferId=null,s.serverFileReference=w}),h.on("transfer",w=>{s.transferId=w}),h.on("load-perceived",w=>{s.activeProcessor=null,s.transferId=null,s.serverFileReference=w,a(N.PROCESSING_COMPLETE),l("process-complete",w)}),h.on("start",()=>{l("process-start")}),h.on("error",w=>{s.activeProcessor=null,a(N.PROCESSING_ERROR),l("process-error",w)}),h.on("abort",w=>{s.activeProcessor=null,s.serverFileReference=w,a(N.IDLE),l("process-abort"),o&&o()}),h.on("progress",w=>{l("process-progress",w)});const G=w=>{s.archived||h.process(w,{...i})},y=console.error;b(s.file,G,y),s.activeProcessor=h},R=()=>{s.processingAborted=!1,a(N.PROCESSING_QUEUED)},O=()=>new Promise(h=>{if(!s.activeProcessor){s.processingAborted=!0,a(N.IDLE),l("process-abort"),h();return}o=()=>{h()},s.activeProcessor.abort()}),P=(h,b)=>new Promise((G,y)=>{const w=s.serverFileReference!==null?s.serverFileReference:s.transferId;if(w===null){G();return}h(w,()=>{s.serverFileReference=null,s.transferId=null,G()},X=>{if(!b){G();return}a(N.PROCESSING_REVERT_ERROR),l("process-revert-error"),y(X)}),a(N.IDLE),l("process-revert")}),M=(h,b,G)=>{const y=h.split("."),w=y[0],X=y.pop();let m=i;y.forEach(U=>m=m[U]),JSON.stringify(m[X])!==JSON.stringify(b)&&(m[X]=b,l("metadata-update",{key:w,value:i[w],silent:G}))},C={id:{get:()=>r},origin:{get:()=>e,set:h=>e=h},serverId:{get:()=>s.serverFileReference},transferId:{get:()=>s.transferId},status:{get:()=>s.status},filename:{get:()=>s.file.name},filenameWithoutExtension:{get:()=>Fn(s.file.name)},fileExtension:{get:d},fileType:{get:c},fileSize:{get:f},file:{get:E},relativePath:{get:()=>s.file._relativePath},source:{get:()=>s.source},getMetadata:h=>Bn(h?i[h]:i),setMetadata:(h,b,G)=>{if(z(h)){const y=h;return Object.keys(y).forEach(w=>{M(w,y[w],b)}),h}return M(h,b,G),b},extend:(h,b)=>B[h]=b,abortLoad:_,retryLoad:u,requestProcessing:R,abortProcessing:O,load:T,process:D,revert:P,...ke(),freeze:()=>s.frozen=!0,release:()=>s.released=!0,released:{get:()=>s.released},archive:()=>s.archived=!0,archived:{get:()=>s.archived},setFile:h=>s.file=h},B=fe(C);return B},Us=(e,t)=>ae(t)?0:j(t)?e.findIndex(n=>n.id===t):-1,xt=(e,t)=>{const n=Us(e,t);if(!(n<0))return e[n]||null},Ht=(e,t,n,r,s,o)=>{const i=_e(null,e,{method:"GET",responseType:"blob"});return i.onload=a=>{const l=a.getAllResponseHeaders(),d=gt(l).name||Ne(e);t(x("load",a.status,ye(a.response,d),l))},i.onerror=a=>{n(x("error",a.status,a.statusText,a.getAllResponseHeaders()))},i.onheaders=a=>{o(x("headers",a.status,null,a.getAllResponseHeaders()))},i.ontimeout=Te(n),i.onprogress=r,i.onabort=s,i},Yt=e=>(e.indexOf("//")===0&&(e=location.protocol+e),e.toLowerCase().replace("blob:","").replace(/([a-z])?:\/\//,"$1").split("/")[0]),Fs=e=>(e.indexOf(":")>-1||e.indexOf("//")>-1)&&Yt(location.href)!==Yt(e),Ge=e=>(...t)=>pe(e)?e(...t):e,Bs=e=>!me(e.file),et=(e,t)=>{clearTimeout(t.listUpdateTimeout),t.listUpdateTimeout=setTimeout(()=>{e("DID_UPDATE_ITEMS",{items:ie(t.items)})},0)},$t=(e,...t)=>new Promise(n=>{if(!e)return n(!0);const r=e(...t);if(r==null)return n(!0);if(typeof r=="boolean")return n(r);typeof r.then=="function"&&r.then(n)}),tt=(e,t)=>{e.items.sort((n,r)=>t(J(n),J(r)))},ne=(e,t)=>({query:n,success:r=()=>{},failure:s=()=>{},...o}={})=>{const i=Ie(e.items,n);if(!i){s({error:x("error",0,"Item not found"),file:null});return}t(i,r,s,o||{})},Vs=(e,t,n)=>({ABORT_ALL:()=>{ie(n.items).forEach(r=>{r.freeze(),r.abortLoad(),r.abortProcessing()})},DID_SET_FILES:({value:r=[]})=>{const s=r.map(i=>({source:i.source?i.source:i,options:i.options}));let o=ie(n.items);o.forEach(i=>{s.find(a=>a.source===i.source||a.source===i.file)||e("REMOVE_ITEM",{query:i,remove:!1})}),o=ie(n.items),s.forEach((i,a)=>{o.find(l=>l.source===i.source||l.file===i.source)||e("ADD_ITEM",{...i,interactionMethod:re.NONE,index:a})})},DID_UPDATE_ITEM_METADATA:({id:r,action:s,change:o})=>{o.silent||(clearTimeout(n.itemUpdateTimeout),n.itemUpdateTimeout=setTimeout(()=>{const i=xt(n.items,r);if(!t("IS_ASYNC")){se("SHOULD_PREPARE_OUTPUT",!1,{item:i,query:t,action:s,change:o}).then(c=>{const f=t("GET_BEFORE_PREPARE_FILE");f&&(c=f(i,c)),c&&e("REQUEST_PREPARE_OUTPUT",{query:r,item:i,success:E=>{e("DID_PREPARE_OUTPUT",{id:r,file:E})}},!0)});return}i.origin===W.LOCAL&&e("DID_LOAD_ITEM",{id:i.id,error:null,serverFileReference:i.source});const a=()=>{setTimeout(()=>{e("REQUEST_ITEM_PROCESSING",{query:r})},32)},l=c=>{i.revert(Me(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(c?a:()=>{}).catch(()=>{})},d=c=>{i.abortProcessing().then(c?a:()=>{})};if(i.status===N.PROCESSING_COMPLETE)return l(n.options.instantUpload);if(i.status===N.PROCESSING)return d(n.options.instantUpload);n.options.instantUpload&&a()},0))},MOVE_ITEM:({query:r,index:s})=>{const o=Ie(n.items,r);if(!o)return;const i=n.items.indexOf(o);s=Nn(s,0,n.items.length-1),i!==s&&n.items.splice(s,0,n.items.splice(i,1)[0])},SORT:({compare:r})=>{tt(n,r),e("DID_SORT_ITEMS",{items:t("GET_ACTIVE_ITEMS")})},ADD_ITEMS:({items:r,index:s,interactionMethod:o,success:i=()=>{},failure:a=()=>{}})=>{let l=s;if(s===-1||typeof s>"u"){const T=t("GET_ITEM_INSERT_LOCATION"),u=t("GET_TOTAL_ITEMS");l=T==="before"?0:u}const d=t("GET_IGNORED_FILES"),c=T=>me(T)?!d.includes(T.name.toLowerCase()):!ae(T),E=r.filter(c).map(T=>new Promise((u,_)=>{e("ADD_ITEM",{interactionMethod:o,source:T.source||T,success:u,failure:_,index:l++,options:T.options||{}})}));Promise.all(E).then(i).catch(a)},ADD_ITEM:({source:r,index:s=-1,interactionMethod:o,success:i=()=>{},failure:a=()=>{},options:l={}})=>{if(ae(r)){a({error:x("error",0,"No source"),file:null});return}if(me(r)&&n.options.ignoredFiles.includes(r.name.toLowerCase()))return;if(!Is(n)){if(n.options.allowMultiple||!n.options.allowMultiple&&!n.options.allowReplace){const O=x("warning",0,"Max files");e("DID_THROW_MAX_FILES",{source:r,error:O}),a({error:O,file:null});return}const R=ie(n.items)[0];if(R.status===N.PROCESSING_COMPLETE||R.status===N.PROCESSING_REVERT_ERROR){const O=t("GET_FORCE_REVERT");if(R.revert(Me(n.options.server.url,n.options.server.revert),O).then(()=>{O&&e("ADD_ITEM",{source:r,index:s,interactionMethod:o,success:i,failure:a,options:l})}).catch(()=>{}),O)return}e("REMOVE_ITEM",{query:R.id})}const d=l.type==="local"?W.LOCAL:l.type==="limbo"?W.LIMBO:W.INPUT,c=Gs(d,d===W.INPUT?null:r,l.file);Object.keys(l.metadata||{}).forEach(R=>{c.setMetadata(R,l.metadata[R])}),Re("DID_CREATE_ITEM",c,{query:t,dispatch:e});const f=t("GET_ITEM_INSERT_LOCATION");n.options.itemInsertLocationFreedom||(s=f==="before"?-1:n.items.length),Ts(n.items,c,s),pe(f)&&r&&tt(n,f);const E=c.id;c.on("init",()=>{e("DID_INIT_ITEM",{id:E})}),c.on("load-init",()=>{e("DID_START_ITEM_LOAD",{id:E})}),c.on("load-meta",()=>{e("DID_UPDATE_ITEM_META",{id:E})}),c.on("load-progress",R=>{e("DID_UPDATE_ITEM_LOAD_PROGRESS",{id:E,progress:R})}),c.on("load-request-error",R=>{const O=Ge(n.options.labelFileLoadError)(R);if(R.code>=400&&R.code<500){e("DID_THROW_ITEM_INVALID",{id:E,error:R,status:{main:O,sub:`${R.code} (${R.body})`}}),a({error:R,file:J(c)});return}e("DID_THROW_ITEM_LOAD_ERROR",{id:E,error:R,status:{main:O,sub:n.options.labelTapToRetry}})}),c.on("load-file-error",R=>{e("DID_THROW_ITEM_INVALID",{id:E,error:R.status,status:R.status}),a({error:R.status,file:J(c)})}),c.on("load-abort",()=>{e("REMOVE_ITEM",{query:E})}),c.on("load-skip",()=>{c.on("metadata-update",R=>{me(c.file)&&e("DID_UPDATE_ITEM_METADATA",{id:E,change:R})}),e("COMPLETE_LOAD_ITEM",{query:E,item:c,data:{source:r,success:i}})}),c.on("load",()=>{const R=O=>{if(!O){e("REMOVE_ITEM",{query:E});return}c.on("metadata-update",P=>{e("DID_UPDATE_ITEM_METADATA",{id:E,change:P})}),se("SHOULD_PREPARE_OUTPUT",!1,{item:c,query:t}).then(P=>{const M=t("GET_BEFORE_PREPARE_FILE");M&&(P=M(c,P));const A=()=>{e("COMPLETE_LOAD_ITEM",{query:E,item:c,data:{source:r,success:i}}),et(e,n)};if(P){e("REQUEST_PREPARE_OUTPUT",{query:E,item:c,success:C=>{e("DID_PREPARE_OUTPUT",{id:E,file:C}),A()}},!0);return}A()})};se("DID_LOAD_ITEM",c,{query:t,dispatch:e}).then(()=>{$t(t("GET_BEFORE_ADD_FILE"),J(c)).then(R)}).catch(O=>{if(!O||!O.error||!O.status)return R(!1);e("DID_THROW_ITEM_INVALID",{id:E,error:O.error,status:O.status})})}),c.on("process-start",()=>{e("DID_START_ITEM_PROCESSING",{id:E})}),c.on("process-progress",R=>{e("DID_UPDATE_ITEM_PROCESS_PROGRESS",{id:E,progress:R})}),c.on("process-error",R=>{e("DID_THROW_ITEM_PROCESSING_ERROR",{id:E,error:R,status:{main:Ge(n.options.labelFileProcessingError)(R),sub:n.options.labelTapToRetry}})}),c.on("process-revert-error",R=>{e("DID_THROW_ITEM_PROCESSING_REVERT_ERROR",{id:E,error:R,status:{main:Ge(n.options.labelFileProcessingRevertError)(R),sub:n.options.labelTapToRetry}})}),c.on("process-complete",R=>{e("DID_COMPLETE_ITEM_PROCESSING",{id:E,error:null,serverFileReference:R}),e("DID_DEFINE_VALUE",{id:E,value:R})}),c.on("process-abort",()=>{e("DID_ABORT_ITEM_PROCESSING",{id:E})}),c.on("process-revert",()=>{e("DID_REVERT_ITEM_PROCESSING",{id:E}),e("DID_DEFINE_VALUE",{id:E,value:null})}),e("DID_ADD_ITEM",{id:E,index:s,interactionMethod:o}),et(e,n);const{url:T,load:u,restore:_,fetch:D}=n.options.server||{};c.load(r,Ps(d===W.INPUT?j(r)&&Fs(r)&&D?Je(T,D):Ht:d===W.LIMBO?Je(T,_):Je(T,u)),(R,O,P)=>{se("LOAD_FILE",R,{query:t}).then(O).catch(P)})},REQUEST_PREPARE_OUTPUT:({item:r,success:s,failure:o=()=>{}})=>{const i={error:x("error",0,"Item not found"),file:null};if(r.archived)return o(i);se("PREPARE_OUTPUT",r.file,{query:t,item:r}).then(a=>{se("COMPLETE_PREPARE_OUTPUT",a,{query:t,item:r}).then(l=>{if(r.archived)return o(i);s(l)})})},COMPLETE_LOAD_ITEM:({item:r,data:s})=>{const{success:o,source:i}=s,a=t("GET_ITEM_INSERT_LOCATION");if(pe(a)&&i&&tt(n,a),e("DID_LOAD_ITEM",{id:r.id,error:null,serverFileReference:r.origin===W.INPUT?null:i}),o(J(r)),r.origin===W.LOCAL){e("DID_LOAD_LOCAL_ITEM",{id:r.id});return}if(r.origin===W.LIMBO){e("DID_COMPLETE_ITEM_PROCESSING",{id:r.id,error:null,serverFileReference:i}),e("DID_DEFINE_VALUE",{id:r.id,value:r.serverId||i});return}t("IS_ASYNC")&&n.options.instantUpload&&e("REQUEST_ITEM_PROCESSING",{query:r.id})},RETRY_ITEM_LOAD:ne(n,r=>{r.retryLoad()}),REQUEST_ITEM_PREPARE:ne(n,(r,s,o)=>{e("REQUEST_PREPARE_OUTPUT",{query:r.id,item:r,success:i=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:i}),s({file:r,output:i})},failure:o},!0)}),REQUEST_ITEM_PROCESSING:ne(n,(r,s,o)=>{if(!(r.status===N.IDLE||r.status===N.PROCESSING_ERROR)){const a=()=>e("REQUEST_ITEM_PROCESSING",{query:r,success:s,failure:o}),l=()=>document.hidden?a():setTimeout(a,32);r.status===N.PROCESSING_COMPLETE||r.status===N.PROCESSING_REVERT_ERROR?r.revert(Me(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(l).catch(()=>{}):r.status===N.PROCESSING&&r.abortProcessing().then(l);return}r.status!==N.PROCESSING_QUEUED&&(r.requestProcessing(),e("DID_REQUEST_ITEM_PROCESSING",{id:r.id}),e("PROCESS_ITEM",{query:r,success:s,failure:o},!0))}),PROCESS_ITEM:ne(n,(r,s,o)=>{const i=t("GET_MAX_PARALLEL_UPLOADS");if(t("GET_ITEMS_BY_STATUS",N.PROCESSING).length===i){n.processingQueue.push({id:r.id,success:s,failure:o});return}if(r.status===N.PROCESSING)return;const l=()=>{const c=n.processingQueue.shift();if(!c)return;const{id:f,success:E,failure:T}=c,u=Ie(n.items,f);if(!u||u.archived){l();return}e("PROCESS_ITEM",{query:f,success:E,failure:T},!0)};r.onOnce("process-complete",()=>{s(J(r)),l();const c=n.options.server;if(n.options.instantUpload&&r.origin===W.LOCAL&&pe(c.remove)){const T=()=>{};r.origin=W.LIMBO,n.options.server.remove(r.source,T,T)}t("GET_ITEMS_BY_STATUS",N.PROCESSING_COMPLETE).length===n.items.length&&e("DID_COMPLETE_ITEM_PROCESSING_ALL")}),r.onOnce("process-error",c=>{o({error:c,file:J(r)}),l()});const d=n.options;r.process(ws(Cs(d.server.url,d.server.process,d.name,{chunkTransferId:r.transferId,chunkServer:d.server.patch,chunkUploads:d.chunkUploads,chunkForce:d.chunkForce,chunkSize:d.chunkSize,chunkRetryDelays:d.chunkRetryDelays}),{allowMinimumUploadDuration:t("GET_ALLOW_MINIMUM_UPLOAD_DURATION")}),(c,f,E)=>{se("PREPARE_OUTPUT",c,{query:t,item:r}).then(T=>{e("DID_PREPARE_OUTPUT",{id:r.id,file:T}),f(T)}).catch(E)})}),RETRY_ITEM_PROCESSING:ne(n,r=>{e("REQUEST_ITEM_PROCESSING",{query:r})}),REQUEST_REMOVE_ITEM:ne(n,r=>{$t(t("GET_BEFORE_REMOVE_FILE"),J(r)).then(s=>{s&&e("REMOVE_ITEM",{query:r})})}),RELEASE_ITEM:ne(n,r=>{r.release()}),REMOVE_ITEM:ne(n,(r,s,o,i)=>{const a=()=>{const d=r.id;xt(n.items,d).archive(),e("DID_REMOVE_ITEM",{error:null,id:d,item:r}),et(e,n),s(J(r))},l=n.options.server;r.origin===W.LOCAL&&l&&pe(l.remove)&&i.remove!==!1?(e("DID_START_ITEM_REMOVE",{id:r.id}),l.remove(r.source,()=>a(),d=>{e("DID_THROW_ITEM_REMOVE_ERROR",{id:r.id,error:x("error",0,d,null),status:{main:Ge(n.options.labelFileRemoveError)(d),sub:n.options.labelTapToRetry}})})):((i.revert&&r.origin!==W.LOCAL&&r.serverId!==null||n.options.chunkUploads&&r.file.size>n.options.chunkSize||n.options.chunkUploads&&n.options.chunkForce)&&r.revert(Me(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")),a())}),ABORT_ITEM_LOAD:ne(n,r=>{r.abortLoad()}),ABORT_ITEM_PROCESSING:ne(n,r=>{if(r.serverId){e("REVERT_ITEM_PROCESSING",{id:r.id});return}r.abortProcessing().then(()=>{n.options.instantUpload&&e("REMOVE_ITEM",{query:r.id})})}),REQUEST_REVERT_ITEM_PROCESSING:ne(n,r=>{if(!n.options.instantUpload){e("REVERT_ITEM_PROCESSING",{query:r});return}const s=a=>{a&&e("REVERT_ITEM_PROCESSING",{query:r})},o=t("GET_BEFORE_REMOVE_FILE");if(!o)return s(!0);const i=o(J(r));if(i==null)return s(!0);if(typeof i=="boolean")return s(i);typeof i.then=="function"&&i.then(s)}),REVERT_ITEM_PROCESSING:ne(n,r=>{r.revert(Me(n.options.server.url,n.options.server.revert),t("GET_FORCE_REVERT")).then(()=>{(n.options.instantUpload||Bs(r))&&e("REMOVE_ITEM",{query:r.id})}).catch(()=>{})}),SET_OPTIONS:({options:r})=>{const s=Object.keys(r),o=qs.filter(a=>s.includes(a));[...o,...Object.keys(r).filter(a=>!o.includes(a))].forEach(a=>{e(`SET_${Xe(a,"_").toUpperCase()}`,{value:r[a]})})}}),qs=["server"],Rt=e=>e,ce=e=>document.createElement(e),H=(e,t)=>{let n=e.childNodes[0];n?t!==n.nodeValue&&(n.nodeValue=t):(n=document.createTextNode(t),e.appendChild(n))},Wt=(e,t,n,r)=>{const s=(r%360-90)*Math.PI/180;return{x:e+n*Math.cos(s),y:t+n*Math.sin(s)}},xs=(e,t,n,r,s,o)=>{const i=Wt(e,t,n,s),a=Wt(e,t,n,r);return["M",i.x,i.y,"A",n,n,0,o,0,a.x,a.y].join(" ")},Hs=(e,t,n,r,s)=>{let o=1;return s>r&&s-r<=.5&&(o=0),r>s&&r-s>=.5&&(o=0),xs(e,t,n,Math.min(.9999,r)*360,Math.min(.9999,s)*360,o)},Ys=({root:e,props:t})=>{t.spin=!1,t.progress=0,t.opacity=0;const n=xe("svg");e.ref.path=xe("path",{"stroke-width":2,"stroke-linecap":"round"}),n.appendChild(e.ref.path),e.ref.svg=n,e.appendChild(n)},$s=({root:e,props:t})=>{if(t.opacity===0)return;t.align&&(e.element.dataset.align=t.align);const n=parseInt(Y(e.ref.path,"stroke-width"),10),r=e.rect.element.width*.5;let s=0,o=0;t.spin?(s=0,o=.5):(s=0,o=t.progress);const i=Hs(r,r,r-n,s,o);Y(e.ref.path,"d",i),Y(e.ref.path,"stroke-opacity",t.spin||t.progress>0?1:0)},zt=$({tag:"div",name:"progress-indicator",ignoreRectUpdate:!0,ignoreRect:!0,create:Ys,write:$s,mixins:{apis:["progress","spin","align"],styles:["opacity"],animations:{opacity:{type:"tween",duration:500},progress:{type:"spring",stiffness:.95,damping:.65,mass:10}}}}),Ws=({root:e,props:t})=>{e.element.innerHTML=(t.icon||"")+`<span>${t.label}</span>`,t.isDisabled=!1},zs=({root:e,props:t})=>{const{isDisabled:n}=t,r=e.query("GET_DISABLED")||t.opacity===0;r&&!n?(t.isDisabled=!0,Y(e.element,"disabled","disabled")):!r&&n&&(t.isDisabled=!1,e.element.removeAttribute("disabled"))},Vn=$({tag:"button",attributes:{type:"button"},ignoreRect:!0,ignoreRectUpdate:!0,name:"file-action-button",mixins:{apis:["label"],styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}},listeners:!0},create:Ws,write:zs}),qn=(e,t=".",n=1e3,r={})=>{const{labelBytes:s="bytes",labelKilobytes:o="KB",labelMegabytes:i="MB",labelGigabytes:a="GB"}=r;e=Math.round(Math.abs(e));const l=n,d=n*n,c=n*n*n;return e<l?`${e} ${s}`:e<d?`${Math.floor(e/l)} ${o}`:e<c?`${Xt(e/d,1,t)} ${i}`:`${Xt(e/c,2,t)} ${a}`},Xt=(e,t,n)=>e.toFixed(t).split(".").filter(r=>r!=="0").join(n),Xs=({root:e,props:t})=>{const n=ce("span");n.className="filepond--file-info-main",Y(n,"aria-hidden","true"),e.appendChild(n),e.ref.fileName=n;const r=ce("span");r.className="filepond--file-info-sub",e.appendChild(r),e.ref.fileSize=r,H(r,e.query("GET_LABEL_FILE_WAITING_FOR_SIZE")),H(n,Rt(e.query("GET_ITEM_NAME",t.id)))},ct=({root:e,props:t})=>{H(e.ref.fileSize,qn(e.query("GET_ITEM_SIZE",t.id),".",e.query("GET_FILE_SIZE_BASE"),e.query("GET_FILE_SIZE_LABELS",e.query))),H(e.ref.fileName,Rt(e.query("GET_ITEM_NAME",t.id)))},kt=({root:e,props:t})=>{if(Le(e.query("GET_ITEM_SIZE",t.id))){ct({root:e,props:t});return}H(e.ref.fileSize,e.query("GET_LABEL_FILE_SIZE_NOT_AVAILABLE"))},ks=$({name:"file-info",ignoreRect:!0,ignoreRectUpdate:!0,write:K({DID_LOAD_ITEM:ct,DID_UPDATE_ITEM_META:ct,DID_THROW_ITEM_LOAD_ERROR:kt,DID_THROW_ITEM_INVALID:kt}),didCreateView:e=>{Re("CREATE_VIEW",{...e,view:e})},create:Xs,mixins:{styles:["translateX","translateY"],animations:{translateX:"spring",translateY:"spring"}}}),xn=e=>Math.round(e*100),Qs=({root:e})=>{const t=ce("span");t.className="filepond--file-status-main",e.appendChild(t),e.ref.main=t;const n=ce("span");n.className="filepond--file-status-sub",e.appendChild(n),e.ref.sub=n,Hn({root:e,action:{progress:null}})},Hn=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_LOADING"):`${e.query("GET_LABEL_FILE_LOADING")} ${xn(t.progress)}%`;H(e.ref.main,n),H(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},js=({root:e,action:t})=>{const n=t.progress===null?e.query("GET_LABEL_FILE_PROCESSING"):`${e.query("GET_LABEL_FILE_PROCESSING")} ${xn(t.progress)}%`;H(e.ref.main,n),H(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Ks=({root:e})=>{H(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING")),H(e.ref.sub,e.query("GET_LABEL_TAP_TO_CANCEL"))},Zs=({root:e})=>{H(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_ABORTED")),H(e.ref.sub,e.query("GET_LABEL_TAP_TO_RETRY"))},Js=({root:e})=>{H(e.ref.main,e.query("GET_LABEL_FILE_PROCESSING_COMPLETE")),H(e.ref.sub,e.query("GET_LABEL_TAP_TO_UNDO"))},Qt=({root:e})=>{H(e.ref.main,""),H(e.ref.sub,"")},be=({root:e,action:t})=>{H(e.ref.main,t.status.main),H(e.ref.sub,t.status.sub)},ei=$({name:"file-status",ignoreRect:!0,ignoreRectUpdate:!0,write:K({DID_LOAD_ITEM:Qt,DID_REVERT_ITEM_PROCESSING:Qt,DID_REQUEST_ITEM_PROCESSING:Ks,DID_ABORT_ITEM_PROCESSING:Zs,DID_COMPLETE_ITEM_PROCESSING:Js,DID_UPDATE_ITEM_PROCESS_PROGRESS:js,DID_UPDATE_ITEM_LOAD_PROGRESS:Hn,DID_THROW_ITEM_LOAD_ERROR:be,DID_THROW_ITEM_INVALID:be,DID_THROW_ITEM_PROCESSING_ERROR:be,DID_THROW_ITEM_PROCESSING_REVERT_ERROR:be,DID_THROW_ITEM_REMOVE_ERROR:be}),didCreateView:e=>{Re("CREATE_VIEW",{...e,view:e})},create:Qs,mixins:{styles:["translateX","translateY","opacity"],animations:{opacity:{type:"tween",duration:250},translateX:"spring",translateY:"spring"}}}),dt={AbortItemLoad:{label:"GET_LABEL_BUTTON_ABORT_ITEM_LOAD",action:"ABORT_ITEM_LOAD",className:"filepond--action-abort-item-load",align:"LOAD_INDICATOR_POSITION"},RetryItemLoad:{label:"GET_LABEL_BUTTON_RETRY_ITEM_LOAD",action:"RETRY_ITEM_LOAD",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-load",align:"BUTTON_PROCESS_ITEM_POSITION"},RemoveItem:{label:"GET_LABEL_BUTTON_REMOVE_ITEM",action:"REQUEST_REMOVE_ITEM",icon:"GET_ICON_REMOVE",className:"filepond--action-remove-item",align:"BUTTON_REMOVE_ITEM_POSITION"},ProcessItem:{label:"GET_LABEL_BUTTON_PROCESS_ITEM",action:"REQUEST_ITEM_PROCESSING",icon:"GET_ICON_PROCESS",className:"filepond--action-process-item",align:"BUTTON_PROCESS_ITEM_POSITION"},AbortItemProcessing:{label:"GET_LABEL_BUTTON_ABORT_ITEM_PROCESSING",action:"ABORT_ITEM_PROCESSING",className:"filepond--action-abort-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RetryItemProcessing:{label:"GET_LABEL_BUTTON_RETRY_ITEM_PROCESSING",action:"RETRY_ITEM_PROCESSING",icon:"GET_ICON_RETRY",className:"filepond--action-retry-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"},RevertItemProcessing:{label:"GET_LABEL_BUTTON_UNDO_ITEM_PROCESSING",action:"REQUEST_REVERT_ITEM_PROCESSING",icon:"GET_ICON_UNDO",className:"filepond--action-revert-item-processing",align:"BUTTON_PROCESS_ITEM_POSITION"}},ut=[];q(dt,e=>{ut.push(e)});const ee=e=>{if(ft(e)==="right")return 0;const t=e.ref.buttonRemoveItem.rect.element;return t.hidden?null:t.width+t.left},ti=e=>e.ref.buttonAbortItemLoad.rect.element.width,Ue=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.height/4),ni=e=>Math.floor(e.ref.buttonRemoveItem.rect.element.left/2),ri=e=>e.query("GET_STYLE_LOAD_INDICATOR_POSITION"),si=e=>e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION"),ft=e=>e.query("GET_STYLE_BUTTON_REMOVE_ITEM_POSITION"),ii={buttonAbortItemLoad:{opacity:0},buttonRetryItemLoad:{opacity:0},buttonRemoveItem:{opacity:0},buttonProcessItem:{opacity:0},buttonAbortItemProcessing:{opacity:0},buttonRetryItemProcessing:{opacity:0},buttonRevertItemProcessing:{opacity:0},loadProgressIndicator:{opacity:0,align:ri},processProgressIndicator:{opacity:0,align:si},processingCompleteIndicator:{opacity:0,scaleX:.75,scaleY:.75},info:{translateX:0,translateY:0,opacity:0},status:{translateX:0,translateY:0,opacity:0}},jt={buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:ee},status:{translateX:ee}},nt={buttonAbortItemProcessing:{opacity:1},processProgressIndicator:{opacity:1},status:{opacity:1}},Se={DID_THROW_ITEM_INVALID:{buttonRemoveItem:{opacity:1},info:{translateX:ee},status:{translateX:ee,opacity:1}},DID_START_ITEM_LOAD:{buttonAbortItemLoad:{opacity:1},loadProgressIndicator:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_LOAD_ERROR:{buttonRetryItemLoad:{opacity:1},buttonRemoveItem:{opacity:1},info:{translateX:ee},status:{opacity:1}},DID_START_ITEM_REMOVE:{processProgressIndicator:{opacity:1,align:ft},info:{translateX:ee},status:{opacity:0}},DID_THROW_ITEM_REMOVE_ERROR:{processProgressIndicator:{opacity:0,align:ft},buttonRemoveItem:{opacity:1},info:{translateX:ee},status:{opacity:1,translateX:ee}},DID_LOAD_ITEM:jt,DID_LOAD_LOCAL_ITEM:{buttonRemoveItem:{opacity:1},info:{translateX:ee},status:{translateX:ee}},DID_START_ITEM_PROCESSING:nt,DID_REQUEST_ITEM_PROCESSING:nt,DID_UPDATE_ITEM_PROCESS_PROGRESS:nt,DID_COMPLETE_ITEM_PROCESSING:{buttonRevertItemProcessing:{opacity:1},info:{opacity:1},status:{opacity:1}},DID_THROW_ITEM_PROCESSING_ERROR:{buttonRemoveItem:{opacity:1},buttonRetryItemProcessing:{opacity:1},status:{opacity:1},info:{translateX:ee}},DID_THROW_ITEM_PROCESSING_REVERT_ERROR:{buttonRevertItemProcessing:{opacity:1},status:{opacity:1},info:{opacity:1}},DID_ABORT_ITEM_PROCESSING:{buttonRemoveItem:{opacity:1},buttonProcessItem:{opacity:1},info:{translateX:ee},status:{opacity:1}},DID_REVERT_ITEM_PROCESSING:jt},oi=$({create:({root:e})=>{e.element.innerHTML=e.query("GET_ICON_DONE")},name:"processing-complete-indicator",ignoreRect:!0,mixins:{styles:["scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",opacity:{type:"tween",duration:250}}}}),li=({root:e,props:t})=>{const n=Object.keys(dt).reduce((u,_)=>(u[_]={...dt[_]},u),{}),{id:r}=t,s=e.query("GET_ALLOW_REVERT"),o=e.query("GET_ALLOW_REMOVE"),i=e.query("GET_ALLOW_PROCESS"),a=e.query("GET_INSTANT_UPLOAD"),l=e.query("IS_ASYNC"),d=e.query("GET_STYLE_BUTTON_REMOVE_ITEM_ALIGN");let c;l?i&&!s?c=u=>!/RevertItemProcessing/.test(u):!i&&s?c=u=>!/ProcessItem|RetryItemProcessing|AbortItemProcessing/.test(u):!i&&!s&&(c=u=>!/Process/.test(u)):c=u=>!/Process/.test(u);const f=c?ut.filter(c):ut.concat();if(a&&s&&(n.RevertItemProcessing.label="GET_LABEL_BUTTON_REMOVE_ITEM",n.RevertItemProcessing.icon="GET_ICON_REMOVE"),l&&!s){const u=Se.DID_COMPLETE_ITEM_PROCESSING;u.info.translateX=ni,u.info.translateY=Ue,u.status.translateY=Ue,u.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}if(l&&!i&&(["DID_START_ITEM_PROCESSING","DID_REQUEST_ITEM_PROCESSING","DID_UPDATE_ITEM_PROCESS_PROGRESS","DID_THROW_ITEM_PROCESSING_ERROR"].forEach(u=>{Se[u].status.translateY=Ue}),Se.DID_THROW_ITEM_PROCESSING_ERROR.status.translateX=ti),d&&s){n.RevertItemProcessing.align="BUTTON_REMOVE_ITEM_POSITION";const u=Se.DID_COMPLETE_ITEM_PROCESSING;u.info.translateX=ee,u.status.translateY=Ue,u.processingCompleteIndicator={opacity:1,scaleX:1,scaleY:1}}o||(n.RemoveItem.disabled=!0),q(n,(u,_)=>{const D=e.createChildView(Vn,{label:e.query(_.label),icon:e.query(_.icon),opacity:0});f.includes(u)&&e.appendChildView(D),_.disabled&&(D.element.setAttribute("disabled","disabled"),D.element.setAttribute("hidden","hidden")),D.element.dataset.align=e.query(`GET_STYLE_${_.align}`),D.element.classList.add(_.className),D.on("click",R=>{R.stopPropagation(),!_.disabled&&e.dispatch(_.action,{query:r})}),e.ref[`button${u}`]=D}),e.ref.processingCompleteIndicator=e.appendChildView(e.createChildView(oi)),e.ref.processingCompleteIndicator.element.dataset.align=e.query("GET_STYLE_BUTTON_PROCESS_ITEM_POSITION"),e.ref.info=e.appendChildView(e.createChildView(ks,{id:r})),e.ref.status=e.appendChildView(e.createChildView(ei,{id:r}));const E=e.appendChildView(e.createChildView(zt,{opacity:0,align:e.query("GET_STYLE_LOAD_INDICATOR_POSITION")}));E.element.classList.add("filepond--load-indicator"),e.ref.loadProgressIndicator=E;const T=e.appendChildView(e.createChildView(zt,{opacity:0,align:e.query("GET_STYLE_PROGRESS_INDICATOR_POSITION")}));T.element.classList.add("filepond--process-indicator"),e.ref.processProgressIndicator=T,e.ref.activeStyles=[]},ai=({root:e,actions:t,props:n})=>{ci({root:e,actions:t,props:n});let r=t.concat().filter(s=>/^DID_/.test(s.type)).reverse().find(s=>Se[s.type]);if(r){e.ref.activeStyles=[];const s=Se[r.type];q(ii,(o,i)=>{const a=e.ref[o];q(i,(l,d)=>{const c=s[o]&&typeof s[o][l]<"u"?s[o][l]:d;e.ref.activeStyles.push({control:a,key:l,value:c})})})}e.ref.activeStyles.forEach(({control:s,key:o,value:i})=>{s[o]=typeof i=="function"?i(e):i})},ci=K({DID_SET_LABEL_BUTTON_ABORT_ITEM_PROCESSING:({root:e,action:t})=>{e.ref.buttonAbortItemProcessing.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_LOAD:({root:e,action:t})=>{e.ref.buttonAbortItemLoad.label=t.value},DID_SET_LABEL_BUTTON_ABORT_ITEM_REMOVAL:({root:e,action:t})=>{e.ref.buttonAbortItemRemoval.label=t.value},DID_REQUEST_ITEM_PROCESSING:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_START_ITEM_LOAD:({root:e})=>{e.ref.loadProgressIndicator.spin=!0,e.ref.loadProgressIndicator.progress=0},DID_START_ITEM_REMOVE:({root:e})=>{e.ref.processProgressIndicator.spin=!0,e.ref.processProgressIndicator.progress=0},DID_UPDATE_ITEM_LOAD_PROGRESS:({root:e,action:t})=>{e.ref.loadProgressIndicator.spin=!1,e.ref.loadProgressIndicator.progress=t.progress},DID_UPDATE_ITEM_PROCESS_PROGRESS:({root:e,action:t})=>{e.ref.processProgressIndicator.spin=!1,e.ref.processProgressIndicator.progress=t.progress}}),di=$({create:li,write:ai,didCreateView:e=>{Re("CREATE_VIEW",{...e,view:e})},name:"file"}),ui=({root:e,props:t})=>{e.ref.fileName=ce("legend"),e.appendChild(e.ref.fileName),e.ref.file=e.appendChildView(e.createChildView(di,{id:t.id})),e.ref.data=!1},fi=({root:e,props:t})=>{H(e.ref.fileName,Rt(e.query("GET_ITEM_NAME",t.id)))},Ei=$({create:ui,ignoreRect:!0,write:K({DID_LOAD_ITEM:fi}),didCreateView:e=>{Re("CREATE_VIEW",{...e,view:e})},tag:"fieldset",name:"file-wrapper"}),Kt={type:"spring",damping:.6,mass:7},pi=({root:e,props:t})=>{[{name:"top"},{name:"center",props:{translateY:null,scaleY:null},mixins:{animations:{scaleY:Kt},styles:["translateY","scaleY"]}},{name:"bottom",props:{translateY:null},mixins:{animations:{translateY:Kt},styles:["translateY"]}}].forEach(n=>{Ii(e,n,t.name)}),e.element.classList.add(`filepond--${t.name}`),e.ref.scalable=null},Ii=(e,t,n)=>{const r=$({name:`panel-${t.name} filepond--${n}`,mixins:t.mixins,ignoreRectUpdate:!0}),s=e.createChildView(r,t.props);e.ref[t.name]=e.appendChildView(s)},_i=({root:e,props:t})=>{if((e.ref.scalable===null||t.scalable!==e.ref.scalable)&&(e.ref.scalable=Dn(t.scalable)?t.scalable:!0,e.element.dataset.scalable=e.ref.scalable),!t.height)return;const n=e.ref.top.rect.element,r=e.ref.bottom.rect.element,s=Math.max(n.height+r.height,t.height);e.ref.center.translateY=n.height,e.ref.center.scaleY=(s-n.height-r.height)/100,e.ref.bottom.translateY=s-r.height},Yn=$({name:"panel",read:({root:e,props:t})=>t.heightCurrent=e.ref.bottom.translateY,write:_i,create:pi,ignoreRect:!0,mixins:{apis:["height","heightCurrent","scalable"]}}),Ti=e=>{const t=e.map(r=>r.id);let n;return{setIndex:r=>{n=r},getIndex:()=>n,getItemIndex:r=>t.indexOf(r.id)}},Zt={type:"spring",stiffness:.75,damping:.45,mass:10},Jt="spring",en={DID_START_ITEM_LOAD:"busy",DID_UPDATE_ITEM_LOAD_PROGRESS:"loading",DID_THROW_ITEM_INVALID:"load-invalid",DID_THROW_ITEM_LOAD_ERROR:"load-error",DID_LOAD_ITEM:"idle",DID_THROW_ITEM_REMOVE_ERROR:"remove-error",DID_START_ITEM_REMOVE:"busy",DID_START_ITEM_PROCESSING:"busy processing",DID_REQUEST_ITEM_PROCESSING:"busy processing",DID_UPDATE_ITEM_PROCESS_PROGRESS:"processing",DID_COMPLETE_ITEM_PROCESSING:"processing-complete",DID_THROW_ITEM_PROCESSING_ERROR:"processing-error",DID_THROW_ITEM_PROCESSING_REVERT_ERROR:"processing-revert-error",DID_ABORT_ITEM_PROCESSING:"cancelled",DID_REVERT_ITEM_PROCESSING:"idle"},mi=({root:e,props:t})=>{if(e.ref.handleClick=r=>e.dispatch("DID_ACTIVATE_ITEM",{id:t.id}),e.element.id=`filepond--item-${t.id}`,e.element.addEventListener("click",e.ref.handleClick),e.ref.container=e.appendChildView(e.createChildView(Ei,{id:t.id})),e.ref.panel=e.appendChildView(e.createChildView(Yn,{name:"item-panel"})),e.ref.panel.height=null,t.markedForRemoval=!1,!e.query("GET_ALLOW_REORDER"))return;e.element.dataset.dragState="idle";const n=r=>{if(!r.isPrimary)return;let s=!1;const o={x:r.pageX,y:r.pageY};t.dragOrigin={x:e.translateX,y:e.translateY},t.dragCenter={x:r.offsetX,y:r.offsetY};const i=Ti(e.query("GET_ACTIVE_ITEMS"));e.dispatch("DID_GRAB_ITEM",{id:t.id,dragState:i});const a=f=>{if(!f.isPrimary)return;f.stopPropagation(),f.preventDefault(),t.dragOffset={x:f.pageX-o.x,y:f.pageY-o.y},t.dragOffset.x*t.dragOffset.x+t.dragOffset.y*t.dragOffset.y>16&&!s&&(s=!0,e.element.removeEventListener("click",e.ref.handleClick)),e.dispatch("DID_DRAG_ITEM",{id:t.id,dragState:i})},l=f=>{f.isPrimary&&(t.dragOffset={x:f.pageX-o.x,y:f.pageY-o.y},c())},d=()=>{c()},c=()=>{document.removeEventListener("pointercancel",d),document.removeEventListener("pointermove",a),document.removeEventListener("pointerup",l),e.dispatch("DID_DROP_ITEM",{id:t.id,dragState:i}),s&&setTimeout(()=>e.element.addEventListener("click",e.ref.handleClick),0)};document.addEventListener("pointercancel",d),document.addEventListener("pointermove",a),document.addEventListener("pointerup",l)};e.element.addEventListener("pointerdown",n)},gi=K({DID_UPDATE_PANEL_HEIGHT:({root:e,action:t})=>{e.height=t.height}}),Ri=K({DID_GRAB_ITEM:({root:e,props:t})=>{t.dragOrigin={x:e.translateX,y:e.translateY}},DID_DRAG_ITEM:({root:e})=>{e.element.dataset.dragState="drag"},DID_DROP_ITEM:({root:e,props:t})=>{t.dragOffset=null,t.dragOrigin=null,e.element.dataset.dragState="drop"}},({root:e,actions:t,props:n,shouldOptimize:r})=>{e.element.dataset.dragState==="drop"&&e.scaleX<=1&&(e.element.dataset.dragState="idle");let s=t.concat().filter(i=>/^DID_/.test(i.type)).reverse().find(i=>en[i.type]);s&&s.type!==n.currentState&&(n.currentState=s.type,e.element.dataset.filepondItemState=en[n.currentState]||"");const o=e.query("GET_ITEM_PANEL_ASPECT_RATIO")||e.query("GET_PANEL_ASPECT_RATIO");o?r||(e.height=e.rect.element.width*o):(gi({root:e,actions:t,props:n}),!e.height&&e.ref.container.rect.element.height>0&&(e.height=e.ref.container.rect.element.height)),r&&(e.ref.panel.height=null),e.ref.panel.height=e.height}),hi=$({create:mi,write:Ri,destroy:({root:e,props:t})=>{e.element.removeEventListener("click",e.ref.handleClick),e.dispatch("RELEASE_ITEM",{query:t.id})},tag:"li",name:"item",mixins:{apis:["id","interactionMethod","markedForRemoval","spawnDate","dragCenter","dragOrigin","dragOffset"],styles:["translateX","translateY","scaleX","scaleY","opacity","height"],animations:{scaleX:Jt,scaleY:Jt,translateX:Zt,translateY:Zt,opacity:{type:"tween",duration:150}}}});var ht=(e,t)=>Math.max(1,Math.floor((e+1)/t));const Ot=(e,t,n)=>{if(!n)return;const r=e.rect.element.width,s=t.length;let o=null;if(s===0||n.top<t[0].rect.element.top)return-1;const a=t[0].rect.element,l=a.marginLeft+a.marginRight,d=a.width+l,c=ht(r,d);if(c===1){for(let T=0;T<s;T++){const u=t[T],_=u.rect.outer.top+u.rect.element.height*.5;if(n.top<_)return T}return s}const f=a.marginTop+a.marginBottom,E=a.height+f;for(let T=0;T<s;T++){const u=T%c,_=Math.floor(T/c),D=u*d,R=_*E,O=R-a.marginTop,P=D+d,M=R+E+a.marginBottom;if(n.top<M&&n.top>O){if(n.left<P)return T;T!==s-1?o=T:o=null}}return o!==null?o:s},Fe={height:0,width:0,get getHeight(){return this.height},set setHeight(e){(this.height===0||e===0)&&(this.height=e)},get getWidth(){return this.width},set setWidth(e){(this.width===0||e===0)&&(this.width=e)},setDimensions:function(e,t){(this.height===0||e===0)&&(this.height=e),(this.width===0||t===0)&&(this.width=t)}},Oi=({root:e})=>{Y(e.element,"role","list"),e.ref.lastItemSpanwDate=Date.now()},Di=({root:e,action:t})=>{const{id:n,index:r,interactionMethod:s}=t;e.ref.addIndex=r;const o=Date.now();let i=o,a=1;if(s!==re.NONE){a=0;const l=e.query("GET_ITEM_INSERT_INTERVAL"),d=o-e.ref.lastItemSpanwDate;i=d<l?o+(l-d):o}e.ref.lastItemSpanwDate=i,e.appendChildView(e.createChildView(hi,{spawnDate:i,id:n,opacity:a,interactionMethod:s}),r)},tn=(e,t,n,r=0,s=1)=>{e.dragOffset?(e.translateX=null,e.translateY=null,e.translateX=e.dragOrigin.x+e.dragOffset.x,e.translateY=e.dragOrigin.y+e.dragOffset.y,e.scaleX=1.025,e.scaleY=1.025):(e.translateX=t,e.translateY=n,Date.now()>e.spawnDate&&(e.opacity===0&&Si(e,t,n,r,s),e.scaleX=1,e.scaleY=1,e.opacity=1))},Si=(e,t,n,r,s)=>{e.interactionMethod===re.NONE?(e.translateX=null,e.translateX=t,e.translateY=null,e.translateY=n):e.interactionMethod===re.DROP?(e.translateX=null,e.translateX=t-r*20,e.translateY=null,e.translateY=n-s*10,e.scaleX=.8,e.scaleY=.8):e.interactionMethod===re.BROWSE?(e.translateY=null,e.translateY=n-30):e.interactionMethod===re.API&&(e.translateX=null,e.translateX=t-30,e.translateY=null)},Ai=({root:e,action:t})=>{const{id:n}=t,r=e.childViews.find(s=>s.id===n);r&&(r.scaleX=.9,r.scaleY=.9,r.opacity=0,r.markedForRemoval=!0)},rt=e=>e.rect.element.height+e.rect.element.marginBottom*.5+e.rect.element.marginTop*.5,yi=e=>e.rect.element.width+e.rect.element.marginLeft*.5+e.rect.element.marginRight*.5,Li=({root:e,action:t})=>{const{id:n,dragState:r}=t,s=e.query("GET_ITEM",{id:n}),o=e.childViews.find(D=>D.id===n),i=e.childViews.length,a=r.getItemIndex(s);if(!o)return;const l={x:o.dragOrigin.x+o.dragOffset.x+o.dragCenter.x,y:o.dragOrigin.y+o.dragOffset.y+o.dragCenter.y},d=rt(o),c=yi(o);let f=Math.floor(e.rect.outer.width/c);f>i&&(f=i);const E=Math.floor(i/f+1);Fe.setHeight=d*E,Fe.setWidth=c*f;var T={y:Math.floor(l.y/d),x:Math.floor(l.x/c),getGridIndex:function(){return l.y>Fe.getHeight||l.y<0||l.x>Fe.getWidth||l.x<0?a:this.y*f+this.x},getColIndex:function(){const R=e.query("GET_ACTIVE_ITEMS"),O=e.childViews.filter(y=>y.rect.element.height),P=R.map(y=>O.find(w=>w.id===y.id)),M=P.findIndex(y=>y===o),A=rt(o),C=P.length;let B=C,h=0,b=0,G=0;for(let y=0;y<C;y++)if(h=rt(P[y]),G=b,b=G+h,l.y<b){if(M>y){if(l.y<G+A){B=y;break}continue}B=y;break}return B}};const u=f>1?T.getGridIndex():T.getColIndex();e.dispatch("MOVE_ITEM",{query:o,index:u});const _=r.getIndex();if(_===void 0||_!==u){if(r.setIndex(u),_===void 0)return;e.dispatch("DID_REORDER_ITEMS",{items:e.query("GET_ACTIVE_ITEMS"),origin:a,target:u})}},Pi=K({DID_ADD_ITEM:Di,DID_REMOVE_ITEM:Ai,DID_DRAG_ITEM:Li}),Mi=({root:e,props:t,actions:n,shouldOptimize:r})=>{Pi({root:e,props:t,actions:n});const{dragCoordinates:s}=t,o=e.rect.element.width,i=e.childViews.filter(P=>P.rect.element.height),a=e.query("GET_ACTIVE_ITEMS").map(P=>i.find(M=>M.id===P.id)).filter(P=>P),l=s?Ot(e,a,s):null,d=e.ref.addIndex||null;e.ref.addIndex=null;let c=0,f=0,E=0;if(a.length===0)return;const T=a[0].rect.element,u=T.marginTop+T.marginBottom,_=T.marginLeft+T.marginRight,D=T.width+_,R=T.height+u,O=ht(o,D);if(O===1){let P=0,M=0;a.forEach((A,C)=>{if(l){let b=C-l;b===-2?M=-u*.25:b===-1?M=-u*.75:b===0?M=u*.75:b===1?M=u*.25:M=0}r&&(A.translateX=null,A.translateY=null),A.markedForRemoval||tn(A,0,P+M);let h=(A.rect.element.height+u)*(A.markedForRemoval?A.opacity:1);P+=h})}else{let P=0,M=0;a.forEach((A,C)=>{C===l&&(c=1),C===d&&(E+=1),A.markedForRemoval&&A.opacity<.5&&(f-=1);const B=C+E+c+f,h=B%O,b=Math.floor(B/O),G=h*D,y=b*R,w=Math.sign(G-P),X=Math.sign(y-M);P=G,M=y,!A.markedForRemoval&&(r&&(A.translateX=null,A.translateY=null),tn(A,G,y,w,X))})}},bi=(e,t)=>t.filter(n=>n.data&&n.data.id?e.id===n.data.id:!0),Ci=$({create:Oi,write:Mi,tag:"ul",name:"list",didWriteView:({root:e})=>{e.childViews.filter(t=>t.markedForRemoval&&t.opacity===0&&t.resting).forEach(t=>{t._destroy(),e.removeChildView(t)})},filterFrameActionsForChild:bi,mixins:{apis:["dragCoordinates"]}}),Ni=({root:e,props:t})=>{e.ref.list=e.appendChildView(e.createChildView(Ci)),t.dragCoordinates=null,t.overflowing=!1},wi=({root:e,props:t,action:n})=>{e.query("GET_ITEM_INSERT_LOCATION_FREEDOM")&&(t.dragCoordinates={left:n.position.scopeLeft-e.ref.list.rect.element.left,top:n.position.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},vi=({props:e})=>{e.dragCoordinates=null},Gi=K({DID_DRAG:wi,DID_END_DRAG:vi}),Ui=({root:e,props:t,actions:n})=>{if(Gi({root:e,props:t,actions:n}),e.ref.list.dragCoordinates=t.dragCoordinates,t.overflowing&&!t.overflow&&(t.overflowing=!1,e.element.dataset.state="",e.height=null),t.overflow){const r=Math.round(t.overflow);r!==e.height&&(t.overflowing=!0,e.element.dataset.state="overflow",e.height=r)}},Fi=$({create:Ni,write:Ui,name:"list-scroller",mixins:{apis:["overflow","dragCoordinates"],styles:["height","translateY"],animations:{translateY:"spring"}}}),oe=(e,t,n,r="")=>{n?Y(e,t,r):e.removeAttribute(t)},Bi=e=>{if(!(!e||e.value==="")){try{e.value=""}catch{}if(e.value){const t=ce("form"),n=e.parentNode,r=e.nextSibling;t.appendChild(e),t.reset(),r?n.insertBefore(e,r):n.appendChild(e)}}},Vi=({root:e,props:t})=>{e.element.id=`filepond--browser-${t.id}`,Y(e.element,"name",e.query("GET_NAME")),Y(e.element,"aria-controls",`filepond--assistant-${t.id}`),Y(e.element,"aria-labelledby",`filepond--drop-label-${t.id}`),$n({root:e,action:{value:e.query("GET_ACCEPTED_FILE_TYPES")}}),Wn({root:e,action:{value:e.query("GET_ALLOW_MULTIPLE")}}),zn({root:e,action:{value:e.query("GET_ALLOW_DIRECTORIES_ONLY")}}),Et({root:e}),Xn({root:e,action:{value:e.query("GET_REQUIRED")}}),kn({root:e,action:{value:e.query("GET_CAPTURE_METHOD")}}),e.ref.handleChange=n=>{if(!e.element.value)return;const r=Array.from(e.element.files).map(s=>(s._relativePath=s.webkitRelativePath,s));setTimeout(()=>{t.onload(r),Bi(e.element)},250)},e.element.addEventListener("change",e.ref.handleChange)},$n=({root:e,action:t})=>{e.query("GET_ALLOW_SYNC_ACCEPT_ATTRIBUTE")&&oe(e.element,"accept",!!t.value,t.value?t.value.join(","):"")},Wn=({root:e,action:t})=>{oe(e.element,"multiple",t.value)},zn=({root:e,action:t})=>{oe(e.element,"webkitdirectory",t.value)},Et=({root:e})=>{const t=e.query("GET_DISABLED"),n=e.query("GET_ALLOW_BROWSE"),r=t||!n;oe(e.element,"disabled",r)},Xn=({root:e,action:t})=>{t.value?e.query("GET_TOTAL_ITEMS")===0&&oe(e.element,"required",!0):oe(e.element,"required",!1)},kn=({root:e,action:t})=>{oe(e.element,"capture",!!t.value,t.value===!0?"":t.value)},nn=({root:e})=>{const{element:t}=e;e.query("GET_TOTAL_ITEMS")>0?(oe(t,"required",!1),oe(t,"name",!1)):(oe(t,"name",!0,e.query("GET_NAME")),e.query("GET_CHECK_VALIDITY")&&t.setCustomValidity(""),e.query("GET_REQUIRED")&&oe(t,"required",!0))},qi=({root:e})=>{e.query("GET_CHECK_VALIDITY")&&e.element.setCustomValidity(e.query("GET_LABEL_INVALID_FIELD"))},xi=$({tag:"input",name:"browser",ignoreRect:!0,ignoreRectUpdate:!0,attributes:{type:"file"},create:Vi,destroy:({root:e})=>{e.element.removeEventListener("change",e.ref.handleChange)},write:K({DID_LOAD_ITEM:nn,DID_REMOVE_ITEM:nn,DID_THROW_ITEM_INVALID:qi,DID_SET_DISABLED:Et,DID_SET_ALLOW_BROWSE:Et,DID_SET_ALLOW_DIRECTORIES_ONLY:zn,DID_SET_ALLOW_MULTIPLE:Wn,DID_SET_ACCEPTED_FILE_TYPES:$n,DID_SET_CAPTURE_METHOD:kn,DID_SET_REQUIRED:Xn})}),rn={ENTER:13,SPACE:32},Hi=({root:e,props:t})=>{const n=ce("label");Y(n,"for",`filepond--browser-${t.id}`),Y(n,"id",`filepond--drop-label-${t.id}`),Y(n,"aria-hidden","true"),e.ref.handleKeyDown=r=>{(r.keyCode===rn.ENTER||r.keyCode===rn.SPACE)&&(r.preventDefault(),e.ref.label.click())},e.ref.handleClick=r=>{r.target===n||n.contains(r.target)||e.ref.label.click()},n.addEventListener("keydown",e.ref.handleKeyDown),e.element.addEventListener("click",e.ref.handleClick),Qn(n,t.caption),e.appendChild(n),e.ref.label=n},Qn=(e,t)=>{e.innerHTML=t;const n=e.querySelector(".filepond--label-action");return n&&Y(n,"tabindex","0"),t},Yi=$({name:"drop-label",ignoreRect:!0,create:Hi,destroy:({root:e})=>{e.ref.label.addEventListener("keydown",e.ref.handleKeyDown),e.element.removeEventListener("click",e.ref.handleClick)},write:K({DID_SET_LABEL_IDLE:({root:e,action:t})=>{Qn(e.ref.label,t.value)}}),mixins:{styles:["opacity","translateX","translateY"],animations:{opacity:{type:"tween",duration:150},translateX:"spring",translateY:"spring"}}}),$i=$({name:"drip-blob",ignoreRect:!0,mixins:{styles:["translateX","translateY","scaleX","scaleY","opacity"],animations:{scaleX:"spring",scaleY:"spring",translateX:"spring",translateY:"spring",opacity:{type:"tween",duration:250}}}}),Wi=({root:e})=>{const t=e.rect.element.width*.5,n=e.rect.element.height*.5;e.ref.blob=e.appendChildView(e.createChildView($i,{opacity:0,scaleX:2.5,scaleY:2.5,translateX:t,translateY:n}))},zi=({root:e,action:t})=>{if(!e.ref.blob){Wi({root:e});return}e.ref.blob.translateX=t.position.scopeLeft,e.ref.blob.translateY=t.position.scopeTop,e.ref.blob.scaleX=1,e.ref.blob.scaleY=1,e.ref.blob.opacity=1},Xi=({root:e})=>{e.ref.blob&&(e.ref.blob.opacity=0)},ki=({root:e})=>{e.ref.blob&&(e.ref.blob.scaleX=2.5,e.ref.blob.scaleY=2.5,e.ref.blob.opacity=0)},Qi=({root:e,props:t,actions:n})=>{ji({root:e,props:t,actions:n});const{blob:r}=e.ref;n.length===0&&r&&r.opacity===0&&(e.removeChildView(r),e.ref.blob=null)},ji=K({DID_DRAG:zi,DID_DROP:ki,DID_END_DRAG:Xi}),Ki=$({ignoreRect:!0,ignoreRectUpdate:!0,name:"drip",write:Qi}),jn=(e,t)=>{try{const n=new DataTransfer;t.forEach(r=>{r instanceof File?n.items.add(r):n.items.add(new File([r],r.name,{type:r.type}))}),e.files=n.files}catch{return!1}return!0},Zi=({root:e})=>e.ref.fields={},je=(e,t)=>e.ref.fields[t],Dt=e=>{e.query("GET_ACTIVE_ITEMS").forEach(t=>{e.ref.fields[t.id]&&e.element.appendChild(e.ref.fields[t.id])})},sn=({root:e})=>Dt(e),Ji=({root:e,action:t})=>{const s=!(e.query("GET_ITEM",t.id).origin===W.LOCAL)&&e.query("SHOULD_UPDATE_FILE_INPUT"),o=ce("input");o.type=s?"file":"hidden",o.name=e.query("GET_NAME"),o.disabled=e.query("GET_DISABLED"),e.ref.fields[t.id]=o,Dt(e)},eo=({root:e,action:t})=>{const n=je(e,t.id);if(!n||(t.serverFileReference!==null&&(n.value=t.serverFileReference),!e.query("SHOULD_UPDATE_FILE_INPUT")))return;const r=e.query("GET_ITEM",t.id);jn(n,[r.file])},to=({root:e,action:t})=>{e.query("SHOULD_UPDATE_FILE_INPUT")&&setTimeout(()=>{const n=je(e,t.id);n&&jn(n,[t.file])},0)},no=({root:e})=>{e.element.disabled=e.query("GET_DISABLED")},ro=({root:e,action:t})=>{const n=je(e,t.id);n&&(n.parentNode&&n.parentNode.removeChild(n),delete e.ref.fields[t.id])},so=({root:e,action:t})=>{const n=je(e,t.id);n&&(t.value===null?n.removeAttribute("value"):n.type!="file"&&(n.value=t.value),Dt(e))},io=K({DID_SET_DISABLED:no,DID_ADD_ITEM:Ji,DID_LOAD_ITEM:eo,DID_REMOVE_ITEM:ro,DID_DEFINE_VALUE:so,DID_PREPARE_OUTPUT:to,DID_REORDER_ITEMS:sn,DID_SORT_ITEMS:sn}),oo=$({tag:"fieldset",name:"data",create:Zi,write:io,ignoreRect:!0}),lo=e=>"getRootNode"in e?e.getRootNode():document,ao=["jpg","jpeg","png","gif","bmp","webp","svg","tiff"],co=["css","csv","html","txt"],uo={zip:"zip|compressed",epub:"application/epub+zip"},Kn=(e="")=>(e=e.toLowerCase(),ao.includes(e)?"image/"+(e==="jpg"?"jpeg":e==="svg"?"svg+xml":e):co.includes(e)?"text/"+e:uo[e]||""),St=e=>new Promise((t,n)=>{const r=go(e);if(r.length&&!fo(e))return t(r);Eo(e).then(t)}),fo=e=>e.files?e.files.length>0:!1,Eo=e=>new Promise((t,n)=>{const r=(e.items?Array.from(e.items):[]).filter(s=>po(s)).map(s=>Io(s));if(!r.length){t(e.files?Array.from(e.files):[]);return}Promise.all(r).then(s=>{const o=[];s.forEach(i=>{o.push.apply(o,i)}),t(o.filter(i=>i).map(i=>(i._relativePath||(i._relativePath=i.webkitRelativePath),i)))}).catch(console.error)}),po=e=>{if(Zn(e)){const t=At(e);if(t)return t.isFile||t.isDirectory}return e.kind==="file"},Io=e=>new Promise((t,n)=>{if(mo(e)){_o(At(e)).then(t).catch(n);return}t([e.getAsFile()])}),_o=e=>new Promise((t,n)=>{const r=[];let s=0,o=0;const i=()=>{o===0&&s===0&&t(r)},a=l=>{s++;const d=l.createReader(),c=()=>{d.readEntries(f=>{if(f.length===0){s--,i();return}f.forEach(E=>{E.isDirectory?a(E):(o++,E.file(T=>{const u=To(T);E.fullPath&&(u._relativePath=E.fullPath),r.push(u),o--,i()}))}),c()},n)};c()};a(e)}),To=e=>{if(e.type.length)return e;const t=e.lastModifiedDate,n=e.name,r=Kn(Qe(e.name));return r.length&&(e=e.slice(0,e.size,r),e.name=n,e.lastModifiedDate=t),e},mo=e=>Zn(e)&&(At(e)||{}).isDirectory,Zn=e=>"webkitGetAsEntry"in e,At=e=>e.webkitGetAsEntry(),go=e=>{let t=[];try{if(t=ho(e),t.length)return t;t=Ro(e)}catch{}return t},Ro=e=>{let t=e.getData("url");return typeof t=="string"&&t.length?[t]:[]},ho=e=>{let t=e.getData("text/html");if(typeof t=="string"&&t.length){const n=t.match(/src\s*=\s*"(.+?)"/);if(n)return[n[1]]}return[]},Ye=[],ge=e=>({pageLeft:e.pageX,pageTop:e.pageY,scopeLeft:e.offsetX||e.layerX,scopeTop:e.offsetY||e.layerY}),Oo=(e,t,n)=>{const r=Do(t),s={element:e,filterElement:n,state:null,ondrop:()=>{},onenter:()=>{},ondrag:()=>{},onexit:()=>{},onload:()=>{},allowdrop:()=>{}};return s.destroy=r.addListener(s),s},Do=e=>{const t=Ye.find(r=>r.element===e);if(t)return t;const n=So(e);return Ye.push(n),n},So=e=>{const t=[],n={dragenter:yo,dragover:Lo,dragleave:Mo,drop:Po},r={};q(n,(o,i)=>{r[o]=i(e,t),e.addEventListener(o,r[o],!1)});const s={element:e,addListener:o=>(t.push(o),()=>{t.splice(t.indexOf(o),1),t.length===0&&(Ye.splice(Ye.indexOf(s),1),q(n,i=>{e.removeEventListener(i,r[i],!1)}))})};return s},Ao=(e,t)=>("elementFromPoint"in e||(e=document),e.elementFromPoint(t.x,t.y)),yt=(e,t)=>{const n=lo(t),r=Ao(n,{x:e.pageX-window.pageXOffset,y:e.pageY-window.pageYOffset});return r===t||t.contains(r)};let Jn=null;const Be=(e,t)=>{try{e.dropEffect=t}catch{}},yo=(e,t)=>n=>{n.preventDefault(),Jn=n.target,t.forEach(r=>{const{element:s,onenter:o}=r;yt(n,s)&&(r.state="enter",o(ge(n)))})},Lo=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;St(r).then(s=>{let o=!1;t.some(i=>{const{filterElement:a,element:l,onenter:d,onexit:c,ondrag:f,allowdrop:E}=i;Be(r,"copy");const T=E(s);if(!T){Be(r,"none");return}if(yt(n,l)){if(o=!0,i.state===null){i.state="enter",d(ge(n));return}if(i.state="over",a&&!T){Be(r,"none");return}f(ge(n))}else a&&!o&&Be(r,"none"),i.state&&(i.state=null,c(ge(n)))})})},Po=(e,t)=>n=>{n.preventDefault();const r=n.dataTransfer;St(r).then(s=>{t.forEach(o=>{const{filterElement:i,element:a,ondrop:l,onexit:d,allowdrop:c}=o;if(o.state=null,!(i&&!yt(n,a))){if(!c(s))return d(ge(n));l(ge(n),s)}})})},Mo=(e,t)=>n=>{Jn===n.target&&t.forEach(r=>{const{onexit:s}=r;r.state=null,s(ge(n))})},bo=(e,t,n)=>{e.classList.add("filepond--hopper");const{catchesDropsOnPage:r,requiresDropOnElement:s,filterItems:o=c=>c}=n,i=Oo(e,r?document.documentElement:e,s);let a="",l="";i.allowdrop=c=>t(o(c)),i.ondrop=(c,f)=>{const E=o(f);if(!t(E)){d.ondragend(c);return}l="drag-drop",d.onload(E,c)},i.ondrag=c=>{d.ondrag(c)},i.onenter=c=>{l="drag-over",d.ondragstart(c)},i.onexit=c=>{l="drag-exit",d.ondragend(c)};const d={updateHopperState:()=>{a!==l&&(e.dataset.hopperState=l,a=l)},onload:()=>{},ondragstart:()=>{},ondrag:()=>{},ondragend:()=>{},destroy:()=>{i.destroy()}};return d};let pt=!1;const Ae=[],er=e=>{const t=document.activeElement;if(t&&/textarea|input/i.test(t.nodeName)){let n=!1,r=t;for(;r!==document.body;){if(r.classList.contains("filepond--root")){n=!0;break}r=r.parentNode}if(!n)return}St(e.clipboardData).then(n=>{n.length&&Ae.forEach(r=>r(n))})},Co=e=>{Ae.includes(e)||(Ae.push(e),!pt&&(pt=!0,document.addEventListener("paste",er)))},No=e=>{Tt(Ae,Ae.indexOf(e)),Ae.length===0&&(document.removeEventListener("paste",er),pt=!1)},wo=()=>{const e=n=>{t.onload(n)},t={destroy:()=>{No(e)},onload:()=>{}};return Co(e),t},vo=({root:e,props:t})=>{e.element.id=`filepond--assistant-${t.id}`,Y(e.element,"role","status"),Y(e.element,"aria-live","polite"),Y(e.element,"aria-relevant","additions")};let on=null,ln=null;const st=[],Ke=(e,t)=>{e.element.textContent=t},Go=e=>{e.element.textContent=""},tr=(e,t,n)=>{const r=e.query("GET_TOTAL_ITEMS");Ke(e,`${n} ${t}, ${r} ${r===1?e.query("GET_LABEL_FILE_COUNT_SINGULAR"):e.query("GET_LABEL_FILE_COUNT_PLURAL")}`),clearTimeout(ln),ln=setTimeout(()=>{Go(e)},1500)},nr=e=>e.element.parentNode.contains(document.activeElement),Uo=({root:e,action:t})=>{if(!nr(e))return;e.element.textContent="";const n=e.query("GET_ITEM",t.id);st.push(n.filename),clearTimeout(on),on=setTimeout(()=>{tr(e,st.join(", "),e.query("GET_LABEL_FILE_ADDED")),st.length=0},750)},Fo=({root:e,action:t})=>{if(!nr(e))return;const n=t.item;tr(e,n.filename,e.query("GET_LABEL_FILE_REMOVED"))},Bo=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,s=e.query("GET_LABEL_FILE_PROCESSING_COMPLETE");Ke(e,`${r} ${s}`)},an=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename,s=e.query("GET_LABEL_FILE_PROCESSING_ABORTED");Ke(e,`${r} ${s}`)},Ve=({root:e,action:t})=>{const r=e.query("GET_ITEM",t.id).filename;Ke(e,`${t.status.main} ${r} ${t.status.sub}`)},Vo=$({create:vo,ignoreRect:!0,ignoreRectUpdate:!0,write:K({DID_LOAD_ITEM:Uo,DID_REMOVE_ITEM:Fo,DID_COMPLETE_ITEM_PROCESSING:Bo,DID_ABORT_ITEM_PROCESSING:an,DID_REVERT_ITEM_PROCESSING:an,DID_THROW_ITEM_REMOVE_ERROR:Ve,DID_THROW_ITEM_LOAD_ERROR:Ve,DID_THROW_ITEM_INVALID:Ve,DID_THROW_ITEM_PROCESSING_ERROR:Ve}),tag:"span",name:"assistant"}),rr=(e,t="-")=>e.replace(new RegExp(`${t}.`,"g"),n=>n.charAt(1).toUpperCase()),sr=(e,t=16,n=!0)=>{let r=Date.now(),s=null;return(...o)=>{clearTimeout(s);const i=Date.now()-r,a=()=>{r=Date.now(),e(...o)};i<t?n||(s=setTimeout(a,t-i)):a()}},qo=1e6,$e=e=>e.preventDefault(),xo=({root:e,props:t})=>{const n=e.query("GET_ID");n&&(e.element.id=n);const r=e.query("GET_CLASS_NAME");r&&r.split(" ").filter(l=>l.length).forEach(l=>{e.element.classList.add(l)}),e.ref.label=e.appendChildView(e.createChildView(Yi,{...t,translateY:null,caption:e.query("GET_LABEL_IDLE")})),e.ref.list=e.appendChildView(e.createChildView(Fi,{translateY:null})),e.ref.panel=e.appendChildView(e.createChildView(Yn,{name:"panel-root"})),e.ref.assistant=e.appendChildView(e.createChildView(Vo,{...t})),e.ref.data=e.appendChildView(e.createChildView(oo,{...t})),e.ref.measure=ce("div"),e.ref.measure.style.height="100%",e.element.appendChild(e.ref.measure),e.ref.bounds=null,e.query("GET_STYLES").filter(l=>!ae(l.value)).map(({name:l,value:d})=>{e.element.dataset[l]=d}),e.ref.widthPrevious=null,e.ref.widthUpdated=sr(()=>{e.ref.updateHistory=[],e.dispatch("DID_RESIZE_ROOT")},250),e.ref.previousAspectRatio=null,e.ref.updateHistory=[];const s=window.matchMedia("(pointer: fine) and (hover: hover)").matches,o="PointerEvent"in window;e.query("GET_ALLOW_REORDER")&&o&&!s&&(e.element.addEventListener("touchmove",$e,{passive:!1}),e.element.addEventListener("gesturestart",$e));const i=e.query("GET_CREDITS");if(i.length===2){const l=document.createElement("a");l.className="filepond--credits",l.setAttribute("aria-hidden","true"),l.href=i[0],l.tabindex=-1,l.target="_blank",l.rel="noopener noreferrer",l.textContent=i[1],e.element.appendChild(l),e.ref.credits=l}},Ho=({root:e,props:t,actions:n})=>{if(Xo({root:e,props:t,actions:n}),n.filter(C=>/^DID_SET_STYLE_/.test(C.type)).filter(C=>!ae(C.data.value)).map(({type:C,data:B})=>{const h=rr(C.substring(8).toLowerCase(),"_");e.element.dataset[h]=B.value,e.invalidateLayout()}),e.rect.element.hidden)return;e.rect.element.width!==e.ref.widthPrevious&&(e.ref.widthPrevious=e.rect.element.width,e.ref.widthUpdated());let r=e.ref.bounds;r||(r=e.ref.bounds=Wo(e),e.element.removeChild(e.ref.measure),e.ref.measure=null);const{hopper:s,label:o,list:i,panel:a}=e.ref;s&&s.updateHopperState();const l=e.query("GET_PANEL_ASPECT_RATIO"),d=e.query("GET_ALLOW_MULTIPLE"),c=e.query("GET_TOTAL_ITEMS"),f=d?e.query("GET_MAX_FILES")||qo:1,E=c===f,T=n.find(C=>C.type==="DID_ADD_ITEM");if(E&&T){const C=T.data.interactionMethod;o.opacity=0,d?o.translateY=-40:C===re.API?o.translateX=40:C===re.BROWSE?o.translateY=40:o.translateY=30}else E||(o.opacity=1,o.translateX=0,o.translateY=0);const u=Yo(e),_=$o(e),D=o.rect.element.height,R=!d||E?0:D,O=E?i.rect.element.marginTop:0,P=c===0?0:i.rect.element.marginBottom,M=R+O+_.visual+P,A=R+O+_.bounds+P;if(i.translateY=Math.max(0,R-i.rect.element.marginTop)-u.top,l){const C=e.rect.element.width,B=C*l;l!==e.ref.previousAspectRatio&&(e.ref.previousAspectRatio=l,e.ref.updateHistory=[]);const h=e.ref.updateHistory;h.push(C);const b=2;if(h.length>b*2){const y=h.length,w=y-10;let X=0;for(let m=y;m>=w;m--)if(h[m]===h[m-2]&&X++,X>=b)return}a.scalable=!1,a.height=B;const G=B-R-(P-u.bottom)-(E?O:0);_.visual>G?i.overflow=G:i.overflow=null,e.height=B}else if(r.fixedHeight){a.scalable=!1;const C=r.fixedHeight-R-(P-u.bottom)-(E?O:0);_.visual>C?i.overflow=C:i.overflow=null}else if(r.cappedHeight){const C=M>=r.cappedHeight,B=Math.min(r.cappedHeight,M);a.scalable=!0,a.height=C?B:B-u.top-u.bottom;const h=B-R-(P-u.bottom)-(E?O:0);M>r.cappedHeight&&_.visual>h?i.overflow=h:i.overflow=null,e.height=Math.min(r.cappedHeight,A-u.top-u.bottom)}else{const C=c>0?u.top+u.bottom:0;a.scalable=!0,a.height=Math.max(D,M-C),e.height=Math.max(D,A-C)}e.ref.credits&&a.heightCurrent&&(e.ref.credits.style.transform=`translateY(${a.heightCurrent}px)`)},Yo=e=>{const t=e.ref.list.childViews[0].childViews[0];return t?{top:t.rect.element.marginTop,bottom:t.rect.element.marginBottom}:{top:0,bottom:0}},$o=e=>{let t=0,n=0;const r=e.ref.list,s=r.childViews[0],o=s.childViews.filter(O=>O.rect.element.height),i=e.query("GET_ACTIVE_ITEMS").map(O=>o.find(P=>P.id===O.id)).filter(O=>O);if(i.length===0)return{visual:t,bounds:n};const a=s.rect.element.width,l=Ot(s,i,r.dragCoordinates),d=i[0].rect.element,c=d.marginTop+d.marginBottom,f=d.marginLeft+d.marginRight,E=d.width+f,T=d.height+c,u=typeof l<"u"&&l>=0?1:0,_=i.find(O=>O.markedForRemoval&&O.opacity<.45)?-1:0,D=i.length+u+_,R=ht(a,E);return R===1?i.forEach(O=>{const P=O.rect.element.height+c;n+=P,t+=P*O.opacity}):(n=Math.ceil(D/R)*T,t=n),{visual:t,bounds:n}},Wo=e=>{const t=e.ref.measureHeight||null;return{cappedHeight:parseInt(e.style.maxHeight,10)||null,fixedHeight:t===0?null:t}},Lt=(e,t)=>{const n=e.query("GET_ALLOW_REPLACE"),r=e.query("GET_ALLOW_MULTIPLE"),s=e.query("GET_TOTAL_ITEMS");let o=e.query("GET_MAX_FILES");const i=t.length;return!r&&i>1?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:x("warning",0,"Max files")}),!0):(o=r?o:1,!r&&n?!1:Le(o)&&s+i>o?(e.dispatch("DID_THROW_MAX_FILES",{source:t,error:x("warning",0,"Max files")}),!0):!1)},zo=(e,t,n)=>{const r=e.childViews[0];return Ot(r,t,{left:n.scopeLeft-r.rect.element.left,top:n.scopeTop-(e.rect.outer.top+e.rect.element.marginTop+e.rect.element.scrollTop)})},cn=e=>{const t=e.query("GET_ALLOW_DROP"),n=e.query("GET_DISABLED"),r=t&&!n;if(r&&!e.ref.hopper){const s=bo(e.element,o=>{const i=e.query("GET_BEFORE_DROP_FILE")||(()=>!0);return e.query("GET_DROP_VALIDATION")?o.every(l=>Re("ALLOW_HOPPER_ITEM",l,{query:e.query}).every(d=>d===!0)&&i(l)):!0},{filterItems:o=>{const i=e.query("GET_IGNORED_FILES");return o.filter(a=>me(a)?!i.includes(a.name.toLowerCase()):!0)},catchesDropsOnPage:e.query("GET_DROP_ON_PAGE"),requiresDropOnElement:e.query("GET_DROP_ON_ELEMENT")});s.onload=(o,i)=>{const l=e.ref.list.childViews[0].childViews.filter(c=>c.rect.element.height),d=e.query("GET_ACTIVE_ITEMS").map(c=>l.find(f=>f.id===c.id)).filter(c=>c);se("ADD_ITEMS",o,{dispatch:e.dispatch}).then(c=>{if(Lt(e,c))return!1;e.dispatch("ADD_ITEMS",{items:c,index:zo(e.ref.list,d,i),interactionMethod:re.DROP})}),e.dispatch("DID_DROP",{position:i}),e.dispatch("DID_END_DRAG",{position:i})},s.ondragstart=o=>{e.dispatch("DID_START_DRAG",{position:o})},s.ondrag=sr(o=>{e.dispatch("DID_DRAG",{position:o})}),s.ondragend=o=>{e.dispatch("DID_END_DRAG",{position:o})},e.ref.hopper=s,e.ref.drip=e.appendChildView(e.createChildView(Ki))}else!r&&e.ref.hopper&&(e.ref.hopper.destroy(),e.ref.hopper=null,e.removeChildView(e.ref.drip))},dn=(e,t)=>{const n=e.query("GET_ALLOW_BROWSE"),r=e.query("GET_DISABLED"),s=n&&!r;s&&!e.ref.browser?e.ref.browser=e.appendChildView(e.createChildView(xi,{...t,onload:o=>{se("ADD_ITEMS",o,{dispatch:e.dispatch}).then(i=>{if(Lt(e,i))return!1;e.dispatch("ADD_ITEMS",{items:i,index:-1,interactionMethod:re.BROWSE})})}}),0):!s&&e.ref.browser&&(e.removeChildView(e.ref.browser),e.ref.browser=null)},un=e=>{const t=e.query("GET_ALLOW_PASTE"),n=e.query("GET_DISABLED"),r=t&&!n;r&&!e.ref.paster?(e.ref.paster=wo(),e.ref.paster.onload=s=>{se("ADD_ITEMS",s,{dispatch:e.dispatch}).then(o=>{if(Lt(e,o))return!1;e.dispatch("ADD_ITEMS",{items:o,index:-1,interactionMethod:re.PASTE})})}):!r&&e.ref.paster&&(e.ref.paster.destroy(),e.ref.paster=null)},Xo=K({DID_SET_ALLOW_BROWSE:({root:e,props:t})=>{dn(e,t)},DID_SET_ALLOW_DROP:({root:e})=>{cn(e)},DID_SET_ALLOW_PASTE:({root:e})=>{un(e)},DID_SET_DISABLED:({root:e,props:t})=>{cn(e),un(e),dn(e,t),e.query("GET_DISABLED")?e.element.dataset.disabled="disabled":e.element.removeAttribute("data-disabled")}}),ko=$({name:"root",read:({root:e})=>{e.ref.measure&&(e.ref.measureHeight=e.ref.measure.offsetHeight)},create:xo,write:Ho,destroy:({root:e})=>{e.ref.paster&&e.ref.paster.destroy(),e.ref.hopper&&e.ref.hopper.destroy(),e.element.removeEventListener("touchmove",$e),e.element.removeEventListener("gesturestart",$e)},mixins:{styles:["height"]}}),Qo=(e={})=>{let t=null;const n=He(),r=ur(jr(n),[ps,Jr(n)],[Vs,Zr(n)]);r.dispatch("SET_OPTIONS",{options:e});const s=()=>{document.hidden||r.dispatch("KICK")};document.addEventListener("visibilitychange",s);let o=null,i=!1,a=!1,l=null,d=null;const c=()=>{i||(i=!0),clearTimeout(o),o=setTimeout(()=>{i=!1,l=null,d=null,a&&(a=!1,r.dispatch("DID_STOP_RESIZE"))},500)};window.addEventListener("resize",c);const f=ko(r,{id:_t()});let E=!1,T=!1;const u={_read:()=>{i&&(d=window.innerWidth,l||(l=d),!a&&d!==l&&(r.dispatch("DID_START_RESIZE"),a=!0)),T&&E&&(E=f.element.offsetParent===null),!E&&(f._read(),T=f.rect.element.hidden)},_write:I=>{const g=r.processActionQueue().filter(L=>!/^SET_/.test(L.type));E&&!g.length||(O(g),E=f._write(I,g,a),ns(r.query("GET_ITEMS")),E&&r.processDispatchQueue())}},_=I=>g=>{const L={type:I};if(!g)return L;if(g.hasOwnProperty("error")&&(L.error=g.error?{...g.error}:null),g.status&&(L.status={...g.status}),g.file&&(L.output=g.file),g.source)L.file=g.source;else if(g.item||g.id){const S=g.item?g.item:r.query("GET_ITEM",g.id);L.file=S?J(S):null}return g.items&&(L.items=g.items.map(J)),/progress/.test(I)&&(L.progress=g.progress),g.hasOwnProperty("origin")&&g.hasOwnProperty("target")&&(L.origin=g.origin,L.target=g.target),L},D={DID_DESTROY:_("destroy"),DID_INIT:_("init"),DID_THROW_MAX_FILES:_("warning"),DID_INIT_ITEM:_("initfile"),DID_START_ITEM_LOAD:_("addfilestart"),DID_UPDATE_ITEM_LOAD_PROGRESS:_("addfileprogress"),DID_LOAD_ITEM:_("addfile"),DID_THROW_ITEM_INVALID:[_("error"),_("addfile")],DID_THROW_ITEM_LOAD_ERROR:[_("error"),_("addfile")],DID_THROW_ITEM_REMOVE_ERROR:[_("error"),_("removefile")],DID_PREPARE_OUTPUT:_("preparefile"),DID_START_ITEM_PROCESSING:_("processfilestart"),DID_UPDATE_ITEM_PROCESS_PROGRESS:_("processfileprogress"),DID_ABORT_ITEM_PROCESSING:_("processfileabort"),DID_COMPLETE_ITEM_PROCESSING:_("processfile"),DID_COMPLETE_ITEM_PROCESSING_ALL:_("processfiles"),DID_REVERT_ITEM_PROCESSING:_("processfilerevert"),DID_THROW_ITEM_PROCESSING_ERROR:[_("error"),_("processfile")],DID_REMOVE_ITEM:_("removefile"),DID_UPDATE_ITEMS:_("updatefiles"),DID_ACTIVATE_ITEM:_("activatefile"),DID_REORDER_ITEMS:_("reorderfiles")},R=I=>{const g={pond:U,...I};delete g.type,f.element.dispatchEvent(new CustomEvent(`FilePond:${I.type}`,{detail:g,bubbles:!0,cancelable:!0,composed:!0}));const L=[];I.hasOwnProperty("error")&&L.push(I.error),I.hasOwnProperty("file")&&L.push(I.file);const S=["type","error","file"];Object.keys(I).filter(V=>!S.includes(V)).forEach(V=>L.push(I[V])),U.fire(I.type,...L);const F=r.query(`GET_ON${I.type.toUpperCase()}`);F&&F(...L)},O=I=>{I.length&&I.filter(g=>D[g.type]).forEach(g=>{const L=D[g.type];(Array.isArray(L)?L:[L]).forEach(S=>{g.type==="DID_INIT_ITEM"?R(S(g.data)):setTimeout(()=>{R(S(g.data))},0)})})},P=I=>r.dispatch("SET_OPTIONS",{options:I}),M=I=>r.query("GET_ACTIVE_ITEM",I),A=I=>new Promise((g,L)=>{r.dispatch("REQUEST_ITEM_PREPARE",{query:I,success:S=>{g(S)},failure:S=>{L(S)}})}),C=(I,g={})=>new Promise((L,S)=>{b([{source:I,options:g}],{index:g.index}).then(F=>L(F&&F[0])).catch(S)}),B=I=>I.file&&I.id,h=(I,g)=>(typeof I=="object"&&!B(I)&&!g&&(g=I,I=void 0),r.dispatch("REMOVE_ITEM",{...g,query:I}),r.query("GET_ACTIVE_ITEM",I)===null),b=(...I)=>new Promise((g,L)=>{const S=[],F={};if(We(I[0]))S.push.apply(S,I[0]),Object.assign(F,I[1]||{});else{const V=I[I.length-1];typeof V=="object"&&!(V instanceof Blob)&&Object.assign(F,I.pop()),S.push(...I)}r.dispatch("ADD_ITEMS",{items:S,index:F.index,interactionMethod:re.API,success:g,failure:L})}),G=()=>r.query("GET_ACTIVE_ITEMS"),y=I=>new Promise((g,L)=>{r.dispatch("REQUEST_ITEM_PROCESSING",{query:I,success:S=>{g(S)},failure:S=>{L(S)}})}),w=(...I)=>{const g=Array.isArray(I[0])?I[0]:I,L=g.length?g:G();return Promise.all(L.map(A))},X=(...I)=>{const g=Array.isArray(I[0])?I[0]:I;if(!g.length){const L=G().filter(S=>!(S.status===N.IDLE&&S.origin===W.LOCAL)&&S.status!==N.PROCESSING&&S.status!==N.PROCESSING_COMPLETE&&S.status!==N.PROCESSING_REVERT_ERROR);return Promise.all(L.map(y))}return Promise.all(g.map(y))},m=(...I)=>{const g=Array.isArray(I[0])?I[0]:I;let L;typeof g[g.length-1]=="object"?L=g.pop():Array.isArray(I[0])&&(L=I[1]);const S=G();return g.length?g.map(V=>Ee(V)?S[V]?S[V].id:null:V).filter(V=>V).map(V=>h(V,L)):Promise.all(S.map(V=>h(V,L)))},U={...ke(),...u,...Kr(r,n),setOptions:P,addFile:C,addFiles:b,getFile:M,processFile:y,prepareFile:A,removeFile:h,moveFile:(I,g)=>r.dispatch("MOVE_ITEM",{query:I,index:g}),getFiles:G,processFiles:X,removeFiles:m,prepareFiles:w,sort:I=>r.dispatch("SORT",{compare:I}),browse:()=>{var I=f.element.querySelector("input[type=file]");I&&I.click()},destroy:()=>{U.fire("destroy",f.element),r.dispatch("ABORT_ALL"),f._destroy(),window.removeEventListener("resize",c),document.removeEventListener("visibilitychange",s),r.dispatch("DID_DESTROY")},insertBefore:I=>wt(f.element,I),insertAfter:I=>vt(f.element,I),appendTo:I=>I.appendChild(f.element),replaceElement:I=>{wt(f.element,I),I.parentNode.removeChild(I),t=I},restoreElement:()=>{t&&(vt(t,f.element),f.element.parentNode.removeChild(f.element),t=null)},isAttachedTo:I=>f.element===I||t===I,element:{get:()=>f.element},status:{get:()=>r.query("GET_STATUS")}};return r.dispatch("DID_INIT"),fe(U)},ir=(e={})=>{const t={};return q(He(),(r,s)=>{t[r]=s[0]}),Qo({...t,...e})},jo=e=>e.charAt(0).toLowerCase()+e.slice(1),Ko=e=>rr(e.replace(/^data-/,"")),or=(e,t)=>{q(t,(n,r)=>{q(e,(s,o)=>{const i=new RegExp(n);if(!i.test(s)||(delete e[s],r===!1))return;if(j(r)){e[r]=o;return}const l=r.group;z(r)&&!e[l]&&(e[l]={}),e[l][jo(s.replace(i,""))]=o}),r.mapping&&or(e[r.group],r.mapping)})},Zo=(e,t={})=>{const n=[];q(e.attributes,s=>{n.push(e.attributes[s])});const r=n.filter(s=>s.name).reduce((s,o)=>{const i=Y(e,o.name);return s[Ko(o.name)]=i===o.name?!0:i,s},{});return or(r,t),r},Jo=(e,t={})=>{const n={"^class$":"className","^multiple$":"allowMultiple","^capture$":"captureMethod","^webkitdirectory$":"allowDirectoriesOnly","^server":{group:"server",mapping:{"^process":{group:"process"},"^revert":{group:"revert"},"^fetch":{group:"fetch"},"^restore":{group:"restore"},"^load":{group:"load"}}},"^type$":!1,"^files$":!1};Re("SET_ATTRIBUTE_TO_OPTION_MAP",n);const r={...t},s=Zo(e.nodeName==="FIELDSET"?e.querySelector("input[type=file]"):e,n);Object.keys(s).forEach(i=>{z(s[i])?(z(r[i])||(r[i]={}),Object.assign(r[i],s[i])):r[i]=s[i]}),r.files=(t.files||[]).concat(Array.from(e.querySelectorAll("input:not([type=file])")).map(i=>({source:i.value,options:{type:i.dataset.type}})));const o=ir(r);return e.files&&Array.from(e.files).forEach(i=>{o.addFile(i)}),o.replaceElement(e),o},el=(...e)=>dr(e[0])?Jo(...e):ir(...e),tl=["fire","_read","_write"],fn=e=>{const t={};return Ln(e,t,tl),t},nl=(e,t)=>e.replace(/(?:{([a-zA-Z]+)})/g,(n,r)=>t[r]),rl=e=>{const t=new Blob(["(",e.toString(),")()"],{type:"application/javascript"}),n=URL.createObjectURL(t),r=new Worker(n);return{transfer:(s,o)=>{},post:(s,o,i)=>{const a=_t();r.onmessage=l=>{l.data.id===a&&o(l.data.message)},r.postMessage({id:a,message:s},i)},terminate:()=>{r.terminate(),URL.revokeObjectURL(n)}}},sl=e=>new Promise((t,n)=>{const r=new Image;r.onload=()=>{t(r)},r.onerror=s=>{n(s)},r.src=e}),lr=(e,t)=>{const n=e.slice(0,e.size,e.type);return n.lastModifiedDate=e.lastModifiedDate,n.name=t,n},il=e=>lr(e,e.name),En=[],ol=e=>{if(En.includes(e))return;En.push(e);const t=e({addFilter:ss,utils:{Type:p,forin:q,isString:j,isFile:me,toNaturalFileSize:qn,replaceInString:nl,getExtensionFromFilename:Qe,getFilenameWithoutExtension:Fn,guesstimateMimeType:Kn,getFileFromBlob:ye,getFilenameFromURL:Ne,createRoute:K,createWorker:rl,createView:$,createItemAPI:J,loadImage:sl,copyFile:il,renameFile:lr,createBlob:vn,applyFilterChain:se,text:H,getNumericAspectRatioFromString:bn},views:{fileActionButton:Vn}});is(t.options)},ll=()=>Object.prototype.toString.call(window.operamini)==="[object OperaMini]",al=()=>"Promise"in window,cl=()=>"slice"in Blob.prototype,dl=()=>"URL"in window&&"createObjectURL"in window.URL,ul=()=>"visibilityState"in document,fl=()=>"performance"in window,El=()=>"supports"in(window.CSS||{}),pl=()=>/MSIE|Trident/.test(window.navigator.userAgent),pn=(()=>{const e=hn()&&!ll()&&ul()&&al()&&cl()&&dl()&&fl()&&(El()||pl());return()=>e})(),ue={apps:[]},Il="filepond",he=()=>{};let _l={},Tl={},ml={},In={},it=he,ot=he,_n=he,Tn=he,mn=he,gn=he,Rn=he;if(pn()){Ur(()=>{ue.apps.forEach(n=>n._read())},n=>{ue.apps.forEach(r=>r._write(n))});const e=()=>{document.dispatchEvent(new CustomEvent("FilePond:loaded",{detail:{supported:pn,create:it,destroy:ot,parse:_n,find:Tn,registerPlugin:mn,setOptions:Rn}})),document.removeEventListener("DOMContentLoaded",e)};document.readyState!=="loading"?setTimeout(()=>e(),0):document.addEventListener("DOMContentLoaded",e);const t=()=>q(He(),(n,r)=>{In[n]=r[1]});_l={...Cn},ml={...W},Tl={...N},In={},t(),it=(...n)=>{const r=el(...n);return r.on("destroy",ot),ue.apps.push(r),fn(r)},ot=n=>{const r=ue.apps.findIndex(s=>s.isAttachedTo(n));return r>=0?(ue.apps.splice(r,1)[0].restoreElement(),!0):!1},_n=n=>Array.from(n.querySelectorAll(`.${Il}`)).filter(o=>!ue.apps.find(i=>i.isAttachedTo(o))).map(o=>it(o)),Tn=n=>{const r=ue.apps.find(s=>s.isAttachedTo(n));return r?fn(r):null},mn=(...n)=>{n.forEach(ol),t()},gn=()=>{const n={};return q(He(),(r,s)=>{n[r]=s[0]}),n},Rn=n=>(z(n)&&(ue.apps.forEach(r=>{r.setOptions(n)}),os(n)),gn())}export{ml as FileOrigin,Tl as FileStatus,In as OptionTypes,_l as Status,it as create,ot as destroy,Tn as find,gn as getOptions,_n as parse,mn as registerPlugin,Rn as setOptions,pn as supported};
