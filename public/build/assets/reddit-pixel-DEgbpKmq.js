console.log("reddit-start");(function(n,r){if(!n.rdt){var e=n.rdt=function(){e.sendEvent?e.sendEvent.apply(e,arguments):e.callQueue.push(arguments)};e.callQueue=[];var t=r.createElement("script");t.src="https://www.redditstatic.com/ads/pixel.js",t.async=!0;var a=r.getElementsByTagName("script")[0];a.parentNode.insertBefore(t,a)}})(window,document);rdt("init","a2_fn61x5rx7t27");rdt("track","PageVisit");console.log("reddit-end");
