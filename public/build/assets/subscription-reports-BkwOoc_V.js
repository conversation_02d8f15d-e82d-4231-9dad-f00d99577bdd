import{C as n}from"./auto-1vNCvF_S.js";document.addEventListener("DOMContentLoaded",function(){if(!window.chartData){console.error("Chart data not found");return}const{dates:e,counts:r,revenue:i}=window.chartData;document.querySelectorAll(".chart-container").forEach(t=>{t.style.height="300px"});const a=document.getElementById("renewalsChart");a&&new n(a.getContext("2d"),{type:"line",data:{labels:e,datasets:[{label:"Number of Renewals",data:r,backgroundColor:"rgba(59, 130, 246, 0.2)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1,tension:.1}]},options:{scales:{y:{beginAtZero:!0,ticks:{precision:0}},x:{ticks:{maxRotation:45,minRotation:45,autoSkip:!0,maxTicksLimit:15}}},responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{callbacks:{title:function(t){return new Date(t[0].label).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"})}}}}}});const o=document.getElementById("revenueChart");o&&new n(o.getContext("2d"),{type:"bar",data:{labels:e,datasets:[{label:"Revenue ($)",data:i,backgroundColor:"rgba(16, 185, 129, 0.2)",borderColor:"rgba(16, 185, 129, 1)",borderWidth:1}]},options:{scales:{y:{beginAtZero:!0},x:{ticks:{maxRotation:45,minRotation:45,autoSkip:!0,maxTicksLimit:15}}},responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"},tooltip:{callbacks:{title:function(t){return new Date(t[0].label).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"})}}}}}})});
