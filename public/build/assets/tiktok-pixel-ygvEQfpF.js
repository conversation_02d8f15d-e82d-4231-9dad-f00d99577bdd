console.log("tiktok-start");(function(a,r,s){a.TiktokAnalyticsObject=s;var t=a[s]=a[s]||[];t.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"],t.setAndDefer=function(o,e){o[e]=function(){o.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<t.methods.length;i++)t.setAndDefer(t,t.methods[i]);t.instance=function(o){for(var e=t._i[o]||[],n=0;n<t.methods.length;n++)t.setAndDefer(e,t.methods[n]);return e},t.load=function(o,e){var n="https://analytics.tiktok.com/i18n/pixel/events.js";e&&e.partner,t._i=t._i||{},t._i[o]=[],t._i[o]._u=n,t._t=t._t||{},t._t[o]=+new Date,t._o=t._o||{},t._o[o]=e||{},e=document.createElement("script"),e.type="text/javascript",e.async=!0,e.src=n+"?sdkid="+o+"&lib="+s,o=document.getElementsByTagName("script")[0],o.parentNode.insertBefore(e,o)},t.load("CRG9C0JC77UB15K073N0"),t.page()})(window,document,"ttq");console.log("tiktok-end");
