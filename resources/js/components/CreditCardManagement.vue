<template>
    <div>
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold">Credit Cards</h2>
            <button class="btn btn-primary" @click="showAddCardModal = true">
                Add Credit Card
            </button>
        </div>

        <!-- Credit Cards List -->
        <div v-if="creditCards.length > 0" class="space-y-4">
            <div
                v-for="card in creditCards"
                :key="card.id"
                class="bg-white p-4 rounded-lg border shadow-sm flex justify-between items-center"
            >
                <div class="flex items-center gap-3">
                    <div class="w-10">
                        <svg v-if="card.brand.toLowerCase() === 'visa'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="w-8 h-8 text-blue-600 fill-current">
                            <path d="M470.1 231.3s7.6 37.2 9.3 45H446c3.3-8.9 16-43.5 16-43.5-.2.3 3.3-9.1 5.3-14.9l2.8 13.4zM576 80v352c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h480c26.5 0 48 21.5 48 48zM152.5 331.2L215.7 176h-42.5l-39.3 106-4.3-21.5-14-71.4c-2.3-9.9-9.4-12.7-18.2-13.1H32.7l-.7 3.1c15.9 4 29.5 9.8 42.3 17.1l35.7 135h42.8zm94.5.2L272.1 176h-40.2l-25.1 155.4h40.1zm139.9-50.8c.2-17.7-10.6-31.2-33.7-42.3-14.1-7.1-22.7-11.9-22.7-19.2.2-6.6 7.3-13.4 23.1-13.4 13.1-.3 22.7 2.8 29.9 5.9l3.6 1.7 5.5-33.6c-7.9-3.1-20.5-6.6-36-6.6-39.7 0-67.6 21.2-67.8 51.4-.3 22.3 20 34.7 35.2 42.2 15.5 7.6 20.8 12.6 20.8 19.3-.2 10.4-12.6 15.2-24.1 15.2-16 0-24.6-2.5-37.7-8.3l-5.3-2.5-5.6 34.9c9.4 4.3 26.8 8.1 44.8 8.3 42.2.1 69.7-20.8 70-53zM528 331.4L495.6 176h-31.1c-9.6 0-16.9 2.8-21 12.9l-59.7 142.5H426s6.9-19.2 8.4-23.3H486c1.2 5.5 4.8 23.3 4.8 23.3H528z" />
                        </svg>
                        <svg v-else-if="card.brand.toLowerCase() === 'mastercard'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="w-8 h-8">
                            <path fill="#FF5F00" d="M492.4 220.8c-8.9-53.2-63.5-91.5-128.9-91.5-65 0-118.3 37.1-128.9 88.4-4.3 21.7-3.3 62.8 17.7 86.6v0c4.3 5.2 8.9 9.8 14.3 13.5 5.7 3.9 12.5 6.6 19.8 7.9 7.5 1.3 15.5 1.8 25.5 1.8 10.1 0 20.8-0.4 31.6-1.8 10.9-1.3 21.7-3.5 32.2-7 10.7-3.6 20.6-8.9 29.6-17.1 4.7-4.3 8.5-9.3 11.3-14.9 2.5-5.6 4.1-11.9 4.1-18.8 0-6.9-1.9-13.2-4.3-18.8-2.8-5.5-6.7-10.7-11.4-15z" />
                            <path fill="#EB001B" d="M492.4 220.8c-8.9-53.2-63.5-91.5-128.9-91.5-65 0-118.3 37.1-128.9 88.4-4.3 21.7-3.3 62.8 17.7 86.6v0c4.3 5.2 8.9 9.8 14.3 13.5 5.7 3.9 12.5 6.6 19.8 7.9 7.5 1.3 15.5 1.8 25.5 1.8 10.1 0 20.8-0.4 31.6-1.8 10.9-1.3 21.7-3.5 32.2-7 10.7-3.6 20.6-8.9 29.6-17.1 4.7-4.3 8.5-9.3 11.3-14.9 2.5-5.6 4.1-11.9 4.1-18.8 0-6.9-1.9-13.2-4.3-18.8-2.8-5.5-6.7-10.7-11.4-15z" />
                            <path fill="#F79E1B" d="M492.4 220.8c-8.9-53.2-63.5-91.5-128.9-91.5-65 0-118.3 37.1-128.9 88.4-4.3 21.7-3.3 62.8 17.7 86.6v0c4.3 5.2 8.9 9.8 14.3 13.5 5.7 3.9 12.5 6.6 19.8 7.9 7.5 1.3 15.5 1.8 25.5 1.8 10.1 0 20.8-0.4 31.6-1.8 10.9-1.3 21.7-3.5 32.2-7 10.7-3.6 20.6-8.9 29.6-17.1 4.7-4.3 8.5-9.3 11.3-14.9 2.5-5.6 4.1-11.9 4.1-18.8 0-6.9-1.9-13.2-4.3-18.8-2.8-5.5-6.7-10.7-11.4-15z" />
                        </svg>
                        <svg v-else-if="card.brand.toLowerCase() === 'amex'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="w-8 h-8 text-blue-400 fill-current">
                            <path d="M325.1 167.8c0-16.4-14.1-18.4-27.4-18.4l-39.1-.3v69.3H275v-25.1h18c18.4 0 14.5 10.3 14.8 25.1h16.6v-13.5c0-9.2-1.5-15.1-11-18.4 7.4-3 11.8-10.7 11.7-18.7zm-29.4 11.3H275v-15.3h21c5.1 0 10.7 1 10.7 7.4 0 6.6-5.3 7.9-11 7.9zM279 268.6h-52.7l-21 22.8-20.5-22.8h-66.5l-.1 69.3h65.4l21.3-23 20.4 23h32.2l.1-23.3c18.9 0 49.3 4.6 49.3-23.3 0-17.3-12.3-22.7-27.9-22.7zm-103.8 54.7h-40.6v-13.8h36.3v-14.1h-36.3v-12.5h41.7l17.9 20.2zm65.8 8.2l-25.3-28.1L241 276zm37.8-31h-21.2v-17.6h21.5c5.6 0 10.2 2.3 10.2 8.4 0 6.4-4.6 9.2-10.5 9.2zm-31.6-136.7v-14.6h-55.5v69.3h55.5v-14.3h-38.9v-13.8h37.8v-14.1h-37.8v-12.5zM576 255.4h-.2zm-194.6 31.9c0-16.4-14.1-18.7-27.1-18.7h-39.4l-.1 69.3h16.6l.1-25.3h17.6c11 0 14.8 2 14.8 13.8l-.1 11.5h16.6l.1-13.8c0-8.9-1.8-15.1-11-18.4 7.7-3.1 11.8-10.8 11.9-18.4zm-29.2 11.2h-20.7v-15.6h21c5.1 0 10.7 1 10.7 7.7 0 6.7-5.4 7.9-11 7.9zm-172.8-80v-69.3h-27.6l-19.7 47-21.7-47H83.3v65.7l-28.1-65.7H30.7L1 187.7h17.9l6.4 15.3h31.1l6.1-15.3h17.9l-22.2 55.2c-2 5.1-3.6 10.4-3.6 10.4s2.6-5.3 7.1-14.9l31.1-55.6v59.9h18.1v-59.9l29.2 59.9h18.1zm-74.6-40.9h-19.2l9.8-24.8zM372.6 53.8c19.5 0 36.3 6.5 45.5 16.3l-13.8 13.8c-5.1-7.7-18.9-14.1-31.7-14.1-27.2 0-48.7 22.8-48.7 51.7 0 28.9 21.5 51.7 48.7 51.7 12.8 0 26.6-6.4 31.7-14.1l13.8 13.8c-9.2 9.8-26 16.3-45.5 16.3-41.2 0-69.6-33.3-69.6-67.7 0-34.5 28.4-67.7 69.6-67.7zm-136.2 0c41 0 69.6 33.2 69.6 67.7 0 34.4-28.5 67.7-69.6 67.7s-69.6-33.3-69.6-67.7c0-34.5 28.5-67.7 69.6-67.7zm0 15.4c-27.2 0-48.7 22.8-48.7 51.7 0 28.9 21.5 51.7 48.7 51.7s48.7-22.8 48.7-51.7c0-28.9-21.5-51.7-48.7-51.7zM152 53.8c19.5 0 36.3 6.5 45.5 16.3l-13.8 13.8c-5.1-7.7-18.9-14.1-31.7-14.1-27.2 0-48.7 22.8-48.7 51.7 0 28.9 21.5 51.7 48.7 51.7 12.8 0 26.6-6.4 31.7-14.1l13.8 13.8c-9.2 9.8-26 16.3-45.5 16.3-41.2 0-69.6-33.3-69.6-67.7 0-34.5 28.4-67.7 69.6-67.7z" />
                        </svg>
                        <svg v-else-if="card.brand.toLowerCase() === 'discover'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="w-8 h-8 text-orange-500 fill-current">
                            <path d="M520.4 196.1c0-7.9-5.5-12.1-15.6-12.1h-4.9v24.9h4.7c10.3 0 15.8-4.4 15.8-12.8zM528 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zm-44.1 138.9c22.6 0 52.9 16.9 52.9 50 0 30.7-24.9 48.1-57.9 48.1h-28.6v38.1h-41.2V170.9h74.8zm-193.7 50.1c0-57.9 31.5-79.9 65.8-79.9 31.5 0 49.8 19.2 49.8 47.5 0 29.2-16.1 47.5-49.8 47.5h-16.3v38.1h-49.5V221zm-97.3-1c0-29.6 30.7-50 65-50 33.1 0 49.2 18.3 49.2 47 0 30.4-16.1 48.1-49.8 48.1h-15.5v39.1h-48.9V220z" />
                        </svg>
                        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" class="w-8 h-8 text-gray-600 fill-current">
                            <path d="M528 32H48C21.5 32 0 53.5 0 80v352c0 26.5 21.5 48 48 48h480c26.5 0 48-21.5 48-48V80c0-26.5-21.5-48-48-48zM176 352c-35.3 0-64-28.7-64-64s28.7-64 64-64 64 28.7 64 64-28.7 64-64 64zm352 32H208c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h8v-24c0-30.9 25.1-56 56-56h64c30.9 0 56 25.1 56 56v24h8c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8zm0-192H208c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h320c4.4 0 8 3.6 8 8v16c0 4.4-3.6 8-8 8z" />
                        </svg>
                    </div>
                    <div>
                        <div class="font-semibold">{{ card.brand }}</div>
                        <div class="text-sm text-gray-600">•••• {{ card.cc }}</div>
                    </div>
                    <div class="text-sm text-gray-500">
                        Expires {{ card.expiration_month }}/{{ card.expiration_year }}
                    </div>
                    <div v-if="card.is_default" class="badge badge-sm badge-primary">Default</div>
                </div>

                <div class="flex items-center gap-2">
                    <button
                        v-if="!card.is_default"
                        class="btn btn-sm btn-outline"
                        @click="setDefaultCard(card.id)"
                        :disabled="isProcessing"
                    >
                        Set Default
                    </button>
                    <button
                        class="btn btn-sm btn-outline btn-error"
                        @click="confirmDeleteCard(card)"
                        :disabled="isProcessing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12 bg-gray-50 rounded-lg border">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-12 h-12 mx-auto text-gray-400 mb-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
            </svg>
            <h3 class="text-lg font-semibold mb-2">No Payment Methods</h3>
            <p class="text-gray-600 mb-6">You haven't added any payment methods yet.</p>
            <button class="btn btn-primary" @click="showAddCardModal = true">
                Add Payment Method
            </button>
        </div>

        <!-- Add Card Modal -->
        <div v-if="showAddCardModal" class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Add Payment Method</h3>

                <form @submit.prevent="addCreditCard">
                    <label class="form-control">
                        <div class="label">
                            <span class="label-text">Card Number</span>
                        </div>
                        <input
                            type="text"
                            v-model="newCard.number"
                            class="input input-bordered w-full"
                            :class="{ 'input-error': validationErrors.card_number?.length > 0 }"
                            placeholder="1234 5678 9012 3456"
                            maxlength="19"
                            @input="formatCardNumber"
                            required
                        />
                        <div v-if="validationErrors.card_number?.length > 0" class="label">
                            <span class="label-text-alt"></span>
                            <span class="label-text-alt text-error" v-text="validationErrors.card_number[0]" />
                        </div>
                    </label>

                    <div class="grid grid-cols-2 gap-4">
                        <label class="form-control">
                            <div class="label">
                                <span class="label-text">Expiration Date</span>
                            </div>
                            <input
                                type="text"
                                v-model="newCard.expiration"
                                class="input input-bordered"
                                :class="{ 'input-error': validationErrors.expiration_date?.length > 0 }"
                                placeholder="MM/YY"
                                maxlength="5"
                                @input="formatExpiration"
                                required
                            />
                            <div v-if="validationErrors.expiration_date?.length > 0" class="label">
                                <span class="label-text-alt"></span>
                                <span class="label-text-alt text-error" v-text="validationErrors.expiration_date[0]" />
                            </div>
                        </label>

                        <label class="form-control">
                            <div class="label">
                                <span class="label-text">CVV</span>
                            </div>
                            <input
                                type="text"
                                v-model="newCard.cvv"
                                class="input input-bordered"
                                :class="{ 'input-error': validationErrors.cvv?.length > 0 }"
                                placeholder="123"
                                maxlength="4"
                                required
                            />
                            <div v-if="validationErrors.cvv?.length > 0" class="label">
                                <span class="label-text-alt"></span>
                                <span class="label-text-alt text-error" v-text="validationErrors.cvv[0]" />
                            </div>
                        </label>
                    </div>

                    <div class="text-sm text-gray-600 mb-4">
                        <p>A $0.01 verification charge will be processed to verify your card. This amount will be refunded.</p>
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn" @click="showAddCardModal = false">Cancel</button>
                        <button
                            type="submit"
                            class="btn btn-primary"
                            :disabled="isProcessing"
                        >
                            <span v-if="isProcessing">
                                <span class="loading loading-spinner loading-xs"></span>
                                Processing...
                            </span>
                            <span v-else>Add Card</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div v-if="showDeleteModal" class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Remove Payment Method</h3>
                <p>Are you sure you want to remove this payment method?</p>
                <div v-if="cardToDelete" class="flex items-center gap-3 my-4 p-3 bg-gray-50 rounded-lg">
                    <div class="font-semibold">{{ cardToDelete.brand }}</div>
                    <div>•••• {{ cardToDelete.cc }}</div>
                    <div class="text-sm text-gray-500">
                        Expires {{ cardToDelete.expiration_month }}/{{ cardToDelete.expiration_year }}
                    </div>
                </div>
                <div class="modal-action">
                    <button type="button" class="btn" @click="showDeleteModal = false">Cancel</button>
                    <button
                        type="button"
                        class="btn btn-error"
                        @click="deleteCard"
                        :disabled="isProcessing"
                    >
                        <span v-if="isProcessing">
                            <span class="loading loading-spinner loading-xs"></span>
                            Processing...
                        </span>
                        <span v-else>Remove</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Subscription Activation Modal -->
        <div v-if="showActivateModal" class="modal modal-open">
            <div class="modal-box max-w-2xl">
                <h3 class="font-bold text-lg mb-4">
                    Subscribe to {{ selectedPlan?.name }}
                </h3>

                <div v-if="selectedPlan" class="bg-blue-50 p-4 rounded-lg mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="font-semibold">{{ selectedPlan.name }}</h4>
                            <p class="text-sm text-gray-600">{{ selectedPlan.duration_months }} month(s)</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold">${{ selectedPlan.price }}</div>
                        </div>
                    </div>
                </div>

                <div v-if="creditCards.length > 0">
                    <h4 class="font-semibold mb-4">Select Payment Method</h4>
                    <div class="space-y-4">
                        <div
                            v-for="card in creditCards"
                            :key="card.id"
                            class="flex items-center justify-between p-4 border rounded-lg"
                        >
                            <div class="flex items-center gap-3">
                                <div class="font-semibold">{{ card.brand }}</div>
                                <div>•••• {{ card.cc }}</div>
                                <div class="text-sm text-gray-500">
                                    Expires {{ card.expiration_month }}/{{ card.expiration_year }}
                                </div>
                                <div v-if="card.is_default" class="badge badge-sm badge-primary">Default</div>
                            </div>

                            <button
                                class="btn btn-primary btn-sm"
                                @click="activatePlan(selectedPlan, card.id)"
                                :disabled="isActivating"
                            >
                                <span v-if="isActivating && activatingCardId === card.id">
                                    <span class="loading loading-spinner loading-xs"></span>
                                    Processing...
                                </span>
                                <span v-else>Pay</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div v-else class="text-center py-8">
                    <p class="text-gray-600 mb-4">You need to add a payment method first.</p>
                    <button class="btn btn-primary" @click="showActivateModal = false; showAddCardModal = true">
                        Add Payment Method
                    </button>
                </div>

                <div class="modal-action">
                    <button type="button" class="btn" @click="showActivateModal = false">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue';

const Splade = inject('$splade');

const props = defineProps({
    initialCreditCards: {
        type: Array,
        default: () => []
    },
    userId: {
        type: Number,
        default: null
    },
    role: {
        type: String,
        default: null
    }
});

// State
let creditCards = ref(props.initialCreditCards || []);
const showAddCardModal = ref(false);
const showDeleteModal = ref(false);
const showActivateModal = ref(false);
const cardToDelete = ref(null);
const selectedPlan = ref(null);
const isProcessing = ref(false);
const isActivating = ref(false);
const activatingCardId = ref(null);

const newCard = reactive({
    number: '',
    expiration: '',
    cvv: ''
});

const formatCardNumber = () => {
    let value = newCard.number.replace(/\D/g, '');

    let formattedValue = '';
    for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
            formattedValue += ' ';
        }
        formattedValue += value[i];
    }

    newCard.number = formattedValue;
};

const formatExpiration = () => {
    let value = newCard.expiration.replace(/\D/g, '');

    if (value.length > 2) {
        newCard.expiration = value.substring(0, 2) + '/' + value.substring(2);
    } else {
        newCard.expiration = value;
    }
};

const fetchCreditCards = async () => {
    try {
        const response = await Splade.request(`/json/credit-cards/${props.userId}`, 'get');
        const { splade, ...actionResponse } = response.data;
        creditCards.value = Object.values(actionResponse.credit_cards);
        Splade.emit('credit-card-list-updated', actionResponse.credit_cards)
    } catch (error) {
        console.error('Error fetching credit cards:', error);
    }
};

onMounted(() => {
    fetchCreditCards()
});

let validationErrors = ref({
    card_number: [],
    expiration_date: [],
    cvv: [],
});
const addCreditCard = async () => {
    isProcessing.value = true;

    try {
        // Extract card details
        const card_number = newCard.number.replace(/\D/g, '');
        const [expiration_month, expiration_year] = newCard.expiration.split('/');
        if (expiration_month > 12) {
            validationErrors.value.expiration_date[0] = 'Invalid month';
            isProcessing.value = false;
            return;
        }
        const cvv = newCard.cvv;

        // Determine the correct URL based on role
        let url;
        if (props.role === 'business') {
            url = '/business/credit-cards';
        } else if (props.role === 'admin') {
            url = `/admin/patients/${props.userId}/payment-methods`;
        } else if (props.role === 'agent') {
            url = `/agent/patients/${props.userId}/payment-methods`;
        } else {
            // For patient role or default
            url = '/p/payment-methods';
        }

        await Splade.request(url, 'post', {
            card_number,
            expiration_month,
            expiration_year,
            cvv
        });

        // Reset form and close modal
        newCard.number = '';
        newCard.expiration = '';
        newCard.cvv = '';
        showAddCardModal.value = false;

        // Refresh credit cards
        await fetchCreditCards();
    } catch (error) {
        console.error('Error adding credit card:', error);
        if (error.response && error.response.data && error.response.data.errors) {
            validationErrors.value = error.response.data.errors;
        }
    } finally {
        isProcessing.value = false;
    }
};

// Set default card
const setDefaultCard = async (cardId) => {
    isProcessing.value = true;

    try {
        // Determine the correct URL based on role
        let url;
        if (props.role === 'business') {
            url = `/business/credit-cards/${cardId}/set-default`;
        } else if (props.role === 'admin') {
            url = `/admin/patients/${props.userId}/payment-methods/${cardId}/set-default`;
        } else if (props.role === 'agent') {
            url = `/agent/patients/${props.userId}/payment-methods/${cardId}/set-default-cc`;
        } else {
            // For patient role or default
            url = `/p/payment-methods/${cardId}/set-default`;
        }

        await Splade.request(url, 'post');
        await fetchCreditCards();
    } catch (error) {
        console.error('Error setting default card:', error);
    } finally {
        isProcessing.value = false;
    }
};

// Confirm delete card
const confirmDeleteCard = (card) => {
    cardToDelete.value = card;
    showDeleteModal.value = true;
};

// Delete card
const deleteCard = async () => {
    if (!cardToDelete.value) return;

    isProcessing.value = true;

    try {
        // Determine the correct URL based on role
        let url;
        if (props.role === 'business') {
            url = `/business/credit-cards/${cardToDelete.value.id}`;
        } else if (props.role === 'admin') {
            url = `/admin/patients/${props.userId}/payment-methods/${cardToDelete.value.id}`;
        } else if (props.role === 'agent') {
            url = `/agent/patients/${props.userId}/payment-methods/${cardToDelete.value.id}/delete`;
        } else {
            // For patient role or default
            url = `/p/payment-methods/${cardToDelete.value.id}`;
        }

        await Splade.request(url, 'delete');
        showDeleteModal.value = false;
        cardToDelete.value = null;
        await fetchCreditCards();
    } catch (error) {
        console.error('Error deleting credit card:', error);
    } finally {
        isProcessing.value = false;
    }
};

// Subscription functionality
// Show activate plan modal
const showActivatePlanModal = (plan) => {
    selectedPlan.value = plan;
    showActivateModal.value = true;
};

// Activate a plan with a specific card
const activatePlan = async (plan, cardId) => {
    if (!plan) return;

    isActivating.value = true;
    activatingCardId.value = cardId;

    try {
        // For redirects, we need to use a form submission instead of AJAX
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/p/subscribe/${plan.id}/with-card/${cardId}`;
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error activating plan:', error);
    } finally {
        isActivating.value = false;
        activatingCardId.value = null;
    }
};

// Expose methods to parent component
defineExpose({
    showActivatePlanModal,
    fetchCreditCards
});
</script>
