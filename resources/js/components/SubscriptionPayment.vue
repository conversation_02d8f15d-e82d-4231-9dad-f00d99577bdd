<template>
    <div>
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold">Payment Methods</h2>
            <button class="btn btn-primary" @click="showAddCardModal = true">
                Add Payment Method
            </button>
        </div>

        <!-- Credit Cards List -->
        <div class="mb-4 flex justify-between items-center">
            <h3 class="text-lg font-semibold">Active Payment Methods</h3>
            <button
                v-if="!showTrashedCards"
                class="btn btn-sm btn-outline"
                @click="showTrashedCards = true; fetchCreditCards(true)"
            >
                Show Deleted Cards
            </button>
            <button
                v-else
                class="btn btn-sm btn-outline"
                @click="showTrashedCards = false; fetchCreditCards(false)"
            >
                Hide Deleted Cards
            </button>
        </div>

        <div v-if="creditCards.length > 0" class="space-y-4">
            <div
                v-for="card in creditCards.filter(c => !c.is_deleted || showTrashedCards)"
                :key="card.id"
                :class="{
                    'bg-white p-4 rounded-lg border shadow-sm flex justify-between items-center': true,
                    'opacity-60 bg-gray-50': card.is_deleted
                }"
            >
                <div class="flex items-center gap-3">
                    <!-- Card brand icons -->
                    <div class="w-10">
                        <!-- Various card brand SVGs -->
                    </div>
                    <div>
                        <div class="font-semibold">{{ card.brand }}</div>
                        <div class="text-sm text-gray-600">•••• {{ card.last_four }}</div>
                    </div>
                    <div class="text-sm text-gray-500">
                        Expires {{ card.expiration_month }}/{{ card.expiration_year }}
                    </div>
                    <div v-if="card.is_default" class="badge badge-sm badge-primary">Default</div>
                    <div v-if="card.is_deleted" class="badge badge-sm badge-ghost">Deleted</div>
                </div>

                <div class="flex items-center gap-2">
                    <button
                        v-if="!card.is_default && !card.is_deleted"
                        class="btn btn-sm btn-outline"
                        @click="setDefaultCard(card.id)"
                        :disabled="isProcessing"
                    >
                        Set Default
                    </button>

                    <!-- Restore button for deleted cards -->
                    <button
                        v-if="card.is_deleted"
                        class="btn btn-sm btn-outline btn-success"
                        @click="restoreCard(card.id)"
                        :disabled="isProcessing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                        </svg>
                        Restore
                    </button>

                    <!-- Delete/Force Delete button -->
                    <button
                        v-if="!card.is_deleted"
                        class="btn btn-sm btn-outline btn-error"
                        @click="confirmDeleteCard(card)"
                        :disabled="isProcessing"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                    <button
                        v-else
                        class="btn btn-sm btn-outline btn-error"
                        @click="forceDeleteCard(card.id)"
                        :disabled="isProcessing"
                        title="Permanently delete this card"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Permanently Delete
                    </button>
                </div>
            </div>
        </div>

        <div v-else class="bg-gray-50 p-6 rounded-lg border border-dashed border-gray-300 text-center">
            <p class="text-gray-500 mb-4">No payment methods found</p>
            <button class="btn btn-primary" @click="showAddCardModal = true">
                Add Your First Payment Method
            </button>
        </div>

        <!-- Add Card Modal -->
        <div v-if="showAddCardModal" class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Add Payment Method</h3>

                <form @submit.prevent="addCreditCard">
                    <label class="form-control">
                        <div class="label">
                            <span class="label-text">Card Number</span>
                        </div>
                        <input
                            type="text"
                            v-model="newCard.number"
                            class="input input-bordered w-full"
                            :class="{ 'input-error': validationErrors.card_number?.length > 0 }"
                            placeholder="1234 5678 9012 3456"
                            maxlength="19"
                            @input="formatCardNumber"
                            required
                        />
                        <div v-if="validationErrors.card_number?.length > 0" class="label">
                            <span class="label-text-alt"></span>
                            <span class="label-text-alt text-error" v-text="validationErrors.card_number[0]" />
                        </div>
                    </label>

                    <div class="grid grid-cols-2 gap-4">
                        <label class="form-control">
                            <div class="label">
                                <span class="label-text">Expiration Date</span>
                            </div>
                            <input
                                type="text"
                                v-model="newCard.expiration"
                                class="input input-bordered"
                                :class="{ 'input-error': validationErrors.expiration_date?.length > 0 }"
                                placeholder="MM/YY"
                                maxlength="5"
                                @input="formatExpiration"
                                required
                            />
                        </label>

                        <label class="form-control">
                            <div class="label">
                                <span class="label-text">CVV</span>
                            </div>
                            <input
                                type="text"
                                v-model="newCard.cvv"
                                class="input input-bordered"
                                :class="{ 'input-error': validationErrors.cvv?.length > 0 }"
                                placeholder="123"
                                maxlength="4"
                                required
                            />
                        </label>
                    </div>

                    <div class="text-sm text-gray-600 mb-4 mt-2">
                        <p>A $0.01 verification charge will be processed to verify your card. This amount will be refunded.</p>
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn" @click="showAddCardModal = false">Cancel</button>
                        <button
                            type="submit"
                            class="btn btn-primary"
                            :disabled="isProcessing"
                        >
                            <span v-if="isProcessing">
                                <span class="loading loading-spinner loading-xs"></span>
                                Processing...
                            </span>
                            <span v-else>Add Card</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Delete Card Confirmation Modal -->
        <div v-if="showDeleteModal" class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Delete Payment Method</h3>
                <p>Are you sure you want to delete this payment method?</p>
                <div class="bg-gray-100 p-3 rounded-lg my-3" v-if="cardToDelete">
                    <p>{{ cardToDelete.brand }} •••• {{ cardToDelete.last_four }}</p>
                    <p class="text-sm text-gray-600">Expires {{ cardToDelete.expiration_month }}/{{ cardToDelete.expiration_year }}</p>
                </div>
                <div class="modal-action">
                    <button class="btn" @click="showDeleteModal = false">Cancel</button>
                    <button
                        class="btn btn-error"
                        @click="deleteCard"
                        :disabled="isProcessing"
                    >
                        <span v-if="isProcessing">
                            <span class="loading loading-spinner loading-xs"></span>
                            Processing...
                        </span>
                        <span v-else>Delete</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Activate Plan Modal -->
        <div v-if="showActivateModal" class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg mb-4">Activate Plan</h3>

                <div v-if="creditCards.length > 0">
                    <p class="mb-4">Select a payment method to activate this plan:</p>

                    <div class="space-y-4">
                        <div
                            v-for="card in creditCards"
                            :key="card.id"
                            class="flex items-center justify-between p-4 border rounded-lg"
                        >
                            <div class="flex items-center gap-3">
                                <div class="font-semibold">{{ card.brand }}</div>
                                <div>•••• {{ card.last_four }}</div>
                                <div class="text-sm text-gray-500">
                                    Expires {{ card.expiration_month }}/{{ card.expiration_year }}
                                </div>
                                <div v-if="card.is_default" class="badge badge-sm">Default</div>
                            </div>

                            <button
                                class="btn btn-primary btn-sm"
                                @click="activatePlan(selectedPlan, card.id)"
                                :disabled="isActivating"
                            >
                                <span v-if="isActivating && activatingCardId === card.id">
                                    <span class="loading loading-spinner loading-xs"></span>
                                    Processing...
                                </span>
                                <span v-else>Pay</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div v-else class="alert alert-warning">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                    <div>
                        <h3 class="font-bold">No payment methods found</h3>
                        <div class="text-sm">Please add a payment method first.</div>
                    </div>
                </div>

                <div class="modal-action">
                    <button class="btn" @click="showActivateModal = false">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, inject, onMounted } from 'vue';

const Splade = inject('$splade');

const props = defineProps({
    initialCreditCards: {
        type: Array,
        default: () => []
    }
});

// State
let creditCards = ref(props.initialCreditCards || []);
const showAddCardModal = ref(false);
const showDeleteModal = ref(false);
const showActivateModal = ref(false);
const showTrashedCards = ref(false);
const cardToDelete = ref(null);
const isProcessing = ref(false);
const isActivating = ref(false);
const activatingCardId = ref(null);
const selectedPlan = ref(null);

const newCard = reactive({
    number: '',
    expiration: '',
    cvv: ''
});

const validationErrors = ref({
    card_number: [],
    expiration_date: [],
    cvv: [],
});

// Format card number with spaces
const formatCardNumber = () => {
    let value = newCard.number.replace(/\D/g, '');

    let formattedValue = '';
    for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
            formattedValue += ' ';
        }
        formattedValue += value[i];
    }

    newCard.number = formattedValue;
};

// Format expiration date with slash
const formatExpiration = () => {
    let value = newCard.expiration.replace(/\D/g, '');

    if (value.length > 2) {
        newCard.expiration = value.substring(0, 2) + '/' + value.substring(2);
    } else {
        newCard.expiration = value;
    }
};

// Fetch credit cards from the server
const fetchCreditCards = async (includeTrashed = false) => {
    try {
        const url = includeTrashed ? '/p/credit-cards?with_trashed=1' : '/p/credit-cards';
        const response = await Splade.request(url, 'get');
        const { splade, ...creditCardsData } = response.data;
        creditCards.value = Object.values(creditCardsData);
        Splade.emit('credit-card-list-updated', creditCardsData)
    } catch (error) {
        console.error('Error fetching credit cards:', error);
    }
};

// Add a new credit card
const addCreditCard = async () => {
    isProcessing.value = true;
    validationErrors.value = {
        card_number: [],
        expiration_date: [],
        cvv: [],
    };

    try {
        // Extract card details
        const card_number = newCard.number.replace(/\D/g, '');
        const [expiration_month, expiration_year] = newCard.expiration.split('/');
        if (expiration_month > 12) {
            validationErrors.value.expiration_date = ['Invalid month'];
            isProcessing.value = false;
            return;
        }
        const cvv = newCard.cvv;

        await Splade.request('/p/credit-cards', 'post', {
            card_number,
            expiration_month,
            expiration_year: '20' + expiration_year,
            cvv
        });

        // Reset form and close modal
        newCard.number = '';
        newCard.expiration = '';
        newCard.cvv = '';
        showAddCardModal.value = false;

        // Refresh credit cards
        await fetchCreditCards();
    } catch (error) {
        console.error('Error adding credit card:', error);
        if (error.response && error.response.data && error.response.data.errors) {
            validationErrors.value = error.response.data.errors;
        }
    } finally {
        isProcessing.value = false;
    }
};

// Set a card as default
const setDefaultCard = async (cardId) => {
    isProcessing.value = true;
    try {
        await Splade.request(`/p/credit-cards/${cardId}/set-default`, 'post');
        await fetchCreditCards();
    } catch (error) {
        console.error('Error setting default card:', error);
    } finally {
        isProcessing.value = false;
    }
};

// Confirm card deletion
const confirmDeleteCard = (card) => {
    cardToDelete.value = card;
    showDeleteModal.value = true;
};

// Delete a card (soft delete)
const deleteCard = async () => {
    if (!cardToDelete.value) return;

    isProcessing.value = true;
    try {
        // Create a form for proper CSRF handling
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/p/credit-cards/${cardToDelete.value.id}`;
        form.style.display = 'none';

        // Add method spoofing for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error deleting card:', error);
        isProcessing.value = false;
    }
};

// Force delete a card (permanent delete)
const forceDeleteCard = async (cardId) => {
    isProcessing.value = true;
    try {
        // Create a form for proper CSRF handling
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/p/credit-cards/${cardId}/force`;
        form.style.display = 'none';

        // Add method spoofing for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error permanently deleting card:', error);
        isProcessing.value = false;
    }
};

// Restore a soft-deleted card
const restoreCard = async (cardId) => {
    isProcessing.value = true;
    try {
        // Create a form for proper CSRF handling
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/p/credit-cards/${cardId}/restore`;
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error restoring card:', error);
        isProcessing.value = false;
    }
};

// Show activate plan modal
const showActivatePlanModal = (plan) => {
    selectedPlan.value = plan;
    showActivateModal.value = true;
};

// Activate a plan with a specific card
const activatePlan = async (plan, cardId) => {
    if (!plan) return;

    isActivating.value = true;
    activatingCardId.value = cardId;

    try {
        // For redirects, we need to use a form submission instead of AJAX
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/p/subscribe/${plan.id}/with-card/${cardId}`;
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    } catch (error) {
        console.error('Error activating plan:', error);
    } finally {
        isActivating.value = false;
        activatingCardId.value = null;
    }
};

// Expose methods to parent component
defineExpose({
    showActivatePlanModal,
    forceDeleteCard,
    restoreCard,
    fetchCreditCards
});

onMounted(() => {
    fetchCreditCards();
});
</script>
