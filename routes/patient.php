<?php

use App\Http\Controllers\Patient\AllergyController;
use App\Http\Controllers\Patient\ConsultationController;
use App\Http\Controllers\Patient\DashboardController;
use App\Http\Controllers\Patient\FamilyMemberController;
use App\Http\Controllers\Patient\MedicalConditionController;
use App\Http\Controllers\Patient\MedicalRecordController;
use App\Http\Controllers\Patient\MedicalQuestionnaireController;
use App\Http\Controllers\Patient\MedicationOrderController;
use App\Http\Controllers\Patient\ProfileController;
use App\Http\Controllers\Patient\RegisterController;
use App\Http\Controllers\Patient\SubscriptionController;
use App\Http\Controllers\Patient\UserMedicationController;
use App\Http\Controllers\Patient\VideoRecordingController;
use Illuminate\Support\Facades\Route;


Route::middleware(['auth', 'role:patient'])->group(function () {
    Route::prefix('p')->name('patient.')->group(function () {
        Route::get('dashboard', [DashboardController::class, 'index'])->middleware(['profile.completion', 'medical.questionnaire'])->name('dashboard');

        // Profile Management
        Route::get('profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::post('profile', [ProfileController::class, 'update'])->name('profile.update');

        Route::get('payment', [SubscriptionController::class, 'showPaymentPage'])->name('payment');
        Route::post('payment/{plan}', [SubscriptionController::class, 'processPayment'])->name('process-payment');
        Route::get('subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
        Route::get('subscriptions/history', [SubscriptionController::class, 'history'])->name('subscriptions.history');
        Route::post('subscribe/{plan}', [SubscriptionController::class, 'subscribe'])->name('subscribe');
        Route::post('subscribe/{plan}/with-card/{creditCard}', [SubscriptionController::class, 'subscribeWithCard'])->name('subscribe.with-card');
        Route::post('subscription/cancel', [SubscriptionController::class, 'cancel'])->name('subscription.cancel');
        Route::post('subscription/renew', [SubscriptionController::class, 'renew'])->name('subscription.renew');
        Route::get('free-promo', [SubscriptionController::class, 'freePromo'])->name('free-promo');

        // Credit Card Management
        Route::get('payment-methods', [\App\Http\Controllers\PaymentMethodsController::class, 'index'])->name('payment-methods.index');
        Route::post('payment-methods', [\App\Http\Controllers\PaymentMethodsController::class, 'storeForCurrentUser'])->name('payment-methods.store');
        Route::post('payment-methods/{creditCard}/set-default', [\App\Http\Controllers\PaymentMethodsController::class, 'setDefaultForCurrentUser'])->name('payment-methods.set-default');
        Route::delete('payment-methods/{creditCard}', [\App\Http\Controllers\PaymentMethodsController::class, 'destroyForCurrentUser'])->name('payment-methods.destroy');

        Route::get('record-video', [VideoRecordingController::class, 'show'])->name('record-video.show');
        Route::post('record-video', [VideoRecordingController::class, 'store'])->name('record-video.store');

        Route::get('consultations/flow/{step?}', [ConsultationController::class, 'flow'])->name('consultations.flow');
        Route::get('consultations/create', [ConsultationController::class, 'create'])->name('consultations.create');
        Route::post('consultations', [ConsultationController::class, 'store'])->name('consultations.store');
        Route::get('consultations/{consultation}', [ConsultationController::class, 'view'])->name('consultations.view');

        Route::get('medical-records/create', [MedicalRecordController::class, 'create'])->name('medical-records.create');
        Route::post('medical-records', [MedicalRecordController::class, 'store'])->name('medical-records.store');

        // Medical Questionnaires
        Route::get('medical-questionnaires', [MedicalQuestionnaireController::class, 'index'])->name('medical-questionnaires.index');
        Route::get('medical-questionnaires/create', [MedicalQuestionnaireController::class, 'create'])->name('medical-questionnaires.create');
        Route::post('medical-questionnaires', [MedicalQuestionnaireController::class, 'store'])->name('medical-questionnaires.store');
        Route::get('medical-questionnaires/{questionnaire}/edit', [MedicalQuestionnaireController::class, 'edit'])->name('medical-questionnaires.edit');
        Route::put('medical-questionnaires/{questionnaire}', [MedicalQuestionnaireController::class, 'update'])->name('medical-questionnaires.update');

        Route::get('allergies/create', [AllergyController::class, 'showForm'])->name('allergies.create');
        Route::get('allergies/{allergy}/edit', [AllergyController::class, 'showForm'])->name('allergies.edit');
        Route::post('allergies/createOrUpdate', [AllergyController::class, 'createOrUpdate'])->name('allergies.create-or-update');
        Route::delete('allergies/{allergy}', [AllergyController::class, 'destroy'])->name('allergies.delete');

        // User Medications
        Route::get('medications/create', [UserMedicationController::class, 'showForm'])->name('medications.create');
        Route::get('medications/{medication}/edit', [UserMedicationController::class, 'showForm'])->name('medications.edit');
        Route::post('medications/createOrUpdate', [UserMedicationController::class, 'createOrUpdate'])->name('medications.create-or-update');
        Route::delete('medications/{medication}', [UserMedicationController::class, 'destroy'])->name('medications.delete');

        // Medical Conditions
        Route::get('medical-conditions', [MedicalConditionController::class, 'index'])->name('medical-conditions.index');
        Route::get('medical-conditions/create', [MedicalConditionController::class, 'create'])->name('medical-conditions.create');
        Route::post('medical-conditions', [MedicalConditionController::class, 'store'])->name('medical-conditions.store');
        Route::get('medical-conditions/{medicalCondition}', [MedicalConditionController::class, 'show'])->name('medical-conditions.show');
        Route::get('medical-conditions/{medicalCondition}/edit', [MedicalConditionController::class, 'edit'])->name('medical-conditions.edit');
        Route::put('medical-conditions/{medicalCondition}', [MedicalConditionController::class, 'update'])->name('medical-conditions.update');
        Route::delete('medical-conditions/{medicalCondition}', [MedicalConditionController::class, 'destroy'])->name('medical-conditions.destroy');

        // Family Members
        Route::middleware(['active.subscription'])->group(function () {
            Route::get('family-members', [FamilyMemberController::class, 'index'])->name('family-members.index');
            Route::get('family-members/create', [FamilyMemberController::class, 'create'])->name('family-members.create');
            Route::post('family-members', [FamilyMemberController::class, 'store'])->name('family-members.store');
            Route::get('family-members/{familyMember}', [FamilyMemberController::class, 'show'])->name('family-members.show');
            Route::get('family-members/{familyMember}/edit', [FamilyMemberController::class, 'edit'])->name('family-members.edit');
            Route::put('family-members/{familyMember}', [FamilyMemberController::class, 'update'])->name('family-members.update');
            Route::delete('family-members/{familyMember}', [FamilyMemberController::class, 'destroy'])->name('family-members.destroy');
            Route::get('family-members/{familyMember}/confirmation', [FamilyMemberController::class, 'confirmation'])->name('family-members.confirmation');
        });

        // Medication Orders
        Route::middleware(['active.subscription'])->group(function () {
            Route::get('medication-orders', [MedicationOrderController::class, 'index'])->name('medication-orders.index');
            Route::get('medication-orders/create', [MedicationOrderController::class, 'create'])->name('medication-orders.create');
            Route::post('medication-orders', [MedicationOrderController::class, 'store'])->name('medication-orders.store');
            Route::get('medication-orders/{medicationOrder}', [MedicationOrderController::class, 'show'])->name('medication-orders.show');
            Route::get('medication-orders/{medicationOrder}/edit', [MedicationOrderController::class, 'edit'])->name('medication-orders.edit');
            Route::put('medication-orders/{medicationOrder}', [MedicationOrderController::class, 'update'])->name('medication-orders.update');
            Route::post('medication-orders/{medicationOrder}/reorder', [MedicationOrderController::class, 'reorder'])->name('medication-orders.reorder');
            Route::delete('medication-orders/{medicationOrder}', [MedicationOrderController::class, 'destroy'])->name('medication-orders.destroy');
            Route::get('medication-orders/{medicationOrder}/pdf', [\App\Http\Controllers\Patient\MedicationOrderPdfController::class, 'export'])->name('medication-orders.pdf');
        });
    });
}); // Close the auth middleware group

Route::middleware('guest')->group(function () {
    Route::get('p/register/{serviceId}', [RegisterController::class, 'create'])->name('patient.register');
    Route::post('p/register/{serviceId}', [RegisterController::class, 'store'])->name('patient.register.submit');
});
