<?php

namespace Tests\Feature;

use App\Models\Medication;
use App\Models\Condition;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class PopulateMedicationConditionCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test medications
        Medication::create([
            'name' => 'Metformin',
            'generic_name' => 'Metformin',
            'description' => 'Used to treat type 2 diabetes',
            'dosage_form' => 'Tablet',
            'strength' => '500mg',
            'unit_price' => 0.50,
            'requires_prescription' => true,
            'controlled_substance' => false,
        ]);

        Medication::create([
            'name' => 'Ibuprofen',
            'generic_name' => 'Ibuprofen',
            'description' => 'Pain reliever and anti-inflammatory',
            'dosage_form' => 'Tablet',
            'strength' => '200mg',
            'unit_price' => 0.25,
            'requires_prescription' => false,
            'controlled_substance' => false,
        ]);

        // Create test conditions
        Condition::create([
            'name' => 'Type 2 Diabetes',
            'therapeutic_use' => 'Diabetes management',
        ]);

        Condition::create([
            'name' => 'Pain',
            'therapeutic_use' => 'Pain management',
        ]);

        Condition::create([
            'name' => 'Inflammation',
            'therapeutic_use' => 'Anti-inflammatory treatment',
        ]);
    }

    public function test_command_runs_successfully_with_dry_run()
    {
        $this->artisan('medication:populate-conditions --dry-run')
            ->expectsOutput('Starting medication-condition relationship population...')
            ->expectsOutput('This was a dry run. No actual records were created.')
            ->assertExitCode(0);
    }

    public function test_command_creates_medication_condition_relationships()
    {
        // Ensure the table is empty initially
        $this->assertEquals(0, DB::table('medication_condition')->count());

        // Run the command
        $this->artisan('medication:populate-conditions')
            ->expectsOutput('Starting medication-condition relationship population...')
            ->expectsOutput('Medication-condition relationships populated successfully!')
            ->assertExitCode(0);

        // Check that relationships were created
        $this->assertGreaterThan(0, DB::table('medication_condition')->count());

        // Check specific relationships
        $metformin = Medication::where('name', 'Metformin')->first();
        $diabetes = Condition::where('name', 'Type 2 Diabetes')->first();
        
        $this->assertDatabaseHas('medication_condition', [
            'medication_id' => $metformin->id,
            'condition_id' => $diabetes->id,
            'is_primary_use' => true,
        ]);

        $ibuprofen = Medication::where('name', 'Ibuprofen')->first();
        $pain = Condition::where('name', 'Pain')->first();
        
        $this->assertDatabaseHas('medication_condition', [
            'medication_id' => $ibuprofen->id,
            'condition_id' => $pain->id,
            'is_primary_use' => true,
        ]);
    }

    public function test_command_with_clear_option()
    {
        // First, create some relationships
        $metformin = Medication::where('name', 'Metformin')->first();
        $diabetes = Condition::where('name', 'Type 2 Diabetes')->first();
        
        DB::table('medication_condition')->insert([
            'medication_id' => $metformin->id,
            'condition_id' => $diabetes->id,
            'is_primary_use' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $this->assertEquals(1, DB::table('medication_condition')->count());

        // Run command with clear option
        $this->artisan('medication:populate-conditions --clear')
            ->expectsOutput('Clearing existing medication-condition relationships...')
            ->expectsOutput('Existing relationships cleared.')
            ->assertExitCode(0);

        // Check that new relationships were created
        $this->assertGreaterThan(0, DB::table('medication_condition')->count());
    }

    public function test_command_with_specific_medication()
    {
        $metformin = Medication::where('name', 'Metformin')->first();

        $this->artisan("medication:populate-conditions --medication={$metformin->id}")
            ->expectsOutput('Starting medication-condition relationship population...')
            ->expectsOutput("Processing 1 medications and 3 conditions...")
            ->assertExitCode(0);

        // Should only create relationships for Metformin
        $relationships = DB::table('medication_condition')
            ->where('medication_id', $metformin->id)
            ->count();
        
        $this->assertGreaterThan(0, $relationships);
    }

    public function test_command_skips_existing_relationships()
    {
        $metformin = Medication::where('name', 'Metformin')->first();
        $diabetes = Condition::where('name', 'Type 2 Diabetes')->first();
        
        // Create an existing relationship
        DB::table('medication_condition')->insert([
            'medication_id' => $metformin->id,
            'condition_id' => $diabetes->id,
            'is_primary_use' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $initialCount = DB::table('medication_condition')->count();

        $this->artisan('medication:populate-conditions')
            ->expectsOutput('Starting medication-condition relationship population...')
            ->assertExitCode(0);

        // Should have more relationships than initially, but should skip the existing one
        $finalCount = DB::table('medication_condition')->count();
        $this->assertGreaterThan($initialCount, $finalCount);
    }

    public function test_command_handles_no_medications()
    {
        // Clear all medications
        Medication::truncate();

        $this->artisan('medication:populate-conditions')
            ->expectsOutput('No medications found to process.')
            ->assertExitCode(1);
    }

    public function test_command_handles_no_conditions()
    {
        // Clear all conditions
        Condition::truncate();

        $this->artisan('medication:populate-conditions')
            ->expectsOutput('No conditions found to process.')
            ->assertExitCode(1);
    }
}
